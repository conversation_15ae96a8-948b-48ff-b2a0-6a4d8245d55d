@echo off
setlocal enabledelayedexpansion

REM IntegriWISE Web - Complete Setup Script for Windows
REM This script sets up the complete development environment

echo.
echo 🚀 Setting up IntegriWISE Web - Complete Fitness-for-Service Assessment Platform
echo ==============================================================================
echo.

REM Check if Node.js is installed
echo [INFO] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18+ and npm 8+
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=1 delims=." %%a in ('node --version') do set NODE_MAJOR=%%a
set NODE_MAJOR=%NODE_MAJOR:v=%
if %NODE_MAJOR% LSS 18 (
    echo [ERROR] Node.js version 18+ is required. Current version: 
    node --version
    pause
    exit /b 1
)

echo [SUCCESS] Node.js is installed
node --version

REM Check if PostgreSQL is available
echo [INFO] Checking PostgreSQL installation...
psql --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] PostgreSQL is not installed or not in PATH
    echo [WARNING] Please install PostgreSQL 15+ and ensure it's running
    echo [WARNING] You can download it from: https://www.postgresql.org/download/
    set SKIP_DB=1
) else (
    echo [SUCCESS] PostgreSQL is available
    psql --version
    set SKIP_DB=0
)

REM Install dependencies
echo [INFO] Installing dependencies...
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install root dependencies
    pause
    exit /b 1
)

echo [INFO] Installing server dependencies...
cd server
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install server dependencies
    pause
    exit /b 1
)
cd ..

echo [INFO] Installing client dependencies...
cd client
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install client dependencies
    pause
    exit /b 1
)
cd ..

echo [SUCCESS] All dependencies installed

REM Setup environment files
echo [INFO] Setting up environment files...

if not exist "server\.env" (
    copy "server\.env.example" "server\.env" >nul
    echo [SUCCESS] Server .env file created from example
    echo [WARNING] Please update server/.env with your database credentials
) else (
    echo [WARNING] Server .env file already exists
)

if not exist "client\.env" (
    copy "client\.env.example" "client\.env" >nul
    echo [SUCCESS] Client .env file created from example
) else (
    echo [WARNING] Client .env file already exists
)

REM Create necessary directories
echo [INFO] Creating necessary directories...
if not exist "server\uploads" mkdir "server\uploads"
if not exist "server\logs" mkdir "server\logs"
if not exist "server\backups" mkdir "server\backups"
if not exist "client\public\assets" mkdir "client\public\assets"
echo [SUCCESS] Directories created

REM Database setup (if PostgreSQL is available)
if %SKIP_DB%==0 (
    echo [INFO] Setting up database...
    
    REM Check if database exists
    psql -lqt | findstr "integriwise_web" >nul 2>&1
    if not errorlevel 1 (
        echo [WARNING] Database 'integriwise_web' already exists
        set /p RECREATE="Do you want to drop and recreate it? (y/N): "
        if /i "!RECREATE!"=="y" (
            dropdb integriwise_web 2>nul
            createdb integriwise_web
            echo [SUCCESS] Database recreated
        ) else (
            echo [INFO] Using existing database
        )
    ) else (
        createdb integriwise_web
        echo [SUCCESS] Database 'integriwise_web' created
    )
    
    echo [INFO] Running database migrations...
    cd server
    call npm run db:migrate
    if errorlevel 1 (
        echo [ERROR] Failed to run migrations
        cd ..
        pause
        exit /b 1
    )
    echo [SUCCESS] Database migrations completed
    
    echo [INFO] Seeding database with initial data...
    call npm run db:seed
    if errorlevel 1 (
        echo [ERROR] Failed to seed database
        cd ..
        pause
        exit /b 1
    )
    echo [SUCCESS] Database seeded with initial data
    cd ..
) else (
    echo [WARNING] Skipping database setup. Please install PostgreSQL and run:
    echo [WARNING]   createdb integriwise_web
    echo [WARNING]   npm run db:migrate
    echo [WARNING]   npm run db:seed
)

REM Build TypeScript
echo [INFO] Building TypeScript...
cd server
call npm run build
if errorlevel 1 (
    echo [ERROR] Failed to build TypeScript
    cd ..
    pause
    exit /b 1
)
echo [SUCCESS] Server TypeScript built
cd ..

echo.
echo [SUCCESS] Setup completed successfully!
echo.
echo 🎉 IntegriWISE Web is ready for development!
echo.
echo Next steps:
echo 1. Update server/.env with your database credentials (if not done already)
echo 2. Start the development server:
echo    npm run dev
echo.
echo The application will be available at:
echo   Frontend: http://localhost:3000
echo   Backend API: http://localhost:5000
echo   API Documentation: http://localhost:5000/api/docs
echo.
echo Default login credentials:
echo   Email: <EMAIL>
echo   Password: admin123
echo.
echo For more information, see README.md
echo.
pause
