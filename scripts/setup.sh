#!/bin/bash

# IntegriWISE Web - Complete Setup Script
# This script sets up the complete development environment

set -e

echo "🚀 Setting up IntegriWISE Web - Complete Fitness-for-Service Assessment Platform"
echo "=============================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ and npm 8+"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_success "Node.js $(node --version) is installed"
}

# Check if PostgreSQL is installed
check_postgres() {
    if ! command -v psql &> /dev/null; then
        print_warning "PostgreSQL is not installed or not in PATH"
        print_warning "Please install PostgreSQL 15+ and ensure it's running"
        print_warning "You can download it from: https://www.postgresql.org/download/"
        return 1
    fi
    
    print_success "PostgreSQL is available"
    return 0
}

# Create database
create_database() {
    print_status "Creating PostgreSQL database..."
    
    # Check if database exists
    if psql -lqt | cut -d \| -f 1 | grep -qw integriwise_web; then
        print_warning "Database 'integriwise_web' already exists"
        read -p "Do you want to drop and recreate it? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            dropdb integriwise_web 2>/dev/null || true
            createdb integriwise_web
            print_success "Database recreated"
        else
            print_status "Using existing database"
        fi
    else
        createdb integriwise_web
        print_success "Database 'integriwise_web' created"
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install root dependencies
    npm install
    
    # Install server dependencies
    print_status "Installing server dependencies..."
    cd server
    npm install
    cd ..
    
    # Install client dependencies
    print_status "Installing client dependencies..."
    cd client
    npm install
    cd ..
    
    print_success "All dependencies installed"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Server environment
    if [ ! -f "server/.env" ]; then
        cp server/.env.example server/.env
        print_success "Server .env file created from example"
        print_warning "Please update server/.env with your database credentials"
    else
        print_warning "Server .env file already exists"
    fi
    
    # Client environment
    if [ ! -f "client/.env" ]; then
        cp client/.env.example client/.env
        print_success "Client .env file created from example"
    else
        print_warning "Client .env file already exists"
    fi
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    cd server
    npm run db:migrate
    print_success "Database migrations completed"
    cd ..
}

# Seed database
seed_database() {
    print_status "Seeding database with initial data..."
    cd server
    npm run db:seed
    print_success "Database seeded with initial data"
    cd ..
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p server/uploads
    mkdir -p server/logs
    mkdir -p server/backups
    mkdir -p client/public/assets
    
    print_success "Directories created"
}

# Build TypeScript
build_typescript() {
    print_status "Building TypeScript..."
    cd server
    npm run build
    print_success "Server TypeScript built"
    cd ..
}

# Main setup function
main() {
    echo
    print_status "Starting setup process..."
    echo
    
    # Check prerequisites
    check_node
    
    # Install dependencies
    install_dependencies
    
    # Setup environment
    setup_environment
    
    # Create directories
    create_directories
    
    # Check PostgreSQL and setup database
    if check_postgres; then
        create_database
        run_migrations
        seed_database
    else
        print_warning "Skipping database setup. Please install PostgreSQL and run:"
        print_warning "  createdb integriwise_web"
        print_warning "  npm run db:migrate"
        print_warning "  npm run db:seed"
    fi
    
    # Build TypeScript
    build_typescript
    
    echo
    print_success "Setup completed successfully!"
    echo
    echo "🎉 IntegriWISE Web is ready for development!"
    echo
    echo "Next steps:"
    echo "1. Update server/.env with your database credentials (if not done already)"
    echo "2. Start the development server:"
    echo "   npm run dev"
    echo
    echo "The application will be available at:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:5000"
    echo "  API Documentation: http://localhost:5000/api/docs"
    echo
    echo "Default login credentials:"
    echo "  Email: <EMAIL>"
    echo "  Password: admin123"
    echo
    echo "For more information, see README.md"
    echo
}

# Run main function
main "$@"
