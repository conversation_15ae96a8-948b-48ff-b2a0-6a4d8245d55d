# API Configuration
REACT_APP_API_URL=http://localhost:5000/api/v1
REACT_APP_API_TIMEOUT=30000

# Application Configuration
REACT_APP_NAME=IntegriWISE Web
REACT_APP_VERSION=1.0.0
REACT_APP_DESCRIPTION=Complete Fitness-for-Service Assessment Platform

# Feature Flags
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_DEBUG=true
REACT_APP_ENABLE_MOCK_DATA=false

# File Upload Configuration
REACT_APP_MAX_FILE_SIZE=10485760
REACT_APP_ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,csv,txt,jpg,jpeg,png

# Chart and Visualization
REACT_APP_CHART_THEME=light
REACT_APP_DEFAULT_CHART_HEIGHT=400

# Pagination
REACT_APP_DEFAULT_PAGE_SIZE=10
REACT_APP_MAX_PAGE_SIZE=100

# Cache Configuration
REACT_APP_CACHE_DURATION=300000

# Error Reporting
REACT_APP_SENTRY_DSN=
REACT_APP_ENABLE_ERROR_REPORTING=false

# Development
GENERATE_SOURCEMAP=true
REACT_APP_MOCK_API=false
