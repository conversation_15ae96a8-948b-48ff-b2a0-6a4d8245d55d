import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Box } from '@mui/material';

import { RootState } from '@/store';
import { checkAuthStatus } from '@/store/slices/authSlice';
import { AppDispatch } from '@/store';

// Layout Components
import MainLayout from '@/components/layout/MainLayout';
import AuthLayout from '@/components/layout/AuthLayout';
import LoadingScreen from '@/components/common/LoadingScreen';

// Pages
import LoginPage from '@/pages/auth/LoginPage';
import RegisterPage from '@/pages/auth/RegisterPage';
import DashboardPage from '@/pages/DashboardPage';
import SitesPage from '@/pages/sites/SitesPage';
import FacilitiesPage from '@/pages/facilities/FacilitiesPage';
import EquipmentPage from '@/pages/equipment/EquipmentPage';
import ComponentsPage from '@/pages/components/ComponentsPage';
import AssessmentsPage from '@/pages/assessments/AssessmentsPage';
import AssessmentDetailPage from '@/pages/assessments/AssessmentDetailPage';
import CreateAssessmentPage from '@/pages/assessments/CreateAssessmentPage';
import MaterialsPage from '@/pages/materials/MaterialsPage';
import ReportsPage from '@/pages/reports/ReportsPage';
import UsersPage from '@/pages/users/UsersPage';
import ProfilePage from '@/pages/profile/ProfilePage';
import SettingsPage from '@/pages/settings/SettingsPage';
import NotFoundPage from '@/pages/NotFoundPage';

// Route Guards
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import AdminRoute from '@/components/auth/AdminRoute';

function App() {
  const dispatch = useDispatch<AppDispatch>();
  const { isAuthenticated, isLoading, user } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    // Check if user is authenticated on app load
    dispatch(checkAuthStatus());
  }, [dispatch]);

  // Show loading screen while checking authentication
  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <Routes>
        {/* Public Routes */}
        <Route
          path="/login"
          element={
            !isAuthenticated ? (
              <AuthLayout>
                <LoginPage />
              </AuthLayout>
            ) : (
              <Navigate to="/dashboard" replace />
            )
          }
        />
        <Route
          path="/register"
          element={
            !isAuthenticated ? (
              <AuthLayout>
                <RegisterPage />
              </AuthLayout>
            ) : (
              <Navigate to="/dashboard" replace />
            )
          }
        />

        {/* Protected Routes */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <MainLayout>
                <Routes>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/dashboard" element={<DashboardPage />} />
                  
                  {/* Asset Management */}
                  <Route path="/sites" element={<SitesPage />} />
                  <Route path="/facilities" element={<FacilitiesPage />} />
                  <Route path="/equipment" element={<EquipmentPage />} />
                  <Route path="/components" element={<ComponentsPage />} />
                  
                  {/* Assessments */}
                  <Route path="/assessments" element={<AssessmentsPage />} />
                  <Route path="/assessments/create" element={<CreateAssessmentPage />} />
                  <Route path="/assessments/:id" element={<AssessmentDetailPage />} />
                  
                  {/* Materials */}
                  <Route path="/materials" element={<MaterialsPage />} />
                  
                  {/* Reports */}
                  <Route path="/reports" element={<ReportsPage />} />
                  
                  {/* User Management - Admin Only */}
                  <Route
                    path="/users"
                    element={
                      <AdminRoute>
                        <UsersPage />
                      </AdminRoute>
                    }
                  />
                  
                  {/* User Profile */}
                  <Route path="/profile" element={<ProfilePage />} />
                  
                  {/* Settings - Admin Only */}
                  <Route
                    path="/settings"
                    element={
                      <AdminRoute>
                        <SettingsPage />
                      </AdminRoute>
                    }
                  />
                  
                  {/* 404 Page */}
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </MainLayout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </Box>
  );
}

export default App;
