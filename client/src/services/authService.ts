import { apiService, ApiResponse } from './api';
import { User, AuthTokens } from '@/types';

// Auth service types
interface LoginCredentials {
  email: string;
  password: string;
}

interface RegisterData {
  username: string;
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  role?: string;
}

interface LoginResponse {
  user: User;
  tokens: AuthTokens;
}

interface ProfileResponse {
  user: User;
}

interface TokenResponse {
  tokens: AuthTokens;
}

// Auth service
export const authService = {
  // Login user
  login: (credentials: LoginCredentials): Promise<{ data: LoginResponse }> => {
    return apiService.post<LoginResponse>('/auth/login', credentials);
  },

  // Register user
  register: (userData: RegisterData): Promise<{ data: LoginResponse }> => {
    return apiService.post<LoginResponse>('/auth/register', userData);
  },

  // Refresh access token
  refreshToken: (refreshToken: string): Promise<{ data: TokenResponse }> => {
    return apiService.post<TokenResponse>('/auth/refresh', { refreshToken });
  },

  // Get user profile
  getProfile: (): Promise<{ data: ProfileResponse }> => {
    return apiService.get<ProfileResponse>('/auth/profile');
  },

  // Update user profile
  updateProfile: (profileData: Partial<User>): Promise<{ data: ProfileResponse }> => {
    return apiService.put<ProfileResponse>('/auth/profile', profileData);
  },

  // Change password
  changePassword: (passwordData: { currentPassword: string; newPassword: string }): Promise<ApiResponse> => {
    return apiService.post('/auth/change-password', passwordData);
  },

  // Logout user
  logout: (): Promise<ApiResponse> => {
    return apiService.post('/auth/logout');
  },

  // Forgot password (if implemented)
  forgotPassword: (email: string): Promise<ApiResponse> => {
    return apiService.post('/auth/forgot-password', { email });
  },

  // Reset password (if implemented)
  resetPassword: (token: string, newPassword: string): Promise<ApiResponse> => {
    return apiService.post('/auth/reset-password', { token, newPassword });
  },

  // Verify email (if implemented)
  verifyEmail: (token: string): Promise<ApiResponse> => {
    return apiService.post('/auth/verify-email', { token });
  },

  // Resend verification email (if implemented)
  resendVerification: (email: string): Promise<ApiResponse> => {
    return apiService.post('/auth/resend-verification', { email });
  },
};

export default authService;
