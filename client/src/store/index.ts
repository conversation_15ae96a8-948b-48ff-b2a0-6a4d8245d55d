import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { combineReducers } from '@reduxjs/toolkit';

// Import slices
import authSlice from './slices/authSlice';
import uiSlice from './slices/uiSlice';
import sitesSlice from './slices/sitesSlice';
import facilitiesSlice from './slices/facilitiesSlice';
import equipmentSlice from './slices/equipmentSlice';
import componentsSlice from './slices/componentsSlice';
import assessmentsSlice from './slices/assessmentsSlice';
import materialsSlice from './slices/materialsSlice';
import usersSlice from './slices/usersSlice';

// Persist configuration
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth'], // Only persist auth state
};

// Root reducer
const rootReducer = combineReducers({
  auth: authSlice,
  ui: uiSlice,
  sites: sitesSlice,
  facilities: facilitiesSlice,
  equipment: equipmentSlice,
  components: componentsSlice,
  assessments: assessmentsSlice,
  materials: materialsSlice,
  users: usersSlice,
});

// Persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// Persistor
export const persistor = persistStore(store);

// Types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;
