import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Component } from '@/types';

interface ComponentsState {
  components: Component[];
  loading: boolean;
  error: string | null;
}

const initialState: ComponentsState = {
  components: [],
  loading: false,
  error: null,
};

const componentsSlice = createSlice({
  name: 'components',
  initialState,
  reducers: {
    setComponents: (state, action: PayloadAction<Component[]>) => {
      state.components = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setComponents, setLoading, setError } = componentsSlice.actions;
export default componentsSlice.reducer;
