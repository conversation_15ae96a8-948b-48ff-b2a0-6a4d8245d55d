import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Assessment } from '@/types';

interface AssessmentsState {
  assessments: Assessment[];
  loading: boolean;
  error: string | null;
}

const initialState: AssessmentsState = {
  assessments: [],
  loading: false,
  error: null,
};

const assessmentsSlice = createSlice({
  name: 'assessments',
  initialState,
  reducers: {
    setAssessments: (state, action: PayloadAction<Assessment[]>) => {
      state.assessments = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setAssessments, setLoading, setError } = assessmentsSlice.actions;
export default assessmentsSlice.reducer;
