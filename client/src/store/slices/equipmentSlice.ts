import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Equipment } from '@/types';

interface EquipmentState {
  equipment: Equipment[];
  loading: boolean;
  error: string | null;
}

const initialState: EquipmentState = {
  equipment: [],
  loading: false,
  error: null,
};

const equipmentSlice = createSlice({
  name: 'equipment',
  initialState,
  reducers: {
    setEquipment: (state, action: PayloadAction<Equipment[]>) => {
      state.equipment = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setEquipment, setLoading, setError } = equipmentSlice.actions;
export default equipmentSlice.reducer;
