import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Site } from '@/types';

interface SitesState {
  sites: Site[];
  loading: boolean;
  error: string | null;
  selectedSite: Site | null;
}

const initialState: SitesState = {
  sites: [],
  loading: false,
  error: null,
  selectedSite: null,
};

const sitesSlice = createSlice({
  name: 'sites',
  initialState,
  reducers: {
    setSites: (state, action: PayloadAction<Site[]>) => {
      state.sites = action.payload;
    },
    addSite: (state, action: PayloadAction<Site>) => {
      state.sites.push(action.payload);
    },
    updateSite: (state, action: PayloadAction<Site>) => {
      const index = state.sites.findIndex(site => site.id === action.payload.id);
      if (index !== -1) {
        state.sites[index] = action.payload;
      }
    },
    removeSite: (state, action: PayloadAction<string>) => {
      state.sites = state.sites.filter(site => site.id !== action.payload);
    },
    setSelectedSite: (state, action: PayloadAction<Site | null>) => {
      state.selectedSite = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  setSites,
  addSite,
  updateSite,
  removeSite,
  setSelectedSite,
  setLoading,
  setError,
} = sitesSlice.actions;

export default sitesSlice.reducer;
