import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Facility } from '@/types';

interface FacilitiesState {
  facilities: Facility[];
  loading: boolean;
  error: string | null;
}

const initialState: FacilitiesState = {
  facilities: [],
  loading: false,
  error: null,
};

const facilitiesSlice = createSlice({
  name: 'facilities',
  initialState,
  reducers: {
    setFacilities: (state, action: PayloadAction<Facility[]>) => {
      state.facilities = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setFacilities, setLoading, setError } = facilitiesSlice.actions;
export default facilitiesSlice.reducer;
