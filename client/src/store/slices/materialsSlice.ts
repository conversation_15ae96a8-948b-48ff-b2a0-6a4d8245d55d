import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Material } from '@/types';

interface MaterialsState {
  materials: Material[];
  loading: boolean;
  error: string | null;
}

const initialState: MaterialsState = {
  materials: [],
  loading: false,
  error: null,
};

const materialsSlice = createSlice({
  name: 'materials',
  initialState,
  reducers: {
    setMaterials: (state, action: PayloadAction<Material[]>) => {
      state.materials = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const { setMaterials, setLoading, setError } = materialsSlice.actions;
export default materialsSlice.reducer;
