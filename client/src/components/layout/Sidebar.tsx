import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Chip,
} from '@mui/material';
import {
  Dashboard,
  Business,
  Factory,
  Precision,
  Engineering,
  Assessment,
  Science,
  Description,
  People,
  Settings,
  AdminPanelSettings,
} from '@mui/icons-material';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';

import { RootState } from '@/store';
import { UserRole } from '@/types';

interface SidebarProps {
  onItemClick?: () => void;
}

interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: React.ComponentType;
  roles?: UserRole[];
  badge?: string;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    path: '/dashboard',
    icon: Dashboard,
  },
  {
    id: 'sites',
    label: 'Sites',
    path: '/sites',
    icon: Business,
  },
  {
    id: 'facilities',
    label: 'Facilities',
    path: '/facilities',
    icon: Factory,
  },
  {
    id: 'equipment',
    label: 'Equipment',
    path: '/equipment',
    icon: Precision,
  },
  {
    id: 'components',
    label: 'Components',
    path: '/components',
    icon: Engineering,
  },
  {
    id: 'assessments',
    label: 'Assessments',
    path: '/assessments',
    icon: Assessment,
  },
  {
    id: 'materials',
    label: 'Materials',
    path: '/materials',
    icon: Science,
  },
  {
    id: 'reports',
    label: 'Reports',
    path: '/reports',
    icon: Description,
  },
  {
    id: 'users',
    label: 'Users',
    path: '/users',
    icon: People,
    roles: [UserRole.ADMIN],
  },
  {
    id: 'settings',
    label: 'Settings',
    path: '/settings',
    icon: Settings,
    roles: [UserRole.ADMIN],
  },
];

const Sidebar: React.FC<SidebarProps> = ({ onItemClick }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);

  const handleItemClick = (path: string) => {
    navigate(path);
    onItemClick?.();
  };

  const isItemVisible = (item: NavigationItem): boolean => {
    if (!item.roles) return true;
    return user?.role ? item.roles.includes(user.role) : false;
  };

  const isItemActive = (path: string): boolean => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo and Title */}
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h5" fontWeight="bold" color="primary.main">
          IntegriWISE
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Fitness-for-Service Assessment
        </Typography>
        {user?.role === UserRole.ADMIN && (
          <Chip
            label="Admin"
            size="small"
            color="primary"
            sx={{ mt: 1 }}
            icon={<AdminPanelSettings />}
          />
        )}
      </Box>

      <Divider />

      {/* Navigation */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <List sx={{ px: 2, py: 1 }}>
          {navigationItems
            .filter(isItemVisible)
            .map((item) => {
              const Icon = item.icon;
              const isActive = isItemActive(item.path);

              return (
                <ListItem key={item.id} disablePadding sx={{ mb: 0.5 }}>
                  <ListItemButton
                    onClick={() => handleItemClick(item.path)}
                    selected={isActive}
                    sx={{
                      borderRadius: 2,
                      '&.Mui-selected': {
                        bgcolor: 'primary.main',
                        color: 'primary.contrastText',
                        '&:hover': {
                          bgcolor: 'primary.dark',
                        },
                        '& .MuiListItemIcon-root': {
                          color: 'primary.contrastText',
                        },
                      },
                      '&:hover': {
                        bgcolor: isActive ? 'primary.dark' : 'action.hover',
                      },
                    }}
                  >
                    <ListItemIcon
                      sx={{
                        minWidth: 40,
                        color: isActive ? 'inherit' : 'text.secondary',
                      }}
                    >
                      <Icon />
                    </ListItemIcon>
                    <ListItemText
                      primary={item.label}
                      primaryTypographyProps={{
                        fontSize: '0.875rem',
                        fontWeight: isActive ? 600 : 400,
                      }}
                    />
                    {item.badge && (
                      <Chip
                        label={item.badge}
                        size="small"
                        color="secondary"
                        sx={{ ml: 1, height: 20, fontSize: '0.75rem' }}
                      />
                    )}
                  </ListItemButton>
                </ListItem>
              );
            })}
        </List>
      </Box>

      <Divider />

      {/* Footer */}
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="caption" color="text.secondary">
          Version 1.0.0
        </Typography>
        <br />
        <Typography variant="caption" color="text.secondary">
          Courtesy of{' '}
          <a
            href="https://linkedin.com/in/whereishassan"
            target="_blank"
            rel="noopener noreferrer"
            style={{ color: 'inherit', textDecoration: 'none' }}
          >
            Hassan Hany
          </a>
        </Typography>
        <br />
        <Box sx={{ mt: 1, display: 'flex', justifyContent: 'center' }}>
          <img
            src="/assets/scimitar-logo.svg"
            alt="Scimitar Production Egypt Ltd"
            style={{ height: 24, opacity: 0.7 }}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default Sidebar;
