{"name": "integriwise-web-client", "version": "1.0.0", "description": "Frontend for IntegriWISE Web application", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "@reduxjs/toolkit": "^1.9.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.3", "react-router-dom": "^6.20.1", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "axios": "^1.6.2", "date-fns": "^2.30.0", "lodash": "^4.17.21", "recharts": "^2.8.0", "react-pdf": "^7.5.1", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.4.1", "formik": "^2.4.5", "yup": "^1.3.3", "moment": "^2.29.4", "numeral": "^2.0.6", "react-helmet-async": "^1.3.0", "web-vitals": "^3.5.0"}, "devDependencies": {"@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "@types/lodash": "^4.14.202", "@types/numeral": "^2.0.5", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "typescript": "^5.2.2", "react-scripts": "5.0.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}