// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.BaseDialog
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.UserInterface;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView;

public class BaseDialog : XtraForm, IBaseDialogView, IView
{
  private const int FooterHeight = 41;
  private const int HeaderHeight = 67;
  private IContainer components;
  private Panel pnlTop;
  public Panel pnlBottom;
  private bool footerVisible;
  private Image headerImage;
  private string headerText;
  private Font headerTextFont;
  private string headerTitle;
  private Font headerTitleFont;
  private bool headerVisible;
  private BaseDialogPresenter presenter;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (BaseDialog));
    this.pnlTop = new Panel();
    this.pnlBottom = new Panel();
    this.SuspendLayout();
    this.pnlTop.BackColor = Color.White;
    this.pnlTop.Dock = DockStyle.Top;
    this.pnlTop.Location = new Point(0, 0);
    this.pnlTop.Name = "pnlTop";
    this.pnlTop.Size = new Size(511 /*0x01FF*/, 62);
    this.pnlTop.TabIndex = 0;
    this.pnlTop.Paint += new PaintEventHandler(this.pnlTop_Paint);
    this.pnlBottom.Dock = DockStyle.Bottom;
    this.pnlBottom.Location = new Point(0, 419);
    this.pnlBottom.Name = "pnlBottom";
    this.pnlBottom.Size = new Size(511 /*0x01FF*/, 41);
    this.pnlBottom.TabIndex = 1;
    this.pnlBottom.Paint += new PaintEventHandler(this.pnlBottom_Paint);
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.ClientSize = new Size(511 /*0x01FF*/, 460);
    this.Controls.Add((Control) this.pnlBottom);
    this.Controls.Add((Control) this.pnlTop);
    this.Icon = (Icon) componentResourceManager.GetObject("$this.Icon");
    this.Name = nameof (BaseDialog);
    this.ShowIcon = false;
    this.Text = "IntegriWISE";
    this.ResumeLayout(false);
  }

  public BaseDialog()
  {
    this.InitializeComponent();
    this.ApplyDefaultValues();
    this.SetStyle(ControlStyles.AllPaintingInWmPaint, true);
    this.SetStyle(ControlStyles.OptimizedDoubleBuffer, true);
    this.SetStyle(ControlStyles.UserPaint, true);
    this.SetStyle(ControlStyles.ResizeRedraw, true);
    this.presenter = new BaseDialogPresenter((IBaseDialogView) this);
  }

  protected override void OnPaint(PaintEventArgs e) => base.OnPaint(e);

  private void ApplyDefaultValues()
  {
    this.headerVisible = true;
    this.headerTitle = "Enter Title";
    this.headerText = "Enter a short description for this dialog";
    this.headerTitleFont = new Font("Arial", 10.25f, FontStyle.Bold);
    this.headerTextFont = new Font("Arial", 8.25f, FontStyle.Regular);
    this.footerVisible = true;
    this.FormBorderStyle = FormBorderStyle.Sizable;
    this.StartPosition = FormStartPosition.CenterParent;
    this.SizeGripStyle = SizeGripStyle.Hide;
    this.Size = new Size(600, 400);
    this.MinimizeBox = false;
    this.MaximizeBox = false;
    this.ShowInTaskbar = false;
    this.ShowIcon = false;
    this.pnlBottom.Height = 41;
    this.pnlTop.Height = 67;
  }

  private void pnlBottom_Paint(object sender, PaintEventArgs e)
  {
    this.presenter.PaintFooter(e.Graphics);
  }

  private void pnlTop_Paint(object sender, PaintEventArgs e)
  {
    this.presenter.PaintHeader(e.Graphics);
  }

  public void DrawFooter(Graphics g)
  {
    Color darkerColor = this.presenter.GetDarkerColor(this.BackColor, 30);
    int num = this.Height - 41;
    g.DrawLine(new Pen(Brushes.White), 0, num, this.Width, num);
    g.DrawLine(new Pen(darkerColor), 0, num - 1, this.Width, num - 1);
  }

  public void DrawHeader(Graphics g)
  {
    Color darkerColor = this.presenter.GetDarkerColor(this.BackColor, 30);
    int num = 65;
    g.FillRectangle(Brushes.White, 0, 0, this.Width, 67);
    g.DrawLine(new Pen(darkerColor), 0, num, this.Width, num);
    this.presenter.DrawHeaderImage(g);
  }

  public void DrawImageWithoutHeader(Graphics g)
  {
    g.DrawString(this.headerTitle, this.headerTitleFont, Brushes.Black, 15f, 16f);
    g.DrawString(this.headerText, this.headerTextFont, Brushes.Black, 25f, 36f);
  }

  public void DrawImageWithHeader(Graphics g)
  {
    g.DrawString(this.headerTitle, this.headerTitleFont, Brushes.Black, 55f, 16f);
    g.DrawString(this.headerText, this.headerTextFont, Brushes.Black, 65f, 36f);
    g.DrawImage(this.headerImage, 15, 16 /*0x10*/);
  }

  [Category("Footer")]
  public bool FooterVisible
  {
    get => this.footerVisible;
    set
    {
      this.footerVisible = value;
      this.Invalidate();
    }
  }

  [Category("Header")]
  public Image HeaderImage
  {
    get => this.headerImage;
    set
    {
      this.headerImage = value;
      this.Invalidate();
    }
  }

  [Localizable(true)]
  [Category("Header")]
  public string HeaderText
  {
    get => this.headerText;
    set
    {
      this.headerText = value;
      this.Invalidate();
    }
  }

  [Category("Header")]
  [Localizable(true)]
  public string HeaderTitle
  {
    get => this.headerTitle;
    set
    {
      this.headerTitle = value;
      this.Invalidate();
    }
  }

  [Category("Header")]
  public bool HeaderVisible
  {
    get => this.headerVisible;
    set
    {
      this.headerVisible = value;
      this.Invalidate();
    }
  }

  string IBaseDialogView.Title
  {
    set => this.Text = value;
  }
}
