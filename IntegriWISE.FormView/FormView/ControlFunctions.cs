// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.ControlFunctions
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using System;
using System.Windows.Forms;
using System.Windows.Forms.Layout;

#nullable disable
namespace IntegriWISE.FormView;

public class ControlFunctions
{
  public static void DisableControl(Control c)
  {
    if (c is BaseEdit baseEdit)
    {
      System.Type type = ((BaseEdit) c).Properties.GetType();
      switch (type.Name)
      {
        case "RepositoryItemCheckEdit":
          baseEdit.Properties.ReadOnly = true;
          break;
        case "SimpleButton":
          baseEdit.Enabled = false;
          break;
        case "RepositoryItemButtonEdit":
          baseEdit.Properties.ReadOnly = true;
          if (baseEdit is ButtonEdit buttonEdit && buttonEdit.Properties.Buttons.Count > 0)
            buttonEdit.Properties.Buttons[0].Enabled = false;
          baseEdit.Properties.AppearanceReadOnly.Options.UseBackColor = false;
          break;
        case "RepositoryItemLookUpEdit":
        case "RepositoryItemComboBox":
        case "RepositoryItemGridLookUpEdit":
        case "RepositoryItemImageComboBox":
        case "RepositoryItemTextEdit":
        case "RepositoryItemMemoEdit":
        case "RepositoryItemSpinEdit":
          baseEdit.Properties.ReadOnly = true;
          break;
        case "RepositoryItemMemoExEdit":
          baseEdit.Properties.ReadOnly = true;
          if (baseEdit is MemoExEdit memoExEdit && memoExEdit.Properties.Buttons.Count > 0)
          {
            memoExEdit.Properties.Buttons[0].Enabled = false;
            break;
          }
          break;
        case "RepositoryItemDateEdit":
          baseEdit.Properties.ReadOnly = true;
          (baseEdit as DateEdit).Properties.Buttons[0].Enabled = false;
          break;
        default:
          Console.WriteLine(type.Name);
          break;
      }
    }
    if (c is SimpleButton)
    {
      c.Enabled = false;
    }
    else
    {
      if (baseEdit != null)
        return;
      foreach (Control control in (ArrangedElementCollection) c.Controls)
        ControlFunctions.DisableControl(control);
    }
  }
}
