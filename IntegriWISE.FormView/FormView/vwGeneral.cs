// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.vwGeneral
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.Common;
using IntegriWISE.DataTransferObjects;
using IntegriWISE.FormView.Properties;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.General;
using IntegriWISE.UserInterface.Material;
using IntegriWISE.UserInterface.Record;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView;

public class vwGeneral : RecordTabControl, IGeneralView, IAssessmentTypeView, IView
{
  private IContainer components;
  private GroupControl groupControl1;
  private GroupControl groupControl2;
  private GroupControl groupControl3;
  private TableLayoutPanel tableLayoutPanel1;
  private TextEdit txtEquipmentNumber;
  private LabelControl labelControl4;
  private LabelControl labelControl6;
  private LabelControl labelControl3;
  private LabelControl labelControl1;
  private LabelControl labelControl7;
  private LabelControl labelControl2;
  private LabelControl labelControl8;
  private LabelControl labelControl11;
  private LabelControl labelControl5;
  private TableLayoutPanel tableLayoutPanel5;
  private LabelControl umHydrotestPressure;
  private TableLayoutPanel tableLayoutPanel4;
  private LabelControl umMinOperatingTemperature;
  private TextEdit txtMinOperatingTemperature;
  private TableLayoutPanel tableLayoutPanel3;
  private LabelControl umDesignTemperature;
  private TextEdit txtDesignTemperature;
  private TableLayoutPanel tableLayoutPanel2;
  private LabelControl umDesignPressure;
  private TextEdit txtDesignPressure;
  private TextEdit txtDesignCode;
  private TableLayoutPanel tableLayoutPanel6;
  private TextEdit txtAssessmentName;
  private LabelControl labelControl9;
  private LabelControl labelControl10;
  private LabelControl labelControl18;
  private TextEdit txtFacilityName;
  private TextEdit txtSiteName;
  private TextEdit txtEquipmenTypeName;
  private TableLayoutPanel tableLayoutPanel7;
  private LabelControl labelControl15;
  private TextEdit txtComponentNumber;
  private LabelControl labelControl12;
  private LabelControl labelControl13;
  private LabelControl labelControl14;
  private ButtonEdit beMaterialGrade;
  private ButtonEdit beMaterialSpecNo;
  private DateEdit dtAssessmentDate;
  private TextEdit txtHydrotestPressure;
  private TextEdit txtComponentTypeName;
  private LabelControl labelControl16;
  private LookUpEdit lookupAssessmentCategory;
  private MemoExEdit memoComment;
  private LabelControl labelControl17;
  private LookUpEdit lookupAssessmentType;
  private TableLayoutPanel tableLayoutPanel8;
  private SimpleButton btnClearMaterial;
  private GroupControl groupControl4;
  private TableLayoutPanel tableLayoutPanel9;
  private TextEdit txtCreatedBy;
  private LabelControl labelControl19;
  private LabelControl labelControl20;
  private LabelControl labelControl21;
  private LabelControl labelControl22;
  private TextEdit txtCreatedDate;
  private TextEdit txtModifiedBy;
  private TextEdit txtModifiedDate;
  private LabelControl labelControl24;
  private LabelControl labelControl23;
  private TextEdit txtOperatingTemperature;
  private TextEdit txtOperatingPressure;
  private LabelControl umOperatingTemperature;
  private LabelControl umOperatingPressure;
  private IRecordView _recordView;
  private GeneralPresenter _presenter;
  private FormDirtyTracker _dirtyTracker;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    SerializableAppearanceObject appearance1 = new SerializableAppearanceObject();
    SerializableAppearanceObject appearance2 = new SerializableAppearanceObject();
    this.groupControl1 = new GroupControl();
    this.tableLayoutPanel6 = new TableLayoutPanel();
    this.labelControl17 = new LabelControl();
    this.txtAssessmentName = new TextEdit();
    this.labelControl9 = new LabelControl();
    this.labelControl10 = new LabelControl();
    this.dtAssessmentDate = new DateEdit();
    this.labelControl16 = new LabelControl();
    this.labelControl18 = new LabelControl();
    this.memoComment = new MemoExEdit();
    this.lookupAssessmentCategory = new LookUpEdit();
    this.lookupAssessmentType = new LookUpEdit();
    this.groupControl2 = new GroupControl();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.txtFacilityName = new TextEdit();
    this.txtSiteName = new TextEdit();
    this.txtEquipmenTypeName = new TextEdit();
    this.txtEquipmentNumber = new TextEdit();
    this.labelControl4 = new LabelControl();
    this.labelControl6 = new LabelControl();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.umDesignPressure = new LabelControl();
    this.txtDesignPressure = new TextEdit();
    this.labelControl2 = new LabelControl();
    this.labelControl8 = new LabelControl();
    this.labelControl11 = new LabelControl();
    this.labelControl5 = new LabelControl();
    this.txtDesignCode = new TextEdit();
    this.labelControl7 = new LabelControl();
    this.labelControl1 = new LabelControl();
    this.labelControl3 = new LabelControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.umDesignTemperature = new LabelControl();
    this.txtDesignTemperature = new TextEdit();
    this.tableLayoutPanel4 = new TableLayoutPanel();
    this.umMinOperatingTemperature = new LabelControl();
    this.txtMinOperatingTemperature = new TextEdit();
    this.tableLayoutPanel5 = new TableLayoutPanel();
    this.umHydrotestPressure = new LabelControl();
    this.txtHydrotestPressure = new TextEdit();
    this.groupControl3 = new GroupControl();
    this.tableLayoutPanel7 = new TableLayoutPanel();
    this.labelControl24 = new LabelControl();
    this.txtComponentNumber = new TextEdit();
    this.labelControl12 = new LabelControl();
    this.labelControl13 = new LabelControl();
    this.labelControl14 = new LabelControl();
    this.labelControl15 = new LabelControl();
    this.txtComponentTypeName = new TextEdit();
    this.tableLayoutPanel8 = new TableLayoutPanel();
    this.beMaterialSpecNo = new ButtonEdit();
    this.btnClearMaterial = new SimpleButton();
    this.labelControl23 = new LabelControl();
    this.txtOperatingTemperature = new TextEdit();
    this.txtOperatingPressure = new TextEdit();
    this.umOperatingTemperature = new LabelControl();
    this.umOperatingPressure = new LabelControl();
    this.beMaterialGrade = new ButtonEdit();
    this.groupControl4 = new GroupControl();
    this.tableLayoutPanel9 = new TableLayoutPanel();
    this.txtCreatedBy = new TextEdit();
    this.labelControl19 = new LabelControl();
    this.labelControl20 = new LabelControl();
    this.labelControl21 = new LabelControl();
    this.labelControl22 = new LabelControl();
    this.txtCreatedDate = new TextEdit();
    this.txtModifiedBy = new TextEdit();
    this.txtModifiedDate = new TextEdit();
    this.groupControl1.BeginInit();
    this.groupControl1.SuspendLayout();
    this.tableLayoutPanel6.SuspendLayout();
    this.txtAssessmentName.Properties.BeginInit();
    this.dtAssessmentDate.Properties.VistaTimeProperties.BeginInit();
    this.dtAssessmentDate.Properties.BeginInit();
    this.memoComment.Properties.BeginInit();
    this.lookupAssessmentCategory.Properties.BeginInit();
    this.lookupAssessmentType.Properties.BeginInit();
    this.groupControl2.BeginInit();
    this.groupControl2.SuspendLayout();
    this.tableLayoutPanel1.SuspendLayout();
    this.txtFacilityName.Properties.BeginInit();
    this.txtSiteName.Properties.BeginInit();
    this.txtEquipmenTypeName.Properties.BeginInit();
    this.txtEquipmentNumber.Properties.BeginInit();
    this.tableLayoutPanel2.SuspendLayout();
    this.txtDesignPressure.Properties.BeginInit();
    this.txtDesignCode.Properties.BeginInit();
    this.tableLayoutPanel3.SuspendLayout();
    this.txtDesignTemperature.Properties.BeginInit();
    this.tableLayoutPanel4.SuspendLayout();
    this.txtMinOperatingTemperature.Properties.BeginInit();
    this.tableLayoutPanel5.SuspendLayout();
    this.txtHydrotestPressure.Properties.BeginInit();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.tableLayoutPanel7.SuspendLayout();
    this.txtComponentNumber.Properties.BeginInit();
    this.txtComponentTypeName.Properties.BeginInit();
    this.tableLayoutPanel8.SuspendLayout();
    this.beMaterialSpecNo.Properties.BeginInit();
    this.txtOperatingTemperature.Properties.BeginInit();
    this.txtOperatingPressure.Properties.BeginInit();
    this.beMaterialGrade.Properties.BeginInit();
    this.groupControl4.BeginInit();
    this.groupControl4.SuspendLayout();
    this.tableLayoutPanel9.SuspendLayout();
    this.txtCreatedBy.Properties.BeginInit();
    this.txtCreatedDate.Properties.BeginInit();
    this.txtModifiedBy.Properties.BeginInit();
    this.txtModifiedDate.Properties.BeginInit();
    this.SuspendLayout();
    this.groupControl1.AutoSize = true;
    this.groupControl1.Controls.Add((Control) this.tableLayoutPanel6);
    this.groupControl1.Dock = DockStyle.Top;
    this.groupControl1.Location = new Point(0, 0);
    this.groupControl1.Name = "groupControl1";
    this.groupControl1.Size = new Size(629, 153);
    this.groupControl1.TabIndex = 0;
    this.groupControl1.Text = "General Assessment Information";
    this.tableLayoutPanel6.AutoSize = true;
    this.tableLayoutPanel6.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel6.ColumnCount = 2;
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 140f));
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel6.Controls.Add((Control) this.labelControl17, 0, 2);
    this.tableLayoutPanel6.Controls.Add((Control) this.txtAssessmentName, 1, 0);
    this.tableLayoutPanel6.Controls.Add((Control) this.labelControl9, 0, 0);
    this.tableLayoutPanel6.Controls.Add((Control) this.labelControl10, 0, 1);
    this.tableLayoutPanel6.Controls.Add((Control) this.dtAssessmentDate, 1, 1);
    this.tableLayoutPanel6.Controls.Add((Control) this.labelControl16, 0, 3);
    this.tableLayoutPanel6.Controls.Add((Control) this.labelControl18, 0, 4);
    this.tableLayoutPanel6.Controls.Add((Control) this.memoComment, 1, 4);
    this.tableLayoutPanel6.Controls.Add((Control) this.lookupAssessmentCategory, 1, 2);
    this.tableLayoutPanel6.Controls.Add((Control) this.lookupAssessmentType, 1, 3);
    this.tableLayoutPanel6.Dock = DockStyle.Fill;
    this.tableLayoutPanel6.Location = new Point(2, 21);
    this.tableLayoutPanel6.Name = "tableLayoutPanel6";
    this.tableLayoutPanel6.Padding = new Padding(10);
    this.tableLayoutPanel6.RowCount = 5;
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel6.Size = new Size(625, 130);
    this.tableLayoutPanel6.TabIndex = 0;
    this.labelControl17.Location = new Point(13, 57);
    this.labelControl17.Name = "labelControl17";
    this.labelControl17.Size = new Size(57, 13);
    this.labelControl17.TabIndex = 4;
    this.labelControl17.Text = "Assessment";
    this.txtAssessmentName.Location = new Point(151, 11);
    this.txtAssessmentName.Margin = new Padding(1);
    this.txtAssessmentName.Name = "txtAssessmentName";
    this.txtAssessmentName.Properties.AllowMouseWheel = false;
    this.txtAssessmentName.Properties.MaxLength = 100;
    this.txtAssessmentName.Size = new Size(250, 20);
    this.txtAssessmentName.TabIndex = 1;
    this.labelControl9.Location = new Point(13, 13);
    this.labelControl9.Name = "labelControl9";
    this.labelControl9.Size = new Size(87, 13);
    this.labelControl9.TabIndex = 0;
    this.labelControl9.Text = "Assessment Name";
    this.labelControl10.Location = new Point(13, 35);
    this.labelControl10.Name = "labelControl10";
    this.labelControl10.Size = new Size(83, 13);
    this.labelControl10.TabIndex = 2;
    this.labelControl10.Text = "Assessment Date";
    this.dtAssessmentDate.EditValue = (object) null;
    this.dtAssessmentDate.Location = new Point(151, 33);
    this.dtAssessmentDate.Margin = new Padding(1);
    this.dtAssessmentDate.Name = "dtAssessmentDate";
    this.dtAssessmentDate.Properties.AllowMouseWheel = false;
    this.dtAssessmentDate.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Combo)
    });
    this.dtAssessmentDate.Properties.DisplayFormat.FormatString = "dd-MMM-yyyy";
    this.dtAssessmentDate.Properties.DisplayFormat.FormatType = FormatType.DateTime;
    this.dtAssessmentDate.Properties.EditFormat.FormatString = "dd-MMM-yyyy";
    this.dtAssessmentDate.Properties.EditFormat.FormatType = FormatType.DateTime;
    this.dtAssessmentDate.Properties.Mask.EditMask = "dd-MMM-yyyy";
    this.dtAssessmentDate.Properties.VistaTimeProperties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton()
    });
    this.dtAssessmentDate.Size = new Size(250, 20);
    this.dtAssessmentDate.TabIndex = 3;
    this.labelControl16.Location = new Point(13, 79);
    this.labelControl16.Name = "labelControl16";
    this.labelControl16.Size = new Size(84, 13);
    this.labelControl16.TabIndex = 6;
    this.labelControl16.Text = "Assessment Type";
    this.labelControl18.Location = new Point(13, 101);
    this.labelControl18.Name = "labelControl18";
    this.labelControl18.Size = new Size(45, 13);
    this.labelControl18.TabIndex = 8;
    this.labelControl18.Text = "Comment";
    this.memoComment.Location = new Point(151, 99);
    this.memoComment.Margin = new Padding(1);
    this.memoComment.Name = "memoComment";
    this.memoComment.Properties.AllowMouseWheel = false;
    this.memoComment.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Combo)
    });
    this.memoComment.Properties.MaxLength = 100;
    this.memoComment.Properties.PopupFormSize = new Size(250, 250);
    this.memoComment.Properties.ShowIcon = false;
    this.memoComment.Size = new Size(350, 20);
    this.memoComment.TabIndex = 9;
    this.lookupAssessmentCategory.Location = new Point(151, 55);
    this.lookupAssessmentCategory.Margin = new Padding(1);
    this.lookupAssessmentCategory.Name = "lookupAssessmentCategory";
    this.lookupAssessmentCategory.Properties.AllowMouseWheel = false;
    this.lookupAssessmentCategory.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Combo)
    });
    this.lookupAssessmentCategory.Properties.Columns.AddRange(new LookUpColumnInfo[1]
    {
      new LookUpColumnInfo("AssessmentCategoryName", "Assessment")
    });
    this.lookupAssessmentCategory.Properties.DisplayMember = "AssessmentCategoryName";
    this.lookupAssessmentCategory.Properties.DropDownRows = 12;
    this.lookupAssessmentCategory.Properties.NullText = "";
    this.lookupAssessmentCategory.Properties.ValueMember = "AssessmentCategoryID";
    this.lookupAssessmentCategory.Size = new Size(350, 20);
    this.lookupAssessmentCategory.TabIndex = 5;
    this.lookupAssessmentCategory.EditValueChanged += new EventHandler(this.lookupAssessmentCategory_EditValueChanged);
    this.lookupAssessmentType.Location = new Point(151, 77);
    this.lookupAssessmentType.Margin = new Padding(1);
    this.lookupAssessmentType.Name = "lookupAssessmentType";
    this.lookupAssessmentType.Properties.AllowMouseWheel = false;
    this.lookupAssessmentType.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Combo)
    });
    this.lookupAssessmentType.Properties.Columns.AddRange(new LookUpColumnInfo[1]
    {
      new LookUpColumnInfo("AssessmentTypeName", "Assessment Type")
    });
    this.lookupAssessmentType.Properties.DisplayMember = "AssessmentTypeName";
    this.lookupAssessmentType.Properties.NullText = "";
    this.lookupAssessmentType.Properties.ValueMember = "AssessmentTypeID";
    this.lookupAssessmentType.Size = new Size(350, 20);
    this.lookupAssessmentType.TabIndex = 7;
    this.lookupAssessmentType.EditValueChanged += new EventHandler(this.lookupAssessmentType_EditValueChanged);
    this.groupControl2.AutoSize = true;
    this.groupControl2.Controls.Add((Control) this.tableLayoutPanel1);
    this.groupControl2.Dock = DockStyle.Top;
    this.groupControl2.Location = new Point(0, 153);
    this.groupControl2.Name = "groupControl2";
    this.groupControl2.Size = new Size(629, 197);
    this.groupControl2.TabIndex = 1;
    this.groupControl2.Text = "Equipment Information";
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 5;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 140f));
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 10f));
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 140f));
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFacilityName, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtSiteName, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtEquipmenTypeName, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtEquipmentNumber, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl4, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl6, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel2, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl8, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl11, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl5, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtDesignCode, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl7, 3, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 3, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl3, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel3, 4, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel4, 4, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel5, 1, 6);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(2, 21);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 9;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(625, 174);
    this.tableLayoutPanel1.TabIndex = 0;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.txtFacilityName, 3);
    this.txtFacilityName.Location = new Point(151, 99);
    this.txtFacilityName.Margin = new Padding(1);
    this.txtFacilityName.Name = "txtFacilityName";
    this.txtFacilityName.Properties.AllowMouseWheel = false;
    this.txtFacilityName.Properties.ReadOnly = true;
    this.txtFacilityName.Size = new Size(250, 20);
    this.txtFacilityName.TabIndex = 9;
    this.txtFacilityName.TabStop = false;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.txtSiteName, 3);
    this.txtSiteName.Location = new Point(151, 77);
    this.txtSiteName.Margin = new Padding(1);
    this.txtSiteName.Name = "txtSiteName";
    this.txtSiteName.Properties.AllowMouseWheel = false;
    this.txtSiteName.Properties.ReadOnly = true;
    this.txtSiteName.Size = new Size(250, 20);
    this.txtSiteName.TabIndex = 7;
    this.txtSiteName.TabStop = false;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.txtEquipmenTypeName, 3);
    this.txtEquipmenTypeName.Location = new Point(151, 33);
    this.txtEquipmenTypeName.Margin = new Padding(1);
    this.txtEquipmenTypeName.Name = "txtEquipmenTypeName";
    this.txtEquipmenTypeName.Properties.AllowMouseWheel = false;
    this.txtEquipmenTypeName.Properties.ReadOnly = true;
    this.txtEquipmenTypeName.Size = new Size(250, 20);
    this.txtEquipmenTypeName.TabIndex = 3;
    this.txtEquipmenTypeName.TabStop = false;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.txtEquipmentNumber, 3);
    this.txtEquipmentNumber.Location = new Point(151, 11);
    this.txtEquipmentNumber.Margin = new Padding(1);
    this.txtEquipmentNumber.Name = "txtEquipmentNumber";
    this.txtEquipmentNumber.Properties.AllowMouseWheel = false;
    this.txtEquipmentNumber.Properties.ReadOnly = true;
    this.txtEquipmentNumber.Size = new Size(250, 20);
    this.txtEquipmentNumber.TabIndex = 1;
    this.txtEquipmentNumber.TabStop = false;
    this.labelControl4.Location = new Point(13, 13);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(90, 13);
    this.labelControl4.TabIndex = 0;
    this.labelControl4.Text = "Equipment Number";
    this.labelControl6.Location = new Point(13, 35);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(77, 13);
    this.labelControl6.TabIndex = 2;
    this.labelControl6.Text = "Equipment Type";
    this.tableLayoutPanel2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel2.ColumnCount = 2;
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.Controls.Add((Control) this.umDesignPressure, 1, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.txtDesignPressure, 0, 0);
    this.tableLayoutPanel2.Location = new Point(151, 121);
    this.tableLayoutPanel2.Margin = new Padding(1);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.RowCount = 1;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.Size = new Size(160 /*0xA0*/, 20);
    this.tableLayoutPanel2.TabIndex = 11;
    this.umDesignPressure.Location = new Point(103, 3);
    this.umDesignPressure.Name = "umDesignPressure";
    this.umDesignPressure.Size = new Size(41, 13);
    this.umDesignPressure.TabIndex = 1;
    this.umDesignPressure.Text = "measure";
    this.txtDesignPressure.Location = new Point(0, 0);
    this.txtDesignPressure.Margin = new Padding(0);
    this.txtDesignPressure.Name = "txtDesignPressure";
    this.txtDesignPressure.Properties.AllowMouseWheel = false;
    this.txtDesignPressure.Size = new Size(100, 20);
    this.txtDesignPressure.TabIndex = 0;
    this.labelControl2.Location = new Point(13, 123);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(77, 13);
    this.labelControl2.TabIndex = 10;
    this.labelControl2.Text = "Design Pressure";
    this.labelControl8.Location = new Point(13, 101);
    this.labelControl8.Name = "labelControl8";
    this.labelControl8.Size = new Size(33, 13);
    this.labelControl8.TabIndex = 8;
    this.labelControl8.Text = "Facility";
    this.labelControl11.Location = new Point(13, 79);
    this.labelControl11.Name = "labelControl11";
    this.labelControl11.Size = new Size(18, 13);
    this.labelControl11.TabIndex = 6;
    this.labelControl11.Text = "Site";
    this.labelControl5.Location = new Point(13, 57);
    this.labelControl5.Name = "labelControl5";
    this.labelControl5.Size = new Size(60, 13);
    this.labelControl5.TabIndex = 4;
    this.labelControl5.Text = "Design Code";
    this.tableLayoutPanel1.SetColumnSpan((Control) this.txtDesignCode, 3);
    this.txtDesignCode.Location = new Point(151, 55);
    this.txtDesignCode.Margin = new Padding(1);
    this.txtDesignCode.Name = "txtDesignCode";
    this.txtDesignCode.Properties.AllowMouseWheel = false;
    this.txtDesignCode.Properties.ReadOnly = true;
    this.txtDesignCode.Size = new Size(250, 20);
    this.txtDesignCode.TabIndex = 5;
    this.txtDesignCode.TabStop = false;
    this.labelControl7.Location = new Point(325, 123);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(97, 13);
    this.labelControl7.TabIndex = 12;
    this.labelControl7.Text = "Design Temperature";
    this.labelControl1.Location = new Point(325, 145);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(105, 13);
    this.labelControl1.TabIndex = 16 /*0x10*/;
    this.labelControl1.Text = "Minimum Temperature";
    this.labelControl3.Location = new Point(13, 145);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(93, 13);
    this.labelControl3.TabIndex = 14;
    this.labelControl3.Text = "Hydrotest Pressure";
    this.tableLayoutPanel3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel3.ColumnCount = 2;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.umDesignTemperature, 1, 0);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtDesignTemperature, 0, 0);
    this.tableLayoutPanel3.Location = new Point(463, 121);
    this.tableLayoutPanel3.Margin = new Padding(1);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel3.Size = new Size(160 /*0xA0*/, 20);
    this.tableLayoutPanel3.TabIndex = 13;
    this.umDesignTemperature.Location = new Point(103, 3);
    this.umDesignTemperature.Name = "umDesignTemperature";
    this.umDesignTemperature.Size = new Size(41, 13);
    this.umDesignTemperature.TabIndex = 1;
    this.umDesignTemperature.Text = "measure";
    this.txtDesignTemperature.Location = new Point(0, 0);
    this.txtDesignTemperature.Margin = new Padding(0);
    this.txtDesignTemperature.Name = "txtDesignTemperature";
    this.txtDesignTemperature.Properties.AllowMouseWheel = false;
    this.txtDesignTemperature.Size = new Size(100, 20);
    this.txtDesignTemperature.TabIndex = 0;
    this.tableLayoutPanel4.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel4.ColumnCount = 2;
    this.tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel4.Controls.Add((Control) this.umMinOperatingTemperature, 1, 0);
    this.tableLayoutPanel4.Controls.Add((Control) this.txtMinOperatingTemperature, 0, 0);
    this.tableLayoutPanel4.Location = new Point(463, 143);
    this.tableLayoutPanel4.Margin = new Padding(1);
    this.tableLayoutPanel4.Name = "tableLayoutPanel4";
    this.tableLayoutPanel4.RowCount = 1;
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel4.Size = new Size(160 /*0xA0*/, 20);
    this.tableLayoutPanel4.TabIndex = 17;
    this.umMinOperatingTemperature.Location = new Point(103, 3);
    this.umMinOperatingTemperature.Name = "umMinOperatingTemperature";
    this.umMinOperatingTemperature.Size = new Size(41, 13);
    this.umMinOperatingTemperature.TabIndex = 1;
    this.umMinOperatingTemperature.Text = "measure";
    this.txtMinOperatingTemperature.Location = new Point(0, 0);
    this.txtMinOperatingTemperature.Margin = new Padding(0);
    this.txtMinOperatingTemperature.Name = "txtMinOperatingTemperature";
    this.txtMinOperatingTemperature.Properties.AllowMouseWheel = false;
    this.txtMinOperatingTemperature.Size = new Size(100, 20);
    this.txtMinOperatingTemperature.TabIndex = 0;
    this.tableLayoutPanel5.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel5.ColumnCount = 2;
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel5.Controls.Add((Control) this.umHydrotestPressure, 1, 0);
    this.tableLayoutPanel5.Controls.Add((Control) this.txtHydrotestPressure, 0, 0);
    this.tableLayoutPanel5.Location = new Point(151, 143);
    this.tableLayoutPanel5.Margin = new Padding(1);
    this.tableLayoutPanel5.Name = "tableLayoutPanel5";
    this.tableLayoutPanel5.RowCount = 1;
    this.tableLayoutPanel5.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel5.Size = new Size(160 /*0xA0*/, 20);
    this.tableLayoutPanel5.TabIndex = 15;
    this.umHydrotestPressure.Location = new Point(103, 3);
    this.umHydrotestPressure.Name = "umHydrotestPressure";
    this.umHydrotestPressure.Size = new Size(41, 13);
    this.umHydrotestPressure.TabIndex = 1;
    this.umHydrotestPressure.Text = "measure";
    this.txtHydrotestPressure.Location = new Point(0, 0);
    this.txtHydrotestPressure.Margin = new Padding(0);
    this.txtHydrotestPressure.Name = "txtHydrotestPressure";
    this.txtHydrotestPressure.Properties.AllowMouseWheel = false;
    this.txtHydrotestPressure.Size = new Size(100, 20);
    this.txtHydrotestPressure.TabIndex = 0;
    this.groupControl3.AutoSize = true;
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel7);
    this.groupControl3.Dock = DockStyle.Top;
    this.groupControl3.Location = new Point(0, 350);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(629, 175);
    this.groupControl3.TabIndex = 2;
    this.groupControl3.Text = "Component Information";
    this.tableLayoutPanel7.AutoSize = true;
    this.tableLayoutPanel7.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel7.ColumnCount = 3;
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 140f));
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel7.Controls.Add((Control) this.labelControl24, 0, 5);
    this.tableLayoutPanel7.Controls.Add((Control) this.txtComponentNumber, 1, 0);
    this.tableLayoutPanel7.Controls.Add((Control) this.labelControl12, 0, 0);
    this.tableLayoutPanel7.Controls.Add((Control) this.labelControl13, 0, 1);
    this.tableLayoutPanel7.Controls.Add((Control) this.labelControl14, 0, 2);
    this.tableLayoutPanel7.Controls.Add((Control) this.labelControl15, 0, 3);
    this.tableLayoutPanel7.Controls.Add((Control) this.txtComponentTypeName, 1, 1);
    this.tableLayoutPanel7.Controls.Add((Control) this.tableLayoutPanel8, 1, 2);
    this.tableLayoutPanel7.Controls.Add((Control) this.labelControl23, 0, 4);
    this.tableLayoutPanel7.Controls.Add((Control) this.txtOperatingTemperature, 1, 4);
    this.tableLayoutPanel7.Controls.Add((Control) this.txtOperatingPressure, 1, 5);
    this.tableLayoutPanel7.Controls.Add((Control) this.umOperatingTemperature, 2, 4);
    this.tableLayoutPanel7.Controls.Add((Control) this.umOperatingPressure, 2, 5);
    this.tableLayoutPanel7.Controls.Add((Control) this.beMaterialGrade, 1, 3);
    this.tableLayoutPanel7.Dock = DockStyle.Fill;
    this.tableLayoutPanel7.Location = new Point(2, 21);
    this.tableLayoutPanel7.Name = "tableLayoutPanel7";
    this.tableLayoutPanel7.Padding = new Padding(10);
    this.tableLayoutPanel7.RowCount = 6;
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel7.Size = new Size(625, 152);
    this.tableLayoutPanel7.TabIndex = 0;
    this.labelControl24.Location = new Point(13, 123);
    this.labelControl24.Name = "labelControl24";
    this.labelControl24.Size = new Size(93, 13);
    this.labelControl24.TabIndex = 10;
    this.labelControl24.Text = "Operating Pressure";
    this.tableLayoutPanel7.SetColumnSpan((Control) this.txtComponentNumber, 2);
    this.txtComponentNumber.Location = new Point(151, 11);
    this.txtComponentNumber.Margin = new Padding(1);
    this.txtComponentNumber.Name = "txtComponentNumber";
    this.txtComponentNumber.Properties.AllowMouseWheel = false;
    this.txtComponentNumber.Properties.ReadOnly = true;
    this.txtComponentNumber.Size = new Size(250, 20);
    this.txtComponentNumber.TabIndex = 1;
    this.txtComponentNumber.TabStop = false;
    this.labelControl12.Location = new Point(13, 13);
    this.labelControl12.Name = "labelControl12";
    this.labelControl12.Size = new Size(95, 13);
    this.labelControl12.TabIndex = 0;
    this.labelControl12.Text = "Component Number";
    this.labelControl13.Location = new Point(13, 35);
    this.labelControl13.Name = "labelControl13";
    this.labelControl13.Size = new Size(82, 13);
    this.labelControl13.TabIndex = 2;
    this.labelControl13.Text = "Component Type";
    this.labelControl14.Location = new Point(13, 57);
    this.labelControl14.Name = "labelControl14";
    this.labelControl14.Size = new Size(117, 13);
    this.labelControl14.TabIndex = 4;
    this.labelControl14.Text = "Material Specification No";
    this.labelControl15.Location = new Point(13, 79);
    this.labelControl15.Name = "labelControl15";
    this.labelControl15.Size = new Size(70, 13);
    this.labelControl15.TabIndex = 5;
    this.labelControl15.Text = "Material Grade";
    this.tableLayoutPanel7.SetColumnSpan((Control) this.txtComponentTypeName, 2);
    this.txtComponentTypeName.Location = new Point(151, 33);
    this.txtComponentTypeName.Margin = new Padding(1);
    this.txtComponentTypeName.Name = "txtComponentTypeName";
    this.txtComponentTypeName.Properties.AllowMouseWheel = false;
    this.txtComponentTypeName.Properties.ReadOnly = true;
    this.txtComponentTypeName.Size = new Size(250, 20);
    this.txtComponentTypeName.TabIndex = 3;
    this.txtComponentTypeName.TabStop = false;
    this.tableLayoutPanel8.AutoSize = true;
    this.tableLayoutPanel8.ColumnCount = 2;
    this.tableLayoutPanel7.SetColumnSpan((Control) this.tableLayoutPanel8, 2);
    this.tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 250f));
    this.tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel8.Controls.Add((Control) this.beMaterialSpecNo, 0, 0);
    this.tableLayoutPanel8.Controls.Add((Control) this.btnClearMaterial, 1, 0);
    this.tableLayoutPanel8.Location = new Point(151, 55);
    this.tableLayoutPanel8.Margin = new Padding(1);
    this.tableLayoutPanel8.Name = "tableLayoutPanel8";
    this.tableLayoutPanel8.RowCount = 1;
    this.tableLayoutPanel8.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel8.Size = new Size(278, 20);
    this.tableLayoutPanel8.TabIndex = 5;
    this.beMaterialSpecNo.Location = new Point(0, 0);
    this.beMaterialSpecNo.Margin = new Padding(0);
    this.beMaterialSpecNo.Name = "beMaterialSpecNo";
    this.beMaterialSpecNo.Properties.AllowMouseWheel = false;
    this.beMaterialSpecNo.Properties.Appearance.Options.UseFont = true;
    this.beMaterialSpecNo.Properties.AppearanceDisabled.Options.UseFont = true;
    this.beMaterialSpecNo.Properties.AppearanceFocused.Options.UseFont = true;
    this.beMaterialSpecNo.Properties.AppearanceReadOnly.BackColor = Color.White;
    this.beMaterialSpecNo.Properties.AppearanceReadOnly.Options.UseBackColor = true;
    this.beMaterialSpecNo.Properties.AppearanceReadOnly.Options.UseFont = true;
    appearance1.Options.UseFont = true;
    this.beMaterialSpecNo.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Ellipsis, "", -1, true, true, false, ImageLocation.MiddleCenter, (Image) null, new KeyShortcut(Keys.None), (AppearanceObject) appearance1, "", (object) null, (SuperToolTip) null, true)
    });
    this.beMaterialSpecNo.Properties.MaxLength = 10;
    this.beMaterialSpecNo.Properties.ReadOnly = true;
    this.beMaterialSpecNo.Size = new Size(250, 20);
    this.beMaterialSpecNo.TabIndex = 0;
    this.beMaterialSpecNo.ToolTip = "Select the material grade of the component you want to assess from the drop-down list.";
    this.beMaterialSpecNo.ButtonClick += new ButtonPressedEventHandler(this.beMaterialSpecNo_ButtonClick);
    this.btnClearMaterial.Image = (Image) Resources.ClearLeft16;
    this.btnClearMaterial.Location = new Point(250, 0);
    this.btnClearMaterial.Margin = new Padding(0);
    this.btnClearMaterial.Name = "btnClearMaterial";
    this.btnClearMaterial.Size = new Size(28, 20);
    this.btnClearMaterial.TabIndex = 1;
    this.btnClearMaterial.ToolTip = "Clear";
    this.btnClearMaterial.Click += new EventHandler(this.btnClearMaterial_Click);
    this.labelControl23.Location = new Point(13, 101);
    this.labelControl23.Name = "labelControl23";
    this.labelControl23.Size = new Size(113, 13);
    this.labelControl23.TabIndex = 7;
    this.labelControl23.Text = "Operating Temperature";
    this.txtOperatingTemperature.Location = new Point(151, 99);
    this.txtOperatingTemperature.Margin = new Padding(1);
    this.txtOperatingTemperature.Name = "txtOperatingTemperature";
    this.txtOperatingTemperature.Properties.AllowMouseWheel = false;
    this.txtOperatingTemperature.Size = new Size(100, 20);
    this.txtOperatingTemperature.TabIndex = 8;
    this.txtOperatingPressure.Location = new Point(151, 121);
    this.txtOperatingPressure.Margin = new Padding(1);
    this.txtOperatingPressure.Name = "txtOperatingPressure";
    this.txtOperatingPressure.Properties.AllowMouseWheel = false;
    this.txtOperatingPressure.Size = new Size(100, 20);
    this.txtOperatingPressure.TabIndex = 11;
    this.umOperatingTemperature.Location = new Point((int) byte.MaxValue, 101);
    this.umOperatingTemperature.Name = "umOperatingTemperature";
    this.umOperatingTemperature.Size = new Size(41, 13);
    this.umOperatingTemperature.TabIndex = 9;
    this.umOperatingTemperature.Text = "measure";
    this.umOperatingPressure.Location = new Point((int) byte.MaxValue, 123);
    this.umOperatingPressure.Name = "umOperatingPressure";
    this.umOperatingPressure.Size = new Size(41, 13);
    this.umOperatingPressure.TabIndex = 12;
    this.umOperatingPressure.Text = "measure";
    this.tableLayoutPanel7.SetColumnSpan((Control) this.beMaterialGrade, 2);
    this.beMaterialGrade.Location = new Point(151, 77);
    this.beMaterialGrade.Margin = new Padding(1);
    this.beMaterialGrade.Name = "beMaterialGrade";
    this.beMaterialGrade.Properties.AllowMouseWheel = false;
    this.beMaterialGrade.Properties.Appearance.Options.UseFont = true;
    this.beMaterialGrade.Properties.AppearanceDisabled.Options.UseFont = true;
    this.beMaterialGrade.Properties.AppearanceFocused.Options.UseFont = true;
    this.beMaterialGrade.Properties.AppearanceReadOnly.BackColor = Color.White;
    this.beMaterialGrade.Properties.AppearanceReadOnly.Options.UseBackColor = true;
    this.beMaterialGrade.Properties.AppearanceReadOnly.Options.UseFont = true;
    appearance2.Options.UseFont = true;
    this.beMaterialGrade.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Ellipsis, "", -1, true, true, false, ImageLocation.MiddleCenter, (Image) null, new KeyShortcut(Keys.None), (AppearanceObject) appearance2, "", (object) null, (SuperToolTip) null, true)
    });
    this.beMaterialGrade.Properties.ReadOnly = true;
    this.beMaterialGrade.Size = new Size(250, 20);
    this.beMaterialGrade.TabIndex = 6;
    this.beMaterialGrade.ToolTip = "Select the material specification number of the component you want to assess from the drop-down list.";
    this.beMaterialGrade.ButtonClick += new ButtonPressedEventHandler(this.beMaterialGrade_ButtonClick);
    this.groupControl4.AutoSize = true;
    this.groupControl4.Controls.Add((Control) this.tableLayoutPanel9);
    this.groupControl4.Dock = DockStyle.Top;
    this.groupControl4.Location = new Point(0, 525);
    this.groupControl4.Name = "groupControl4";
    this.groupControl4.Size = new Size(629, 131);
    this.groupControl4.TabIndex = 3;
    this.groupControl4.Text = "Audit Information";
    this.tableLayoutPanel9.AutoSize = true;
    this.tableLayoutPanel9.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel9.ColumnCount = 2;
    this.tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 140f));
    this.tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel9.Controls.Add((Control) this.txtCreatedBy, 1, 0);
    this.tableLayoutPanel9.Controls.Add((Control) this.labelControl19, 0, 0);
    this.tableLayoutPanel9.Controls.Add((Control) this.labelControl20, 0, 1);
    this.tableLayoutPanel9.Controls.Add((Control) this.labelControl21, 0, 2);
    this.tableLayoutPanel9.Controls.Add((Control) this.labelControl22, 0, 3);
    this.tableLayoutPanel9.Controls.Add((Control) this.txtCreatedDate, 1, 1);
    this.tableLayoutPanel9.Controls.Add((Control) this.txtModifiedBy, 1, 2);
    this.tableLayoutPanel9.Controls.Add((Control) this.txtModifiedDate, 1, 3);
    this.tableLayoutPanel9.Dock = DockStyle.Fill;
    this.tableLayoutPanel9.Location = new Point(2, 21);
    this.tableLayoutPanel9.Name = "tableLayoutPanel9";
    this.tableLayoutPanel9.Padding = new Padding(10);
    this.tableLayoutPanel9.RowCount = 4;
    this.tableLayoutPanel9.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel9.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel9.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel9.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel9.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel9.Size = new Size(625, 108);
    this.tableLayoutPanel9.TabIndex = 0;
    this.txtCreatedBy.Location = new Point(151, 11);
    this.txtCreatedBy.Margin = new Padding(1);
    this.txtCreatedBy.Name = "txtCreatedBy";
    this.txtCreatedBy.Properties.AllowMouseWheel = false;
    this.txtCreatedBy.Properties.ReadOnly = true;
    this.txtCreatedBy.Size = new Size(250, 20);
    this.txtCreatedBy.TabIndex = 1;
    this.txtCreatedBy.TabStop = false;
    this.labelControl19.Location = new Point(13, 13);
    this.labelControl19.Name = "labelControl19";
    this.labelControl19.Size = new Size(54, 13);
    this.labelControl19.TabIndex = 0;
    this.labelControl19.Text = "Created By";
    this.labelControl20.Location = new Point(13, 35);
    this.labelControl20.Name = "labelControl20";
    this.labelControl20.Size = new Size(65, 13);
    this.labelControl20.TabIndex = 2;
    this.labelControl20.Text = "Date Created";
    this.labelControl21.Location = new Point(13, 57);
    this.labelControl21.Name = "labelControl21";
    this.labelControl21.Size = new Size(78, 13);
    this.labelControl21.TabIndex = 4;
    this.labelControl21.Text = "Last Modified By";
    this.labelControl22.Location = new Point(13, 79);
    this.labelControl22.Name = "labelControl22";
    this.labelControl22.Size = new Size(89, 13);
    this.labelControl22.TabIndex = 6;
    this.labelControl22.Text = "Date Last Modified";
    this.txtCreatedDate.Location = new Point(151, 33);
    this.txtCreatedDate.Margin = new Padding(1);
    this.txtCreatedDate.Name = "txtCreatedDate";
    this.txtCreatedDate.Properties.AllowMouseWheel = false;
    this.txtCreatedDate.Properties.ReadOnly = true;
    this.txtCreatedDate.Size = new Size(250, 20);
    this.txtCreatedDate.TabIndex = 3;
    this.txtCreatedDate.TabStop = false;
    this.txtModifiedBy.Location = new Point(151, 55);
    this.txtModifiedBy.Margin = new Padding(1);
    this.txtModifiedBy.Name = "txtModifiedBy";
    this.txtModifiedBy.Properties.AllowMouseWheel = false;
    this.txtModifiedBy.Properties.ReadOnly = true;
    this.txtModifiedBy.Size = new Size(250, 20);
    this.txtModifiedBy.TabIndex = 7;
    this.txtModifiedBy.TabStop = false;
    this.txtModifiedDate.Location = new Point(151, 77);
    this.txtModifiedDate.Margin = new Padding(1);
    this.txtModifiedDate.Name = "txtModifiedDate";
    this.txtModifiedDate.Properties.AllowMouseWheel = false;
    this.txtModifiedDate.Properties.ReadOnly = true;
    this.txtModifiedDate.Size = new Size(250, 20);
    this.txtModifiedDate.TabIndex = 8;
    this.txtModifiedDate.TabStop = false;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.AutoSize = true;
    this.Controls.Add((Control) this.groupControl4);
    this.Controls.Add((Control) this.groupControl3);
    this.Controls.Add((Control) this.groupControl2);
    this.Controls.Add((Control) this.groupControl1);
    this.Name = nameof (vwGeneral);
    this.Size = new Size(629, 656);
    this.Load += new EventHandler(this.vwGeneral_Load);
    this.groupControl1.EndInit();
    this.groupControl1.ResumeLayout(false);
    this.groupControl1.PerformLayout();
    this.tableLayoutPanel6.ResumeLayout(false);
    this.tableLayoutPanel6.PerformLayout();
    this.txtAssessmentName.Properties.EndInit();
    this.dtAssessmentDate.Properties.VistaTimeProperties.EndInit();
    this.dtAssessmentDate.Properties.EndInit();
    this.memoComment.Properties.EndInit();
    this.lookupAssessmentCategory.Properties.EndInit();
    this.lookupAssessmentType.Properties.EndInit();
    this.groupControl2.EndInit();
    this.groupControl2.ResumeLayout(false);
    this.groupControl2.PerformLayout();
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.txtFacilityName.Properties.EndInit();
    this.txtSiteName.Properties.EndInit();
    this.txtEquipmenTypeName.Properties.EndInit();
    this.txtEquipmentNumber.Properties.EndInit();
    this.tableLayoutPanel2.ResumeLayout(false);
    this.tableLayoutPanel2.PerformLayout();
    this.txtDesignPressure.Properties.EndInit();
    this.txtDesignCode.Properties.EndInit();
    this.tableLayoutPanel3.ResumeLayout(false);
    this.tableLayoutPanel3.PerformLayout();
    this.txtDesignTemperature.Properties.EndInit();
    this.tableLayoutPanel4.ResumeLayout(false);
    this.tableLayoutPanel4.PerformLayout();
    this.txtMinOperatingTemperature.Properties.EndInit();
    this.tableLayoutPanel5.ResumeLayout(false);
    this.tableLayoutPanel5.PerformLayout();
    this.txtHydrotestPressure.Properties.EndInit();
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.groupControl3.PerformLayout();
    this.tableLayoutPanel7.ResumeLayout(false);
    this.tableLayoutPanel7.PerformLayout();
    this.txtComponentNumber.Properties.EndInit();
    this.txtComponentTypeName.Properties.EndInit();
    this.tableLayoutPanel8.ResumeLayout(false);
    this.beMaterialSpecNo.Properties.EndInit();
    this.txtOperatingTemperature.Properties.EndInit();
    this.txtOperatingPressure.Properties.EndInit();
    this.beMaterialGrade.Properties.EndInit();
    this.groupControl4.EndInit();
    this.groupControl4.ResumeLayout(false);
    this.groupControl4.PerformLayout();
    this.tableLayoutPanel9.ResumeLayout(false);
    this.tableLayoutPanel9.PerformLayout();
    this.txtCreatedBy.Properties.EndInit();
    this.txtCreatedDate.Properties.EndInit();
    this.txtModifiedBy.Properties.EndInit();
    this.txtModifiedDate.Properties.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public vwGeneral(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.GeneralView = (IGeneralView) this;
    this._presenter = new GeneralPresenter(this._recordView, (IGeneralView) this);
  }

  private void vwGeneral_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView);
    this._dirtyTracker.IsHandled = true;
    this._presenter.InitGeneralView();
    this.txtDesignPressure.AllowOnlyN4();
    this.txtDesignTemperature.AllowOnlyD4N4AndNeg();
    this.txtHydrotestPressure.AllowOnlyN4();
    this.txtMinOperatingTemperature.AllowOnlyD4N4AndNeg();
    this.txtOperatingTemperature.AllowOnlyD4N4AndNeg();
    this.txtOperatingPressure.AllowOnlyN4();
    this._dirtyTracker.IsHandled = false;
  }

  private void lookupAssessmentCategory_EditValueChanged(object sender, EventArgs e)
  {
    this.lookupAssessmentType.EditValue = (object) null;
    new AssessmentTypePresenter(this._recordView, (IAssessmentTypeView) this).GetAssessmentTypeList(this._recordView.Component.ComponentTypeID, this._recordView.Equipment.DesignCodeID, (this.lookupAssessmentCategory.GetSelectedDataRow() as AssessmentCategoryDTO).AssessmentCategoryID);
    this._presenter.HideAssessmentViews();
  }

  private void lookupAssessmentType_EditValueChanged(object sender, EventArgs e)
  {
    this._presenter.ChangeAssessmentType(this.lookupAssessmentType.GetSelectedDataRow() as AssessmentTypeDTO);
  }

  private void beMaterialSpecNo_ButtonClick(object sender, ButtonPressedEventArgs e)
  {
    this._presenter.ShowMaterialLookup();
  }

  private void beMaterialGrade_ButtonClick(object sender, ButtonPressedEventArgs e)
  {
    this._presenter.ShowMaterialLookup();
  }

  public string AssessmentName
  {
    get => this.txtAssessmentName.Text;
    set => this.txtAssessmentName.Text = value;
  }

  public DateTime? AssessmentDate
  {
    get => Helpers.ParseNullDateTime(this.dtAssessmentDate.EditValue);
    set => this.dtAssessmentDate.EditValue = (object) value;
  }

  public string AssessmentCategoryName
  {
    set => this.lookupAssessmentCategory.Properties.NullText = value;
  }

  public string AssessmentTypeName
  {
    get => this.lookupAssessmentType.Text;
    set => this.lookupAssessmentType.Properties.NullText = value;
  }

  public int? AssessmentTypeID
  {
    get => Helpers.ParseNullInt32(this.lookupAssessmentType.EditValue);
    set => this.lookupAssessmentType.EditValue = (object) value;
  }

  public string Comment
  {
    get => this.memoComment.Text;
    set => this.memoComment.Text = value;
  }

  public string EquipmentNumber
  {
    get => this.txtEquipmentNumber.Text;
    set => this.txtEquipmentNumber.Text = value;
  }

  public string CreatedBy
  {
    get
    {
      return this._recordView == null || this._recordView.Assessment == null || this._recordView.Assessment.CreatedBy == null ? this._recordView.UserID : this._recordView.Assessment.CreatedBy;
    }
    set => this.txtCreatedBy.Text = value;
  }

  public string CreatedDate
  {
    get => this.txtCreatedDate.Text;
    set => this.txtCreatedDate.Text = value;
  }

  public string ModifiedBy
  {
    get => this.txtModifiedBy.Text;
    set => this.txtModifiedBy.Text = value;
  }

  public string ModifiedDate
  {
    get => this.txtModifiedDate.Text;
    set => this.txtModifiedDate.Text = value;
  }

  public string Manufactured => this._recordView.Equipment.ManufacturerName;

  public string EquipmentTypeName
  {
    get => this.txtEquipmenTypeName.Text;
    set => this.txtEquipmenTypeName.Text = value;
  }

  public string DesignCode
  {
    get => this.txtDesignCode.Text;
    set => this.txtDesignCode.Text = value;
  }

  public string SiteName
  {
    get => this.txtSiteName.Text;
    set => this.txtSiteName.Text = value;
  }

  public string FacilityName
  {
    get => this.txtFacilityName.Text;
    set => this.txtFacilityName.Text = value;
  }

  public double? DesignPressure
  {
    get => Helpers.ParseNullDouble((object) this.txtDesignPressure.Text);
    set => this.txtDesignPressure.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? DesignTemperature
  {
    get => Helpers.ParseNullDouble((object) this.txtDesignTemperature.Text);
    set => this.txtDesignTemperature.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? MinOperatingTemperature
  {
    get => Helpers.ParseNullDouble((object) this.txtMinOperatingTemperature.Text);
    set => this.txtMinOperatingTemperature.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? HydrotestPressure
  {
    get => Helpers.ParseNullDouble((object) this.txtHydrotestPressure.Text);
    set => this.txtHydrotestPressure.Text = Helpers.ParseObjectToString((object) value);
  }

  public string ComponentNumber
  {
    get => this.txtComponentNumber.Text;
    set => this.txtComponentNumber.Text = value;
  }

  public string ComponentTypeName
  {
    get => this.txtComponentTypeName.Text;
    set => this.txtComponentTypeName.Text = value;
  }

  public string MaterialSpecNo
  {
    get => this.beMaterialSpecNo.Text;
    set => this.beMaterialSpecNo.Text = value;
  }

  public string MaterialGrade
  {
    get => this.beMaterialGrade.Text;
    set => this.beMaterialGrade.Text = value;
  }

  public int? MaterialID { get; set; }

  public double? OperatingTemperature
  {
    get => Helpers.ParseNullDouble((object) this.txtOperatingTemperature.Text);
    set => this.txtOperatingTemperature.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? OperatingPressure
  {
    get => Helpers.ParseNullDouble((object) this.txtOperatingPressure.Text);
    set => this.txtOperatingPressure.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UM_OperatingTemperature
  {
    set => this.umOperatingTemperature.Text = value;
  }

  public string UM_OperatingPressure
  {
    set => this.umOperatingPressure.Text = value;
  }

  public string UMDesignPressure
  {
    set => this.umDesignPressure.Text = value;
  }

  public string UMDesignTemperature
  {
    set => this.umDesignTemperature.Text = value;
  }

  public string UMMinOperatingTemperature
  {
    set => this.umMinOperatingTemperature.Text = value;
  }

  public string UMHydrotestPressure
  {
    set => this.umHydrotestPressure.Text = value;
  }

  public List<AssessmentCategoryDTO> AssessmentCategoryList
  {
    set => this.lookupAssessmentCategory.Properties.DataSource = (object) value;
  }

  public List<AssessmentTypeDTO> AssessmentTypeList
  {
    set => this.lookupAssessmentType.Properties.DataSource = (object) value;
  }

  public void InitAssessmentCategory()
  {
    new AssessmentTypePresenter(this._recordView, (IAssessmentTypeView) this).GetAssessmentCategoryList(this._recordView.Component.ComponentTypeID, this._recordView.Equipment.DesignCodeID);
  }

  public void DisableViewControls()
  {
    this.lookupAssessmentCategory.Properties.TextEditStyle = TextEditStyles.DisableTextEditor;
    this.lookupAssessmentCategory.Properties.ReadOnly = true;
    this.lookupAssessmentType.Properties.TextEditStyle = TextEditStyles.DisableTextEditor;
    this.lookupAssessmentType.Properties.ReadOnly = true;
  }

  public void ShowMaterialLookup(string designCodeApp)
  {
    object instance = Activator.CreateInstance(System.Type.GetType($"IntegriWISE.FormView.Material.frmMaterial_{designCodeApp}, IntegriWISE.FormView"));
    if ((instance as Form).ShowDialog() != DialogResult.OK)
      return;
    IMaterialBaseView materialBaseView = (IMaterialBaseView) instance;
    this._presenter.GetMaterial(materialBaseView.MaterialSpecNo, materialBaseView.MaterialGrade, materialBaseView.MaterialXMLString);
    this._recordView.PopulateMaterial(materialBaseView.MaterialXMLString);
  }

  public bool ValidateGeneral() => this._presenter.ValidateInput();

  public void Save() => this._presenter.Save();

  private void btnClearMaterial_Click(object sender, EventArgs e)
  {
    this._presenter.ClearMaterial();
    this._recordView.PopulateMaterial((string) null);
  }
}
