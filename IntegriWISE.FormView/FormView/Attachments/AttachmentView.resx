<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>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</value>
  </data>
</root>