// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Attachments.frmImageViewer
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Controls.ImageViewer;
using IntegriWISE.DataTransferObjects;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Attachment;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Attachments;

public class frmImageViewer : XtraForm, IImageViewerView, IView, IBaseView
{
  private IContainer components;
  private ImageControl imageControl1;
  private MemoEdit memoDescription;
  private PanelControl panelControl1;
  private TableLayoutPanel tableLayoutPanel1;
  private SimpleButton btnDelete;
  private SimpleButton btnSaveAs;
  private SimpleButton btnClose;
  private SimpleButton btnUpdate;
  private PanelControl panelControl2;
  private TableLayoutPanel tableLayoutPanel2;
  private LabelControl labelControl1;
  private LabelControl labelControl2;
  private TextEdit txtName;
  private ImageDTO _image;
  private ImageViewerPresenter _presenter;
  private MemoryStream _imageStream;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (frmImageViewer));
    this.memoDescription = new MemoEdit();
    this.panelControl1 = new PanelControl();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.btnDelete = new SimpleButton();
    this.btnSaveAs = new SimpleButton();
    this.btnClose = new SimpleButton();
    this.btnUpdate = new SimpleButton();
    this.panelControl2 = new PanelControl();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.txtName = new TextEdit();
    this.labelControl1 = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.imageControl1 = new ImageControl();
    this.memoDescription.Properties.BeginInit();
    this.panelControl1.BeginInit();
    this.panelControl1.SuspendLayout();
    this.tableLayoutPanel1.SuspendLayout();
    this.panelControl2.BeginInit();
    this.panelControl2.SuspendLayout();
    this.tableLayoutPanel2.SuspendLayout();
    this.txtName.Properties.BeginInit();
    this.SuspendLayout();
    this.memoDescription.Dock = DockStyle.Fill;
    this.memoDescription.EditValue = (object) "";
    this.memoDescription.Location = new Point(93, 23);
    this.memoDescription.Margin = new Padding(1);
    this.memoDescription.Name = "memoDescription";
    this.memoDescription.Properties.MaxLength = 500;
    this.memoDescription.Size = new Size(786, 60);
    this.memoDescription.TabIndex = 3;
    this.panelControl1.Controls.Add((Control) this.tableLayoutPanel1);
    this.panelControl1.Dock = DockStyle.Bottom;
    this.panelControl1.Location = new Point(0, 627);
    this.panelControl1.Name = "panelControl1";
    this.panelControl1.Size = new Size(884, 35);
    this.panelControl1.TabIndex = 2;
    this.tableLayoutPanel1.ColumnCount = 5;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.btnDelete, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.btnSaveAs, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.btnClose, 4, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.btnUpdate, 3, 0);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(2, 2);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.RowCount = 1;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(880, 31 /*0x1F*/);
    this.tableLayoutPanel1.TabIndex = 0;
    this.btnDelete.Location = new Point(3, 3);
    this.btnDelete.Name = "btnDelete";
    this.btnDelete.Size = new Size(75, 23);
    this.btnDelete.TabIndex = 3;
    this.btnDelete.Text = "Delete";
    this.btnDelete.Click += new EventHandler(this.btnDelete_Click);
    this.btnSaveAs.Location = new Point(84, 3);
    this.btnSaveAs.Name = "btnSaveAs";
    this.btnSaveAs.Size = new Size(75, 23);
    this.btnSaveAs.TabIndex = 2;
    this.btnSaveAs.Text = "Save As ...";
    this.btnSaveAs.Click += new EventHandler(this.btnSaveAs_Click);
    this.btnClose.Location = new Point(802, 3);
    this.btnClose.Name = "btnClose";
    this.btnClose.Size = new Size(75, 23);
    this.btnClose.TabIndex = 0;
    this.btnClose.Text = "Close";
    this.btnClose.Click += new EventHandler(this.btnClose_Click);
    this.btnUpdate.Location = new Point(626, 3);
    this.btnUpdate.Name = "btnUpdate";
    this.btnUpdate.Size = new Size(170, 23);
    this.btnUpdate.TabIndex = 1;
    this.btnUpdate.Text = "Update Name and Description";
    this.btnUpdate.Click += new EventHandler(this.btnUpdate_Click);
    this.panelControl2.AutoSize = true;
    this.panelControl2.Controls.Add((Control) this.tableLayoutPanel2);
    this.panelControl2.Dock = DockStyle.Bottom;
    this.panelControl2.Location = new Point(0, 539);
    this.panelControl2.Name = "panelControl2";
    this.panelControl2.Size = new Size(884, 88);
    this.panelControl2.TabIndex = 1;
    this.tableLayoutPanel2.AutoSize = true;
    this.tableLayoutPanel2.ColumnCount = 2;
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel2.Controls.Add((Control) this.txtName, 1, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.memoDescription, 1, 1);
    this.tableLayoutPanel2.Controls.Add((Control) this.labelControl1, 0, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.labelControl2, 0, 1);
    this.tableLayoutPanel2.Dock = DockStyle.Fill;
    this.tableLayoutPanel2.Location = new Point(2, 2);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.RowCount = 3;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.Size = new Size(880, 84);
    this.tableLayoutPanel2.TabIndex = 0;
    this.txtName.Dock = DockStyle.Fill;
    this.txtName.EditValue = (object) "";
    this.txtName.Location = new Point(93, 1);
    this.txtName.Margin = new Padding(1);
    this.txtName.Name = "txtName";
    this.txtName.Properties.MaxLength = 50;
    this.txtName.Size = new Size(786, 20);
    this.txtName.TabIndex = 1;
    this.labelControl1.Location = new Point(3, 3);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(64 /*0x40*/, 13);
    this.labelControl1.TabIndex = 0;
    this.labelControl1.Text = "Image Name:";
    this.labelControl2.Location = new Point(3, 25);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(86, 13);
    this.labelControl2.TabIndex = 2;
    this.labelControl2.Text = "Image Description";
    this.imageControl1.BorderStyle = BorderStyle.FixedSingle;
    this.imageControl1.Dock = DockStyle.Fill;
    this.imageControl1.Image = (Image) null;
    this.imageControl1.initialimage = (Image) null;
    this.imageControl1.Location = new Point(0, 0);
    this.imageControl1.Name = "imageControl1";
    this.imageControl1.Origin = new Point(0, 0);
    this.imageControl1.PanButton = MouseButtons.Left;
    this.imageControl1.PanMode = true;
    this.imageControl1.ScrollbarsVisible = true;
    this.imageControl1.Size = new Size(884, 539);
    this.imageControl1.StretchImageToFit = false;
    this.imageControl1.TabIndex = 0;
    this.imageControl1.ZoomFactor = 1.0;
    this.imageControl1.ZoomOnMouseWheel = true;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.ClientSize = new Size(884, 662);
    this.Controls.Add((Control) this.imageControl1);
    this.Controls.Add((Control) this.panelControl2);
    this.Controls.Add((Control) this.panelControl1);
    this.Icon = (Icon) componentResourceManager.GetObject("$this.Icon");
    this.MinimumSize = new Size(900, 700);
    this.Name = nameof (frmImageViewer);
    this.StartPosition = FormStartPosition.CenterScreen;
    this.Text = "Image Viewer";
    this.memoDescription.Properties.EndInit();
    this.panelControl1.EndInit();
    this.panelControl1.ResumeLayout(false);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.panelControl2.EndInit();
    this.panelControl2.ResumeLayout(false);
    this.panelControl2.PerformLayout();
    this.tableLayoutPanel2.ResumeLayout(false);
    this.tableLayoutPanel2.PerformLayout();
    this.txtName.Properties.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public frmImageViewer(ImageDTO image)
  {
    this.InitializeComponent();
    this._image = image;
    this._presenter = new ImageViewerPresenter((IImageViewerView) this);
    this._presenter.GetImage();
  }

  private void btnClose_Click(object sender, EventArgs e) => this.Close();

  private void btnDelete_Click(object sender, EventArgs e)
  {
    this._presenter.Delete();
    if (!this.Succeeded)
      return;
    this.DialogResult = DialogResult.OK;
  }

  private void btnUpdate_Click(object sender, EventArgs e)
  {
    this._presenter.UpdateImageDescription();
  }

  private void btnSaveAs_Click(object sender, EventArgs e)
  {
    SaveFileDialog saveFileDialog = new SaveFileDialog();
    saveFileDialog.FileName = $"{this.ImageName}-{DateTime.Now:dd-MMM-yyyy HHmmssfff}";
    saveFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Personal);
    saveFileDialog.Filter = "JPEG (*.jpg)|*.jpg";
    saveFileDialog.DefaultExt = ".jpg";
    if (saveFileDialog.ShowDialog() != DialogResult.OK)
      return;
    try
    {
      Image.FromStream((Stream) this._imageStream).Save(saveFileDialog.FileName, ImageFormat.Jpeg);
      int num = (int) MessageBox.Show($"The image has been saved as {saveFileDialog.FileName}", "Image", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
    }
    catch
    {
      int num = (int) MessageBox.Show($"There was a problem saving the image as as {saveFileDialog.FileName}", "Image", MessageBoxButtons.OK, MessageBoxIcon.Hand);
    }
  }

  public string ImageName
  {
    get => this.txtName.Text.Trim();
    set => this.txtName.Text = value;
  }

  public string ImageDescription
  {
    get => this.memoDescription.Text.Trim();
    set => this.memoDescription.Text = value;
  }

  public int ImageID => this._image.ImageID.Value;

  public void DisplayImage(byte[] imageBinary)
  {
    this._imageStream = new MemoryStream(imageBinary);
    this.imageControl1.Image = Image.FromStream((Stream) this._imageStream);
  }

  public string ErrorMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "Image", MessageBoxButtons.OK, MessageBoxIcon.Hand);
    }
  }

  public string WarningMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "Image", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
    }
  }

  public string InformationMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "Image", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
    }
  }

  public string QuestionMessage
  {
    set
    {
      switch (XtraMessageBox.Show((IWin32Window) this, value, "Image", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2))
      {
        case DialogResult.Yes:
          this.QuestionDecision = "YES";
          break;
        case DialogResult.No:
          this.QuestionDecision = "NO";
          break;
        default:
          this.QuestionDecision = "CANCEL";
          break;
      }
    }
  }

  public string QuestionDecision { get; set; }

  public string Caption
  {
    get => this.Text;
    set => this.Text = value;
  }

  public bool IsNew { get; set; }

  public bool IsDirty { get; set; }

  public bool Succeeded { get; set; }

  public string UserID => string.Empty;
}
