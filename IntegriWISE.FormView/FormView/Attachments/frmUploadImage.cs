// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Attachments.frmUploadImage
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Attachment;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Attachments;

public class frmUploadImage : BaseDialog, IImageUploadView, IView, IBaseView
{
  private const int _thumbnailWidth = 150;
  private const int _thumbnailHeight = 150;
  private int _itemID;
  private byte[] _barrImg;
  private byte[] _barrImgSmall;
  private ImageUploadPresenter _presenter;
  private IContainer components;
  private MemoEdit memoDescription;
  private TextEdit txtName;
  private SimpleButton btnBrowse;
  private TextEdit txtImage;
  private LabelControl labelControl3;
  private LabelControl labelControl2;
  private LabelControl labelControl1;
  private GroupControl groupControl1;
  private PanelControl panelImage;
  private PictureBox picPreview;
  private SimpleButton btnUpload;
  private SimpleButton btnCancel;
  private OpenFileDialog openFileDialog1;
  private PanelControl panelControl1;

  public frmUploadImage(int itemID)
  {
    this.InitializeComponent();
    this._itemID = itemID;
    this._presenter = new ImageUploadPresenter((IImageUploadView) this);
  }

  private void btnBrowse_Click(object sender, EventArgs e) => this.LoadImage();

  private void btnUpload_Click(object sender, EventArgs e)
  {
    this._presenter.UploadImage();
    if (!this.Succeeded)
      return;
    this.DialogResult = DialogResult.OK;
  }

  private void btnCancel_Click(object sender, EventArgs e)
  {
    this.DialogResult = DialogResult.Cancel;
  }

  protected void LoadImage()
  {
    try
    {
      if (this.openFileDialog1.ShowDialog((IWin32Window) this) != DialogResult.OK)
        return;
      string fileName = this.openFileDialog1.FileName;
      this.txtImage.Text = fileName;
      this.txtName.Text = Path.GetFileNameWithoutExtension(fileName);
      Image img = Image.FromFile(fileName);
      MemoryStream memoryStream1 = this.JPEGStream(img, 80 /*0x50*/);
      this._barrImg = new byte[Convert.ToInt32(memoryStream1.Length)];
      this._barrImg = memoryStream1.ToArray();
      memoryStream1.Close();
      img.Dispose();
      this.picPreview.SizeMode = PictureBoxSizeMode.CenterImage;
      this.picPreview.Image = Image.FromStream((Stream) this.ResizeImage(fileName, true, 320, 800));
      MemoryStream memoryStream2 = this.ResizeImage(fileName, true, 150, 150);
      this._barrImgSmall = new byte[Convert.ToInt32(memoryStream2.Length)];
      this._barrImgSmall = memoryStream2.ToArray();
      memoryStream2.Close();
      this.txtName.Focus();
    }
    catch (Exception ex)
    {
      int num = (int) MessageBox.Show(ex.Message);
    }
  }

  private MemoryStream JPEGStream(Image img, int quality)
  {
    EncoderParameter encoderParameter = new EncoderParameter(Encoder.Quality, (long) quality);
    ImageCodecInfo encoderInfo = frmUploadImage.GetEncoderInfo("image/jpeg");
    EncoderParameters encoderParams = new EncoderParameters(1);
    encoderParams.Param[0] = encoderParameter;
    MemoryStream memoryStream = new MemoryStream();
    img.Save((Stream) memoryStream, encoderInfo, encoderParams);
    return memoryStream;
  }

  private MemoryStream ResizeImage(
    string OriginalFile,
    bool OnlyResizeIfWider,
    int Height,
    int Width)
  {
    Image image = Image.FromFile(OriginalFile);
    image.RotateFlip(RotateFlipType.Rotate180FlipNone);
    image.RotateFlip(RotateFlipType.Rotate180FlipNone);
    if (OnlyResizeIfWider && image.Width <= Width)
      Width = image.Width;
    int thumbHeight = image.Height * Width / image.Width;
    if (thumbHeight > Height)
    {
      Width = image.Width * Height / image.Height;
      thumbHeight = Height;
    }
    Image thumbnailImage = image.GetThumbnailImage(Width, thumbHeight, (Image.GetThumbnailImageAbort) null, IntPtr.Zero);
    image.Dispose();
    MemoryStream memoryStream = this.JPEGStream(thumbnailImage, 80 /*0x50*/);
    thumbnailImage.Dispose();
    return memoryStream;
  }

  private static ImageCodecInfo GetEncoderInfo(string mimeType)
  {
    ImageCodecInfo[] imageEncoders = ImageCodecInfo.GetImageEncoders();
    for (int index = 0; index < imageEncoders.Length; ++index)
    {
      if (imageEncoders[index].MimeType == mimeType)
        return imageEncoders[index];
    }
    return (ImageCodecInfo) null;
  }

  public int? ID => new int?(this._itemID);

  public string ImageFileName => this.txtImage.Text.Trim();

  public string ImageName => this.txtName.Text.Trim();

  public string ImageDescription => this.memoDescription.Text.Trim();

  public byte[] ImageBinary => this._barrImg;

  public byte[] ImageBinarySmall => this._barrImgSmall;

  public string ErrorMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "Image Upload", MessageBoxButtons.OK, MessageBoxIcon.Hand);
    }
  }

  public string WarningMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "Image Upload", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
    }
  }

  public string InformationMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "Image Upload", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
    }
  }

  public string QuestionMessage
  {
    set
    {
      switch (XtraMessageBox.Show((IWin32Window) this, value, "Image Upload", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2))
      {
        case DialogResult.Yes:
          this.QuestionDecision = "YES";
          break;
        case DialogResult.No:
          this.QuestionDecision = "NO";
          break;
        default:
          this.QuestionDecision = "CANCEL";
          break;
      }
    }
  }

  public string QuestionDecision { get; set; }

  public string Caption
  {
    get => this.Text;
    set => this.Text = value;
  }

  public bool IsNew { get; set; }

  public bool IsDirty { get; set; }

  public bool Succeeded { get; set; }

  public string UserID => string.Empty;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.memoDescription = new MemoEdit();
    this.txtName = new TextEdit();
    this.btnBrowse = new SimpleButton();
    this.txtImage = new TextEdit();
    this.labelControl3 = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.labelControl1 = new LabelControl();
    this.groupControl1 = new GroupControl();
    this.panelImage = new PanelControl();
    this.picPreview = new PictureBox();
    this.btnCancel = new SimpleButton();
    this.btnUpload = new SimpleButton();
    this.openFileDialog1 = new OpenFileDialog();
    this.panelControl1 = new PanelControl();
    this.pnlBottom.SuspendLayout();
    this.memoDescription.Properties.BeginInit();
    this.txtName.Properties.BeginInit();
    this.txtImage.Properties.BeginInit();
    this.groupControl1.BeginInit();
    this.groupControl1.SuspendLayout();
    this.panelImage.BeginInit();
    this.panelImage.SuspendLayout();
    ((ISupportInitialize) this.picPreview).BeginInit();
    this.panelControl1.BeginInit();
    this.panelControl1.SuspendLayout();
    this.SuspendLayout();
    this.pnlBottom.Controls.Add((Control) this.btnUpload);
    this.pnlBottom.Controls.Add((Control) this.btnCancel);
    this.pnlBottom.Location = new Point(0, 561);
    this.pnlBottom.Size = new Size(884, 41);
    this.pnlBottom.TabIndex = 3;
    this.memoDescription.EditValue = (object) "";
    this.memoDescription.Location = new Point(103, 61);
    this.memoDescription.Name = "memoDescription";
    this.memoDescription.Properties.MaxLength = 500;
    this.memoDescription.Size = new Size(769, 60);
    this.memoDescription.TabIndex = 6;
    this.txtName.EditValue = (object) "";
    this.txtName.Location = new Point(103, 35);
    this.txtName.Name = "txtName";
    this.txtName.Properties.MaxLength = 50;
    this.txtName.Size = new Size(769, 20);
    this.txtName.TabIndex = 4;
    this.btnBrowse.Location = new Point(813, 6);
    this.btnBrowse.Name = "btnBrowse";
    this.btnBrowse.Size = new Size(59, 23);
    this.btnBrowse.TabIndex = 2;
    this.btnBrowse.Text = "Browse...";
    this.btnBrowse.Click += new EventHandler(this.btnBrowse_Click);
    this.txtImage.EditValue = (object) "";
    this.txtImage.Location = new Point(103, 9);
    this.txtImage.Name = "txtImage";
    this.txtImage.Properties.ReadOnly = true;
    this.txtImage.Size = new Size(704, 20);
    this.txtImage.TabIndex = 1;
    this.txtImage.TabStop = false;
    this.labelControl3.Location = new Point(15, 64 /*0x40*/);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(57, 13);
    this.labelControl3.TabIndex = 5;
    this.labelControl3.Text = "Description:";
    this.labelControl2.Location = new Point(15, 38);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(31 /*0x1F*/, 13);
    this.labelControl2.TabIndex = 3;
    this.labelControl2.Text = "Name:";
    this.labelControl1.Location = new Point(15, 12);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(34, 13);
    this.labelControl1.TabIndex = 0;
    this.labelControl1.Text = "Image:";
    this.groupControl1.Appearance.BackColor = Color.White;
    this.groupControl1.Appearance.Options.UseBackColor = true;
    this.groupControl1.Controls.Add((Control) this.panelImage);
    this.groupControl1.Dock = DockStyle.Fill;
    this.groupControl1.Location = new Point(0, 197);
    this.groupControl1.Name = "groupControl1";
    this.groupControl1.Size = new Size(884, 364);
    this.groupControl1.TabIndex = 2;
    this.groupControl1.Text = "Image Preview";
    this.panelImage.Appearance.BackColor = Color.White;
    this.panelImage.Appearance.Options.UseBackColor = true;
    this.panelImage.BorderStyle = BorderStyles.NoBorder;
    this.panelImage.Controls.Add((Control) this.picPreview);
    this.panelImage.Dock = DockStyle.Fill;
    this.panelImage.Location = new Point(2, 21);
    this.panelImage.Name = "panelImage";
    this.panelImage.Size = new Size(880, 341);
    this.panelImage.TabIndex = 0;
    this.picPreview.BackgroundImageLayout = ImageLayout.Stretch;
    this.picPreview.Dock = DockStyle.Fill;
    this.picPreview.Location = new Point(0, 0);
    this.picPreview.Name = "picPreview";
    this.picPreview.Size = new Size(880, 341);
    this.picPreview.TabIndex = 0;
    this.picPreview.TabStop = false;
    this.btnCancel.DialogResult = DialogResult.Cancel;
    this.btnCancel.Location = new Point(797, 6);
    this.btnCancel.Name = "btnCancel";
    this.btnCancel.Size = new Size(75, 23);
    this.btnCancel.TabIndex = 1;
    this.btnCancel.Text = "Cancel";
    this.btnCancel.Click += new EventHandler(this.btnCancel_Click);
    this.btnUpload.Location = new Point(716, 6);
    this.btnUpload.Name = "btnUpload";
    this.btnUpload.Size = new Size(75, 23);
    this.btnUpload.TabIndex = 0;
    this.btnUpload.Text = "Upload";
    this.btnUpload.Click += new EventHandler(this.btnUpload_Click);
    this.openFileDialog1.Filter = "Supported Picture Files|*.bmp;*.jpg;*.jpeg;*.png;*.gif";
    this.panelControl1.Controls.Add((Control) this.memoDescription);
    this.panelControl1.Controls.Add((Control) this.labelControl1);
    this.panelControl1.Controls.Add((Control) this.labelControl2);
    this.panelControl1.Controls.Add((Control) this.txtName);
    this.panelControl1.Controls.Add((Control) this.labelControl3);
    this.panelControl1.Controls.Add((Control) this.btnBrowse);
    this.panelControl1.Controls.Add((Control) this.txtImage);
    this.panelControl1.Dock = DockStyle.Top;
    this.panelControl1.Location = new Point(0, 67);
    this.panelControl1.Name = "panelControl1";
    this.panelControl1.Size = new Size(884, 130);
    this.panelControl1.TabIndex = 1;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.CancelButton = (IButtonControl) this.btnCancel;
    this.ClientSize = new Size(884, 602);
    this.Controls.Add((Control) this.groupControl1);
    this.Controls.Add((Control) this.panelControl1);
    this.HeaderText = "Browse and upload an image";
    this.HeaderTitle = "Upload Image";
    this.MinimumSize = new Size(900, 640);
    this.Name = nameof (frmUploadImage);
    this.Text = "Upload Image";
    this.Controls.SetChildIndex((Control) this.pnlBottom, 0);
    this.Controls.SetChildIndex((Control) this.panelControl1, 0);
    this.Controls.SetChildIndex((Control) this.groupControl1, 0);
    this.pnlBottom.ResumeLayout(false);
    this.memoDescription.Properties.EndInit();
    this.txtName.Properties.EndInit();
    this.txtImage.Properties.EndInit();
    this.groupControl1.EndInit();
    this.groupControl1.ResumeLayout(false);
    this.panelImage.EndInit();
    this.panelImage.ResumeLayout(false);
    ((ISupportInitialize) this.picPreview).EndInit();
    this.panelControl1.EndInit();
    this.panelControl1.ResumeLayout(false);
    this.panelControl1.PerformLayout();
    this.ResumeLayout(false);
  }
}
