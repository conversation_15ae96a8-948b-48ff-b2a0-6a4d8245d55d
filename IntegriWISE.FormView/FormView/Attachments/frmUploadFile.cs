// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Attachments.frmUploadFile
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using ICSharpCode.SharpZipLib.Core;
using ICSharpCode.SharpZipLib.Zip;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Attachment;
using System;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Attachments;

public class frmUploadFile : BaseDialog, IFileUploadView, IView, IBaseView
{
  private int _itemID;
  private byte[] _fileBinary;
  private string _fileExt;
  private string _fileName;
  private string _fileSize;
  private int _fileType;
  private FileUploadPresenter _presenter;
  private IContainer components;
  private SimpleButton btnCancel;
  private SimpleButton btnUpload;
  private TextEdit txtFileSize;
  private LabelControl labelControl4;
  private MemoEdit memoDescription;
  private TextEdit txtName;
  private SimpleButton btnBrowse;
  private TextEdit txtFile;
  private LabelControl labelControl3;
  private LabelControl labelControl2;
  private LabelControl labelControl1;
  private OpenFileDialog openFileDialog1;

  public frmUploadFile(int itemID)
  {
    this.InitializeComponent();
    this._itemID = itemID;
    this._presenter = new FileUploadPresenter((IFileUploadView) this);
  }

  private void btnBrowse_Click(object sender, EventArgs e)
  {
    if (this.openFileDialog1.ShowDialog() != DialogResult.OK)
      return;
    string fileName = this.openFileDialog1.FileName;
    this.txtFile.Text = fileName;
    FileInfo fileInfo = new FileInfo(fileName);
    this._fileExt = fileInfo.Extension.ToLower();
    this._fileName = fileInfo.Name;
    this._fileSize = this.FormatBytes(fileInfo.Length);
    this.txtFileSize.Text = this._fileSize;
    this.txtName.Text = Path.GetFileNameWithoutExtension(fileName);
    this.txtName.Focus();
    this.GetFileType();
  }

  private void btnUpload_Click(object sender, EventArgs e)
  {
    this.Cursor = Cursors.WaitCursor;
    this._presenter.UploadFile();
    this.Cursor = Cursors.Default;
    if (!this.Succeeded)
      return;
    this.DialogResult = DialogResult.OK;
  }

  private void btnCancel_Click(object sender, EventArgs e)
  {
    this.DialogResult = DialogResult.Cancel;
  }

  private void GetFileType()
  {
    switch (this._fileExt)
    {
      case ".pdf":
        this._fileType = 1;
        break;
      case ".doc":
      case ".docx":
        this._fileType = 4;
        break;
      case ".xls":
      case ".xlsx":
        this._fileType = 2;
        break;
      case ".ppt":
      case ".pptx":
        this._fileType = 3;
        break;
      case ".txt":
        this._fileType = 5;
        break;
      default:
        this._fileType = 9;
        break;
    }
  }

  private string FormatBytes(long bytes)
  {
    string[] strArray = new string[4]
    {
      "GB",
      "MB",
      "KB",
      "Bytes"
    };
    long d2 = (long) Math.Pow(1024.0, (double) (strArray.Length - 1));
    foreach (string str in strArray)
    {
      if (bytes > d2)
        return $"{Decimal.Divide((Decimal) bytes, (Decimal) d2):##.##} {str}";
      d2 /= 1024L /*0x0400*/;
    }
    return "0 Bytes";
  }

  public int? ID => new int?(this._itemID);

  public string FileOriginalName => this._fileName;

  public string FileName => this.txtName.Text.Trim();

  public string FileDescription => this.memoDescription.Text.Trim();

  public string FileSize => this._fileSize;

  public string FileExt => this._fileExt;

  public int FileType => this._fileType;

  public byte[] FileBinary => this._fileBinary;

  public string FileFullName => this.txtFile.Text.Trim();

  public void CompressFile(string fileToCompress)
  {
    try
    {
      this.Cursor = Cursors.WaitCursor;
      FileStream source = File.OpenRead(fileToCompress);
      MemoryStream baseOutputStream = new MemoryStream();
      ZipOutputStream destination = new ZipOutputStream((Stream) baseOutputStream);
      destination.SetLevel(9);
      destination.PutNextEntry(new ZipEntry(fileToCompress)
      {
        DateTime = DateTime.Now
      });
      StreamUtils.Copy((Stream) source, (Stream) destination, new byte[4096 /*0x1000*/]);
      destination.CloseEntry();
      destination.IsStreamOwner = false;
      destination.Close();
      baseOutputStream.Position = 0L;
      this._fileBinary = baseOutputStream.ToArray();
      baseOutputStream.Close();
    }
    catch (Exception ex)
    {
      throw ex;
    }
    finally
    {
      this.Cursor = Cursors.Default;
    }
  }

  public string ErrorMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "File Upload", MessageBoxButtons.OK, MessageBoxIcon.Hand);
    }
  }

  public string WarningMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "File Upload", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
    }
  }

  public string InformationMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "File Upload", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
    }
  }

  public string QuestionMessage
  {
    set
    {
      switch (XtraMessageBox.Show((IWin32Window) this, value, "File Upload", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2))
      {
        case DialogResult.Yes:
          this.QuestionDecision = "YES";
          break;
        case DialogResult.No:
          this.QuestionDecision = "NO";
          break;
        default:
          this.QuestionDecision = "CANCEL";
          break;
      }
    }
  }

  public string QuestionDecision { get; set; }

  public string Caption
  {
    get => this.Text;
    set => this.Text = value;
  }

  public bool IsNew { get; set; }

  public bool IsDirty { get; set; }

  public bool Succeeded { get; set; }

  public string UserID => string.Empty;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.txtFileSize = new TextEdit();
    this.labelControl4 = new LabelControl();
    this.memoDescription = new MemoEdit();
    this.txtName = new TextEdit();
    this.btnBrowse = new SimpleButton();
    this.txtFile = new TextEdit();
    this.labelControl3 = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.labelControl1 = new LabelControl();
    this.btnCancel = new SimpleButton();
    this.btnUpload = new SimpleButton();
    this.openFileDialog1 = new OpenFileDialog();
    this.pnlBottom.SuspendLayout();
    this.txtFileSize.Properties.BeginInit();
    this.memoDescription.Properties.BeginInit();
    this.txtName.Properties.BeginInit();
    this.txtFile.Properties.BeginInit();
    this.SuspendLayout();
    this.pnlBottom.Controls.Add((Control) this.btnCancel);
    this.pnlBottom.Controls.Add((Control) this.btnUpload);
    this.pnlBottom.Location = new Point(0, 391);
    this.pnlBottom.Size = new Size(594, 41);
    this.txtFileSize.Location = new Point(106, 330);
    this.txtFileSize.Name = "txtFileSize";
    this.txtFileSize.Properties.Appearance.BackColor = Color.FromArgb(233, 237, 241);
    this.txtFileSize.Properties.Appearance.Options.UseBackColor = true;
    this.txtFileSize.Properties.ReadOnly = true;
    this.txtFileSize.Size = new Size(122, 20);
    this.txtFileSize.TabIndex = 18;
    this.txtFileSize.TabStop = false;
    this.labelControl4.Location = new Point(16 /*0x10*/, 333);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(42, 13);
    this.labelControl4.TabIndex = 17;
    this.labelControl4.Text = "File Size:";
    this.memoDescription.EditValue = (object) "";
    this.memoDescription.Location = new Point(106, 139);
    this.memoDescription.Name = "memoDescription";
    this.memoDescription.Properties.MaxLength = 500;
    this.memoDescription.Size = new Size(408, 185);
    this.memoDescription.TabIndex = 16 /*0x10*/;
    this.txtName.EditValue = (object) "";
    this.txtName.Location = new Point(106, 113);
    this.txtName.Name = "txtName";
    this.txtName.Properties.MaxLength = 100;
    this.txtName.Size = new Size(408, 20);
    this.txtName.TabIndex = 14;
    this.btnBrowse.Location = new Point(520, 84);
    this.btnBrowse.Name = "btnBrowse";
    this.btnBrowse.Size = new Size(59, 23);
    this.btnBrowse.TabIndex = 12;
    this.btnBrowse.Text = "Browse..";
    this.btnBrowse.Click += new EventHandler(this.btnBrowse_Click);
    this.txtFile.EditValue = (object) "";
    this.txtFile.Location = new Point(106, 87);
    this.txtFile.Name = "txtFile";
    this.txtFile.Properties.ReadOnly = true;
    this.txtFile.Size = new Size(408, 20);
    this.txtFile.TabIndex = 11;
    this.txtFile.TabStop = false;
    this.labelControl3.Location = new Point(18, 142);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(57, 13);
    this.labelControl3.TabIndex = 15;
    this.labelControl3.Text = "Description:";
    this.labelControl2.Location = new Point(18, 116);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(31 /*0x1F*/, 13);
    this.labelControl2.TabIndex = 13;
    this.labelControl2.Text = "Name:";
    this.labelControl1.Location = new Point(18, 90);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(20, 13);
    this.labelControl1.TabIndex = 10;
    this.labelControl1.Text = "File:";
    this.btnCancel.DialogResult = DialogResult.Cancel;
    this.btnCancel.Location = new Point(507, 6);
    this.btnCancel.Name = "btnCancel";
    this.btnCancel.Size = new Size(75, 23);
    this.btnCancel.TabIndex = 3;
    this.btnCancel.Text = "&Cancel";
    this.btnCancel.Click += new EventHandler(this.btnCancel_Click);
    this.btnUpload.Location = new Point(426, 6);
    this.btnUpload.Name = "btnUpload";
    this.btnUpload.Size = new Size(75, 23);
    this.btnUpload.TabIndex = 2;
    this.btnUpload.Text = "&Upload";
    this.btnUpload.Click += new EventHandler(this.btnUpload_Click);
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.CancelButton = (IButtonControl) this.btnCancel;
    this.ClientSize = new Size(594, 432);
    this.Controls.Add((Control) this.txtFileSize);
    this.Controls.Add((Control) this.labelControl4);
    this.Controls.Add((Control) this.memoDescription);
    this.Controls.Add((Control) this.txtName);
    this.Controls.Add((Control) this.btnBrowse);
    this.Controls.Add((Control) this.txtFile);
    this.Controls.Add((Control) this.labelControl3);
    this.Controls.Add((Control) this.labelControl2);
    this.Controls.Add((Control) this.labelControl1);
    this.HeaderText = "Browse and upload a file";
    this.HeaderTitle = "Upload File";
    this.MaximumSize = new Size(610, 470);
    this.MinimumSize = new Size(610, 470);
    this.Name = nameof (frmUploadFile);
    this.Text = "Upload File";
    this.Controls.SetChildIndex((Control) this.pnlBottom, 0);
    this.Controls.SetChildIndex((Control) this.labelControl1, 0);
    this.Controls.SetChildIndex((Control) this.labelControl2, 0);
    this.Controls.SetChildIndex((Control) this.labelControl3, 0);
    this.Controls.SetChildIndex((Control) this.txtFile, 0);
    this.Controls.SetChildIndex((Control) this.btnBrowse, 0);
    this.Controls.SetChildIndex((Control) this.txtName, 0);
    this.Controls.SetChildIndex((Control) this.memoDescription, 0);
    this.Controls.SetChildIndex((Control) this.labelControl4, 0);
    this.Controls.SetChildIndex((Control) this.txtFileSize, 0);
    this.pnlBottom.ResumeLayout(false);
    this.txtFileSize.Properties.EndInit();
    this.memoDescription.Properties.EndInit();
    this.txtName.Properties.EndInit();
    this.txtFile.Properties.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
