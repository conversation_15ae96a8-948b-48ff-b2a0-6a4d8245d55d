// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Attachments.AttachmentView
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Layout;
using DevExpress.XtraLayout;
using IntegriWISE.DataTransferObjects;
using IntegriWISE.Tools;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Attachment;
using IntegriWISE.UserInterface.Record;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Attachments;

public class AttachmentView : 
  RecordTabControl,
  IAttachmentView,
  IAttachmentManagerView,
  IBaseView,
  IView
{
  public IRecordView _recordView;
  private AttachmentPresenter _presenter;
  private List<ImageDTO> _imageList;
  private IContainer components;
  private GridControl gridFile;
  private GridView gridViewFile;
  private GridColumn colType;
  private RepositoryItemImageComboBox repositoryItemImageComboBox1;
  private System.Windows.Forms.ImageList imageList1;
  private GridColumn colName;
  private RepositoryItemHyperLinkEdit repositoryItemHyperLinkEdit2;
  private GridColumn colDescription;
  private GridColumn colFileName;
  private GridColumn colFileSize;
  private GridColumn colDate;
  private GridColumn colUploadedBy;
  private GridColumn colFileID;
  private GridColumn colSaveAs;
  private RepositoryItemButtonEdit btnSaveAs;
  private GridColumn colEdit;
  private RepositoryItemButtonEdit btnEdit;
  private GridColumn colRemove;
  private RepositoryItemButtonEdit btnDeleteFile;
  private RepositoryItemImageComboBox repositoryItemImageComboBox2;
  private SimpleButton btnRefreshImage;
  private GroupControl groupControl2;
  private TableLayoutPanel tableLayoutPanel1;
  private SimpleButton btnBrowseFile;
  private SimpleButton btnRefreshFiles;
  private GridControl gridControl1;
  private LayoutView layoutView1;
  private LayoutViewColumn colCaption;
  private RepositoryItemTextEdit repositoryItemTextEdit1;
  private LayoutViewField Item1;
  private LayoutViewColumn colDesc;
  private RepositoryItemMemoEdit repositoryItemMemoEdit2;
  private LayoutViewField layoutViewField_colDesc;
  private LayoutViewColumn colPhoto;
  private RepositoryItemPictureEdit repositoryItemPictureEdit1;
  private LayoutViewField layoutViewField_colPhoto;
  private LayoutViewColumn colViewLarger;
  private RepositoryItemHyperLinkEdit repositoryItemHyperLinkEdit1;
  private LayoutViewField layoutViewField_colViewLarger;
  private LayoutViewColumn colImageID;
  private LayoutViewField Item2;
  private LayoutViewColumn layoutViewColumn1;
  private LayoutViewField layoutViewField_layoutViewColumn1;
  private LayoutViewCard layoutViewCard1;
  private SimpleButton btnBrowseImage;
  private TableLayoutPanel tableLayoutPanel2;
  private GroupControl groupControl3;
  private GroupControl groupControl1;
  private SplitContainerControl splitContainerControl1;
  private SaveFileDialog saveFileDialog1;

  public AttachmentView(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._presenter = new AttachmentPresenter(recordView, (IAttachmentView) this);
    this._presenter.PopulateRecordImageList();
    this._presenter.PopulateRecordFileList();
  }

  private void btnBrowseFile_Click(object sender, EventArgs e)
  {
    if (!this._presenter.SaveAssessmentBeforeUpload())
      return;
    frmUploadFile frmUploadFile = new frmUploadFile(this._recordView.AssessmentID.Value);
    if (frmUploadFile.ShowDialog() == DialogResult.OK)
    {
      this.Cursor = Cursors.WaitCursor;
      this._presenter.PopulateRecordFileList();
      this.Cursor = Cursors.Default;
    }
    frmUploadFile.Dispose();
  }

  public List<ImageDTO> ImageList
  {
    set
    {
      this._imageList = value;
      this.gridControl1.DataSource = (object) this._imageList;
    }
  }

  public List<FileDTO> FileList
  {
    set => this.gridFile.DataSource = (object) value;
  }

  public void OpenFile(FileDTO file)
  {
    this.Cursor = Cursors.WaitCursor;
    new FileManager().OpenFile(file);
    this.Cursor = Cursors.Default;
  }

  public void SaveAs(FileDTO file, string fileName)
  {
    this.Cursor = Cursors.WaitCursor;
    new FileManager().SaveAs(file, fileName);
    this.Cursor = Cursors.Default;
    this.Succeeded = true;
  }

  public bool IsNew { get; set; }

  public bool IsDirty { get; set; }

  public bool Succeeded { get; set; }

  public string UserID => string.Empty;

  public string ErrorMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "Attachment", MessageBoxButtons.OK, MessageBoxIcon.Hand);
    }
  }

  public string WarningMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "Attachment", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
    }
  }

  public string InformationMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "Attachment", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
    }
  }

  public string QuestionMessage
  {
    set
    {
      switch (XtraMessageBox.Show((IWin32Window) this, value, "Attachment", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2))
      {
        case DialogResult.Yes:
          this.QuestionDecision = "YES";
          break;
        case DialogResult.No:
          this.QuestionDecision = "NO";
          break;
        default:
          this.QuestionDecision = "CANCEL";
          break;
      }
    }
  }

  public string QuestionDecision { get; set; }

  public string Caption
  {
    get => this.Text;
    set => this.Text = value;
  }

  private void btnBrowseImage_Click(object sender, EventArgs e)
  {
    if (!this._presenter.SaveAssessmentBeforeUpload())
      return;
    frmUploadImage frmUploadImage = new frmUploadImage(this._recordView.AssessmentID.Value);
    if (frmUploadImage.ShowDialog() == DialogResult.OK)
    {
      this.Cursor = Cursors.WaitCursor;
      this._presenter.PopulateRecordImageList();
      this.Cursor = Cursors.Default;
    }
    frmUploadImage.Dispose();
  }

  private void btnRefreshImage_Click(object sender, EventArgs e)
  {
    this.Cursor = Cursors.WaitCursor;
    this._presenter.PopulateRecordImageList();
    this.Cursor = Cursors.Default;
  }

  private void btnRefreshFiles_Click(object sender, EventArgs e)
  {
    this.Cursor = Cursors.WaitCursor;
    this._presenter.PopulateRecordFileList();
    this.Cursor = Cursors.Default;
  }

  private void gridViewFile_RowCellClick(object sender, RowCellClickEventArgs e)
  {
    if (e.Column != this.colName)
      return;
    FileDTO focusedRow = (sender as GridView).GetFocusedRow() as FileDTO;
    if (XtraMessageBox.Show((IWin32Window) this, $"Open {focusedRow.FileDocName}?", "Open File", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) != DialogResult.Yes)
      return;
    this._presenter.OpenFile(focusedRow);
  }

  private void btnDeleteFile_ButtonClick(object sender, ButtonPressedEventArgs e)
  {
    this._presenter.DeleteFile(this.gridViewFile.GetFocusedRow() as FileDTO);
    if (!this.Succeeded)
      return;
    this.Cursor = Cursors.WaitCursor;
    this._presenter.PopulateRecordFileList();
    this.Cursor = Cursors.Default;
  }

  private void btnSaveAs_ButtonClick(object sender, ButtonPressedEventArgs e)
  {
    if (!(this.gridViewFile.GetFocusedRow() is FileDTO focusedRow))
      return;
    this.saveFileDialog1.FileName = focusedRow.OriFileName;
    this.saveFileDialog1.Filter = string.Format("*{0}|*{0}", (object) focusedRow.FileExt);
    if (this.saveFileDialog1.ShowDialog() != DialogResult.OK)
      return;
    this._presenter.SaveAs(focusedRow, this.saveFileDialog1.FileName);
  }

  private void btnEdit_ButtonClick(object sender, ButtonPressedEventArgs e)
  {
    if (new frmFileDetail(this.gridViewFile.GetFocusedRow() as FileDTO).ShowDialog() != DialogResult.OK)
      return;
    this.Cursor = Cursors.WaitCursor;
    this._presenter.PopulateRecordFileList();
    this.Cursor = Cursors.Default;
  }

  private void layoutView1_Click(object sender, EventArgs e)
  {
    LayoutView layoutView1 = this.layoutView1;
    if (layoutView1.FocusedColumn == null || layoutView1.FocusedRowHandle < 0 || layoutView1.FocusedColumn != this.colViewLarger)
      return;
    frmImageViewer frmImageViewer = new frmImageViewer(layoutView1.GetFocusedRow() as ImageDTO);
    if (frmImageViewer.ShowDialog() == DialogResult.OK)
    {
      this.Cursor = Cursors.WaitCursor;
      this._presenter.PopulateRecordImageList();
      this.Cursor = Cursors.Default;
    }
    frmImageViewer.Dispose();
  }

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (AttachmentView));
    SerializableAppearanceObject appearance1 = new SerializableAppearanceObject();
    SerializableAppearanceObject appearance2 = new SerializableAppearanceObject();
    SerializableAppearanceObject appearance3 = new SerializableAppearanceObject();
    this.gridFile = new GridControl();
    this.gridViewFile = new GridView();
    this.colType = new GridColumn();
    this.repositoryItemImageComboBox1 = new RepositoryItemImageComboBox();
    this.imageList1 = new System.Windows.Forms.ImageList(this.components);
    this.colName = new GridColumn();
    this.repositoryItemHyperLinkEdit2 = new RepositoryItemHyperLinkEdit();
    this.colDescription = new GridColumn();
    this.colFileName = new GridColumn();
    this.colFileSize = new GridColumn();
    this.colDate = new GridColumn();
    this.colUploadedBy = new GridColumn();
    this.colFileID = new GridColumn();
    this.colSaveAs = new GridColumn();
    this.btnSaveAs = new RepositoryItemButtonEdit();
    this.colEdit = new GridColumn();
    this.btnEdit = new RepositoryItemButtonEdit();
    this.colRemove = new GridColumn();
    this.btnDeleteFile = new RepositoryItemButtonEdit();
    this.repositoryItemImageComboBox2 = new RepositoryItemImageComboBox();
    this.btnRefreshImage = new SimpleButton();
    this.groupControl2 = new GroupControl();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.btnBrowseFile = new SimpleButton();
    this.btnRefreshFiles = new SimpleButton();
    this.gridControl1 = new GridControl();
    this.layoutView1 = new LayoutView();
    this.colCaption = new LayoutViewColumn();
    this.repositoryItemTextEdit1 = new RepositoryItemTextEdit();
    this.Item1 = new LayoutViewField();
    this.colDesc = new LayoutViewColumn();
    this.repositoryItemMemoEdit2 = new RepositoryItemMemoEdit();
    this.layoutViewField_colDesc = new LayoutViewField();
    this.colPhoto = new LayoutViewColumn();
    this.repositoryItemPictureEdit1 = new RepositoryItemPictureEdit();
    this.layoutViewField_colPhoto = new LayoutViewField();
    this.colViewLarger = new LayoutViewColumn();
    this.repositoryItemHyperLinkEdit1 = new RepositoryItemHyperLinkEdit();
    this.layoutViewField_colViewLarger = new LayoutViewField();
    this.colImageID = new LayoutViewColumn();
    this.Item2 = new LayoutViewField();
    this.layoutViewColumn1 = new LayoutViewColumn();
    this.layoutViewField_layoutViewColumn1 = new LayoutViewField();
    this.layoutViewCard1 = new LayoutViewCard();
    this.btnBrowseImage = new SimpleButton();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.groupControl3 = new GroupControl();
    this.groupControl1 = new GroupControl();
    this.splitContainerControl1 = new SplitContainerControl();
    this.saveFileDialog1 = new SaveFileDialog();
    this.gridFile.BeginInit();
    this.gridViewFile.BeginInit();
    this.repositoryItemImageComboBox1.BeginInit();
    this.repositoryItemHyperLinkEdit2.BeginInit();
    this.btnSaveAs.BeginInit();
    this.btnEdit.BeginInit();
    this.btnDeleteFile.BeginInit();
    this.repositoryItemImageComboBox2.BeginInit();
    this.groupControl2.BeginInit();
    this.groupControl2.SuspendLayout();
    this.tableLayoutPanel1.SuspendLayout();
    this.gridControl1.BeginInit();
    this.layoutView1.BeginInit();
    this.repositoryItemTextEdit1.BeginInit();
    this.Item1.BeginInit();
    this.repositoryItemMemoEdit2.BeginInit();
    this.layoutViewField_colDesc.BeginInit();
    this.repositoryItemPictureEdit1.BeginInit();
    this.layoutViewField_colPhoto.BeginInit();
    this.repositoryItemHyperLinkEdit1.BeginInit();
    this.layoutViewField_colViewLarger.BeginInit();
    this.Item2.BeginInit();
    this.layoutViewField_layoutViewColumn1.BeginInit();
    this.layoutViewCard1.BeginInit();
    this.tableLayoutPanel2.SuspendLayout();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.groupControl1.BeginInit();
    this.groupControl1.SuspendLayout();
    this.splitContainerControl1.BeginInit();
    this.splitContainerControl1.SuspendLayout();
    this.SuspendLayout();
    this.gridFile.Dock = DockStyle.Fill;
    this.gridFile.Location = new Point(2, 21);
    this.gridFile.MainView = (BaseView) this.gridViewFile;
    this.gridFile.Name = "gridFile";
    this.gridFile.RepositoryItems.AddRange(new RepositoryItem[6]
    {
      (RepositoryItem) this.repositoryItemImageComboBox1,
      (RepositoryItem) this.repositoryItemHyperLinkEdit2,
      (RepositoryItem) this.repositoryItemImageComboBox2,
      (RepositoryItem) this.btnDeleteFile,
      (RepositoryItem) this.btnSaveAs,
      (RepositoryItem) this.btnEdit
    });
    this.gridFile.Size = new Size(879, 397);
    this.gridFile.TabIndex = 0;
    this.gridFile.ViewCollection.AddRange(new BaseView[1]
    {
      (BaseView) this.gridViewFile
    });
    this.gridViewFile.Columns.AddRange(new GridColumn[11]
    {
      this.colType,
      this.colName,
      this.colDescription,
      this.colFileName,
      this.colFileSize,
      this.colDate,
      this.colUploadedBy,
      this.colFileID,
      this.colSaveAs,
      this.colEdit,
      this.colRemove
    });
    this.gridViewFile.GridControl = this.gridFile;
    this.gridViewFile.Name = "gridViewFile";
    this.gridViewFile.OptionsBehavior.AllowAddRows = DefaultBoolean.False;
    this.gridViewFile.OptionsBehavior.AllowDeleteRows = DefaultBoolean.False;
    this.gridViewFile.OptionsSelection.EnableAppearanceFocusedCell = false;
    this.gridViewFile.OptionsSelection.EnableAppearanceFocusedRow = false;
    this.gridViewFile.OptionsSelection.EnableAppearanceHideSelection = false;
    this.gridViewFile.OptionsSelection.InvertSelection = true;
    this.gridViewFile.OptionsSelection.UseIndicatorForSelection = false;
    this.gridViewFile.OptionsView.ColumnAutoWidth = false;
    this.gridViewFile.OptionsView.ShowGroupPanel = false;
    this.gridViewFile.OptionsView.ShowIndicator = false;
    this.gridViewFile.RowCellClick += new RowCellClickEventHandler(this.gridViewFile_RowCellClick);
    this.colType.AppearanceCell.Options.UseTextOptions = true;
    this.colType.AppearanceCell.TextOptions.HAlignment = HorzAlignment.Center;
    this.colType.AppearanceHeader.Options.UseTextOptions = true;
    this.colType.AppearanceHeader.TextOptions.HAlignment = HorzAlignment.Center;
    this.colType.Caption = "Type";
    this.colType.ColumnEdit = (RepositoryItem) this.repositoryItemImageComboBox1;
    this.colType.FieldName = "FileType";
    this.colType.ImageAlignment = StringAlignment.Center;
    this.colType.MaxWidth = 35;
    this.colType.MinWidth = 35;
    this.colType.Name = "colType";
    this.colType.OptionsColumn.AllowEdit = false;
    this.colType.OptionsColumn.AllowSort = DefaultBoolean.True;
    this.colType.OptionsFilter.AllowFilter = false;
    this.colType.Visible = true;
    this.colType.VisibleIndex = 0;
    this.colType.Width = 35;
    this.repositoryItemImageComboBox1.AutoHeight = false;
    this.repositoryItemImageComboBox1.GlyphAlignment = HorzAlignment.Center;
    this.repositoryItemImageComboBox1.Items.AddRange(new ImageComboBoxItem[6]
    {
      new ImageComboBoxItem("", (object) 1, 0),
      new ImageComboBoxItem("", (object) 2, 1),
      new ImageComboBoxItem("", (object) 3, 2),
      new ImageComboBoxItem("", (object) 4, 3),
      new ImageComboBoxItem("", (object) 5, 4),
      new ImageComboBoxItem("", (object) 9, 5)
    });
    this.repositoryItemImageComboBox1.Name = "repositoryItemImageComboBox1";
    this.repositoryItemImageComboBox1.SmallImages = (object) this.imageList1;
    this.imageList1.ImageStream = (ImageListStreamer) componentResourceManager.GetObject("imageList1.ImageStream");
    this.imageList1.TransparentColor = Color.Transparent;
    this.imageList1.Images.SetKeyName(0, "Adobe-PDF-Document-icon.png");
    this.imageList1.Images.SetKeyName(1, "Excel-icon.png");
    this.imageList1.Images.SetKeyName(2, "PowerPoint-icon.png");
    this.imageList1.Images.SetKeyName(3, "Word-icon.png");
    this.imageList1.Images.SetKeyName(4, "text-icon.png");
    this.imageList1.Images.SetKeyName(5, "unknown.png");
    this.imageList1.Images.SetKeyName(6, "Delete-icon.png");
    this.colName.Caption = "Name";
    this.colName.ColumnEdit = (RepositoryItem) this.repositoryItemHyperLinkEdit2;
    this.colName.FieldName = "FileDocName";
    this.colName.Name = "colName";
    this.colName.OptionsColumn.AllowEdit = false;
    this.colName.Visible = true;
    this.colName.VisibleIndex = 1;
    this.colName.Width = 150;
    this.repositoryItemHyperLinkEdit2.AutoHeight = false;
    this.repositoryItemHyperLinkEdit2.Name = "repositoryItemHyperLinkEdit2";
    this.colDescription.Caption = "Description";
    this.colDescription.FieldName = "FileDescription";
    this.colDescription.Name = "colDescription";
    this.colDescription.OptionsColumn.AllowEdit = false;
    this.colDescription.Visible = true;
    this.colDescription.VisibleIndex = 2;
    this.colDescription.Width = 132;
    this.colFileName.Caption = "File Name";
    this.colFileName.FieldName = "OriFileName";
    this.colFileName.Name = "colFileName";
    this.colFileName.OptionsColumn.AllowEdit = false;
    this.colFileName.Visible = true;
    this.colFileName.VisibleIndex = 3;
    this.colFileName.Width = 165;
    this.colFileSize.Caption = "File Size";
    this.colFileSize.FieldName = "FileSize";
    this.colFileSize.Name = "colFileSize";
    this.colFileSize.OptionsColumn.AllowEdit = false;
    this.colFileSize.Visible = true;
    this.colFileSize.VisibleIndex = 4;
    this.colFileSize.Width = 80 /*0x50*/;
    this.colDate.AppearanceCell.Options.UseTextOptions = true;
    this.colDate.AppearanceCell.TextOptions.HAlignment = HorzAlignment.Center;
    this.colDate.Caption = "Date Uploaded";
    this.colDate.DisplayFormat.FormatString = "dd-MMM-yyyy";
    this.colDate.DisplayFormat.FormatType = FormatType.DateTime;
    this.colDate.FieldName = "DateUploaded";
    this.colDate.Name = "colDate";
    this.colDate.OptionsColumn.AllowEdit = false;
    this.colDate.Visible = true;
    this.colDate.VisibleIndex = 5;
    this.colDate.Width = 80 /*0x50*/;
    this.colUploadedBy.Caption = "Uploaded By";
    this.colUploadedBy.FieldName = "ModifiedBy";
    this.colUploadedBy.Name = "colUploadedBy";
    this.colUploadedBy.OptionsColumn.AllowEdit = false;
    this.colUploadedBy.Visible = true;
    this.colUploadedBy.VisibleIndex = 6;
    this.colUploadedBy.Width = 82;
    this.colFileID.Caption = "File ID";
    this.colFileID.FieldName = "FileID";
    this.colFileID.Name = "colFileID";
    this.colSaveAs.ColumnEdit = (RepositoryItem) this.btnSaveAs;
    this.colSaveAs.MaxWidth = 30;
    this.colSaveAs.MinWidth = 30;
    this.colSaveAs.Name = "colSaveAs";
    this.colSaveAs.Visible = true;
    this.colSaveAs.VisibleIndex = 7;
    this.colSaveAs.Width = 30;
    this.btnSaveAs.AutoHeight = false;
    this.btnSaveAs.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Glyph, "", -1, true, true, false, ImageLocation.MiddleCenter, (Image) null, new KeyShortcut(Keys.None), (AppearanceObject) appearance1, "Save As", (object) null, (SuperToolTip) null, true)
    });
    this.btnSaveAs.Name = "btnSaveAs";
    this.btnSaveAs.TextEditStyle = TextEditStyles.HideTextEditor;
    this.btnSaveAs.ButtonClick += new ButtonPressedEventHandler(this.btnSaveAs_ButtonClick);
    this.colEdit.ColumnEdit = (RepositoryItem) this.btnEdit;
    this.colEdit.MaxWidth = 30;
    this.colEdit.MinWidth = 30;
    this.colEdit.Name = "colEdit";
    this.colEdit.Visible = true;
    this.colEdit.VisibleIndex = 8;
    this.colEdit.Width = 30;
    this.btnEdit.AutoHeight = false;
    this.btnEdit.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Glyph, "", -1, true, true, false, ImageLocation.MiddleCenter, (Image) null, new KeyShortcut(Keys.None), (AppearanceObject) appearance2, "Edit", (object) null, (SuperToolTip) null, true)
    });
    this.btnEdit.Name = "btnEdit";
    this.btnEdit.TextEditStyle = TextEditStyles.HideTextEditor;
    this.btnEdit.ButtonClick += new ButtonPressedEventHandler(this.btnEdit_ButtonClick);
    this.colRemove.ColumnEdit = (RepositoryItem) this.btnDeleteFile;
    this.colRemove.MaxWidth = 30;
    this.colRemove.MinWidth = 30;
    this.colRemove.Name = "colRemove";
    this.colRemove.OptionsColumn.AllowSort = DefaultBoolean.False;
    this.colRemove.OptionsColumn.ShowCaption = false;
    this.colRemove.OptionsFilter.AllowFilter = false;
    this.colRemove.Visible = true;
    this.colRemove.VisibleIndex = 9;
    this.colRemove.Width = 30;
    this.btnDeleteFile.AutoHeight = false;
    this.btnDeleteFile.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Glyph, "", -1, true, true, false, ImageLocation.MiddleCenter, (Image) null, new KeyShortcut(Keys.None), (AppearanceObject) appearance3, "Delete", (object) null, (SuperToolTip) null, true)
    });
    this.btnDeleteFile.Name = "btnDeleteFile";
    this.btnDeleteFile.TextEditStyle = TextEditStyles.HideTextEditor;
    this.btnDeleteFile.ButtonClick += new ButtonPressedEventHandler(this.btnDeleteFile_ButtonClick);
    this.repositoryItemImageComboBox2.AutoHeight = false;
    this.repositoryItemImageComboBox2.GlyphAlignment = HorzAlignment.Center;
    this.repositoryItemImageComboBox2.Items.AddRange(new ImageComboBoxItem[1]
    {
      new ImageComboBoxItem("", (object) 9, 6)
    });
    this.repositoryItemImageComboBox2.Name = "repositoryItemImageComboBox2";
    this.repositoryItemImageComboBox2.SmallImages = (object) this.imageList1;
    this.btnRefreshImage.Location = new Point(681, 1);
    this.btnRefreshImage.Margin = new Padding(1);
    this.btnRefreshImage.Name = "btnRefreshImage";
    this.btnRefreshImage.Size = new Size(75, 23);
    this.btnRefreshImage.TabIndex = 1;
    this.btnRefreshImage.Text = "Refresh";
    this.btnRefreshImage.Click += new EventHandler(this.btnRefreshImage_Click);
    this.groupControl2.Controls.Add((Control) this.gridFile);
    this.groupControl2.Controls.Add((Control) this.tableLayoutPanel1);
    this.groupControl2.Dock = DockStyle.Fill;
    this.groupControl2.Location = new Point(0, 0);
    this.groupControl2.Name = "groupControl2";
    this.groupControl2.Size = new Size(883, 445);
    this.groupControl2.TabIndex = 0;
    this.groupControl2.Text = "Other Files";
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.btnBrowseFile, 2, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.btnRefreshFiles, 1, 0);
    this.tableLayoutPanel1.Dock = DockStyle.Bottom;
    this.tableLayoutPanel1.Location = new Point(2, 418);
    this.tableLayoutPanel1.Margin = new Padding(1);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.RowCount = 1;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(879, 25);
    this.tableLayoutPanel1.TabIndex = 6;
    this.btnBrowseFile.Location = new Point(758, 1);
    this.btnBrowseFile.Margin = new Padding(1);
    this.btnBrowseFile.Name = "btnBrowseFile";
    this.btnBrowseFile.Size = new Size(120, 23);
    this.btnBrowseFile.TabIndex = 0;
    this.btnBrowseFile.Text = "Upload &Files";
    this.btnBrowseFile.Click += new EventHandler(this.btnBrowseFile_Click);
    this.btnRefreshFiles.Location = new Point(681, 1);
    this.btnRefreshFiles.Margin = new Padding(1);
    this.btnRefreshFiles.Name = "btnRefreshFiles";
    this.btnRefreshFiles.Size = new Size(75, 23);
    this.btnRefreshFiles.TabIndex = 2;
    this.btnRefreshFiles.Text = "Refresh";
    this.btnRefreshFiles.Click += new EventHandler(this.btnRefreshFiles_Click);
    this.gridControl1.Cursor = Cursors.Default;
    this.gridControl1.Dock = DockStyle.Fill;
    this.gridControl1.Location = new Point(2, 21);
    this.gridControl1.MainView = (BaseView) this.layoutView1;
    this.gridControl1.Name = "gridControl1";
    this.gridControl1.RepositoryItems.AddRange(new RepositoryItem[4]
    {
      (RepositoryItem) this.repositoryItemTextEdit1,
      (RepositoryItem) this.repositoryItemPictureEdit1,
      (RepositoryItem) this.repositoryItemHyperLinkEdit1,
      (RepositoryItem) this.repositoryItemMemoEdit2
    });
    this.gridControl1.Size = new Size(879, 252);
    this.gridControl1.TabIndex = 6;
    this.gridControl1.ViewCollection.AddRange(new BaseView[1]
    {
      (BaseView) this.layoutView1
    });
    this.layoutView1.Appearance.CardCaption.Font = new Font("Tahoma", 12f, FontStyle.Bold);
    this.layoutView1.Appearance.CardCaption.Options.UseFont = true;
    this.layoutView1.Appearance.CardCaption.Options.UseTextOptions = true;
    this.layoutView1.Appearance.CardCaption.TextOptions.HAlignment = HorzAlignment.Center;
    this.layoutView1.Appearance.CardCaption.TextOptions.Trimming = Trimming.EllipsisCharacter;
    this.layoutView1.Appearance.FocusedCardCaption.Font = new Font("Tahoma", 12f, FontStyle.Bold);
    this.layoutView1.Appearance.FocusedCardCaption.Options.UseFont = true;
    this.layoutView1.Appearance.FocusedCardCaption.Options.UseTextOptions = true;
    this.layoutView1.Appearance.FocusedCardCaption.TextOptions.HAlignment = HorzAlignment.Center;
    this.layoutView1.Appearance.FocusedCardCaption.TextOptions.Trimming = Trimming.EllipsisCharacter;
    this.layoutView1.Appearance.HideSelectionCardCaption.Font = new Font("Tahoma", 12f, FontStyle.Bold);
    this.layoutView1.Appearance.HideSelectionCardCaption.Options.UseFont = true;
    this.layoutView1.Appearance.HideSelectionCardCaption.Options.UseTextOptions = true;
    this.layoutView1.Appearance.HideSelectionCardCaption.TextOptions.HAlignment = HorzAlignment.Center;
    this.layoutView1.Appearance.HideSelectionCardCaption.TextOptions.Trimming = Trimming.EllipsisCharacter;
    this.layoutView1.Appearance.SelectedCardCaption.Font = new Font("Tahoma", 12f, FontStyle.Bold);
    this.layoutView1.Appearance.SelectedCardCaption.Options.UseFont = true;
    this.layoutView1.Appearance.SelectedCardCaption.Options.UseTextOptions = true;
    this.layoutView1.Appearance.SelectedCardCaption.TextOptions.HAlignment = HorzAlignment.Center;
    this.layoutView1.Appearance.SelectedCardCaption.TextOptions.Trimming = Trimming.EllipsisCharacter;
    this.layoutView1.CardCaptionFormat = "{2}";
    this.layoutView1.CardMinSize = new Size(152, 189);
    this.layoutView1.Columns.AddRange(new LayoutViewColumn[6]
    {
      this.colCaption,
      this.colDesc,
      this.colPhoto,
      this.colViewLarger,
      this.colImageID,
      this.layoutViewColumn1
    });
    this.layoutView1.GridControl = this.gridControl1;
    this.layoutView1.HiddenItems.AddRange(new BaseLayoutItem[4]
    {
      (BaseLayoutItem) this.Item2,
      (BaseLayoutItem) this.layoutViewField_colDesc,
      (BaseLayoutItem) this.Item1,
      (BaseLayoutItem) this.layoutViewField_layoutViewColumn1
    });
    this.layoutView1.Name = "layoutView1";
    this.layoutView1.OptionsBehavior.Editable = false;
    this.layoutView1.OptionsCarouselMode.StretchCardToViewHeight = true;
    this.layoutView1.OptionsView.AnimationType = GridAnimationType.AnimateAllContent;
    this.layoutView1.OptionsView.ShowCardExpandButton = false;
    this.layoutView1.OptionsView.ShowFilterPanelMode = ShowFilterPanelMode.Never;
    this.layoutView1.OptionsView.ShowHeaderPanel = false;
    this.layoutView1.OptionsView.ViewMode = LayoutViewMode.MultiRow;
    this.layoutView1.TemplateCard = this.layoutViewCard1;
    this.layoutView1.Click += new EventHandler(this.layoutView1_Click);
    this.colCaption.Caption = "Caption";
    this.colCaption.ColumnEdit = (RepositoryItem) this.repositoryItemTextEdit1;
    this.colCaption.CustomizationCaption = "Caption";
    this.colCaption.FieldName = "ImageName";
    this.colCaption.LayoutViewField = this.Item1;
    this.colCaption.Name = "colCaption";
    this.colCaption.OptionsColumn.AllowSort = DefaultBoolean.False;
    this.colCaption.OptionsFilter.AllowFilter = false;
    this.repositoryItemTextEdit1.AutoHeight = false;
    this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
    this.Item1.EditorPreferredWidth = 153;
    this.Item1.Location = new Point(0, 0);
    this.Item1.Name = "Item1";
    this.Item1.Size = new Size(153, 50);
    this.Item1.TextSize = new Size(41, 13);
    this.Item1.TextToControlDistance = 5;
    this.colDesc.AppearanceCell.Options.UseTextOptions = true;
    this.colDesc.AppearanceCell.TextOptions.HAlignment = HorzAlignment.Near;
    this.colDesc.AppearanceCell.TextOptions.VAlignment = VertAlignment.Top;
    this.colDesc.Caption = "Description";
    this.colDesc.ColumnEdit = (RepositoryItem) this.repositoryItemMemoEdit2;
    this.colDesc.CustomizationCaption = "Description";
    this.colDesc.FieldName = "ImageDescription";
    this.colDesc.LayoutViewField = this.layoutViewField_colDesc;
    this.colDesc.Name = "colDesc";
    this.colDesc.OptionsColumn.AllowEdit = false;
    this.colDesc.OptionsColumn.AllowSort = DefaultBoolean.False;
    this.colDesc.OptionsColumn.ReadOnly = true;
    this.colDesc.OptionsColumn.ShowCaption = false;
    this.colDesc.OptionsFilter.AllowFilter = false;
    this.repositoryItemMemoEdit2.Name = "repositoryItemMemoEdit2";
    this.layoutViewField_colDesc.EditorPreferredWidth = 199;
    this.layoutViewField_colDesc.Location = new Point(0, 0);
    this.layoutViewField_colDesc.Name = "layoutViewField_colDesc";
    this.layoutViewField_colDesc.Size = new Size(153, 50);
    this.layoutViewField_colDesc.TextSize = new Size(0, 0);
    this.layoutViewField_colDesc.TextToControlDistance = 0;
    this.layoutViewField_colDesc.TextVisible = false;
    this.colPhoto.ColumnEdit = (RepositoryItem) this.repositoryItemPictureEdit1;
    this.colPhoto.CustomizationCaption = "Photo";
    this.colPhoto.FieldName = "ImageBinarySmall";
    this.colPhoto.LayoutViewField = this.layoutViewField_colPhoto;
    this.colPhoto.Name = "colPhoto";
    this.colPhoto.OptionsColumn.AllowSort = DefaultBoolean.False;
    this.colPhoto.OptionsColumn.ShowCaption = false;
    this.colPhoto.OptionsFilter.AllowFilter = false;
    this.repositoryItemPictureEdit1.Name = "repositoryItemPictureEdit1";
    this.repositoryItemPictureEdit1.ZoomPercent = 50;
    this.layoutViewField_colPhoto.EditorPreferredWidth = 128 /*0x80*/;
    this.layoutViewField_colPhoto.Location = new Point(0, 0);
    this.layoutViewField_colPhoto.Name = "layoutViewField_colPhoto";
    this.layoutViewField_colPhoto.Size = new Size(132, 26);
    this.layoutViewField_colPhoto.TextSize = new Size(0, 0);
    this.layoutViewField_colPhoto.TextToControlDistance = 0;
    this.layoutViewField_colPhoto.TextVisible = false;
    this.colViewLarger.AppearanceCell.Options.UseTextOptions = true;
    this.colViewLarger.AppearanceCell.TextOptions.HAlignment = HorzAlignment.Far;
    this.colViewLarger.ColumnEdit = (RepositoryItem) this.repositoryItemHyperLinkEdit1;
    this.colViewLarger.LayoutViewField = this.layoutViewField_colViewLarger;
    this.colViewLarger.Name = "colViewLarger";
    this.colViewLarger.OptionsColumn.AllowEdit = false;
    this.colViewLarger.OptionsColumn.AllowSort = DefaultBoolean.False;
    this.colViewLarger.OptionsFilter.AllowFilter = false;
    this.repositoryItemHyperLinkEdit1.AutoHeight = false;
    this.repositoryItemHyperLinkEdit1.Name = "repositoryItemHyperLinkEdit1";
    this.repositoryItemHyperLinkEdit1.NullText = "View Larger Image";
    this.layoutViewField_colViewLarger.EditorPreferredWidth = 123;
    this.layoutViewField_colViewLarger.Location = new Point(0, 26);
    this.layoutViewField_colViewLarger.Name = "layoutViewField_colViewLarger";
    this.layoutViewField_colViewLarger.Size = new Size(132, 24);
    this.layoutViewField_colViewLarger.TextSize = new Size(0, 13);
    this.layoutViewField_colViewLarger.TextToControlDistance = 5;
    this.colImageID.Caption = "Image ID";
    this.colImageID.FieldName = "ImageID";
    this.colImageID.LayoutViewField = this.Item2;
    this.colImageID.Name = "colImageID";
    this.Item2.EditorPreferredWidth = 199;
    this.Item2.Location = new Point(0, 0);
    this.Item2.Name = "Item2";
    this.Item2.Size = new Size(153, 50);
    this.Item2.TextSize = new Size(0, 0);
    this.Item2.TextToControlDistance = 0;
    this.Item2.TextVisible = false;
    this.layoutViewColumn1.Caption = "ImageCategoryID";
    this.layoutViewColumn1.FieldName = "ImageCategoryID";
    this.layoutViewColumn1.LayoutViewField = this.layoutViewField_layoutViewColumn1;
    this.layoutViewColumn1.Name = "layoutViewColumn1";
    this.layoutViewField_layoutViewColumn1.EditorPreferredWidth = 10;
    this.layoutViewField_layoutViewColumn1.Location = new Point(0, 50);
    this.layoutViewField_layoutViewColumn1.Name = "layoutViewField_layoutViewColumn1";
    this.layoutViewField_layoutViewColumn1.Size = new Size(229, 20);
    this.layoutViewField_layoutViewColumn1.TextSize = new Size(94, 20);
    this.layoutViewField_layoutViewColumn1.TextToControlDistance = 5;
    this.layoutViewCard1.CustomizationFormText = "TemplateCard";
    this.layoutViewCard1.ExpandButtonLocation = GroupElementLocation.AfterText;
    this.layoutViewCard1.Items.AddRange(new BaseLayoutItem[2]
    {
      (BaseLayoutItem) this.layoutViewField_colPhoto,
      (BaseLayoutItem) this.layoutViewField_colViewLarger
    });
    this.layoutViewCard1.Name = "layoutViewTemplateCard";
    this.layoutViewCard1.OptionsItemText.TextToControlDistance = 5;
    this.layoutViewCard1.Text = "TemplateCard";
    this.btnBrowseImage.Location = new Point(758, 1);
    this.btnBrowseImage.Margin = new Padding(1);
    this.btnBrowseImage.Name = "btnBrowseImage";
    this.btnBrowseImage.Size = new Size(120, 23);
    this.btnBrowseImage.TabIndex = 0;
    this.btnBrowseImage.Text = "&Upload Image";
    this.btnBrowseImage.Click += new EventHandler(this.btnBrowseImage_Click);
    this.tableLayoutPanel2.AutoSize = true;
    this.tableLayoutPanel2.ColumnCount = 3;
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.Controls.Add((Control) this.btnBrowseImage, 2, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.btnRefreshImage, 1, 0);
    this.tableLayoutPanel2.Dock = DockStyle.Bottom;
    this.tableLayoutPanel2.Location = new Point(2, 273);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.RowCount = 1;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.Size = new Size(879, 25);
    this.tableLayoutPanel2.TabIndex = 5;
    this.groupControl3.Controls.Add((Control) this.gridControl1);
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel2);
    this.groupControl3.Dock = DockStyle.Fill;
    this.groupControl3.Location = new Point(0, 0);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(883, 300);
    this.groupControl3.TabIndex = 2;
    this.groupControl3.Text = "Images";
    this.groupControl1.AppearanceCaption.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.groupControl1.AppearanceCaption.Options.UseFont = true;
    this.groupControl1.Controls.Add((Control) this.splitContainerControl1);
    this.groupControl1.Dock = DockStyle.Fill;
    this.groupControl1.Location = new Point(0, 0);
    this.groupControl1.Name = "groupControl1";
    this.groupControl1.Size = new Size(887, 773);
    this.groupControl1.TabIndex = 3;
    this.groupControl1.Text = "Attachments";
    this.splitContainerControl1.Dock = DockStyle.Fill;
    this.splitContainerControl1.Horizontal = false;
    this.splitContainerControl1.Location = new Point(2, 21);
    this.splitContainerControl1.Name = "splitContainerControl1";
    this.splitContainerControl1.Panel1.Controls.Add((Control) this.groupControl3);
    this.splitContainerControl1.Panel1.MinSize = 300;
    this.splitContainerControl1.Panel1.Text = "Panel1";
    this.splitContainerControl1.Panel2.Controls.Add((Control) this.groupControl2);
    this.splitContainerControl1.Panel2.Text = "Panel2";
    this.splitContainerControl1.Size = new Size(883, 750);
    this.splitContainerControl1.SplitterPosition = 300;
    this.splitContainerControl1.TabIndex = 0;
    this.splitContainerControl1.Text = "splitContainerControl1";
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.Controls.Add((Control) this.groupControl1);
    this.Name = nameof (AttachmentView);
    this.Size = new Size(887, 773);
    this.gridFile.EndInit();
    this.gridViewFile.EndInit();
    this.repositoryItemImageComboBox1.EndInit();
    this.repositoryItemHyperLinkEdit2.EndInit();
    this.btnSaveAs.EndInit();
    this.btnEdit.EndInit();
    this.btnDeleteFile.EndInit();
    this.repositoryItemImageComboBox2.EndInit();
    this.groupControl2.EndInit();
    this.groupControl2.ResumeLayout(false);
    this.groupControl2.PerformLayout();
    this.tableLayoutPanel1.ResumeLayout(false);
    this.gridControl1.EndInit();
    this.layoutView1.EndInit();
    this.repositoryItemTextEdit1.EndInit();
    this.Item1.EndInit();
    this.repositoryItemMemoEdit2.EndInit();
    this.layoutViewField_colDesc.EndInit();
    this.repositoryItemPictureEdit1.EndInit();
    this.layoutViewField_colPhoto.EndInit();
    this.repositoryItemHyperLinkEdit1.EndInit();
    this.layoutViewField_colViewLarger.EndInit();
    this.Item2.EndInit();
    this.layoutViewField_layoutViewColumn1.EndInit();
    this.layoutViewCard1.EndInit();
    this.tableLayoutPanel2.ResumeLayout(false);
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.groupControl3.PerformLayout();
    this.groupControl1.EndInit();
    this.groupControl1.ResumeLayout(false);
    this.splitContainerControl1.EndInit();
    this.splitContainerControl1.ResumeLayout(false);
    this.ResumeLayout(false);
  }
}
