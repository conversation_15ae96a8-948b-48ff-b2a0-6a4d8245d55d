// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Attachments.frmFileDetail
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.DataTransferObjects;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Attachment;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Attachments;

public class frmFileDetail : BaseDialog, IFileDetailView, IView, IBaseView
{
  private IContainer components;
  private SimpleButton btnClose;
  private SimpleButton btnUpdate;
  private TextEdit txtFileSize;
  private LabelControl labelControl4;
  private MemoEdit memoDescription;
  private TextEdit txtName;
  private TextEdit txtFile;
  private LabelControl labelControl3;
  private LabelControl labelControl2;
  private LabelControl labelControl1;
  private OpenFileDialog openFileDialog1;
  private TextEdit txtFileExt;
  private LabelControl labelControl6;
  private FileDetailPresenter _presenter;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.txtFileSize = new TextEdit();
    this.labelControl4 = new LabelControl();
    this.memoDescription = new MemoEdit();
    this.txtName = new TextEdit();
    this.txtFile = new TextEdit();
    this.labelControl3 = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.labelControl1 = new LabelControl();
    this.btnClose = new SimpleButton();
    this.btnUpdate = new SimpleButton();
    this.openFileDialog1 = new OpenFileDialog();
    this.txtFileExt = new TextEdit();
    this.labelControl6 = new LabelControl();
    this.pnlBottom.SuspendLayout();
    this.txtFileSize.Properties.BeginInit();
    this.memoDescription.Properties.BeginInit();
    this.txtName.Properties.BeginInit();
    this.txtFile.Properties.BeginInit();
    this.txtFileExt.Properties.BeginInit();
    this.SuspendLayout();
    this.pnlBottom.Controls.Add((Control) this.btnClose);
    this.pnlBottom.Controls.Add((Control) this.btnUpdate);
    this.pnlBottom.Location = new Point(0, 501);
    this.pnlBottom.Size = new Size(534, 41);
    this.pnlBottom.TabIndex = 13;
    this.txtFileSize.Location = new Point(113, 330);
    this.txtFileSize.Name = "txtFileSize";
    this.txtFileSize.Properties.Appearance.BackColor = Color.FromArgb(233, 237, 241);
    this.txtFileSize.Properties.Appearance.Options.UseBackColor = true;
    this.txtFileSize.Properties.ReadOnly = true;
    this.txtFileSize.Size = new Size(122, 20);
    this.txtFileSize.TabIndex = 8;
    this.txtFileSize.TabStop = false;
    this.labelControl4.Location = new Point(16 /*0x10*/, 333);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(42, 13);
    this.labelControl4.TabIndex = 7;
    this.labelControl4.Text = "File Size:";
    this.memoDescription.EditValue = (object) "";
    this.memoDescription.Location = new Point(113, 139);
    this.memoDescription.Name = "memoDescription";
    this.memoDescription.Properties.MaxLength = 500;
    this.memoDescription.Size = new Size(408, 185);
    this.memoDescription.TabIndex = 6;
    this.txtName.EditValue = (object) "";
    this.txtName.Location = new Point(113, 113);
    this.txtName.Name = "txtName";
    this.txtName.Size = new Size(408, 20);
    this.txtName.TabIndex = 4;
    this.txtFile.EditValue = (object) "";
    this.txtFile.Location = new Point(113, 87);
    this.txtFile.Name = "txtFile";
    this.txtFile.Properties.ReadOnly = true;
    this.txtFile.Size = new Size(408, 20);
    this.txtFile.TabIndex = 0;
    this.txtFile.TabStop = false;
    this.labelControl3.Location = new Point(18, 142);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(57, 13);
    this.labelControl3.TabIndex = 5;
    this.labelControl3.Text = "Description:";
    this.labelControl2.Location = new Point(18, 116);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(31 /*0x1F*/, 13);
    this.labelControl2.TabIndex = 3;
    this.labelControl2.Text = "Name:";
    this.labelControl1.Location = new Point(18, 90);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(89, 13);
    this.labelControl1.TabIndex = 2;
    this.labelControl1.Text = "Original File Name:";
    this.btnClose.DialogResult = DialogResult.Cancel;
    this.btnClose.Location = new Point(447, 6);
    this.btnClose.Name = "btnClose";
    this.btnClose.Size = new Size(75, 23);
    this.btnClose.TabIndex = 0;
    this.btnClose.Text = "&Close";
    this.btnClose.Click += new EventHandler(this.btnClose_Click);
    this.btnUpdate.Location = new Point(366, 6);
    this.btnUpdate.Name = "btnUpdate";
    this.btnUpdate.Size = new Size(75, 23);
    this.btnUpdate.TabIndex = 1;
    this.btnUpdate.Text = "&Update";
    this.btnUpdate.Click += new EventHandler(this.btnUpdate_Click);
    this.openFileDialog1.FileName = "openFileDialog1";
    this.txtFileExt.Location = new Point(399, 330);
    this.txtFileExt.Name = "txtFileExt";
    this.txtFileExt.Properties.Appearance.BackColor = Color.FromArgb(233, 237, 241);
    this.txtFileExt.Properties.Appearance.Options.UseBackColor = true;
    this.txtFileExt.Properties.ReadOnly = true;
    this.txtFileExt.Size = new Size(122, 20);
    this.txtFileExt.TabIndex = 10;
    this.txtFileExt.TabStop = false;
    this.labelControl6.Location = new Point(302, 333);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(43, 13);
    this.labelControl6.TabIndex = 9;
    this.labelControl6.Text = "File Ext.:";
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.ClientSize = new Size(534, 542);
    this.Controls.Add((Control) this.txtFileExt);
    this.Controls.Add((Control) this.labelControl6);
    this.Controls.Add((Control) this.txtFileSize);
    this.Controls.Add((Control) this.labelControl4);
    this.Controls.Add((Control) this.memoDescription);
    this.Controls.Add((Control) this.txtName);
    this.Controls.Add((Control) this.txtFile);
    this.Controls.Add((Control) this.labelControl3);
    this.Controls.Add((Control) this.labelControl2);
    this.Controls.Add((Control) this.labelControl1);
    this.HeaderText = "Detail of the file";
    this.HeaderTitle = "File Detail";
    this.Name = nameof (frmFileDetail);
    this.Text = "File Detail";
    this.Controls.SetChildIndex((Control) this.pnlBottom, 0);
    this.Controls.SetChildIndex((Control) this.labelControl1, 0);
    this.Controls.SetChildIndex((Control) this.labelControl2, 0);
    this.Controls.SetChildIndex((Control) this.labelControl3, 0);
    this.Controls.SetChildIndex((Control) this.txtFile, 0);
    this.Controls.SetChildIndex((Control) this.txtName, 0);
    this.Controls.SetChildIndex((Control) this.memoDescription, 0);
    this.Controls.SetChildIndex((Control) this.labelControl4, 0);
    this.Controls.SetChildIndex((Control) this.txtFileSize, 0);
    this.Controls.SetChildIndex((Control) this.labelControl6, 0);
    this.Controls.SetChildIndex((Control) this.txtFileExt, 0);
    this.pnlBottom.ResumeLayout(false);
    this.txtFileSize.Properties.EndInit();
    this.memoDescription.Properties.EndInit();
    this.txtName.Properties.EndInit();
    this.txtFile.Properties.EndInit();
    this.txtFileExt.Properties.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public frmFileDetail(FileDTO file)
  {
    this.InitializeComponent();
    this._presenter = new FileDetailPresenter(file, (IFileDetailView) this);
    this._presenter.InitFileDetail();
  }

  private void btnUpdate_Click(object sender, EventArgs e)
  {
    this._presenter.Update();
    if (!this.Succeeded)
      return;
    this.DialogResult = DialogResult.OK;
  }

  private void btnClose_Click(object sender, EventArgs e)
  {
    this.DialogResult = DialogResult.Cancel;
  }

  public string FileOriName
  {
    set => this.txtFile.Text = value;
  }

  public string FileName
  {
    get => this.txtName.Text.Trim();
    set => this.txtName.Text = value;
  }

  public string FileDescription
  {
    get => this.memoDescription.Text.Trim();
    set => this.memoDescription.Text = value;
  }

  public string FileExt
  {
    set => this.txtFileExt.Text = value;
  }

  public string FileSize
  {
    set => this.txtFileSize.Text = value;
  }

  public string ErrorMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "File", MessageBoxButtons.OK, MessageBoxIcon.Hand);
    }
  }

  public string WarningMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "File", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
    }
  }

  public string InformationMessage
  {
    set
    {
      int num = (int) XtraMessageBox.Show((IWin32Window) this, value, "File", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
    }
  }

  public string QuestionMessage
  {
    set
    {
      switch (XtraMessageBox.Show((IWin32Window) this, value, "File", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2))
      {
        case DialogResult.Yes:
          this.QuestionDecision = "YES";
          break;
        case DialogResult.No:
          this.QuestionDecision = "NO";
          break;
        default:
          this.QuestionDecision = "CANCEL";
          break;
      }
    }
  }

  public string QuestionDecision { get; set; }

  public string Caption
  {
    get => this.Text;
    set => this.Text = value;
  }

  public bool IsNew { get; set; }

  public bool IsDirty { get; set; }

  public bool Succeeded { get; set; }

  public string UserID => string.Empty;
}
