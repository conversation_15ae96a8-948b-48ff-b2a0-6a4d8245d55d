// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.frmPittingGrade
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.FormView.Properties;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment;

public class frmPittingGrade : XtraForm
{
  private IContainer components;
  private PictureEdit picturePittingGrade;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (frmPittingGrade));
    this.picturePittingGrade = new PictureEdit();
    this.picturePittingGrade.Properties.BeginInit();
    this.SuspendLayout();
    this.picturePittingGrade.Dock = DockStyle.Fill;
    this.picturePittingGrade.Location = new Point(0, 0);
    this.picturePittingGrade.Name = "picturePittingGrade";
    this.picturePittingGrade.Properties.AllowFocused = false;
    this.picturePittingGrade.Properties.AllowScrollViaMouseDrag = false;
    this.picturePittingGrade.Properties.Appearance.BackColor = Color.Transparent;
    this.picturePittingGrade.Properties.Appearance.Options.UseBackColor = true;
    this.picturePittingGrade.Properties.BorderStyle = BorderStyles.NoBorder;
    this.picturePittingGrade.Properties.ReadOnly = true;
    this.picturePittingGrade.Properties.ShowMenu = false;
    this.picturePittingGrade.Size = new Size(694, 676);
    this.picturePittingGrade.TabIndex = 40;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.ClientSize = new Size(694, 676);
    this.Controls.Add((Control) this.picturePittingGrade);
    this.FormBorderStyle = FormBorderStyle.FixedToolWindow;
    this.Icon = (Icon) componentResourceManager.GetObject("$this.Icon");
    this.MaximumSize = new Size(700, 700);
    this.MinimumSize = new Size(700, 700);
    this.Name = nameof (frmPittingGrade);
    this.Text = "Pitting Grade";
    this.picturePittingGrade.Properties.EndInit();
    this.ResumeLayout(false);
  }

  public frmPittingGrade() => this.InitializeComponent();

  public void ShowPittingGradePicture(int pittingGrade)
  {
    switch (pittingGrade)
    {
      case 1:
        this.picturePittingGrade.EditValue = (object) Resources.IW_PittingGrade1;
        break;
      case 2:
        this.picturePittingGrade.EditValue = (object) Resources.IW_PittingGrade2;
        break;
      case 3:
        this.picturePittingGrade.EditValue = (object) Resources.IW_PittingGrade3;
        break;
      case 4:
        this.picturePittingGrade.EditValue = (object) Resources.IW_PittingGrade4;
        break;
      case 5:
        this.picturePittingGrade.EditValue = (object) Resources.IW_PittingGrade5;
        break;
      case 6:
        this.picturePittingGrade.EditValue = (object) Resources.IW_PittingGrade6;
        break;
      case 7:
        this.picturePittingGrade.EditValue = (object) Resources.IW_PittingGrade7;
        break;
      case 8:
        this.picturePittingGrade.EditValue = (object) Resources.IW_PittingGrade8;
        break;
      default:
        this.Close();
        break;
    }
  }

  public Image ObtainPittingGradePicture(int pittingGrade)
  {
    switch (pittingGrade)
    {
      case 1:
        return (Image) Resources.IW_PittingGrade1;
      case 2:
        return (Image) Resources.IW_PittingGrade2;
      case 3:
        return (Image) Resources.IW_PittingGrade3;
      case 4:
        return (Image) Resources.IW_PittingGrade4;
      case 5:
        return (Image) Resources.IW_PittingGrade5;
      case 6:
        return (Image) Resources.IW_PittingGrade6;
      case 7:
        return (Image) Resources.IW_PittingGrade7;
      case 8:
        return (Image) Resources.IW_PittingGrade8;
      default:
        return (Image) null;
    }
  }
}
