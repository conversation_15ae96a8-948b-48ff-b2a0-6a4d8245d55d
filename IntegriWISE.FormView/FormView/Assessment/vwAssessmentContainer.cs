// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.vwAssessmentContainer
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment;

public class vwAssessmentContainer : RecordTabControl, IAssessmentContainerView, IView
{
  private IAssessmentBaseView _assessmentView;
  private IRecordView _recordView;
  private AssessmentContainerPresenter _presenter;
  private IContainer components;
  private PanelControl AssessmentContainer;

  public vwAssessmentContainer(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._presenter = new AssessmentContainerPresenter(recordView, (IAssessmentContainerView) this);
  }

  public void InitAssessmentView() => this._presenter.InitAssessmentView();

  public void InitAssessmentView(string namespaceString, string assessmentCode)
  {
    System.Type type = System.Type.GetType($"IntegriWISE.FormView.Assessment.{namespaceString}.vw{assessmentCode}");
    if (type != (System.Type) null)
    {
      object instance = Activator.CreateInstance(type, (object) this._recordView);
      this._assessmentView = (IAssessmentBaseView) instance;
      XtraUserControl viewObj = instance as XtraUserControl;
      this.AssessmentContainer.Controls.Add((Control) viewObj);
      viewObj.BringToFront();
      this.SetTableLayoutAutoSize(viewObj);
      viewObj.AutoSize = true;
      viewObj.Dock = DockStyle.Fill;
    }
    else
    {
      int num = (int) XtraMessageBox.Show("This assessment is not available in Demo Version of IntegriWISE.", "IntegriWISE", MessageBoxButtons.OK, MessageBoxIcon.Asterisk);
      this._recordView.EnableAssessment = false;
      this._recordView.EnableReport = false;
      this._recordView.EnableResult = false;
    }
  }

  public void SetTableLayoutAutoSize(XtraUserControl viewObj)
  {
    for (int index = 0; index < viewObj.Controls.Count; ++index)
    {
      if (viewObj.Controls[index].GetType() == typeof (TableLayoutPanel))
      {
        TableLayoutPanel control = viewObj.Controls[index] as TableLayoutPanel;
        control.AutoSize = true;
        control.Dock = DockStyle.Fill;
      }
    }
  }

  public bool ValidateAssessment() => this._assessmentView.ValidateAssessment();

  public void Save() => this._assessmentView.Save();

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.AssessmentContainer = new PanelControl();
    this.AssessmentContainer.BeginInit();
    this.SuspendLayout();
    this.AssessmentContainer.AutoSize = true;
    this.AssessmentContainer.BorderStyle = BorderStyles.NoBorder;
    this.AssessmentContainer.Dock = DockStyle.Top;
    this.AssessmentContainer.Location = new Point(0, 0);
    this.AssessmentContainer.Name = "AssessmentContainer";
    this.AssessmentContainer.Size = new Size(450, 0);
    this.AssessmentContainer.TabIndex = 3;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.AutoSize = true;
    this.Controls.Add((Control) this.AssessmentContainer);
    this.Name = nameof (vwAssessmentContainer);
    this.Size = new Size(450, 355);
    this.AssessmentContainer.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
