// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_8.CylindricalSection.Pitting.vwPitting
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.Common;
using IntegriWISE.DataTransferObjects.Lookup;
using IntegriWISE.FormView.Properties;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Assessment.ASMEB31_8.CylindricalSection.Pitting;
using IntegriWISE.UserInterface.Record;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_8.CylindricalSection.Pitting;

public class vwPitting : XtraUserControl, IPittingView, IAssessmentBaseView, IView
{
  private PittingPresenter _presenter;
  private IRecordView _recordView;
  private FormDirtyTracker _dirtyTracker;
  private IContainer components;
  private TableLayoutPanel tableLayoutPanel1;
  private LabelControl labelControl1;
  private LabelControl umLOSSe;
  private LabelControl umLOSSi;
  private LabelControl labelControl2;
  private LabelControl labelControl3;
  private LabelControl umFCAe;
  private LabelControl umFCAi;
  private LabelControl labelControl4;
  private TextEdit txtFCAi;
  private TextEdit txtFCAe;
  private TextEdit txtLOSSi;
  private TextEdit txtLOSSe;
  private DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider dxErrorProvider1;
  private LabelControl labelControl5;
  private LabelControl labelControl6;
  private CheckEdit chkMAWP;
  private TextEdit txtWmax;
  private LabelControl umWmax;
  private LookUpEdit cboPittingGrade;
  private TableLayoutPanel tableLayoutPanel2;
  private PictureEdit pictureGeometry1;
  private PictureEdit pictureGeometry3;
  private PictureEdit pictureGeometry2;
  private SimpleButton btnPittingGrade;

  public vwPitting(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.AssessmentView = (IAssessmentBaseView) this;
    this._presenter = new PittingPresenter(this._recordView, (IPittingView) this);
  }

  private void vwPitting_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView);
    this._dirtyTracker.IsHandled = true;
    this.txtFCAi.AllowOnlyN4();
    this.txtFCAe.AllowOnlyN4();
    this.txtLOSSi.AllowOnlyN4();
    this.txtLOSSe.AllowOnlyN4();
    this.txtWmax.AllowOnlyN4();
    this._presenter.LoadAssessment();
    this._dirtyTracker.IsHandled = false;
  }

  private void btnPittingGrade_Click(object sender, EventArgs e)
  {
    this._presenter.DisplayPittingGradeImage();
  }

  public int? AssessmentID { get; set; }

  public double? FCAi
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAi.Text);
    set => this.txtFCAi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? FCAe
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAe.Text);
    set => this.txtFCAe.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSi
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSi.Text);
    set => this.txtLOSSi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSe
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSe.Text);
    set => this.txtLOSSe.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool MAWP
  {
    get => this.chkMAWP.Checked;
    set => this.chkMAWP.Checked = value;
  }

  public double? Wmax
  {
    get => Helpers.ParseNullDouble((object) this.txtWmax.Text);
    set => this.txtWmax.Text = Helpers.ParseObjectToString((object) value);
  }

  public int? Grade
  {
    get => Helpers.ParseNullInt32(this.cboPittingGrade.EditValue);
    set => this.cboPittingGrade.EditValue = (object) Helpers.ParseNullInt32((object) value);
  }

  public string UMFCAi
  {
    set => this.umFCAi.Text = value;
  }

  public string UMFCAe
  {
    set => this.umFCAe.Text = value;
  }

  public string UMLOSSi
  {
    set => this.umLOSSi.Text = value;
  }

  public string UMLOSSe
  {
    set => this.umLOSSe.Text = value;
  }

  public string UMWmax
  {
    set => this.umWmax.Text = value;
  }

  public string FCAiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAi, value);
  }

  public string FCAeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAe, value);
  }

  public string LOSSiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSi, value);
  }

  public string LOSSeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSe, value);
  }

  public string WmaxErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtWmax, value);
  }

  public string GradeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.cboPittingGrade, value);
  }

  public string FCAiInfo
  {
    set => this.txtFCAi.ToolTip = value;
  }

  public string FCAeInfo
  {
    set => this.txtFCAe.ToolTip = value;
  }

  public string LOSSiInfo
  {
    set => this.txtLOSSi.ToolTip = value;
  }

  public string LOSSeInfo
  {
    set => this.txtLOSSe.ToolTip = value;
  }

  public string WmaxInfo
  {
    set => this.txtWmax.ToolTip = value;
  }

  public string GradeInfo
  {
    set => this.cboPittingGrade.ToolTip = value;
  }

  public List<PittingGradeDTO> PittingGradeList
  {
    set => this.cboPittingGrade.Properties.DataSource = (object) value;
  }

  public bool ValidateAssessment() => this._presenter.Validate();

  public void Save() => this._presenter.Save();

  public void ClearErrors() => this.dxErrorProvider1.ClearErrors();

  public Image ReturnPittingGradeImage(int pittingGrade)
  {
    return new frmPittingGrade().ObtainPittingGradePicture(pittingGrade);
  }

  public void DisplayPittingGradeImage(int pittingGrade)
  {
    foreach (Form openForm in (ReadOnlyCollectionBase) Application.OpenForms)
    {
      if (openForm is frmPittingGrade)
      {
        openForm.BringToFront();
        (openForm as frmPittingGrade).ShowPittingGradePicture(pittingGrade);
        return;
      }
    }
    frmPittingGrade frmPittingGrade = new frmPittingGrade();
    frmPittingGrade.Show();
    frmPittingGrade.ShowPittingGradePicture(pittingGrade);
  }

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.pictureGeometry2 = new PictureEdit();
    this.pictureGeometry1 = new PictureEdit();
    this.pictureGeometry3 = new PictureEdit();
    this.labelControl4 = new LabelControl();
    this.txtLOSSe = new TextEdit();
    this.labelControl3 = new LabelControl();
    this.txtLOSSi = new TextEdit();
    this.txtFCAe = new TextEdit();
    this.txtFCAi = new TextEdit();
    this.umLOSSe = new LabelControl();
    this.umLOSSi = new LabelControl();
    this.umFCAe = new LabelControl();
    this.umFCAi = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.labelControl1 = new LabelControl();
    this.labelControl5 = new LabelControl();
    this.labelControl6 = new LabelControl();
    this.chkMAWP = new CheckEdit();
    this.txtWmax = new TextEdit();
    this.umWmax = new LabelControl();
    this.cboPittingGrade = new LookUpEdit();
    this.btnPittingGrade = new SimpleButton();
    this.dxErrorProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(this.components);
    this.tableLayoutPanel1.SuspendLayout();
    this.tableLayoutPanel2.SuspendLayout();
    this.pictureGeometry2.Properties.BeginInit();
    this.pictureGeometry1.Properties.BeginInit();
    this.pictureGeometry3.Properties.BeginInit();
    this.txtLOSSe.Properties.BeginInit();
    this.txtLOSSi.Properties.BeginInit();
    this.txtFCAe.Properties.BeginInit();
    this.txtFCAi.Properties.BeginInit();
    this.chkMAWP.Properties.BeginInit();
    this.txtWmax.Properties.BeginInit();
    this.cboPittingGrade.Properties.BeginInit();
    ((ISupportInitialize) this.dxErrorProvider1).BeginInit();
    this.SuspendLayout();
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel2, 0, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl4, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSe, 1, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl3, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSi, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAe, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAi, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSe, 2, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSi, 2, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAe, 2, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAi, 2, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl5, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl6, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkMAWP, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtWmax, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.umWmax, 2, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.cboPittingGrade, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.btnPittingGrade, 2, 2);
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 8;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(506, 481);
    this.tableLayoutPanel1.TabIndex = 0;
    this.tableLayoutPanel2.AutoSize = true;
    this.tableLayoutPanel2.ColumnCount = 2;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.tableLayoutPanel2, 3);
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));
    this.tableLayoutPanel2.Controls.Add((Control) this.pictureGeometry2, 0, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.pictureGeometry1, 0, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.pictureGeometry3, 0, 1);
    this.tableLayoutPanel2.Location = new Point(13, 166);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.RowCount = 2;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.Size = new Size(480, 302);
    this.tableLayoutPanel2.TabIndex = 19;
    this.pictureGeometry2.EditValue = (object) Resources.IW_Pitting_2;
    this.pictureGeometry2.Location = new Point(250, 10);
    this.pictureGeometry2.Margin = new Padding(10);
    this.pictureGeometry2.Name = "pictureGeometry2";
    this.pictureGeometry2.Properties.AllowFocused = false;
    this.pictureGeometry2.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry2.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry2.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry2.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry2.Properties.ReadOnly = true;
    this.pictureGeometry2.Properties.ShowMenu = false;
    this.pictureGeometry2.Size = new Size(219, 131);
    this.pictureGeometry2.TabIndex = 1;
    this.pictureGeometry1.EditValue = (object) Resources.IW_Pitting_1;
    this.pictureGeometry1.Location = new Point(10, 10);
    this.pictureGeometry1.Margin = new Padding(10);
    this.pictureGeometry1.Name = "pictureGeometry1";
    this.pictureGeometry1.Properties.AllowFocused = false;
    this.pictureGeometry1.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry1.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry1.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry1.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry1.Properties.ReadOnly = true;
    this.pictureGeometry1.Properties.ShowMenu = false;
    this.pictureGeometry1.Size = new Size(219, 131);
    this.pictureGeometry1.TabIndex = 0;
    this.pictureGeometry3.EditValue = (object) Resources.IW_Pitting_3;
    this.pictureGeometry3.Location = new Point(10, 161);
    this.pictureGeometry3.Margin = new Padding(10);
    this.pictureGeometry3.Name = "pictureGeometry3";
    this.pictureGeometry3.Properties.AllowFocused = false;
    this.pictureGeometry3.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry3.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry3.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry3.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry3.Properties.ReadOnly = true;
    this.pictureGeometry3.Properties.ShowMenu = false;
    this.pictureGeometry3.Size = new Size(220, 131);
    this.pictureGeometry3.TabIndex = 2;
    this.labelControl4.Location = new Point(13, 144 /*0x90*/);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(171, 13);
    this.labelControl4.TabIndex = 16 /*0x10*/;
    this.labelControl4.Text = "External Uniform Metal Loss, LOSSe";
    this.txtLOSSe.Location = new Point(225, 142);
    this.txtLOSSe.Margin = new Padding(1);
    this.txtLOSSe.Name = "txtLOSSe";
    this.txtLOSSe.Size = new Size(100, 20);
    this.txtLOSSe.TabIndex = 17;
    this.labelControl3.Location = new Point(13, 122);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(165, 13);
    this.labelControl3.TabIndex = 13;
    this.labelControl3.Text = "Internal Uniform Metal Loss, LOSSi";
    this.txtLOSSi.Location = new Point(225, 120);
    this.txtLOSSi.Margin = new Padding(1);
    this.txtLOSSi.Name = "txtLOSSi";
    this.txtLOSSi.Size = new Size(100, 20);
    this.txtLOSSi.TabIndex = 14;
    this.txtFCAe.Location = new Point(225, 98);
    this.txtFCAe.Margin = new Padding(1);
    this.txtFCAe.Name = "txtFCAe";
    this.txtFCAe.Size = new Size(100, 20);
    this.txtFCAe.TabIndex = 11;
    this.txtFCAi.Location = new Point(225, 76);
    this.txtFCAi.Margin = new Padding(1);
    this.txtFCAi.Name = "txtFCAi";
    this.txtFCAi.Size = new Size(100, 20);
    this.txtFCAi.TabIndex = 8;
    this.umLOSSe.Location = new Point(329, 144 /*0x90*/);
    this.umLOSSe.Name = "umLOSSe";
    this.umLOSSe.Size = new Size(41, 13);
    this.umLOSSe.TabIndex = 18;
    this.umLOSSe.Text = "measure";
    this.umLOSSi.Location = new Point(329, 122);
    this.umLOSSi.Name = "umLOSSi";
    this.umLOSSi.Size = new Size(41, 13);
    this.umLOSSi.TabIndex = 15;
    this.umLOSSi.Text = "measure";
    this.umFCAe.Location = new Point(329, 100);
    this.umFCAe.Name = "umFCAe";
    this.umFCAe.Size = new Size(41, 13);
    this.umFCAe.TabIndex = 12;
    this.umFCAe.Text = "measure";
    this.umFCAi.Location = new Point(329, 78);
    this.umFCAi.Name = "umFCAi";
    this.umFCAi.Size = new Size(41, 13);
    this.umFCAi.TabIndex = 9;
    this.umFCAi.Text = "measure";
    this.labelControl2.Location = new Point(13, 100);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(208 /*0xD0*/, 13);
    this.labelControl2.TabIndex = 10;
    this.labelControl2.Text = "External Future Corrosion Allowance, FCAe";
    this.labelControl1.Location = new Point(13, 78);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(202, 13);
    this.labelControl1.TabIndex = 7;
    this.labelControl1.Text = "Internal Future Corrosion Allowance, FCAi";
    this.labelControl5.Location = new Point(13, 56);
    this.labelControl5.Name = "labelControl5";
    this.labelControl5.Size = new Size(62, 13);
    this.labelControl5.TabIndex = 4;
    this.labelControl5.Text = "Pitting Grade";
    this.labelControl6.Location = new Point(13, 34);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(128 /*0x80*/, 13);
    this.labelControl6.TabIndex = 1;
    this.labelControl6.Text = "Maximum Pit Depth, Wmax";
    this.chkMAWP.Location = new Point(11, 11);
    this.chkMAWP.Margin = new Padding(1);
    this.chkMAWP.Name = "chkMAWP";
    this.chkMAWP.Properties.AutoWidth = true;
    this.chkMAWP.Properties.Caption = "MAWP";
    this.chkMAWP.Size = new Size(54, 19);
    this.chkMAWP.TabIndex = 0;
    this.txtWmax.Location = new Point(225, 32 /*0x20*/);
    this.txtWmax.Margin = new Padding(1);
    this.txtWmax.Name = "txtWmax";
    this.txtWmax.Size = new Size(100, 20);
    this.txtWmax.TabIndex = 2;
    this.umWmax.Location = new Point(329, 34);
    this.umWmax.Name = "umWmax";
    this.umWmax.Size = new Size(41, 13);
    this.umWmax.TabIndex = 3;
    this.umWmax.Text = "measure";
    this.cboPittingGrade.Location = new Point(225, 54);
    this.cboPittingGrade.Margin = new Padding(1);
    this.cboPittingGrade.Name = "cboPittingGrade";
    this.cboPittingGrade.Properties.BestFitMode = BestFitMode.BestFitResizePopup;
    this.cboPittingGrade.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Combo)
    });
    this.cboPittingGrade.Properties.Columns.AddRange(new LookUpColumnInfo[2]
    {
      new LookUpColumnInfo("GradeID", "Pitting Grade", 20, FormatType.None, "", false, HorzAlignment.Default),
      new LookUpColumnInfo("PittingGrade", "Pitting Grade")
    });
    this.cboPittingGrade.Properties.DisplayMember = "PittingGrade";
    this.cboPittingGrade.Properties.DropDownRows = 10;
    this.cboPittingGrade.Properties.NullText = "";
    this.cboPittingGrade.Properties.ValueMember = "GradeID";
    this.cboPittingGrade.Size = new Size(100, 20);
    this.cboPittingGrade.TabIndex = 5;
    this.btnPittingGrade.Image = (Image) Resources.PittingGradeInfo;
    this.btnPittingGrade.ImageLocation = ImageLocation.MiddleCenter;
    this.btnPittingGrade.Location = new Point(327, 54);
    this.btnPittingGrade.Margin = new Padding(1);
    this.btnPittingGrade.Name = "btnPittingGrade";
    this.btnPittingGrade.Size = new Size(20, 20);
    this.btnPittingGrade.TabIndex = 6;
    this.btnPittingGrade.TabStop = false;
    this.btnPittingGrade.ToolTip = "Show Pitting Grade Image";
    this.btnPittingGrade.Click += new EventHandler(this.btnPittingGrade_Click);
    this.dxErrorProvider1.ContainerControl = (ContainerControl) this;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = nameof (vwPitting);
    this.Size = new Size(509, 484);
    this.Load += new EventHandler(this.vwPitting_Load);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.tableLayoutPanel2.ResumeLayout(false);
    this.pictureGeometry2.Properties.EndInit();
    this.pictureGeometry1.Properties.EndInit();
    this.pictureGeometry3.Properties.EndInit();
    this.txtLOSSe.Properties.EndInit();
    this.txtLOSSi.Properties.EndInit();
    this.txtFCAe.Properties.EndInit();
    this.txtFCAi.Properties.EndInit();
    this.chkMAWP.Properties.EndInit();
    this.txtWmax.Properties.EndInit();
    this.cboPittingGrade.Properties.EndInit();
    ((ISupportInitialize) this.dxErrorProvider1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
