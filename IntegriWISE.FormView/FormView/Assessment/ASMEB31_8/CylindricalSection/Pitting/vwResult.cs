// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_8.CylindricalSection.Pitting.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Mask;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEB31_8.CylindricalSection.Pitting;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.Layout;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_8.CylindricalSection.Pitting;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private IContainer components;
  private GroupControl grpIntermediateResult;
  private GroupControl grpMAWP;
  private MemoEdit txtWarningMessages;
  private GroupControl groupControl3;
  private TableLayoutPanel tableLayoutPanel3;
  private GroupControl grpLevel1Criteria;
  private GroupControl grpLevel1Conclusion;
  private TableLayoutPanel tblIntermediateResult;
  private TableLayoutPanel tblMAWP;
  private TableLayoutPanel tblLevel1Criteria;
  private TableLayoutPanel tblLevel1Conclusion;
  private bool? _RSF_NA = new bool?();
  private string _ResultMessages;
  private string _Level1Conclusion;
  private string _LBLevel1Conclusion;
  private bool _Level1Passed;
  private string _LBRSFMoreEqualRSFa;
  private bool _RSFMoreEqualRSFa;
  private bool _RwtMoreEqual0_2;
  private string _LBMAWPr;
  private double? _MAWPr;
  private string _LBMAWP;
  private double? _MAWP;
  private bool _EnableIntermediateResult;
  private bool _EnableLevel1Conclusion;
  private bool _EnableLevel1Criteria;
  private bool _EnableMAWP;
  private string _LBRSF;
  private string _LBAllowableStrength;
  private double? _Rwt;
  private double? _AllowableStrength;
  private string _LBRwt;
  private double? _RSF;
  private string _LBRwtMoreEqual0_2;
  private readonly IRecordView _recordView;
  private readonly ResultPresenter _presenter;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.grpIntermediateResult = new GroupControl();
    this.tblIntermediateResult = new TableLayoutPanel();
    this.grpMAWP = new GroupControl();
    this.tblMAWP = new TableLayoutPanel();
    this.txtWarningMessages = new MemoEdit();
    this.groupControl3 = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.grpLevel1Criteria = new GroupControl();
    this.tblLevel1Criteria = new TableLayoutPanel();
    this.grpLevel1Conclusion = new GroupControl();
    this.tblLevel1Conclusion = new TableLayoutPanel();
    this.grpIntermediateResult.BeginInit();
    this.grpIntermediateResult.SuspendLayout();
    this.grpMAWP.BeginInit();
    this.grpMAWP.SuspendLayout();
    this.txtWarningMessages.Properties.BeginInit();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.grpLevel1Criteria.BeginInit();
    this.grpLevel1Criteria.SuspendLayout();
    this.grpLevel1Conclusion.BeginInit();
    this.grpLevel1Conclusion.SuspendLayout();
    this.SuspendLayout();
    this.grpIntermediateResult.AutoSize = true;
    this.grpIntermediateResult.Controls.Add((Control) this.tblIntermediateResult);
    this.grpIntermediateResult.Dock = DockStyle.Top;
    this.grpIntermediateResult.Location = new Point(0, 0);
    this.grpIntermediateResult.Name = "grpIntermediateResult";
    this.grpIntermediateResult.Size = new Size(636, 43);
    this.grpIntermediateResult.TabIndex = 2;
    this.grpIntermediateResult.Text = "Intermediate Assessment Result";
    this.tblIntermediateResult.AutoSize = true;
    this.tblIntermediateResult.ColumnCount = 3;
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.Dock = DockStyle.Fill;
    this.tblIntermediateResult.Location = new Point(2, 21);
    this.tblIntermediateResult.Name = "tblIntermediateResult";
    this.tblIntermediateResult.Padding = new Padding(10);
    this.tblIntermediateResult.RowCount = 1;
    this.tblIntermediateResult.RowStyles.Add(new RowStyle());
    this.tblIntermediateResult.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblIntermediateResult.Size = new Size(632, 20);
    this.tblIntermediateResult.TabIndex = 0;
    this.grpMAWP.AutoSize = true;
    this.grpMAWP.Controls.Add((Control) this.tblMAWP);
    this.grpMAWP.Dock = DockStyle.Top;
    this.grpMAWP.Location = new Point(0, 43);
    this.grpMAWP.Name = "grpMAWP";
    this.grpMAWP.Size = new Size(636, 43);
    this.grpMAWP.TabIndex = 3;
    this.grpMAWP.Text = "Maximum Allowable Working Pressure";
    this.tblMAWP.AutoSize = true;
    this.tblMAWP.ColumnCount = 3;
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.Dock = DockStyle.Fill;
    this.tblMAWP.Location = new Point(2, 21);
    this.tblMAWP.Name = "tblMAWP";
    this.tblMAWP.Padding = new Padding(10);
    this.tblMAWP.RowCount = 1;
    this.tblMAWP.RowStyles.Add(new RowStyle());
    this.tblMAWP.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblMAWP.Size = new Size(632, 20);
    this.tblMAWP.TabIndex = 1;
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(10, 10);
    this.txtWarningMessages.TabIndex = 4;
    this.groupControl3.AutoSize = true;
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel3);
    this.groupControl3.Dock = DockStyle.Fill;
    this.groupControl3.Location = new Point(0, 172);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(636, 59);
    this.groupControl3.TabIndex = 5;
    this.groupControl3.Text = "Warning Messages";
    this.tableLayoutPanel3.AutoSize = true;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel3.Size = new Size(632, 36);
    this.tableLayoutPanel3.TabIndex = 0;
    this.grpLevel1Criteria.AutoSize = true;
    this.grpLevel1Criteria.Controls.Add((Control) this.tblLevel1Criteria);
    this.grpLevel1Criteria.Dock = DockStyle.Top;
    this.grpLevel1Criteria.Location = new Point(0, 86);
    this.grpLevel1Criteria.Name = "grpLevel1Criteria";
    this.grpLevel1Criteria.Size = new Size(636, 43);
    this.grpLevel1Criteria.TabIndex = 6;
    this.grpLevel1Criteria.Text = "Level 1 Assessment Criteria";
    this.tblLevel1Criteria.AutoSize = true;
    this.tblLevel1Criteria.ColumnCount = 3;
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.Dock = DockStyle.Fill;
    this.tblLevel1Criteria.Location = new Point(2, 21);
    this.tblLevel1Criteria.Name = "tblLevel1Criteria";
    this.tblLevel1Criteria.Padding = new Padding(10);
    this.tblLevel1Criteria.RowCount = 1;
    this.tblLevel1Criteria.RowStyles.Add(new RowStyle());
    this.tblLevel1Criteria.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblLevel1Criteria.Size = new Size(632, 20);
    this.tblLevel1Criteria.TabIndex = 1;
    this.grpLevel1Conclusion.AutoSize = true;
    this.grpLevel1Conclusion.Controls.Add((Control) this.tblLevel1Conclusion);
    this.grpLevel1Conclusion.Dock = DockStyle.Top;
    this.grpLevel1Conclusion.Location = new Point(0, 129);
    this.grpLevel1Conclusion.Name = "grpLevel1Conclusion";
    this.grpLevel1Conclusion.Size = new Size(636, 43);
    this.grpLevel1Conclusion.TabIndex = 7;
    this.grpLevel1Conclusion.Text = "Level 1 Assessment Conclusion";
    this.tblLevel1Conclusion.AutoSize = true;
    this.tblLevel1Conclusion.ColumnCount = 3;
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.Dock = DockStyle.Fill;
    this.tblLevel1Conclusion.Location = new Point(2, 21);
    this.tblLevel1Conclusion.Name = "tblLevel1Conclusion";
    this.tblLevel1Conclusion.Padding = new Padding(10);
    this.tblLevel1Conclusion.RowCount = 1;
    this.tblLevel1Conclusion.RowStyles.Add(new RowStyle());
    this.tblLevel1Conclusion.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblLevel1Conclusion.Size = new Size(632, 20);
    this.tblLevel1Conclusion.TabIndex = 1;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.Controls.Add((Control) this.groupControl3);
    this.Controls.Add((Control) this.grpLevel1Conclusion);
    this.Controls.Add((Control) this.grpLevel1Criteria);
    this.Controls.Add((Control) this.grpMAWP);
    this.Controls.Add((Control) this.grpIntermediateResult);
    this.Name = nameof (vwResult);
    this.Size = new Size(250, 250);
    this.Load += new EventHandler(this.vwResult_Load);
    this.grpIntermediateResult.EndInit();
    this.grpIntermediateResult.ResumeLayout(false);
    this.grpIntermediateResult.PerformLayout();
    this.grpMAWP.EndInit();
    this.grpMAWP.ResumeLayout(false);
    this.grpMAWP.PerformLayout();
    this.txtWarningMessages.Properties.EndInit();
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.groupControl3.PerformLayout();
    this.tableLayoutPanel3.ResumeLayout(false);
    this.grpLevel1Criteria.EndInit();
    this.grpLevel1Criteria.ResumeLayout(false);
    this.grpLevel1Criteria.PerformLayout();
    this.grpLevel1Conclusion.EndInit();
    this.grpLevel1Conclusion.ResumeLayout(false);
    this.grpLevel1Conclusion.PerformLayout();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.ResultView = (IResultBaseView) this;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  private void vwResult_Load(object sender, EventArgs e)
  {
  }

  private bool AddToTableOrUpdateExisting(TableLayoutPanel tlp, string value, string name)
  {
    foreach (Control control in (ArrangedElementCollection) tlp.Controls)
    {
      if (control is TextEdit textEdit && string.Compare(textEdit.Name, name) == 0)
      {
        textEdit.Text = value;
        textEdit.Properties.Mask.MaskType = value == "N/A" ? MaskType.None : MaskType.RegEx;
        return true;
      }
    }
    return false;
  }

  private void AddIntermediateResultValue(int rowNum, string value, string name)
  {
    if (this.AddToTableOrUpdateExisting(this.tblIntermediateResult, value, name))
      return;
    if (this.tblIntermediateResult.RowCount < rowNum + 1)
      this.tblIntermediateResult.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    textEdit1.Name = name;
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    textEdit2.AllowOnlyN4();
    this.tblIntermediateResult.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddIntermediateResultLabel(int rowNum, string value)
  {
    if (this.tblIntermediateResult.RowCount < rowNum + 1)
      this.tblIntermediateResult.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblIntermediateResult.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddIntermediateResultUMLabel(int rowNum, string value)
  {
    if (this.tblIntermediateResult.RowCount < rowNum + 1)
      this.tblIntermediateResult.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblIntermediateResult.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  private void AddMAWPValue(int rowNum, double? value)
  {
    if (this.tblMAWP.RowCount < rowNum + 1)
      this.tblMAWP.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    this.tblMAWP.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddMAWPLabel(int rowNum, string value)
  {
    if (this.tblMAWP.RowCount < rowNum + 1)
      this.tblMAWP.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblMAWP.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddMAWPUMLabel(int rowNum, string value)
  {
    if (this.tblMAWP.RowCount < rowNum + 1)
      this.tblMAWP.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblMAWP.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  private void AddLevel1CriteriaLabel(int rowNum, string value)
  {
    if (this.tblLevel1Criteria.RowCount < rowNum + 1)
      this.tblLevel1Criteria.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel1Criteria.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddLevel1CriteriaValue(int rowNum, string value, string name)
  {
    if (this.AddToTableOrUpdateExisting(this.tblLevel1Criteria, value, name))
      return;
    if (this.tblLevel1Criteria.RowCount < rowNum + 1)
      this.tblLevel1Criteria.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = value;
    textEdit1.Margin = new Padding(1);
    textEdit1.Name = name;
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    this.tblLevel1Criteria.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddLevel1ConclusionLabel(int rowNum, string value)
  {
    if (this.tblLevel1Conclusion.RowCount < rowNum + 1)
      this.tblLevel1Conclusion.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel1Conclusion.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddLevel1ConclusionValue(int rowNum, string value)
  {
    if (this.tblLevel1Conclusion.RowCount < rowNum + 1)
      this.tblLevel1Conclusion.RowCount = rowNum + 1;
    MemoEdit memoEdit1 = new MemoEdit();
    memoEdit1.Dock = DockStyle.Fill;
    memoEdit1.Text = Helpers.ParseObjectToString((object) value);
    memoEdit1.Margin = new Padding(1);
    MemoEdit memoEdit2 = memoEdit1;
    memoEdit2.Properties.ReadOnly = true;
    this.tblLevel1Conclusion.SetColumnSpan((Control) memoEdit2, 3);
    this.tblLevel1Conclusion.Controls.Add((Control) memoEdit2, 0, rowNum + 1);
    memoEdit2.AutoSizeInLayoutControl = true;
  }

  private void AddLevel1Passed(int rowNum, bool value)
  {
    if (this.tblLevel1Conclusion.RowCount < rowNum + 1)
      this.tblLevel1Conclusion.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    textEdit2.BackColor = value ? Color.Green : Color.Red;
    this.tblLevel1Conclusion.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  public bool Calculate()
  {
    this.tblIntermediateResult.Controls.Clear();
    this.tblMAWP.Controls.Clear();
    this.tblLevel1Conclusion.Controls.Clear();
    this.tblLevel1Criteria.Controls.Clear();
    return this._presenter.Calculate();
  }

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public bool EnableMAWPr { get; set; }

  public bool EnableMAWP
  {
    get => this._EnableMAWP;
    set
    {
      this.grpMAWP.Visible = value;
      if (this._EnableMAWP == value)
        return;
      this._EnableMAWP = value;
    }
  }

  public bool EnableIntermediateResult
  {
    get => this._EnableIntermediateResult;
    set
    {
      this.grpIntermediateResult.Visible = value;
      this._EnableIntermediateResult = value;
    }
  }

  public bool EnableLevel1Criteria
  {
    get => this._EnableLevel1Criteria;
    set
    {
      this.grpLevel1Criteria.Visible = value;
      this._EnableLevel1Criteria = value;
    }
  }

  public bool EnableLevel1Conclusion
  {
    get => this._EnableLevel1Conclusion;
    set
    {
      this.grpLevel1Conclusion.Visible = value;
      this._EnableLevel1Conclusion = value;
    }
  }

  public double? AllowableStrength
  {
    get => this._AllowableStrength;
    set
    {
      this.AddIntermediateResultValue(1, Helpers.ParseObjectToString((object) value), nameof (AllowableStrength));
      double? allowableStrength = this._AllowableStrength;
      double? nullable = value;
      if ((allowableStrength.GetValueOrDefault() != nullable.GetValueOrDefault() ? 0 : (allowableStrength.HasValue == nullable.HasValue ? 1 : 0)) != 0)
        return;
      this._AllowableStrength = value;
    }
  }

  public string UMAllowableStrength
  {
    set => this.AddIntermediateResultUMLabel(1, value);
  }

  public string LBAllowableStrength
  {
    get => this._LBAllowableStrength;
    set
    {
      this.AddIntermediateResultLabel(1, value);
      if (string.Compare(this._LBAllowableStrength, value, false) == 0)
        return;
      this._LBAllowableStrength = value;
    }
  }

  public double? Rwt
  {
    get => this._Rwt;
    set
    {
      this.AddIntermediateResultValue(2, Helpers.ParseObjectToString((object) value), nameof (Rwt));
      double? rwt = this._Rwt;
      double? nullable = value;
      if ((rwt.GetValueOrDefault() != nullable.GetValueOrDefault() ? 0 : (rwt.HasValue == nullable.HasValue ? 1 : 0)) != 0)
        return;
      this._Rwt = value;
    }
  }

  public string UMRwt
  {
    set => this.AddIntermediateResultUMLabel(2, value);
  }

  public string LBRwt
  {
    get => this._LBRwt;
    set
    {
      this.AddIntermediateResultLabel(2, value);
      if (string.Compare(this._LBRwt, value, false) == 0)
        return;
      this._LBRwt = value;
    }
  }

  public double? RSF
  {
    get => this._RSF;
    set
    {
      bool? rsfNa = this.RSF_NA;
      if ((!rsfNa.GetValueOrDefault() ? 0 : (rsfNa.HasValue ? 1 : 0)) != 0)
        this.AddIntermediateResultValue(3, "N/A", nameof (RSF));
      else
        this.AddIntermediateResultValue(3, Helpers.ParseObjectToString((object) value), nameof (RSF));
      double? rsf = this._RSF;
      double? nullable = value;
      if ((rsf.GetValueOrDefault() != nullable.GetValueOrDefault() ? 0 : (rsf.HasValue == nullable.HasValue ? 1 : 0)) != 0)
        return;
      this._RSF = value;
    }
  }

  public string UMRSF
  {
    set => this.AddIntermediateResultUMLabel(3, value);
  }

  public string LBRSF
  {
    get => this._LBRSF;
    set
    {
      this.AddIntermediateResultLabel(3, value);
      if (string.Compare(this._LBRSF, value, false) == 0)
        return;
      this._LBRSF = value;
    }
  }

  public double? MAWP
  {
    get => this._MAWP;
    set
    {
      this.AddMAWPValue(1, value);
      this._MAWP = value;
    }
  }

  public string UMMAWP
  {
    set => this.AddMAWPUMLabel(1, value);
  }

  public string LBMAWP
  {
    get => this._LBMAWP;
    set
    {
      this._LBMAWP = value;
      this.AddMAWPLabel(1, value);
    }
  }

  public double? MAWPr
  {
    get => this._MAWPr;
    set
    {
      this._MAWPr = value;
      this.AddMAWPValue(2, value);
    }
  }

  public string UMMAWPr
  {
    set => this.AddMAWPUMLabel(2, value);
  }

  public string LBMAWPr
  {
    get => this._LBMAWPr;
    set
    {
      this._LBMAWPr = value;
      this.AddMAWPLabel(2, value);
    }
  }

  public bool RwtMoreEqual0_2
  {
    get => this._RwtMoreEqual0_2;
    set
    {
      this.AddLevel1CriteriaValue(1, Helpers.ParseObjectToString((object) value), nameof (RwtMoreEqual0_2));
      this._RwtMoreEqual0_2 = value;
    }
  }

  public string LBRwtMoreEqual0_2
  {
    get => this._LBRwtMoreEqual0_2;
    set
    {
      this.AddLevel1CriteriaLabel(1, value);
      if (string.Compare(this._LBRwtMoreEqual0_2, value, false) == 0)
        return;
      this._LBRwtMoreEqual0_2 = value;
    }
  }

  public bool RSFMoreEqualRSFa
  {
    get => this._RSFMoreEqualRSFa;
    set
    {
      this._RSFMoreEqualRSFa = value;
      bool? rsfNa = this.RSF_NA;
      if ((!rsfNa.GetValueOrDefault() ? 0 : (rsfNa.HasValue ? 1 : 0)) != 0)
        this.AddLevel1CriteriaValue(2, "N/A", nameof (RSFMoreEqualRSFa));
      else
        this.AddLevel1CriteriaValue(2, Helpers.ParseObjectToString((object) value), nameof (RSFMoreEqualRSFa));
    }
  }

  public string LBRSFMoreEqualRSFa
  {
    get => this._LBRSFMoreEqualRSFa;
    set
    {
      this._LBRSFMoreEqualRSFa = value;
      this.AddLevel1CriteriaLabel(2, value);
    }
  }

  public bool Level1Passed
  {
    get => this._Level1Passed;
    set
    {
      this._Level1Passed = value;
      this.AddLevel1Passed(1, value);
    }
  }

  public string LBLevel1Conclusion
  {
    get => this._LBLevel1Conclusion;
    set
    {
      this._LBLevel1Conclusion = value;
      this.AddLevel1ConclusionLabel(1, value);
    }
  }

  public string Level1Conclusion
  {
    get => this._Level1Conclusion;
    set
    {
      this._Level1Conclusion = value;
      this.AddLevel1ConclusionValue(2, value);
    }
  }

  public string ResultMessages
  {
    get => this._ResultMessages;
    set
    {
      this._ResultMessages = value;
      this.txtWarningMessages.Text = value;
    }
  }

  public bool? RSF_NA
  {
    get => this._RSF_NA;
    set
    {
      this._RSF_NA = value;
      this.RSFMoreEqualRSFa = this.RSFMoreEqualRSFa;
      this.RSF = this.RSF;
    }
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public string CodeEdition => this._presenter.CodeEdition;

  public string Title => "Assessment to API 579 Part 6. Pitting Corrosion.";

  public string Introduction
  {
    get
    {
      return "Pitting is defined as localised regions of metal loss that can be characterised by a pit diameter on the order of the plate thickness or less, and a pit depth that is less than the plate thickness. \nIn a Level 1 Assessment standard pit charts (damage surface) and the maximum pit depth in the area being evaluated are used to estimate the Remaining Strength Factor, RSF. In a Level 2 Assessment a representative sample of pit-couples is used to define the area of pitting damage.";
    }
  }

  public string CommentsAndAssumptions
  {
    get
    {
      return "The pitting assessment procedure in Part 6 assumes that: \n\n1. The original design criteria were in accordance with a recognised code or standard \n2. The component is not operating in the creep regime \n3. The material is considered to have sufficient material toughness. If the user is uncertain about toughness, a \n    Part 3 (Brittle Fracture) assessment should be performed. If the component is subject to embrittlement during \n    operation due to temperature and/or the process environment, a Level 3 assessment should be performed \n4. The component is not in cyclic service \n5. The component under evaluation does not contain crack-like flaws. If crack-like flaws are present, the \n    assessment procedures in Part 9 (Crack-like flaws) shall be utilised. \n6. Level 1 assessment covers Type A components (see Part 4, paragraph 4.2.5) subject to internal pressure \n    (i.e. supplemental loads are assumed to be negligible).\n7. Level 2 assessment covers Type A or B components (see Part 4, paragraph 4.2.5) subject to internal \n    pressure, external pressure, supplemental loads, or any combination of them. \n8. The pitting damage is composed of many pits; individual pits or isolated pairs should be evaluated using \n    the assessment procedure in Part 5.  \n\nLevel 1 Assessment shall be limited to components with one-sided widespread pitting damage, on equipment designed to a recognised code or standard. Additional requirements for Level 1 Assessment are:\n  1) The pitting damage is arrested.    \n  2) The pitting damage is located on only one surface (ID or OD) of the component.\n\nLevel 2 Assessment requirements are: \n  1) The pitting damage is located on either one surface or both surfaces. \n  2) The pitting damage is characterized as widespread pitting, localized region of pitting, LTA located in a region \n      of pitting damage, or pitting that is confined within a LTA.\n  3) A representative sample of pit-couples should be used in the assessment. \n  4) It is assumed that supplemental loads are not presented or negligible, otherwise the assessment procedure in \n      paragraph ******* API 579 should be used to determine the acceptability of the longitudinal stress direction \n      in a cylindrical/conical shell or pipe with pitting damage.";
    }
  }

  public string References
  {
    get
    {
      return "API 579 \"Fitness-for-Service\", Second Edition, The American Society of Mechanical Engineers. Part 6: Assessment of Pitting Corrosion.";
    }
  }

  public string Limitations => string.Empty;
}
