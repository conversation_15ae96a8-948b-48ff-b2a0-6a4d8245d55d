// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_8.CylindricalSection.MembraneStress.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEB31_8.CylindricalSection.MembraneStress;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_8.CylindricalSection.MembraneStress;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private IRecordView _recordView;
  private ResultPresenter _presenter;
  private IContainer components;
  private GroupControl groupControl1;
  private TableLayoutPanel tableLayoutPanel6;
  private LabelControl labelControl12;
  private TableLayoutPanel tableLayoutPanel9;
  private TextEdit txtMembraneStress;
  private LabelControl umMembraneStress;
  private MemoEdit txtWarningMessages;
  private GroupControl groupControl3;
  private TableLayoutPanel tableLayoutPanel3;

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.ResultView = (IResultBaseView) this;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  private void vwResult_Load(object sender, EventArgs e) => this.txtMembraneStress.AllowOnlyN4();

  public bool Calculate() => this._presenter.Calculate();

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public double? MembraneStress
  {
    get => Helpers.ParseNullDouble((object) this.txtMembraneStress.Text);
    set => this.txtMembraneStress.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMMembraneStress
  {
    set => this.umMembraneStress.Text = value;
  }

  public string ResultMessages
  {
    get => this.txtWarningMessages.Text;
    set => this.txtWarningMessages.Text = value;
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public string CodeEdition => this._presenter.CodeEdition;

  public string Title => "Nominal Membrane Stress";

  public string Introduction
  {
    get
    {
      return "This assessment calculates Nominal Membrane Stress for pipelines designed to ASME B31.8. The code calculation is taken from ASME B31.8 clause 841.1 (\"Steel Piping Systems Design Requirements\").";
    }
  }

  public string CommentsAndAssumptions => "The calculations are based on data from ASME B31.8";

  public string References
  {
    get
    {
      return "ASME B31.8  - 2007, \"Gas Transmission and Distribution Piping Systems\", The American Society of Mechanical Engineers.";
    }
  }

  public string Limitations => string.Empty;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.groupControl1 = new GroupControl();
    this.tableLayoutPanel6 = new TableLayoutPanel();
    this.labelControl12 = new LabelControl();
    this.tableLayoutPanel9 = new TableLayoutPanel();
    this.txtMembraneStress = new TextEdit();
    this.umMembraneStress = new LabelControl();
    this.txtWarningMessages = new MemoEdit();
    this.groupControl3 = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.groupControl1.BeginInit();
    this.groupControl1.SuspendLayout();
    this.tableLayoutPanel6.SuspendLayout();
    this.tableLayoutPanel9.SuspendLayout();
    this.txtMembraneStress.Properties.BeginInit();
    this.txtWarningMessages.Properties.BeginInit();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.SuspendLayout();
    this.groupControl1.Controls.Add((Control) this.tableLayoutPanel6);
    this.groupControl1.Dock = DockStyle.Top;
    this.groupControl1.Location = new Point(0, 0);
    this.groupControl1.Name = "groupControl1";
    this.groupControl1.Size = new Size(580, 65);
    this.groupControl1.TabIndex = 3;
    this.groupControl1.Text = "Assessment Result";
    this.tableLayoutPanel6.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel6.ColumnCount = 2;
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 170f));
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel6.Controls.Add((Control) this.labelControl12, 0, 0);
    this.tableLayoutPanel6.Controls.Add((Control) this.tableLayoutPanel9, 1, 0);
    this.tableLayoutPanel6.Dock = DockStyle.Fill;
    this.tableLayoutPanel6.Location = new Point(2, 21);
    this.tableLayoutPanel6.Name = "tableLayoutPanel6";
    this.tableLayoutPanel6.Padding = new Padding(10);
    this.tableLayoutPanel6.RowCount = 1;
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel6.Size = new Size(576, 42);
    this.tableLayoutPanel6.TabIndex = 0;
    this.labelControl12.Location = new Point(13, 13);
    this.labelControl12.Name = "labelControl12";
    this.labelControl12.Size = new Size(140, 13);
    this.labelControl12.TabIndex = 16 /*0x10*/;
    this.labelControl12.Text = "Nominal Membrane Stress (σ)";
    this.tableLayoutPanel9.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel9.ColumnCount = 2;
    this.tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel9.Controls.Add((Control) this.txtMembraneStress, 0, 0);
    this.tableLayoutPanel9.Controls.Add((Control) this.umMembraneStress, 1, 0);
    this.tableLayoutPanel9.Location = new Point(181, 11);
    this.tableLayoutPanel9.Margin = new Padding(1);
    this.tableLayoutPanel9.Name = "tableLayoutPanel9";
    this.tableLayoutPanel9.RowCount = 1;
    this.tableLayoutPanel9.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel9.Size = new Size(196, 20);
    this.tableLayoutPanel9.TabIndex = 17;
    this.txtMembraneStress.Location = new Point(0, 0);
    this.txtMembraneStress.Margin = new Padding(0);
    this.txtMembraneStress.Name = "txtMembraneStress";
    this.txtMembraneStress.Properties.ReadOnly = true;
    this.txtMembraneStress.Size = new Size(100, 20);
    this.txtMembraneStress.TabIndex = 0;
    this.umMembraneStress.Location = new Point(103, 3);
    this.umMembraneStress.Name = "umMembraneStress";
    this.umMembraneStress.Size = new Size(41, 13);
    this.umMembraneStress.TabIndex = 1;
    this.umMembraneStress.Text = "measure";
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(550, 116);
    this.txtWarningMessages.TabIndex = 4;
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel3);
    this.groupControl3.Dock = DockStyle.Fill;
    this.groupControl3.Location = new Point(0, 65);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(580, 165);
    this.groupControl3.TabIndex = 5;
    this.groupControl3.Text = "Warning Messages";
    this.tableLayoutPanel3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel3.Size = new Size(576, 142);
    this.tableLayoutPanel3.TabIndex = 0;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.Controls.Add((Control) this.groupControl3);
    this.Controls.Add((Control) this.groupControl1);
    this.Name = nameof (vwResult);
    this.Size = new Size(580, 230);
    this.Load += new EventHandler(this.vwResult_Load);
    this.groupControl1.EndInit();
    this.groupControl1.ResumeLayout(false);
    this.tableLayoutPanel6.ResumeLayout(false);
    this.tableLayoutPanel6.PerformLayout();
    this.tableLayoutPanel9.ResumeLayout(false);
    this.tableLayoutPanel9.PerformLayout();
    this.txtMembraneStress.Properties.EndInit();
    this.txtWarningMessages.Properties.EndInit();
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.tableLayoutPanel3.ResumeLayout(false);
    this.ResumeLayout(false);
  }
}
