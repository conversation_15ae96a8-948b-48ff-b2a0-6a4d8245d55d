// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_8.CylindricalSection.MAWP.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEB31_8.CylindricalSection.MAWP;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_8.CylindricalSection.MAWP;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private IContainer components;
  private GroupControl grpIntermediateResult;
  private GroupControl grpAssessmentResult;
  private MemoEdit txtWarningMessages;
  private GroupControl groupControl3;
  private TableLayoutPanel tableLayoutPanel3;
  private TableLayoutPanel tblIntermediateResult;
  private TableLayoutPanel tblMAWP;
  private IRecordView _recordView;
  private ResultPresenter _presenter;
  private double? _mawp;
  private double? _allowableStrength;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.grpIntermediateResult = new GroupControl();
    this.tblIntermediateResult = new TableLayoutPanel();
    this.grpAssessmentResult = new GroupControl();
    this.tblMAWP = new TableLayoutPanel();
    this.txtWarningMessages = new MemoEdit();
    this.groupControl3 = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.grpIntermediateResult.BeginInit();
    this.grpIntermediateResult.SuspendLayout();
    this.grpAssessmentResult.BeginInit();
    this.grpAssessmentResult.SuspendLayout();
    this.txtWarningMessages.Properties.BeginInit();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.SuspendLayout();
    this.grpIntermediateResult.AutoSize = true;
    this.grpIntermediateResult.Controls.Add((Control) this.tblIntermediateResult);
    this.grpIntermediateResult.Dock = DockStyle.Top;
    this.grpIntermediateResult.Location = new Point(0, 0);
    this.grpIntermediateResult.Name = "grpIntermediateResult";
    this.grpIntermediateResult.Size = new Size(466, 43);
    this.grpIntermediateResult.TabIndex = 2;
    this.grpIntermediateResult.Text = "Intermediate Assessment Result";
    this.tblIntermediateResult.AutoSize = true;
    this.tblIntermediateResult.ColumnCount = 3;
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.Dock = DockStyle.Fill;
    this.tblIntermediateResult.Location = new Point(2, 21);
    this.tblIntermediateResult.Margin = new Padding(10);
    this.tblIntermediateResult.Name = "tblIntermediateResult";
    this.tblIntermediateResult.Padding = new Padding(10);
    this.tblIntermediateResult.RowCount = 1;
    this.tblIntermediateResult.RowStyles.Add(new RowStyle());
    this.tblIntermediateResult.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblIntermediateResult.Size = new Size(462, 20);
    this.tblIntermediateResult.TabIndex = 0;
    this.grpAssessmentResult.AutoSize = true;
    this.grpAssessmentResult.Controls.Add((Control) this.tblMAWP);
    this.grpAssessmentResult.Dock = DockStyle.Top;
    this.grpAssessmentResult.Location = new Point(0, 43);
    this.grpAssessmentResult.Name = "grpAssessmentResult";
    this.grpAssessmentResult.Size = new Size(466, 43);
    this.grpAssessmentResult.TabIndex = 3;
    this.grpAssessmentResult.Text = "Assessment Result";
    this.tblMAWP.AutoSize = true;
    this.tblMAWP.ColumnCount = 3;
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.Dock = DockStyle.Fill;
    this.tblMAWP.Location = new Point(2, 21);
    this.tblMAWP.Margin = new Padding(10);
    this.tblMAWP.Name = "tblMAWP";
    this.tblMAWP.Padding = new Padding(10);
    this.tblMAWP.RowCount = 1;
    this.tblMAWP.RowStyles.Add(new RowStyle());
    this.tblMAWP.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblMAWP.Size = new Size(462, 20);
    this.tblMAWP.TabIndex = 1;
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(436, 96 /*0x60*/);
    this.txtWarningMessages.TabIndex = 4;
    this.groupControl3.AutoSize = true;
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel3);
    this.groupControl3.Dock = DockStyle.Fill;
    this.groupControl3.Location = new Point(0, 86);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(466, 145);
    this.groupControl3.TabIndex = 5;
    this.groupControl3.Text = "Warning Messages";
    this.tableLayoutPanel3.AutoSize = true;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.Size = new Size(462, 122);
    this.tableLayoutPanel3.TabIndex = 0;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.groupControl3);
    this.Controls.Add((Control) this.grpAssessmentResult);
    this.Controls.Add((Control) this.grpIntermediateResult);
    this.Name = nameof (vwResult);
    this.Size = new Size(466, 231);
    this.Load += new EventHandler(this.vwResult_Load);
    this.grpIntermediateResult.EndInit();
    this.grpIntermediateResult.ResumeLayout(false);
    this.grpIntermediateResult.PerformLayout();
    this.grpAssessmentResult.EndInit();
    this.grpAssessmentResult.ResumeLayout(false);
    this.grpAssessmentResult.PerformLayout();
    this.txtWarningMessages.Properties.EndInit();
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.groupControl3.PerformLayout();
    this.tableLayoutPanel3.ResumeLayout(false);
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.ResultView = (IResultBaseView) this;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  private void vwResult_Load(object sender, EventArgs e)
  {
  }

  private void AddIntermediateResultValue(int rowNum, double? value)
  {
    if (this.tblIntermediateResult.RowCount < rowNum + 1)
      this.tblIntermediateResult.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    textEdit2.AllowOnlyN4();
    this.tblIntermediateResult.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddIntermediateResultLabel(int rowNum, string value)
  {
    if (this.tblIntermediateResult.RowCount < rowNum + 1)
      this.tblIntermediateResult.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblIntermediateResult.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddIntermediateResultUMLabel(int rowNum, string value)
  {
    if (this.tblIntermediateResult.RowCount < rowNum + 1)
      this.tblIntermediateResult.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblIntermediateResult.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  private void AddAssessmentResultValue(int rowNum, double? value)
  {
    if (this.tblMAWP.RowCount < rowNum + 1)
      this.tblMAWP.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    this.tblMAWP.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddAssessmentResultLabel(int rowNum, string value)
  {
    if (this.tblMAWP.RowCount < rowNum + 1)
      this.tblMAWP.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblMAWP.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddAssessmentResultUMLabel(int rowNum, string value)
  {
    if (this.tblMAWP.RowCount < rowNum + 1)
      this.tblMAWP.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblMAWP.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  public bool Calculate()
  {
    this.tblIntermediateResult.Controls.Clear();
    this.tblMAWP.Controls.Clear();
    return this._presenter.Calculate();
  }

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public bool EnableAssessmentResult
  {
    set => this.grpAssessmentResult.Visible = value;
  }

  public bool EnableIntermediateResult
  {
    set => this.grpIntermediateResult.Visible = value;
  }

  public double? AllowableStrength
  {
    get => this._allowableStrength;
    set
    {
      this._allowableStrength = value;
      this.AddIntermediateResultValue(1, value);
    }
  }

  public string UMAllowableStrength
  {
    set => this.AddIntermediateResultUMLabel(1, value);
  }

  public string LBAllowableStrength
  {
    set => this.AddIntermediateResultLabel(1, value);
  }

  public double? MAWP
  {
    get => this._mawp;
    set
    {
      this._mawp = value;
      this.AddAssessmentResultValue(1, value);
    }
  }

  public string UMMAWP
  {
    set => this.AddAssessmentResultUMLabel(1, value);
  }

  public string LBMAWP
  {
    set => this.AddAssessmentResultLabel(1, value);
  }

  public string ResultMessages
  {
    get => this.txtWarningMessages.Text;
    set => this.txtWarningMessages.Text = value;
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public string CodeEdition => this._presenter.CodeEdition;

  public string Title => "Maximum Allowable Working Pressure";

  public string Introduction
  {
    get
    {
      return "This assessment calculates the Maximum Allowable Working Pressure (MAWP) for pipelines designed to ASME B31.8. The code calculation is taken from ASME B31.8, clause 841.1 (\"Steel Piping Systems Design Requirements\").";
    }
  }

  public string CommentsAndAssumptions
  {
    get => "The calculations are based on data from ASME B31.8 [Edition]";
  }

  public string Limitations => string.Empty;

  public string References
  {
    get
    {
      return "ASME B31.8 -2007, \"Gas Transmission and Distribution Piping Systems\", The American Society of Mechanical Engineers.";
    }
  }
}
