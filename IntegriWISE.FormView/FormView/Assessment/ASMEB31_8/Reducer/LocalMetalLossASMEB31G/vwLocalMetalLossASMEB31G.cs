// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_8.Reducer.LocalMetalLossASMEB31G.vwLocalMetalLossASMEB31G
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.Common;
using IntegriWISE.FormView.Properties;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Assessment.ASMEB31_8.Reducer.LocalMetalLossASMEB31G;
using IntegriWISE.UserInterface.Record;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_8.Reducer.LocalMetalLossASMEB31G;

public class vwLocalMetalLossASMEB31G : 
  XtraUserControl,
  ILocalMetalLossASMEB31GView,
  IThicknessAssessmentBaseView,
  IAssessmentBaseView,
  IView,
  ILongitudinalSpacingView,
  ICircumferentialSpacingView
{
  private LocalMetalLossASMEB31GPresenter _presenter;
  private IRecordView _recordView;
  private FormDirtyTracker _dirtyTracker;
  private IContainer components;
  private TableLayoutPanel tableLayoutPanel1;
  private DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider dxErrorProvider1;
  private LabelControl lbLmLabel;
  private LabelControl lbLcLabel;
  private LabelControl lbUMLm;
  private LabelControl lbUMLc;
  private TextEdit txtLm;
  private TextEdit txtLc;
  private CheckEdit chkLevel2;
  private LabelControl lbLevel2Label;
  private PictureEdit pictureAssessment;
  private LabelControl lbMaxCorrodedDepthLabel;
  private LabelControl lbUMD;
  private TextEdit txtD;
  private LabelControl lbLongitudinalExtentLabel;
  private TextEdit txtSF;
  private LabelControl lbUML;
  private TextEdit txtL;
  private LabelControl lbSafetyFactorLabel;
  private LabelControl lbSflowLookupLabel;
  private ComboBoxEdit cbeSflowLookup;

  public vwLocalMetalLossASMEB31G(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.AssessmentView = (IAssessmentBaseView) this;
    this._presenter = new LocalMetalLossASMEB31GPresenter(this._recordView, (ILocalMetalLossASMEB31GView) this);
    this._recordView.EnableThicknessReadings = true;
  }

  private void vwLocalMetalLossASMEB31G_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView);
    this._dirtyTracker.IsHandled = true;
    this.txtD.AllowOnlyN4();
    this.txtL.AllowOnlyN4();
    this.txtSF.AllowOnlyN4();
    this.txtLc.AllowOnlyN4();
    this.txtLm.AllowOnlyN4();
    this._presenter.LoadAssessment();
    this._dirtyTracker.IsHandled = false;
  }

  public int? AssessmentID { get; set; }

  public string Level2Label
  {
    get => this.lbLevel2Label.Text;
    set => this.lbLevel2Label.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? MaxCorrodedDepth
  {
    get => Helpers.ParseNullDouble((object) this.txtD.Text);
    set => this.txtD.Text = Helpers.ParseObjectToString((object) value);
  }

  public string MaxCorrodedDepthLabel
  {
    get => this.lbMaxCorrodedDepthLabel.Text;
    set => this.lbMaxCorrodedDepthLabel.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LongitudinalExtent
  {
    get => Helpers.ParseNullDouble((object) this.txtL.Text);
    set => this.txtL.Text = Helpers.ParseObjectToString((object) value);
  }

  public string LongitudinalExtentLabel
  {
    get => this.lbLongitudinalExtentLabel.Text;
    set => this.lbLongitudinalExtentLabel.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? SafetyFactor
  {
    get => Helpers.ParseNullDouble((object) this.txtSF.Text);
    set => this.txtSF.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SafetyFactorLabel
  {
    get => this.lbSafetyFactorLabel.Text;
    set => this.lbSafetyFactorLabel.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SflowLookup
  {
    get => this.cbeSflowLookup.Text;
    set => this.cbeSflowLookup.Text = value;
  }

  public string SflowLookupLabel
  {
    get => this.lbSflowLookupLabel.Text;
    set => this.lbSflowLookupLabel.Text = value;
  }

  public List<string> SflowLookupValues
  {
    get
    {
      List<string> sflowLookupValues = new List<string>();
      foreach (string str in (CollectionBase) this.cbeSflowLookup.Properties.Items)
        sflowLookupValues.Add(str);
      return sflowLookupValues;
    }
    set
    {
      foreach (object obj in value)
        this.cbeSflowLookup.Properties.Items.Add(obj);
    }
  }

  public double? Lc
  {
    get => Helpers.ParseNullDouble((object) this.txtLc.Text);
    set => this.txtLc.Text = Helpers.ParseObjectToString((object) value);
  }

  public string LcLabel
  {
    get => this.lbLcLabel.Text;
    set => this.lbLcLabel.Text = value;
  }

  public double? Lm
  {
    get => Helpers.ParseNullDouble((object) this.txtLm.Text);
    set => this.txtLm.Text = Helpers.ParseObjectToString((object) value);
  }

  public string LmLabel
  {
    get => this.lbLmLabel.Text;
    set => this.lbLmLabel.Text = value;
  }

  public bool Level2
  {
    get => this.chkLevel2.Checked;
    set => this.chkLevel2.Checked = value;
  }

  public string UMMaxCorrodedDepth
  {
    get => this.lbUMD.Text;
    set => this.lbUMD.Text = value;
  }

  public string UMLongitudinalExtent
  {
    get => this.lbUML.Text;
    set => this.lbUML.Text = value;
  }

  public string UMLc
  {
    get => this.lbUMLc.Text;
    set => this.lbUMLc.Text = value;
  }

  public string UMLm
  {
    get => this.lbUMLm.Text;
    set => this.lbUMLm.Text = value;
  }

  public string MaxCorrodedDepthErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtD, value);
  }

  public string LongitudinalExtentErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtL, value);
  }

  public string SafetyFactorErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtSF, value);
  }

  public string SflowLookupErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.cbeSflowLookup, value);
  }

  public string LcErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLc, value);
  }

  public string LmErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLm, value);
  }

  public string MaxCorrodedDepthInfo
  {
    set => this.txtD.ToolTip = value;
  }

  public string LongitudinalExtentInfo
  {
    set => this.txtL.ToolTip = value;
  }

  public string SafetyFactorInfo
  {
    set => this.txtSF.ToolTip = value;
  }

  public string SflowLookupInfo
  {
    set => this.cbeSflowLookup.ToolTip = value;
  }

  public string LcInfo
  {
    set => this.txtLc.ToolTip = value;
  }

  public string LmInfo
  {
    set => this.txtLm.ToolTip = value;
  }

  public void CalculateTRD() => this._presenter.CalculateTRD();

  public bool ValidateAssessment() => this._presenter.Validate();

  public void Save() => this._presenter.Save();

  public void ClearErrors() => this.dxErrorProvider1.ClearErrors();

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.txtSF = new TextEdit();
    this.lbUML = new LabelControl();
    this.txtL = new TextEdit();
    this.lbUMD = new LabelControl();
    this.txtD = new TextEdit();
    this.pictureAssessment = new PictureEdit();
    this.lbLcLabel = new LabelControl();
    this.lbUMLm = new LabelControl();
    this.lbUMLc = new LabelControl();
    this.txtLc = new TextEdit();
    this.lbLmLabel = new LabelControl();
    this.txtLm = new TextEdit();
    this.lbLevel2Label = new LabelControl();
    this.chkLevel2 = new CheckEdit();
    this.lbMaxCorrodedDepthLabel = new LabelControl();
    this.lbLongitudinalExtentLabel = new LabelControl();
    this.lbSafetyFactorLabel = new LabelControl();
    this.lbSflowLookupLabel = new LabelControl();
    this.cbeSflowLookup = new ComboBoxEdit();
    this.dxErrorProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(this.components);
    this.tableLayoutPanel1.SuspendLayout();
    this.txtSF.Properties.BeginInit();
    this.txtL.Properties.BeginInit();
    this.txtD.Properties.BeginInit();
    this.pictureAssessment.Properties.BeginInit();
    this.txtLc.Properties.BeginInit();
    this.txtLm.Properties.BeginInit();
    this.chkLevel2.Properties.BeginInit();
    this.cbeSflowLookup.Properties.BeginInit();
    ((ISupportInitialize) this.dxErrorProvider1).BeginInit();
    this.SuspendLayout();
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.txtSF, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbUML, 2, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtL, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbUMD, 2, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtD, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.pictureAssessment, 0, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbLcLabel, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbUMLm, 2, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbUMLc, 2, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLc, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbLmLabel, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLm, 1, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbLevel2Label, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkLevel2, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbMaxCorrodedDepthLabel, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbLongitudinalExtentLabel, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbSafetyFactorLabel, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbSflowLookupLabel, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.cbeSflowLookup, 1, 4);
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 13;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.Size = new Size(431, 351);
    this.tableLayoutPanel1.TabIndex = 0;
    this.txtSF.Dock = DockStyle.Bottom;
    this.txtSF.Location = new Point(243, 80 /*0x50*/);
    this.txtSF.Margin = new Padding(1);
    this.txtSF.Name = "txtSF";
    this.txtSF.Size = new Size(130, 20);
    this.txtSF.TabIndex = 9;
    this.lbUML.Dock = DockStyle.Bottom;
    this.lbUML.Location = new Point(377, 63 /*0x3F*/);
    this.lbUML.Name = "lbUML";
    this.lbUML.Size = new Size(41, 13);
    this.lbUML.TabIndex = 7;
    this.lbUML.Text = "measure";
    this.txtL.Dock = DockStyle.Bottom;
    this.txtL.Location = new Point(243, 58);
    this.txtL.Margin = new Padding(1);
    this.txtL.Name = "txtL";
    this.txtL.Size = new Size(130, 20);
    this.txtL.TabIndex = 6;
    this.lbUMD.Dock = DockStyle.Bottom;
    this.lbUMD.Location = new Point(377, 41);
    this.lbUMD.Name = "lbUMD";
    this.lbUMD.Size = new Size(41, 13);
    this.lbUMD.TabIndex = 4;
    this.lbUMD.Text = "measure";
    this.txtD.Dock = DockStyle.Bottom;
    this.txtD.Location = new Point(243, 36);
    this.txtD.Margin = new Padding(1);
    this.txtD.Name = "txtD";
    this.txtD.Size = new Size(130, 20);
    this.txtD.TabIndex = 3;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.pictureAssessment, 3);
    this.pictureAssessment.EditValue = (object) Resources.IW_GeneralMetalLossCylindricalShell;
    this.pictureAssessment.Location = new Point(20, 181);
    this.pictureAssessment.Margin = new Padding(10);
    this.pictureAssessment.Name = "pictureAssessment";
    this.pictureAssessment.Properties.AllowFocused = false;
    this.pictureAssessment.Properties.AllowScrollViaMouseDrag = false;
    this.pictureAssessment.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureAssessment.Properties.Appearance.Options.UseBackColor = true;
    this.pictureAssessment.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureAssessment.Properties.ReadOnly = true;
    this.pictureAssessment.Properties.ShowMenu = false;
    this.pictureAssessment.Size = new Size(220, 150);
    this.pictureAssessment.TabIndex = 18;
    this.lbLcLabel.AutoSizeMode = LabelAutoSizeMode.Vertical;
    this.lbLcLabel.Location = new Point(13, 130);
    this.lbLcLabel.Name = "lbLcLabel";
    this.lbLcLabel.Size = new Size(208 /*0xD0*/, 13);
    this.lbLcLabel.TabIndex = 12;
    this.lbLcLabel.Text = "LcLabel";
    this.lbUMLm.Dock = DockStyle.Bottom;
    this.lbUMLm.Location = new Point(377, 155);
    this.lbUMLm.Name = "lbUMLm";
    this.lbUMLm.Size = new Size(41, 13);
    this.lbUMLm.TabIndex = 17;
    this.lbUMLm.Text = "measure";
    this.lbUMLc.Dock = DockStyle.Bottom;
    this.lbUMLc.Location = new Point(377, 133);
    this.lbUMLc.Name = "lbUMLc";
    this.lbUMLc.Size = new Size(41, 13);
    this.lbUMLc.TabIndex = 14;
    this.lbUMLc.Text = "measure";
    this.txtLc.Dock = DockStyle.Bottom;
    this.txtLc.Location = new Point(243, 128 /*0x80*/);
    this.txtLc.Margin = new Padding(1);
    this.txtLc.Name = "txtLc";
    this.txtLc.Size = new Size(130, 20);
    this.txtLc.TabIndex = 13;
    this.lbLmLabel.AutoSizeMode = LabelAutoSizeMode.Vertical;
    this.lbLmLabel.Location = new Point(13, 152);
    this.lbLmLabel.Name = "lbLmLabel";
    this.lbLmLabel.Size = new Size(226, 13);
    this.lbLmLabel.TabIndex = 15;
    this.lbLmLabel.Text = "LmLabel";
    this.txtLm.Dock = DockStyle.Bottom;
    this.txtLm.Location = new Point(243, 150);
    this.txtLm.Margin = new Padding(1);
    this.txtLm.Name = "txtLm";
    this.txtLm.Size = new Size(130, 20);
    this.txtLm.TabIndex = 16 /*0x10*/;
    this.lbLevel2Label.Location = new Point(13, 13);
    this.lbLevel2Label.Name = "lbLevel2Label";
    this.lbLevel2Label.Size = new Size(56, 13);
    this.lbLevel2Label.TabIndex = 0;
    this.lbLevel2Label.Text = "Level2Label";
    this.chkLevel2.Location = new Point(245, 13);
    this.chkLevel2.Name = "chkLevel2";
    this.chkLevel2.Properties.Caption = " ";
    this.chkLevel2.Size = new Size(24, 19);
    this.chkLevel2.TabIndex = 1;
    this.lbMaxCorrodedDepthLabel.Location = new Point(13, 38);
    this.lbMaxCorrodedDepthLabel.Name = "lbMaxCorrodedDepthLabel";
    this.lbMaxCorrodedDepthLabel.Size = new Size(119, 13);
    this.lbMaxCorrodedDepthLabel.TabIndex = 2;
    this.lbMaxCorrodedDepthLabel.Text = "MaxCorrodedDepthLabel";
    this.lbLongitudinalExtentLabel.Location = new Point(13, 60);
    this.lbLongitudinalExtentLabel.Name = "lbLongitudinalExtentLabel";
    this.lbLongitudinalExtentLabel.Size = new Size(114, 13);
    this.lbLongitudinalExtentLabel.TabIndex = 5;
    this.lbLongitudinalExtentLabel.Text = "LongitudinalExtentLabel";
    this.lbSafetyFactorLabel.Location = new Point(13, 82);
    this.lbSafetyFactorLabel.Name = "lbSafetyFactorLabel";
    this.lbSafetyFactorLabel.Size = new Size(88, 13);
    this.lbSafetyFactorLabel.TabIndex = 8;
    this.lbSafetyFactorLabel.Text = "SafetyFactorLabel";
    this.lbSflowLookupLabel.Location = new Point(13, 104);
    this.lbSflowLookupLabel.Name = "lbSflowLookupLabel";
    this.lbSflowLookupLabel.Size = new Size(85, 13);
    this.lbSflowLookupLabel.TabIndex = 10;
    this.lbSflowLookupLabel.Text = "SflowLookupLabel";
    this.cbeSflowLookup.Location = new Point(245, 104);
    this.cbeSflowLookup.Name = "cbeSflowLookup";
    this.cbeSflowLookup.Properties.AllowNullInput = DefaultBoolean.True;
    this.cbeSflowLookup.Properties.AutoComplete = false;
    this.cbeSflowLookup.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Combo)
    });
    this.cbeSflowLookup.Properties.TextEditStyle = TextEditStyles.DisableTextEditor;
    this.cbeSflowLookup.Size = new Size(126, 20);
    this.cbeSflowLookup.TabIndex = 11;
    this.dxErrorProvider1.ContainerControl = (ContainerControl) this;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = nameof (vwLocalMetalLossASMEB31G);
    this.Size = new Size(439, 380);
    this.Load += new EventHandler(this.vwLocalMetalLossASMEB31G_Load);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.txtSF.Properties.EndInit();
    this.txtL.Properties.EndInit();
    this.txtD.Properties.EndInit();
    this.pictureAssessment.Properties.EndInit();
    this.txtLc.Properties.EndInit();
    this.txtLm.Properties.EndInit();
    this.chkLevel2.Properties.EndInit();
    this.cbeSflowLookup.Properties.EndInit();
    ((ISupportInitialize) this.dxErrorProvider1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
