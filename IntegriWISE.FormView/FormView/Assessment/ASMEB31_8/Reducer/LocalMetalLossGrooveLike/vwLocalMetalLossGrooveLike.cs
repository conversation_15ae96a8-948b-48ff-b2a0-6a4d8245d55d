// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_8.Reducer.LocalMetalLossGrooveLike.vwLocalMetalLossGrooveLike
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using IntegriWISE.FormView.Assessment.AbstractView.LocalMetalLossGrooveLike;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Assessment.AbstractUserInterface.LocalMetalLossGrooveLike;
using IntegriWISE.UserInterface.Assessment.ASMEB31_8.Reducer.LocalMetalLossGrooveLike;
using IntegriWISE.UserInterface.Record;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_8.Reducer.LocalMetalLossGrooveLike;

public class vwLocalMetalLossGrooveLike : 
  AbstractLocalMetalLossGrooveLikeView,
  ILocalMetalLossGrooveLikeView,
  IAssessmentBaseView,
  IView
{
  private LocalMetalLossGrooveLikePresenter _presenter;

  public vwLocalMetalLossGrooveLike(IRecordView recordView)
    : base(recordView)
  {
    recordView.AssessmentView = (IAssessmentBaseView) this;
    this._presenter = new LocalMetalLossGrooveLikePresenter(recordView, (ILocalMetalLossGrooveLikeView) this);
    this._presenter.LoadAssessment();
  }

  public bool ValidateAssessment() => this._presenter.Validate();

  public void Save() => this._presenter.Save();
}
