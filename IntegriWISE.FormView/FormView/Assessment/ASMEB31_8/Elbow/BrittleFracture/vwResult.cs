// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_8.Elbow.BrittleFracture.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEB31_8.Elbow.BrittleFracture;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_8.Elbow.BrittleFracture;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private IRecordView _recordView;
  private ResultPresenter _presenter;
  private vwResult.ResultItems _resultItems;
  private IContainer components;
  private GroupControl grpIntermediateLevel1Result;
  private TableLayoutPanel tblIntermediateLevel1Result;
  private GroupControl grpLevel1AssessmentResult;
  private TableLayoutPanel tblLevel1Result;
  private GroupControl grpLevel1Criteria;
  private TableLayoutPanel tblLevel1Criteria;
  private GroupControl grpLevel1Conclusion;
  private TableLayoutPanel tblLevel1Conclusion;
  private GroupControl grpLevel2Conclusion;
  private TableLayoutPanel tblLevel2Conclusion;
  private GroupControl grpLevel2Criteria;
  private TableLayoutPanel tblLevel2Criteria;
  private GroupControl grpLevel2AssessmentResult;
  private TableLayoutPanel tblLevel2Result;
  private GroupControl grpIntermediateLevel2Result;
  private TableLayoutPanel tblIntermediateLevel2Result;
  private GroupControl groupControl3;
  private TableLayoutPanel tableLayoutPanel3;
  private MemoEdit txtWarningMessages;

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.ResultView = (IResultBaseView) this;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
    this._resultItems = new vwResult.ResultItems();
  }

  private void vwResult_Load(object sender, EventArgs e)
  {
  }

  private void AddIntermediateResultLabel_1(int rowNum, string value)
  {
    if (this.tblIntermediateLevel1Result.RowCount < rowNum + 1)
      this.tblIntermediateLevel1Result.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblIntermediateLevel1Result.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddIntermediateResultValue_1(int rowNum, string value)
  {
    if (this.tblIntermediateLevel1Result.RowCount < rowNum + 1)
      this.tblIntermediateLevel1Result.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = value;
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    this.tblIntermediateLevel1Result.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddIntermediateResultUMLabel_1(int rowNum, string value)
  {
    if (this.tblIntermediateLevel1Result.RowCount < rowNum + 1)
      this.tblIntermediateLevel1Result.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblIntermediateLevel1Result.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  private void AddResultLabel_1(int rowNum, string value)
  {
    if (this.tblLevel1Result.RowCount < rowNum + 1)
      this.tblLevel1Result.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel1Result.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddResultValue_1(int rowNum, double? value)
  {
    if (this.tblLevel1Result.RowCount < rowNum + 1)
      this.tblLevel1Result.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    textEdit2.AllowOnlyN4AndNeg();
    this.tblLevel1Result.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddResultUMLabel_1(int rowNum, string value)
  {
    if (this.tblLevel1Result.RowCount < rowNum + 1)
      this.tblLevel1Result.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel1Result.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  private void AddCriteriaLabel_1(int rowNum, string value)
  {
    if (this.tblLevel1Criteria.RowCount < rowNum + 1)
      this.tblLevel1Criteria.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel1Criteria.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddCriteriaValue_1(int rowNum, string value)
  {
    if (this.tblLevel1Criteria.RowCount < rowNum + 1)
      this.tblLevel1Criteria.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    this.tblLevel1Criteria.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddCriteriaUMLabel_1(int rowNum, string value)
  {
    if (this.tblLevel1Criteria.RowCount < rowNum + 1)
      this.tblLevel1Criteria.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel1Criteria.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  private void AddConclusionValue_1(int rowNum, string value, bool result)
  {
    if (this.tblLevel1Conclusion.RowCount < rowNum + 1)
      this.tblLevel1Conclusion.RowCount = rowNum + 1;
    MemoEdit memoEdit1 = new MemoEdit();
    memoEdit1.Dock = DockStyle.Fill;
    memoEdit1.Text = Helpers.ParseObjectToString((object) value);
    memoEdit1.Margin = new Padding(1);
    MemoEdit memoEdit2 = memoEdit1;
    memoEdit2.Properties.ReadOnly = true;
    memoEdit2.AutoSizeInLayoutControl = true;
    memoEdit2.Size = new Size(this.tblLevel1Conclusion.Width, 20);
    memoEdit2.BackColor = result ? Color.Green : Color.Red;
    this.tblLevel1Conclusion.SetColumnSpan((Control) memoEdit2, 3);
    this.tblLevel1Conclusion.Controls.Add((Control) memoEdit2, 0, rowNum + 1);
  }

  private void AddIntermediateResultLabel_2(int rowNum, string value)
  {
    if (this.tblIntermediateLevel2Result.RowCount < rowNum + 1)
      this.tblIntermediateLevel2Result.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblIntermediateLevel2Result.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddIntermediateResultValue_2(int rowNum, string value)
  {
    if (this.tblIntermediateLevel2Result.RowCount < rowNum + 1)
      this.tblIntermediateLevel2Result.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = value;
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    this.tblIntermediateLevel2Result.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddIntermediateResultUMLabel_2(int rowNum, string value)
  {
    if (this.tblIntermediateLevel2Result.RowCount < rowNum + 1)
      this.tblIntermediateLevel2Result.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblIntermediateLevel2Result.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  private void AddResultLabel_2(int rowNum, string value)
  {
    if (this.tblLevel2Result.RowCount < rowNum + 1)
      this.tblLevel2Result.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel2Result.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddResultValue_2(int rowNum, double? value)
  {
    if (this.tblLevel2Result.RowCount < rowNum + 1)
      this.tblLevel2Result.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    textEdit2.AllowOnlyN4AndNeg();
    this.tblLevel2Result.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddResultUMLabel_2(int rowNum, string value)
  {
    if (this.tblLevel2Result.RowCount < rowNum + 1)
      this.tblLevel2Result.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel2Result.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  private void AddCriteriaLabel_2(int rowNum, string value)
  {
    if (this.tblLevel2Criteria.RowCount < rowNum + 1)
      this.tblLevel2Criteria.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel2Criteria.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddCriteriaValue_2(int rowNum, string value)
  {
    if (this.tblLevel2Criteria.RowCount < rowNum + 1)
      this.tblLevel2Criteria.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    this.tblLevel2Criteria.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddCriteriaUMLabel_2(int rowNum, string value)
  {
    if (this.tblLevel2Criteria.RowCount < rowNum + 1)
      this.tblLevel2Criteria.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel2Criteria.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  private void AddConclusionValue_2(int rowNum, string value, bool result)
  {
    if (this.tblLevel2Conclusion.RowCount < rowNum + 1)
      this.tblLevel2Conclusion.RowCount = rowNum + 1;
    MemoEdit memoEdit1 = new MemoEdit();
    memoEdit1.Dock = DockStyle.Fill;
    memoEdit1.Text = Helpers.ParseObjectToString((object) value);
    memoEdit1.Margin = new Padding(1);
    MemoEdit memoEdit2 = memoEdit1;
    memoEdit2.Properties.ReadOnly = true;
    memoEdit2.AutoSizeInLayoutControl = true;
    memoEdit2.Size = new Size(this.tblLevel2Conclusion.Width, 20);
    memoEdit2.BackColor = result ? Color.Green : Color.Red;
    this.tblLevel2Conclusion.SetColumnSpan((Control) memoEdit2, 3);
    this.tblLevel2Conclusion.Controls.Add((Control) memoEdit2, 0, rowNum + 1);
  }

  public bool Calculate()
  {
    this.tblIntermediateLevel1Result.Controls.Clear();
    this.tblLevel1Conclusion.Controls.Clear();
    this.tblLevel1Criteria.Controls.Clear();
    this.tblLevel1Result.Controls.Clear();
    this.tblIntermediateLevel2Result.Controls.Clear();
    this.tblLevel2Conclusion.Controls.Clear();
    this.tblLevel2Criteria.Controls.Clear();
    this.tblLevel2Result.Controls.Clear();
    return this._presenter.Calculate();
  }

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public bool ShowLevel2
  {
    get => this._resultItems.ShowLevel2;
    set
    {
      this.grpLevel2Conclusion.Visible = value;
      this.grpLevel2Criteria.Visible = value;
      this._resultItems.ShowLevel2 = value;
      this.grpLevel2AssessmentResult.Visible = value;
      this.grpIntermediateLevel2Result.Visible = value;
    }
  }

  public string MAT_OptionA
  {
    get => this._resultItems.MAT_OptionA;
    set
    {
      this.AddIntermediateResultValue_1(1, Helpers.ParseObjectToString((object) value));
      this._resultItems.MAT_OptionA = value;
    }
  }

  public string MAT_OptionALabel
  {
    get => this._resultItems.MAT_OptionALabel;
    set
    {
      this.AddIntermediateResultLabel_1(1, value);
      this._resultItems.MAT_OptionALabel = value;
    }
  }

  public string MAT_OptionAUM
  {
    get => this._resultItems.MAT_OptionAUM;
    set
    {
      this.AddIntermediateResultUMLabel_1(1, value);
      this._resultItems.MAT_OptionAUM = value;
    }
  }

  public string MAT_OptionB
  {
    get => this._resultItems.MAT_OptionB;
    set
    {
      this.AddIntermediateResultValue_1(2, value);
      this._resultItems.MAT_OptionB = value;
    }
  }

  public string MAT_OptionBLabel
  {
    get => this._resultItems.MAT_OptionBLabel;
    set
    {
      this.AddIntermediateResultLabel_1(2, value);
      this._resultItems.MAT_OptionBLabel = value;
    }
  }

  public string MAT_OptionBUM
  {
    get => this._resultItems.MAT_OptionBUM;
    set
    {
      this.AddIntermediateResultUMLabel_1(2, value);
      this._resultItems.MAT_OptionBUM = value;
    }
  }

  public double Level1MAT
  {
    get => this._resultItems.Level1MAT;
    set
    {
      this.AddResultValue_1(1, new double?(value));
      this._resultItems.Level1MAT = value;
    }
  }

  public string Level1MATLabel
  {
    get => this._resultItems.Level1MATLabel;
    set
    {
      this.AddResultLabel_1(1, value);
      this._resultItems.Level1MATLabel = value;
    }
  }

  public string Level1MATUM
  {
    get => this._resultItems.Level1MATUM;
    set
    {
      this.AddResultUMLabel_1(1, value);
      this._resultItems.Level1MATUM = value;
    }
  }

  public string CETGreaterThanMAT
  {
    get => this._resultItems.CETGreaterThanMAT;
    set
    {
      this.AddCriteriaValue_1(1, value);
      this._resultItems.CETGreaterThanMAT = value;
    }
  }

  public string CETGreaterThanMATLabel
  {
    get => this._resultItems.CETGreaterThanMATLabel;
    set
    {
      this.AddCriteriaLabel_1(1, value);
      this._resultItems.CETGreaterThanMATLabel = value;
    }
  }

  public void Level1Conclusion(string text, bool result)
  {
    this._resultItems.Level1Conclusion = text;
    this._resultItems.Level1Result = result;
    this.AddConclusionValue_1(1, text, result);
  }

  public bool GetLevel1Conclusion(out string text)
  {
    text = this._resultItems.Level1Conclusion;
    return this._resultItems.Level1Result;
  }

  public string MAT_Level2_MethodA
  {
    get => this._resultItems.MAT_Level2_MethodA;
    set
    {
      this.AddIntermediateResultValue_2(1, Helpers.ParseObjectToString((object) value));
      this._resultItems.MAT_Level2_MethodA = value;
    }
  }

  public string MAT_Level2_MethodALabel
  {
    get => this._resultItems.MAT_Level2_MethodALabel;
    set
    {
      this.AddIntermediateResultLabel_2(1, value);
      this._resultItems.MAT_Level2_MethodALabel = value;
    }
  }

  public string MAT_Level2_MethodAUM
  {
    get => this._resultItems.MAT_Level2_MethodAUM;
    set
    {
      this.AddIntermediateResultUMLabel_2(1, value);
      this._resultItems.MAT_Level2_MethodAUM = value;
    }
  }

  public string MAT_Level2_MethodB
  {
    get => this._resultItems.MAT_Level2_MethodB;
    set
    {
      this.AddIntermediateResultValue_2(2, value);
      this._resultItems.MAT_Level2_MethodB = value;
    }
  }

  public string MAT_Level2_MethodBLabel
  {
    get => this._resultItems.MAT_Level2_MethodBLabel;
    set
    {
      this.AddIntermediateResultLabel_2(2, value);
      this._resultItems.MAT_Level2_MethodBLabel = value;
    }
  }

  public string MAT_Level2_MethodBUM
  {
    get => this._resultItems.MAT_Level2_MethodBUM;
    set
    {
      this.AddIntermediateResultUMLabel_2(2, value);
      this._resultItems.MAT_Level2_MethodBUM = value;
    }
  }

  public double Level2MAT
  {
    get => this._resultItems.Level2MAT;
    set
    {
      this.AddResultValue_2(1, new double?(value));
      this._resultItems.Level2MAT = value;
    }
  }

  public string Level2MATLabel
  {
    get => this._resultItems.Level2MATLabel;
    set
    {
      this.AddResultLabel_2(1, value);
      this._resultItems.Level2MATLabel = value;
    }
  }

  public string Level2MATUM
  {
    get => this._resultItems.Level2MATUM;
    set
    {
      this.AddResultUMLabel_2(1, value);
      this._resultItems.Level2MATUM = value;
    }
  }

  public string CETGreaterThanMATLevel2
  {
    get => this._resultItems.CETGreaterThanMATLevel2;
    set
    {
      this.AddCriteriaValue_2(1, value);
      this._resultItems.CETGreaterThanMATLevel2 = value;
    }
  }

  public string CETGreaterThanMATlevel2Label
  {
    get => this._resultItems.CETGreaterThanMATLevel2Label;
    set
    {
      this.AddCriteriaLabel_2(1, value);
      this._resultItems.CETGreaterThanMATLevel2Label = value;
    }
  }

  public void Level2Conclusion(string text, bool result)
  {
    this._resultItems.Level2Conclusion = text;
    this._resultItems.Level2Result = result;
    this.AddConclusionValue_2(1, text, result);
  }

  public bool GetLevel2Conclusion(out string text)
  {
    text = this._resultItems.Level2Conclusion;
    return this._resultItems.Level2Result;
  }

  public string ResultMessages
  {
    get => this.txtWarningMessages.Text;
    set => this.txtWarningMessages.Text = value;
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public string CodeEdition => this._presenter.CodeEdition;

  public string Title
  {
    get
    {
      return $"Assessment to API 579 Part 3. Brittle Fracture. {(this._resultItems.ShowLevel2 ? (object) "To Level 2" : (object) string.Empty)}";
    }
  }

  public string Introduction
  {
    get
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append("The Brittle Fracture assessment procedure is aimed at examining pressure equipment ");
      stringBuilder.Append("e.g. pressure vessels, piping and tanks, constructed from carbon and low alloy steels ");
      stringBuilder.Append("for its susceptibility to failure by brittle fracture. The methodology for conducting ");
      stringBuilder.AppendLine("such an assessment is based on API 579 procedures, which in turn are based on the ASME VIII design philosophy.");
      stringBuilder.Append("The assessment methodology considers the lowest temperature to which the equipment can be ");
      stringBuilder.Append("subjected while under load, termed the Critical Exposure Temperature (CET), and compares this with the ");
      stringBuilder.Append("lowest temperature at which the metal retains good resistance to brittle fracture, termed the ");
      stringBuilder.Append("Minimum Allowable Temperature (MAT). In general, if the CET is greater than or equal to the ");
      stringBuilder.Append("MAT then the equipment has met the Assessment requirements for the examined service.");
      return stringBuilder.ToString();
    }
  }

  public string CommentsAndAssumptions
  {
    get
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append("1. When determining the MAT, parts such as shells, heads, nozzles, reinforcing pads, flanges, ");
      stringBuilder.Append("tubesheets, flat cover plates, skirts, and attachments that are essential to the structural ");
      stringBuilder.Append("integrity of the vessel shell be treated as a seperate components. Each such component shall ");
      stringBuilder.AppendLine("be evaluated based on its individual material classification.");
      stringBuilder.AppendLine("2. Components with crack like flaws shall be evaluated using the procedures in part 9");
      stringBuilder.Append("3. The Level 1 and 2 procedures in this Part may be applied to components subject to general ");
      stringBuilder.Append("corrosion, local metal loss and pitting damage provided the assessment criteria in Part 4, Part 5 ");
      stringBuilder.AppendLine("and Part 6, respectively, are satisfied.");
      stringBuilder.Append("4. If the component was subject to PWHT, it is supposed that the status of the PWHT has not been ");
      stringBuilder.AppendLine("changed because of repairs and/or alterations.");
      stringBuilder.Append("5. It is supposed that \"Shock chilling or significant mechanical shock loadings are not credible events. ");
      stringBuilder.Append("and \"Cyclic loading is not a controlling design requirement\". (Requirements in paragraph 3.4.2.1.e  ");
      stringBuilder.AppendLine("for Vessels constructed to ASME VIII, Division 1).");
      stringBuilder.AppendLine("6. If a change in the operating conditions is made that affects the CET, a reassessment should be undertaken.");
      stringBuilder.Append("7. If an hydrotest has been performed, the metal temperature during hydrotest, rather than water temperature, ");
      stringBuilder.Append("is the relevant parameter in a Level 2 assessment and the test pressure should be limited to a value that will not ");
      stringBuilder.Append("produce a primary membrance stress higher than the 90% of the specified minimum yield strength for the ");
      stringBuilder.AppendLine("material of the component.");
      return stringBuilder.ToString();
    }
  }

  public string Limitations
  {
    get
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append("The MAT for flanges meeting ASME B16.5 or B16.47 shall be set at -29°C (-20°F), unless the MAT ");
      stringBuilder.Append("determined by the governing thickness at the flange nozzle neck weld joint together with the curve ");
      stringBuilder.Append("associated with the flange material gives a higher value. The MAT for carbon steel components with a ");
      stringBuilder.Append("governing thickness of less than 2.5mm (0.098 inch) shall be -48°C (-55°F). The MAT for carbon steel ");
      stringBuilder.AppendLine("nuts shall be -48°C (-55°F)");
      stringBuilder.Append("Exemption curves shown in Figure 3.4 API 579 are used in this assessment. These curves are limited to ");
      stringBuilder.Append("components design to ASME Cod, Section VIII, Division 1 or 2 and other recognised pressure vessel codes ");
      stringBuilder.Append("provided the design allowable stress is less than or equal to 172.5 MPa (25 ksi). ");
      stringBuilder.AppendLine("These curves can also be used to evaluate piping components.");
      return stringBuilder.ToString();
    }
  }

  public string References
  {
    get
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append("1. API 579 \"Fitness-for-Service\", Second Edition, The American Society of Mechanical Engineers. ");
      stringBuilder.AppendLine("Part 3: Assessment of Existing Equipment for Brittle Fracture.");
      stringBuilder.Append("2. ASME Boiler and Pressure Vessel Code, Section VIII, Division 1, \"Rules for Construction of Pressure ");
      stringBuilder.AppendLine("Vessel\", The American Society of Mechanical Engineers.");
      stringBuilder.AppendLine("3. ASME Code Pressure Piping B31, ASME B31.3, \"Process Piping\", The American Society of Mechanical Engineers, 2008");
      stringBuilder.AppendLine("4. API 650: \"Welded Steel Tanks for Oil Storage\", Tenth Edition, November 1998, American Petroleum Institute");
      return stringBuilder.ToString();
    }
  }

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.grpIntermediateLevel1Result = new GroupControl();
    this.tblIntermediateLevel1Result = new TableLayoutPanel();
    this.grpLevel1AssessmentResult = new GroupControl();
    this.tblLevel1Result = new TableLayoutPanel();
    this.grpLevel1Criteria = new GroupControl();
    this.tblLevel1Criteria = new TableLayoutPanel();
    this.grpLevel1Conclusion = new GroupControl();
    this.tblLevel1Conclusion = new TableLayoutPanel();
    this.grpLevel2Conclusion = new GroupControl();
    this.tblLevel2Conclusion = new TableLayoutPanel();
    this.grpLevel2Criteria = new GroupControl();
    this.tblLevel2Criteria = new TableLayoutPanel();
    this.grpLevel2AssessmentResult = new GroupControl();
    this.tblLevel2Result = new TableLayoutPanel();
    this.grpIntermediateLevel2Result = new GroupControl();
    this.tblIntermediateLevel2Result = new TableLayoutPanel();
    this.groupControl3 = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.txtWarningMessages = new MemoEdit();
    this.grpIntermediateLevel1Result.BeginInit();
    this.grpIntermediateLevel1Result.SuspendLayout();
    this.grpLevel1AssessmentResult.BeginInit();
    this.grpLevel1AssessmentResult.SuspendLayout();
    this.grpLevel1Criteria.BeginInit();
    this.grpLevel1Criteria.SuspendLayout();
    this.grpLevel1Conclusion.BeginInit();
    this.grpLevel1Conclusion.SuspendLayout();
    this.grpLevel2Conclusion.BeginInit();
    this.grpLevel2Conclusion.SuspendLayout();
    this.grpLevel2Criteria.BeginInit();
    this.grpLevel2Criteria.SuspendLayout();
    this.grpLevel2AssessmentResult.BeginInit();
    this.grpLevel2AssessmentResult.SuspendLayout();
    this.grpIntermediateLevel2Result.BeginInit();
    this.grpIntermediateLevel2Result.SuspendLayout();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.txtWarningMessages.Properties.BeginInit();
    this.SuspendLayout();
    this.grpIntermediateLevel1Result.AutoSize = true;
    this.grpIntermediateLevel1Result.Controls.Add((Control) this.tblIntermediateLevel1Result);
    this.grpIntermediateLevel1Result.Dock = DockStyle.Top;
    this.grpIntermediateLevel1Result.Location = new Point(0, 0);
    this.grpIntermediateLevel1Result.Name = "grpIntermediateLevel1Result";
    this.grpIntermediateLevel1Result.Size = new Size(681, 43);
    this.grpIntermediateLevel1Result.TabIndex = 2;
    this.grpIntermediateLevel1Result.Text = "Intermediate Level 1 Result";
    this.tblIntermediateLevel1Result.AutoSize = true;
    this.tblIntermediateLevel1Result.ColumnCount = 3;
    this.tblIntermediateLevel1Result.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateLevel1Result.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateLevel1Result.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateLevel1Result.Dock = DockStyle.Fill;
    this.tblIntermediateLevel1Result.Location = new Point(2, 21);
    this.tblIntermediateLevel1Result.Name = "tblIntermediateLevel1Result";
    this.tblIntermediateLevel1Result.Padding = new Padding(10);
    this.tblIntermediateLevel1Result.RowCount = 1;
    this.tblIntermediateLevel1Result.RowStyles.Add(new RowStyle());
    this.tblIntermediateLevel1Result.Size = new Size(677, 20);
    this.tblIntermediateLevel1Result.TabIndex = 0;
    this.grpLevel1AssessmentResult.AutoSize = true;
    this.grpLevel1AssessmentResult.Controls.Add((Control) this.tblLevel1Result);
    this.grpLevel1AssessmentResult.Dock = DockStyle.Top;
    this.grpLevel1AssessmentResult.Location = new Point(0, 43);
    this.grpLevel1AssessmentResult.Name = "grpLevel1AssessmentResult";
    this.grpLevel1AssessmentResult.Size = new Size(681, 43);
    this.grpLevel1AssessmentResult.TabIndex = 8;
    this.grpLevel1AssessmentResult.Text = "Level 1 Assessment Result";
    this.tblLevel1Result.AutoSize = true;
    this.tblLevel1Result.ColumnCount = 3;
    this.tblLevel1Result.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Result.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Result.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Result.Dock = DockStyle.Fill;
    this.tblLevel1Result.Location = new Point(2, 21);
    this.tblLevel1Result.Name = "tblLevel1Result";
    this.tblLevel1Result.Padding = new Padding(10);
    this.tblLevel1Result.RowCount = 1;
    this.tblLevel1Result.RowStyles.Add(new RowStyle());
    this.tblLevel1Result.Size = new Size(677, 20);
    this.tblLevel1Result.TabIndex = 0;
    this.grpLevel1Criteria.AutoSize = true;
    this.grpLevel1Criteria.Controls.Add((Control) this.tblLevel1Criteria);
    this.grpLevel1Criteria.Dock = DockStyle.Top;
    this.grpLevel1Criteria.Location = new Point(0, 86);
    this.grpLevel1Criteria.Name = "grpLevel1Criteria";
    this.grpLevel1Criteria.Size = new Size(681, 43);
    this.grpLevel1Criteria.TabIndex = 9;
    this.grpLevel1Criteria.Text = "Level 1 Assessment Criteria";
    this.tblLevel1Criteria.AutoSize = true;
    this.tblLevel1Criteria.ColumnCount = 3;
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.Dock = DockStyle.Fill;
    this.tblLevel1Criteria.Location = new Point(2, 21);
    this.tblLevel1Criteria.Name = "tblLevel1Criteria";
    this.tblLevel1Criteria.Padding = new Padding(10);
    this.tblLevel1Criteria.RowCount = 1;
    this.tblLevel1Criteria.RowStyles.Add(new RowStyle());
    this.tblLevel1Criteria.Size = new Size(677, 20);
    this.tblLevel1Criteria.TabIndex = 1;
    this.grpLevel1Conclusion.AutoSize = true;
    this.grpLevel1Conclusion.Controls.Add((Control) this.tblLevel1Conclusion);
    this.grpLevel1Conclusion.Dock = DockStyle.Top;
    this.grpLevel1Conclusion.Location = new Point(0, 129);
    this.grpLevel1Conclusion.Name = "grpLevel1Conclusion";
    this.grpLevel1Conclusion.Size = new Size(681, 43);
    this.grpLevel1Conclusion.TabIndex = 10;
    this.grpLevel1Conclusion.Text = "Level 1 Assessment Conclusion";
    this.tblLevel1Conclusion.AutoSize = true;
    this.tblLevel1Conclusion.ColumnCount = 3;
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.Dock = DockStyle.Fill;
    this.tblLevel1Conclusion.Location = new Point(2, 21);
    this.tblLevel1Conclusion.Name = "tblLevel1Conclusion";
    this.tblLevel1Conclusion.Padding = new Padding(10);
    this.tblLevel1Conclusion.RowCount = 1;
    this.tblLevel1Conclusion.RowStyles.Add(new RowStyle());
    this.tblLevel1Conclusion.Size = new Size(677, 20);
    this.tblLevel1Conclusion.TabIndex = 1;
    this.grpLevel2Conclusion.AutoSize = true;
    this.grpLevel2Conclusion.Controls.Add((Control) this.tblLevel2Conclusion);
    this.grpLevel2Conclusion.Dock = DockStyle.Top;
    this.grpLevel2Conclusion.Location = new Point(0, 301);
    this.grpLevel2Conclusion.Name = "grpLevel2Conclusion";
    this.grpLevel2Conclusion.Size = new Size(681, 43);
    this.grpLevel2Conclusion.TabIndex = 14;
    this.grpLevel2Conclusion.Text = "Level 2 Assessment Conclusion";
    this.tblLevel2Conclusion.AutoSize = true;
    this.tblLevel2Conclusion.ColumnCount = 3;
    this.tblLevel2Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Conclusion.Dock = DockStyle.Fill;
    this.tblLevel2Conclusion.Location = new Point(2, 21);
    this.tblLevel2Conclusion.Name = "tblLevel2Conclusion";
    this.tblLevel2Conclusion.Padding = new Padding(10);
    this.tblLevel2Conclusion.RowCount = 1;
    this.tblLevel2Conclusion.RowStyles.Add(new RowStyle());
    this.tblLevel2Conclusion.Size = new Size(677, 20);
    this.tblLevel2Conclusion.TabIndex = 1;
    this.grpLevel2Criteria.AutoSize = true;
    this.grpLevel2Criteria.Controls.Add((Control) this.tblLevel2Criteria);
    this.grpLevel2Criteria.Dock = DockStyle.Top;
    this.grpLevel2Criteria.Location = new Point(0, 258);
    this.grpLevel2Criteria.Name = "grpLevel2Criteria";
    this.grpLevel2Criteria.Size = new Size(681, 43);
    this.grpLevel2Criteria.TabIndex = 13;
    this.grpLevel2Criteria.Text = "Level 2 Assessment Criteria";
    this.tblLevel2Criteria.AutoSize = true;
    this.tblLevel2Criteria.ColumnCount = 3;
    this.tblLevel2Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Criteria.Dock = DockStyle.Fill;
    this.tblLevel2Criteria.Location = new Point(2, 21);
    this.tblLevel2Criteria.Name = "tblLevel2Criteria";
    this.tblLevel2Criteria.Padding = new Padding(10);
    this.tblLevel2Criteria.RowCount = 1;
    this.tblLevel2Criteria.RowStyles.Add(new RowStyle());
    this.tblLevel2Criteria.Size = new Size(677, 20);
    this.tblLevel2Criteria.TabIndex = 1;
    this.grpLevel2AssessmentResult.AutoSize = true;
    this.grpLevel2AssessmentResult.Controls.Add((Control) this.tblLevel2Result);
    this.grpLevel2AssessmentResult.Dock = DockStyle.Top;
    this.grpLevel2AssessmentResult.Location = new Point(0, 215);
    this.grpLevel2AssessmentResult.Name = "grpLevel2AssessmentResult";
    this.grpLevel2AssessmentResult.Size = new Size(681, 43);
    this.grpLevel2AssessmentResult.TabIndex = 12;
    this.grpLevel2AssessmentResult.Text = "Level 2 Assessment Result";
    this.tblLevel2Result.AutoSize = true;
    this.tblLevel2Result.ColumnCount = 3;
    this.tblLevel2Result.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Result.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Result.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Result.Dock = DockStyle.Fill;
    this.tblLevel2Result.Location = new Point(2, 21);
    this.tblLevel2Result.Name = "tblLevel2Result";
    this.tblLevel2Result.Padding = new Padding(10);
    this.tblLevel2Result.RowCount = 1;
    this.tblLevel2Result.RowStyles.Add(new RowStyle());
    this.tblLevel2Result.Size = new Size(677, 20);
    this.tblLevel2Result.TabIndex = 0;
    this.grpIntermediateLevel2Result.AutoSize = true;
    this.grpIntermediateLevel2Result.Controls.Add((Control) this.tblIntermediateLevel2Result);
    this.grpIntermediateLevel2Result.Dock = DockStyle.Top;
    this.grpIntermediateLevel2Result.Location = new Point(0, 172);
    this.grpIntermediateLevel2Result.Name = "grpIntermediateLevel2Result";
    this.grpIntermediateLevel2Result.Size = new Size(681, 43);
    this.grpIntermediateLevel2Result.TabIndex = 11;
    this.grpIntermediateLevel2Result.Text = "Intermediate Level 2 Result";
    this.tblIntermediateLevel2Result.AutoSize = true;
    this.tblIntermediateLevel2Result.ColumnCount = 3;
    this.tblIntermediateLevel2Result.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateLevel2Result.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateLevel2Result.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateLevel2Result.Dock = DockStyle.Fill;
    this.tblIntermediateLevel2Result.Location = new Point(2, 21);
    this.tblIntermediateLevel2Result.Name = "tblIntermediateLevel2Result";
    this.tblIntermediateLevel2Result.Padding = new Padding(10);
    this.tblIntermediateLevel2Result.RowCount = 1;
    this.tblIntermediateLevel2Result.RowStyles.Add(new RowStyle());
    this.tblIntermediateLevel2Result.Size = new Size(677, 20);
    this.tblIntermediateLevel2Result.TabIndex = 0;
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel3);
    this.groupControl3.Dock = DockStyle.Fill;
    this.groupControl3.Location = new Point(0, 344);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(681, 170);
    this.groupControl3.TabIndex = 15;
    this.groupControl3.Text = "Warning Messages";
    this.tableLayoutPanel3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 428f));
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 428f));
    this.tableLayoutPanel3.Size = new Size(677, 147);
    this.tableLayoutPanel3.TabIndex = 0;
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.MinimumSize = new Size(10, 10);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(651, 121);
    this.txtWarningMessages.TabIndex = 4;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.Controls.Add((Control) this.groupControl3);
    this.Controls.Add((Control) this.grpLevel2Conclusion);
    this.Controls.Add((Control) this.grpLevel2Criteria);
    this.Controls.Add((Control) this.grpLevel2AssessmentResult);
    this.Controls.Add((Control) this.grpIntermediateLevel2Result);
    this.Controls.Add((Control) this.grpLevel1Conclusion);
    this.Controls.Add((Control) this.grpLevel1Criteria);
    this.Controls.Add((Control) this.grpLevel1AssessmentResult);
    this.Controls.Add((Control) this.grpIntermediateLevel1Result);
    this.Name = nameof (vwResult);
    this.Size = new Size(681, 514);
    this.Load += new EventHandler(this.vwResult_Load);
    this.grpIntermediateLevel1Result.EndInit();
    this.grpIntermediateLevel1Result.ResumeLayout(false);
    this.grpIntermediateLevel1Result.PerformLayout();
    this.grpLevel1AssessmentResult.EndInit();
    this.grpLevel1AssessmentResult.ResumeLayout(false);
    this.grpLevel1AssessmentResult.PerformLayout();
    this.grpLevel1Criteria.EndInit();
    this.grpLevel1Criteria.ResumeLayout(false);
    this.grpLevel1Criteria.PerformLayout();
    this.grpLevel1Conclusion.EndInit();
    this.grpLevel1Conclusion.ResumeLayout(false);
    this.grpLevel1Conclusion.PerformLayout();
    this.grpLevel2Conclusion.EndInit();
    this.grpLevel2Conclusion.ResumeLayout(false);
    this.grpLevel2Conclusion.PerformLayout();
    this.grpLevel2Criteria.EndInit();
    this.grpLevel2Criteria.ResumeLayout(false);
    this.grpLevel2Criteria.PerformLayout();
    this.grpLevel2AssessmentResult.EndInit();
    this.grpLevel2AssessmentResult.ResumeLayout(false);
    this.grpLevel2AssessmentResult.PerformLayout();
    this.grpIntermediateLevel2Result.EndInit();
    this.grpIntermediateLevel2Result.ResumeLayout(false);
    this.grpIntermediateLevel2Result.PerformLayout();
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.tableLayoutPanel3.ResumeLayout(false);
    this.txtWarningMessages.Properties.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  private class ResultItems
  {
    public string MAT_OptionA { get; set; }

    public string MAT_OptionALabel { get; set; }

    public string MAT_OptionAUM { get; set; }

    public string MAT_OptionB { get; set; }

    public string MAT_OptionBLabel { get; set; }

    public string MAT_OptionBUM { get; set; }

    public double Level1MAT { get; set; }

    public string Level1MATLabel { get; set; }

    public string Level1MATUM { get; set; }

    public string CETGreaterThanMAT { get; set; }

    public string CETGreaterThanMATLabel { get; set; }

    public string MAT_Level2_MethodA { get; set; }

    public string MAT_Level2_MethodALabel { get; set; }

    public string MAT_Level2_MethodAUM { get; set; }

    public string MAT_Level2_MethodB { get; set; }

    public string MAT_Level2_MethodBLabel { get; set; }

    public string MAT_Level2_MethodBUM { get; set; }

    public double Level2MAT { get; set; }

    public string Level2MATLabel { get; set; }

    public string Level2MATUM { get; set; }

    public string CETGreaterThanMATLevel2 { get; set; }

    public string CETGreaterThanMATLevel2Label { get; set; }

    public string Level1Conclusion { get; set; }

    public bool Level1Result { get; set; }

    public string Level2Conclusion { get; set; }

    public bool Level2Result { get; set; }

    public bool ShowLevel2 { get; set; }
  }
}
