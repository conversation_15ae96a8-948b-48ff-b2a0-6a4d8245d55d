// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_8.Elbow.BrittleFracture.vwBrittleFracture
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.Common;
using IntegriWISE.FormView.Material;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Assessment.ASMEB31_8.Elbow.BrittleFracture;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_8.Elbow.BrittleFracture;

public class vwBrittleFracture : XtraUserControl, IBrittleFractureView, IAssessmentBaseView, IView
{
  private BrittleFracturePresenter _presenter;
  private IRecordView _recordView;
  private FormDirtyTracker _dirtyTracker;
  private IContainer components;
  private TableLayoutPanel tableLayoutPanel1;
  private LabelControl labelControl1;
  private LabelControl umLOSSe;
  private LabelControl umLOSSi;
  private LabelControl labelControl2;
  private LabelControl labelControl3;
  private LabelControl umFCAe;
  private LabelControl umFCAi;
  private LabelControl labelControl4;
  private TextEdit txtFCAi;
  private TextEdit txtFCAe;
  private TextEdit txtLOSSi;
  private TextEdit txtLOSSe;
  private DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider dxErrorProvider1;
  private LabelControl labelControl5;
  private TextEdit txtTg;
  private TextEdit txtCET;
  private LabelControl labelControl6;
  private LabelControl labelControl7;
  private LabelControl labelControl12;
  private LabelControl labelControl13;
  private LabelControl umtg;
  private LabelControl umCET;
  private LabelControl umImpactTemperature;
  private TextEdit txtImpactTemp;
  private LabelControl labelControl8;
  private LabelControl labelControl10;
  private ButtonEdit beMaterialSpecNo;
  private ComboBoxEdit cboPWHT;
  private ComboBoxEdit cboHydrotestPerformed;
  private ComboBoxEdit cboImpactTestAvailable;
  private LabelControl umHydrotestTemperature;
  private TextEdit txtHydrotestTemp;
  private LabelControl labelControl9;
  private CheckEdit chkLevel2;

  public vwBrittleFracture(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.AssessmentView = (IAssessmentBaseView) this;
    this._presenter = new BrittleFracturePresenter(this._recordView, (IBrittleFractureView) this);
  }

  private void vwBrittleFracture_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView);
    this._dirtyTracker.IsHandled = true;
    this.txtTg.AllowOnlyN4();
    this.txtCET.AllowOnlyD4N4AndNeg();
    this.txtImpactTemp.AllowOnlyD4N4AndNeg();
    this.txtHydrotestTemp.AllowOnlyD4N4AndNeg();
    this.txtFCAi.AllowOnlyN4();
    this.txtFCAe.AllowOnlyN4();
    this.txtLOSSi.AllowOnlyN4();
    this.txtLOSSe.AllowOnlyN4();
    this._presenter.LoadAssessment();
    this._dirtyTracker.IsHandled = false;
  }

  public int? AssessmentID { get; set; }

  public string ToughnessCurve { get; set; }

  public bool Level2
  {
    get => this.chkLevel2.Checked;
    set => this.chkLevel2.Checked = value;
  }

  public string Material
  {
    get => this.beMaterialSpecNo.Text;
    set => this.beMaterialSpecNo.Text = value;
  }

  public double? tg
  {
    get => Helpers.ParseNullDouble((object) this.txtTg.Text);
    set => this.txtTg.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? CET
  {
    get => Helpers.ParseNullDouble((object) this.txtCET.Text);
    set => this.txtCET.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool PWHT
  {
    get => this.cboPWHT.Text.ToLower() == "yes";
    set => this.cboPWHT.SelectedIndex = value ? 0 : 1;
  }

  public bool ImpactTested
  {
    get => this.cboImpactTestAvailable.Text.ToLower() == "yes";
    set => this.cboImpactTestAvailable.SelectedIndex = value ? 0 : 1;
  }

  public bool HydrotestPerformed
  {
    get => this.cboHydrotestPerformed.Text.ToLower() == "yes";
    set => this.cboHydrotestPerformed.SelectedIndex = value ? 0 : 1;
  }

  public double? ImpactTemperature
  {
    get => Helpers.ParseNullDouble((object) this.txtImpactTemp.Text);
    set => this.txtImpactTemp.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? HydrotestTemperature
  {
    get => Helpers.ParseNullDouble((object) this.txtHydrotestTemp.Text);
    set => this.txtHydrotestTemp.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? FCAi
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAi.Text);
    set => this.txtFCAi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? FCAe
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAe.Text);
    set => this.txtFCAe.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSi
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSi.Text);
    set => this.txtLOSSi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSe
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSe.Text);
    set => this.txtLOSSe.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMtg
  {
    set => this.umtg.Text = value;
  }

  public string UMCET
  {
    set => this.umCET.Text = value;
  }

  public string UMImpactTemperature
  {
    set => this.umImpactTemperature.Text = value;
  }

  public string UMFCAi
  {
    set => this.umFCAi.Text = value;
  }

  public string UMFCAe
  {
    set => this.umFCAe.Text = value;
  }

  public string UMLOSSi
  {
    set => this.umLOSSi.Text = value;
  }

  public string UMLOSSe
  {
    set => this.umLOSSe.Text = value;
  }

  public string UMHydrotestTemperature
  {
    set => this.umHydrotestTemperature.Text = value;
  }

  public string MaterialErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.beMaterialSpecNo, value);
  }

  public string ToughnessCurveErr
  {
    set
    {
    }
  }

  public string tgErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtTg, value);
  }

  public string CETErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtCET, value);
  }

  public string PWHTErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.cboPWHT, value);
  }

  public string ImpactTestedErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.cboImpactTestAvailable, value);
  }

  public string HydrotestPerformedErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.cboHydrotestPerformed, value);
  }

  public string ImpactTemperatureErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtImpactTemp, value);
  }

  public string FCAiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAi, value);
  }

  public string FCAeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAe, value);
  }

  public string LOSSiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSi, value);
  }

  public string LOSSeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSe, value);
  }

  public string HydrotestTemperatureErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtHydrotestTemp, value);
  }

  public string MaterialInfo
  {
    set => this.beMaterialSpecNo.ToolTip = value;
  }

  public string ToughnessCurveInfo
  {
    set
    {
    }
  }

  public string tgInfo
  {
    set => this.txtTg.ToolTip = value;
  }

  public string CETInfo
  {
    set => this.txtCET.ToolTip = value;
  }

  public string PWHTInfo
  {
    set => this.cboPWHT.ToolTip = value;
  }

  public string ImpactTestedInfo
  {
    set => this.cboImpactTestAvailable.ToolTip = value;
  }

  public string ImpactTemperatureInfo
  {
    set => this.txtImpactTemp.ToolTip = value;
  }

  public string HydrotestPerformedInfo
  {
    set => this.cboHydrotestPerformed.ToolTip = value;
  }

  public string FCAiInfo
  {
    set => this.txtFCAi.ToolTip = value;
  }

  public string FCAeInfo
  {
    set => this.txtFCAe.ToolTip = value;
  }

  public string LOSSiInfo
  {
    set => this.txtLOSSi.ToolTip = value;
  }

  public string LOSSeInfo
  {
    set => this.txtLOSSe.ToolTip = value;
  }

  public string HydrotestTemperatureInfo
  {
    set => this.txtHydrotestTemp.ToolTip = value;
  }

  public void ShowMaterialLookup()
  {
    frmLookupAPI579_Table_3_2_Vessel i579Table32Vessel = new frmLookupAPI579_Table_3_2_Vessel();
    if (i579Table32Vessel.ShowDialog() != DialogResult.OK)
      return;
    this._presenter.SetMaterial(i579Table32Vessel.Material);
  }

  public bool ValidateAssessment() => this._presenter.Validate();

  public void Save() => this._presenter.Save();

  public void ClearErrors() => this.dxErrorProvider1.ClearErrors();

  private void beMaterialSpecNo_ButtonClick(object sender, ButtonPressedEventArgs e)
  {
    this._presenter.ShowMaterialLookup();
  }

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    SerializableAppearanceObject appearance = new SerializableAppearanceObject();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.umHydrotestTemperature = new LabelControl();
    this.chkLevel2 = new CheckEdit();
    this.txtHydrotestTemp = new TextEdit();
    this.labelControl9 = new LabelControl();
    this.labelControl7 = new LabelControl();
    this.txtCET = new TextEdit();
    this.umCET = new LabelControl();
    this.labelControl6 = new LabelControl();
    this.txtTg = new TextEdit();
    this.umtg = new LabelControl();
    this.labelControl5 = new LabelControl();
    this.labelControl12 = new LabelControl();
    this.labelControl4 = new LabelControl();
    this.txtLOSSe = new TextEdit();
    this.umLOSSe = new LabelControl();
    this.umLOSSi = new LabelControl();
    this.txtLOSSi = new TextEdit();
    this.labelControl3 = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.labelControl1 = new LabelControl();
    this.labelControl8 = new LabelControl();
    this.labelControl13 = new LabelControl();
    this.txtFCAe = new TextEdit();
    this.txtFCAi = new TextEdit();
    this.txtImpactTemp = new TextEdit();
    this.umFCAe = new LabelControl();
    this.umFCAi = new LabelControl();
    this.umImpactTemperature = new LabelControl();
    this.labelControl10 = new LabelControl();
    this.beMaterialSpecNo = new ButtonEdit();
    this.cboPWHT = new ComboBoxEdit();
    this.cboHydrotestPerformed = new ComboBoxEdit();
    this.cboImpactTestAvailable = new ComboBoxEdit();
    this.dxErrorProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(this.components);
    this.tableLayoutPanel1.SuspendLayout();
    this.chkLevel2.Properties.BeginInit();
    this.txtHydrotestTemp.Properties.BeginInit();
    this.txtCET.Properties.BeginInit();
    this.txtTg.Properties.BeginInit();
    this.txtLOSSe.Properties.BeginInit();
    this.txtLOSSi.Properties.BeginInit();
    this.txtFCAe.Properties.BeginInit();
    this.txtFCAi.Properties.BeginInit();
    this.txtImpactTemp.Properties.BeginInit();
    this.beMaterialSpecNo.Properties.BeginInit();
    this.cboPWHT.Properties.BeginInit();
    this.cboHydrotestPerformed.Properties.BeginInit();
    this.cboImpactTestAvailable.Properties.BeginInit();
    ((ISupportInitialize) this.dxErrorProvider1).BeginInit();
    this.SuspendLayout();
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.umHydrotestTemperature, 2, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkLevel2, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtHydrotestTemp, 1, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl9, 0, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl7, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtCET, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.umCET, 2, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl6, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtTg, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.umtg, 2, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl5, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl12, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl4, 0, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSe, 1, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSe, 2, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSi, 2, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSi, 1, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl3, 0, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 0, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl8, 0, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl13, 0, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAe, 1, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAi, 1, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtImpactTemp, 1, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAe, 2, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAi, 2, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.umImpactTemperature, 2, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl10, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.beMaterialSpecNo, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.cboPWHT, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.cboHydrotestPerformed, 1, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.cboImpactTestAvailable, 1, 7);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 14;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(597, 557);
    this.tableLayoutPanel1.TabIndex = 0;
    this.umHydrotestTemperature.Location = new Point(329, 185);
    this.umHydrotestTemperature.Name = "umHydrotestTemperature";
    this.umHydrotestTemperature.Size = new Size(41, 13);
    this.umHydrotestTemperature.TabIndex = 20;
    this.umHydrotestTemperature.Text = "measure";
    this.chkLevel2.Location = new Point(13, 13);
    this.chkLevel2.Name = "chkLevel2";
    this.chkLevel2.Properties.Caption = "Level 2";
    this.chkLevel2.Size = new Size(120, 19);
    this.chkLevel2.TabIndex = 0;
    this.txtHydrotestTemp.Location = new Point(225, 183);
    this.txtHydrotestTemp.Margin = new Padding(1);
    this.txtHydrotestTemp.Name = "txtHydrotestTemp";
    this.txtHydrotestTemp.Size = new Size(100, 20);
    this.txtHydrotestTemp.TabIndex = 19;
    this.labelControl9.Location = new Point(13, 185);
    this.labelControl9.Name = "labelControl9";
    this.labelControl9.Size = new Size(113, 13);
    this.labelControl9.TabIndex = 18;
    this.labelControl9.Text = "Hydrotest Temperature";
    this.labelControl7.Location = new Point(13, 75);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(145, 13);
    this.labelControl7.TabIndex = 6;
    this.labelControl7.Text = "Critical Exposure Temperature";
    this.txtCET.Location = new Point(225, 73);
    this.txtCET.Margin = new Padding(1);
    this.txtCET.Name = "txtCET";
    this.txtCET.Size = new Size(100, 20);
    this.txtCET.TabIndex = 7;
    this.umCET.Location = new Point(329, 75);
    this.umCET.Name = "umCET";
    this.umCET.Size = new Size(41, 13);
    this.umCET.TabIndex = 8;
    this.umCET.Text = "measure";
    this.labelControl6.Location = new Point(13, 53);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(157, 13);
    this.labelControl6.TabIndex = 3;
    this.labelControl6.Text = "Uncorroded Governing Thickness";
    this.txtTg.Location = new Point(225, 51);
    this.txtTg.Margin = new Padding(1);
    this.txtTg.Name = "txtTg";
    this.txtTg.Size = new Size(100, 20);
    this.txtTg.TabIndex = 4;
    this.umtg.Location = new Point(329, 53);
    this.umtg.Name = "umtg";
    this.umtg.Size = new Size(41, 13);
    this.umtg.TabIndex = 5;
    this.umtg.Text = "measure";
    this.labelControl5.Location = new Point(13, 33);
    this.labelControl5.Name = "labelControl5";
    this.labelControl5.Size = new Size(96 /*0x60*/, 13);
    this.labelControl5.TabIndex = 1;
    this.labelControl5.Text = "Component Material";
    this.labelControl12.Location = new Point(13, 97);
    this.labelControl12.Name = "labelControl12";
    this.labelControl12.Size = new Size(29, 13);
    this.labelControl12.TabIndex = 9;
    this.labelControl12.Text = "PWHT";
    this.labelControl4.Location = new Point(13, 273);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(171, 13);
    this.labelControl4.TabIndex = 30;
    this.labelControl4.Text = "External Uniform Metal Loss, LOSSe";
    this.txtLOSSe.Location = new Point(225, 271);
    this.txtLOSSe.Margin = new Padding(1);
    this.txtLOSSe.Name = "txtLOSSe";
    this.txtLOSSe.Size = new Size(100, 20);
    this.txtLOSSe.TabIndex = 31 /*0x1F*/;
    this.umLOSSe.Location = new Point(329, 273);
    this.umLOSSe.Name = "umLOSSe";
    this.umLOSSe.Size = new Size(41, 13);
    this.umLOSSe.TabIndex = 32 /*0x20*/;
    this.umLOSSe.Text = "measure";
    this.umLOSSi.Location = new Point(329, 251);
    this.umLOSSi.Name = "umLOSSi";
    this.umLOSSi.Size = new Size(41, 13);
    this.umLOSSi.TabIndex = 29;
    this.umLOSSi.Text = "measure";
    this.txtLOSSi.Location = new Point(225, 249);
    this.txtLOSSi.Margin = new Padding(1);
    this.txtLOSSi.Name = "txtLOSSi";
    this.txtLOSSi.Size = new Size(100, 20);
    this.txtLOSSi.TabIndex = 28;
    this.labelControl3.Location = new Point(13, 251);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(165, 13);
    this.labelControl3.TabIndex = 27;
    this.labelControl3.Text = "Internal Uniform Metal Loss, LOSSi";
    this.labelControl2.Location = new Point(13, 229);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(208 /*0xD0*/, 13);
    this.labelControl2.TabIndex = 24;
    this.labelControl2.Text = "External Future Corrosion Allowance, FCAe";
    this.labelControl1.Location = new Point(13, 207);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(202, 13);
    this.labelControl1.TabIndex = 21;
    this.labelControl1.Text = "Internal Future Corrosion Allowance, FCAi";
    this.labelControl8.Location = new Point(13, 163);
    this.labelControl8.Name = "labelControl8";
    this.labelControl8.Size = new Size(139, 13);
    this.labelControl8.TabIndex = 15;
    this.labelControl8.Text = "Max impact test temperature";
    this.labelControl13.Location = new Point(13, 141);
    this.labelControl13.Name = "labelControl13";
    this.labelControl13.Size = new Size(130, 13);
    this.labelControl13.TabIndex = 13;
    this.labelControl13.Text = "Impact test result available";
    this.txtFCAe.Location = new Point(225, 227);
    this.txtFCAe.Margin = new Padding(1);
    this.txtFCAe.Name = "txtFCAe";
    this.txtFCAe.Size = new Size(100, 20);
    this.txtFCAe.TabIndex = 25;
    this.txtFCAi.Location = new Point(225, 205);
    this.txtFCAi.Margin = new Padding(1);
    this.txtFCAi.Name = "txtFCAi";
    this.txtFCAi.Size = new Size(100, 20);
    this.txtFCAi.TabIndex = 22;
    this.txtImpactTemp.Location = new Point(225, 161);
    this.txtImpactTemp.Margin = new Padding(1);
    this.txtImpactTemp.Name = "txtImpactTemp";
    this.txtImpactTemp.Size = new Size(100, 20);
    this.txtImpactTemp.TabIndex = 16 /*0x10*/;
    this.umFCAe.Location = new Point(329, 229);
    this.umFCAe.Name = "umFCAe";
    this.umFCAe.Size = new Size(41, 13);
    this.umFCAe.TabIndex = 26;
    this.umFCAe.Text = "measure";
    this.umFCAi.Location = new Point(329, 207);
    this.umFCAi.Name = "umFCAi";
    this.umFCAi.Size = new Size(41, 13);
    this.umFCAi.TabIndex = 23;
    this.umFCAi.Text = "measure";
    this.umImpactTemperature.Location = new Point(329, 163);
    this.umImpactTemperature.Name = "umImpactTemperature";
    this.umImpactTemperature.Size = new Size(41, 13);
    this.umImpactTemperature.TabIndex = 17;
    this.umImpactTemperature.Text = "measure";
    this.labelControl10.Location = new Point(13, 119);
    this.labelControl10.Name = "labelControl10";
    this.labelControl10.Size = new Size(112 /*0x70*/, 13);
    this.labelControl10.TabIndex = 11;
    this.labelControl10.Text = "Hydrotest Performance";
    this.tableLayoutPanel1.SetColumnSpan((Control) this.beMaterialSpecNo, 2);
    this.beMaterialSpecNo.Location = new Point(224 /*0xE0*/, 30);
    this.beMaterialSpecNo.Margin = new Padding(0);
    this.beMaterialSpecNo.Name = "beMaterialSpecNo";
    this.beMaterialSpecNo.Properties.Appearance.Options.UseFont = true;
    this.beMaterialSpecNo.Properties.AppearanceDisabled.Options.UseFont = true;
    this.beMaterialSpecNo.Properties.AppearanceFocused.Options.UseFont = true;
    this.beMaterialSpecNo.Properties.AppearanceReadOnly.BackColor = Color.White;
    this.beMaterialSpecNo.Properties.AppearanceReadOnly.Options.UseBackColor = true;
    this.beMaterialSpecNo.Properties.AppearanceReadOnly.Options.UseFont = true;
    appearance.Options.UseFont = true;
    this.beMaterialSpecNo.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Ellipsis, "", -1, true, true, false, ImageLocation.MiddleCenter, (Image) null, new KeyShortcut(Keys.None), (AppearanceObject) appearance, "", (object) null, (SuperToolTip) null, true)
    });
    this.beMaterialSpecNo.Properties.MaxLength = 10;
    this.beMaterialSpecNo.Properties.ReadOnly = true;
    this.beMaterialSpecNo.Size = new Size(350, 20);
    this.beMaterialSpecNo.TabIndex = 2;
    this.beMaterialSpecNo.ToolTip = "Select the material grade of the component you want to assess from the drop-down list.";
    this.beMaterialSpecNo.ButtonClick += new ButtonPressedEventHandler(this.beMaterialSpecNo_ButtonClick);
    this.cboPWHT.Location = new Point(225, 95);
    this.cboPWHT.Margin = new Padding(1);
    this.cboPWHT.Name = "cboPWHT";
    this.cboPWHT.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Combo)
    });
    this.cboPWHT.Properties.Items.AddRange(new object[2]
    {
      (object) "Yes",
      (object) "No"
    });
    this.cboPWHT.Size = new Size(100, 20);
    this.cboPWHT.TabIndex = 10;
    this.cboHydrotestPerformed.Location = new Point(225, 117);
    this.cboHydrotestPerformed.Margin = new Padding(1);
    this.cboHydrotestPerformed.Name = "cboHydrotestPerformed";
    this.cboHydrotestPerformed.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Combo)
    });
    this.cboHydrotestPerformed.Properties.Items.AddRange(new object[2]
    {
      (object) "Yes",
      (object) "No"
    });
    this.cboHydrotestPerformed.Size = new Size(100, 20);
    this.cboHydrotestPerformed.TabIndex = 12;
    this.cboImpactTestAvailable.Location = new Point(225, 139);
    this.cboImpactTestAvailable.Margin = new Padding(1);
    this.cboImpactTestAvailable.Name = "cboImpactTestAvailable";
    this.cboImpactTestAvailable.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Combo)
    });
    this.cboImpactTestAvailable.Properties.Items.AddRange(new object[2]
    {
      (object) "Yes",
      (object) "No"
    });
    this.cboImpactTestAvailable.Size = new Size(100, 20);
    this.cboImpactTestAvailable.TabIndex = 14;
    this.dxErrorProvider1.ContainerControl = (ContainerControl) this;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = nameof (vwBrittleFracture);
    this.Size = new Size(597, 557);
    this.Load += new EventHandler(this.vwBrittleFracture_Load);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.chkLevel2.Properties.EndInit();
    this.txtHydrotestTemp.Properties.EndInit();
    this.txtCET.Properties.EndInit();
    this.txtTg.Properties.EndInit();
    this.txtLOSSe.Properties.EndInit();
    this.txtLOSSi.Properties.EndInit();
    this.txtFCAe.Properties.EndInit();
    this.txtFCAi.Properties.EndInit();
    this.txtImpactTemp.Properties.EndInit();
    this.beMaterialSpecNo.Properties.EndInit();
    this.cboPWHT.Properties.EndInit();
    this.cboHydrotestPerformed.Properties.EndInit();
    this.cboImpactTestAvailable.Properties.EndInit();
    ((ISupportInitialize) this.dxErrorProvider1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
