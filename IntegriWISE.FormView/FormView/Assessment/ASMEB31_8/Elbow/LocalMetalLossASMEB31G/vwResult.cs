// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_8.Elbow.LocalMetalLossASMEB31G.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEB31_8.Elbow.LocalMetalLossASMEB31G;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_8.Elbow.LocalMetalLossASMEB31G;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private IRecordView _recordView;
  private ResultPresenter _presenter;
  private bool _level2Performed;
  private bool _level1OriginalPassed;
  private bool _level2OriginalPassed;
  private bool _level1ModifiedPassed;
  private bool _level2ModifiedPassed;
  private bool _level2Visible;
  private IContainer components;
  private LabelControl lbSoOriginal;
  private MemoEdit txtWarningMessages;
  private GroupControl grcWarnings;
  private TableLayoutPanel tableLayoutPanel3;
  private GroupControl grcAssessmentResultsOriginal;
  private TableLayoutPanel tblpIntermediateResults;
  private LabelControl lbUMSflowOriginal;
  private TextEdit txtSflowOriginal;
  private LabelControl lbUMSoOriginal;
  private TextEdit txtSoOriginal;
  private LabelControl lbSflowOriginal;
  private LabelControl lbSFailureOriginalL1;
  private LabelControl lbPressureOriginalL1;
  private LabelControl lbUMSFailureOriginalL1;
  private TextEdit txtSFailureOriginalL1;
  private LabelControl lbUMPressureOriginalL1;
  private TextEdit txtPressureOriginalL1;
  private GroupControl grcAssessmentConclusionOriginalL1;
  private TableLayoutPanel tableLayoutPanel22;
  private LabelControl lbConclusionOriginalLevel1;
  private GroupControl grcAssessmentConclusionOriginalL2;
  private TableLayoutPanel tableLayoutPanel27;
  private LabelControl lbConclusionOriginalLevel2;
  private GroupControl grcAssessmentResultsOriginalL2;
  private TableLayoutPanel tableLayoutPanel7;
  private TextEdit txtminRSFiOriginal;
  private LabelControl lbminRSFiOriginal;
  private GroupControl grcAssessmentCriteriaOriginalL1;
  private TableLayoutPanel tbpMawp;
  private TextEdit txtSFailureCriteriaOriginalL1;
  private LabelControl lbSFailureCriteriaOriginalL1;
  private LabelControl lbPressureCriteriaOriginalL1;
  private TextEdit txtPressureCriteriaOriginalL1;
  private GroupControl grcAssessmentConclusionModifiedL1;
  private TableLayoutPanel tableLayoutPanel2;
  private LabelControl lbConclusionModifiedLevel1;
  private GroupControl grcAssessmentCriteriaModifiedL1;
  private TableLayoutPanel tableLayoutPanel5;
  private LabelControl lbSFailureCriteriaModifiedL1;
  private LabelControl lbPressureCriteriaModifiedL1;
  private TextEdit txtSFailureCriteriaModifiedL1;
  private TextEdit txtPressureCriteriaModifiedL1;
  private GroupControl grcAssessmentResultsModified;
  private TableLayoutPanel tableLayoutPanel6;
  private TextEdit txtPressureModifiedL1;
  private TextEdit txtSFailureModifiedL1;
  private TextEdit txtSflowModified;
  private TextEdit txtSoModified;
  private LabelControl lbUMPressureModifiedL1;
  private LabelControl lbUMSFailureModifiedL1;
  private LabelControl lbUMSflowModified;
  private LabelControl lbUMSoModified;
  private LabelControl lbSoModified;
  private LabelControl lbSflowModified;
  private LabelControl lbSFailureModifiedL1;
  private LabelControl lbPressureModifiedL1;
  private LabelControl lbPressureOriginalL2;
  private LabelControl lbSFailureOriginalL2;
  private TextEdit txtSFailureOriginalL2;
  private TextEdit txtPressureOriginalL2;
  private LabelControl lbUMSFailureOriginalL2;
  private LabelControl lbUMPressureOriginalL2;
  private GroupControl grcAssessmentCriteriaOriginalL2;
  private TableLayoutPanel tableLayoutPanel8;
  private LabelControl lbSFailureCriteriaOriginalL2;
  private LabelControl lbPressureCriteriaOriginalL2;
  private TextEdit txtSFailureCriteriaOriginalL2;
  private TextEdit txtPressureCriteriaOriginalL2;
  private GroupControl grcAssessmentConclusionModifiedL2;
  private TableLayoutPanel tableLayoutPanel4;
  private LabelControl lbConclusionModifiedLevel2;
  private GroupControl grcAssessmentCriteriaModifiedL2;
  private TableLayoutPanel tableLayoutPanel9;
  private LabelControl lbSFailureCriteriaModifiedL2;
  private LabelControl lbPressureCriteriaModifiedL2;
  private TextEdit txtSFailureCriteriaModifiedL2;
  private TextEdit txtPressureCriteriaModifiedL2;
  private GroupControl grcAssessmentResultsModifiedL2;
  private TableLayoutPanel tableLayoutPanel10;
  private LabelControl lbPressureModifiedL2;
  private TextEdit txtminRSFiModified;
  private LabelControl lbminRSFiModified;
  private LabelControl lbSFailureModifiedL2;
  private TextEdit txtSFailureModifiedL2;
  private TextEdit txtPressureModifiedL2;
  private LabelControl lbUMSFailureModifiedL2;
  private LabelControl lbUMPressureModifiedL2;
  private PanelControl pcBottom;

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  public bool Calculate() => this._presenter.Calculate();

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public bool Level2
  {
    get => this._level2Performed;
    set => this._level2Performed = value;
  }

  public bool Level2Visible
  {
    get => this._level2Visible;
    set
    {
      this._level2Visible = value;
      if (!value)
        this.HideLevel2Results();
      else
        this.ShowLevel2Results();
    }
  }

  public double? So
  {
    get => Helpers.ParseNullDouble((object) this.txtSoOriginal.Text);
    set => this.txtSoOriginal.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SoLabel
  {
    get => this.lbSoOriginal.Text;
    set => this.lbSoOriginal.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMSo
  {
    get => this.lbUMSoOriginal.Text;
    set => this.lbUMSoOriginal.Text = value;
  }

  public double? SflowOriginal
  {
    get => Helpers.ParseNullDouble((object) this.txtSflowOriginal.Text);
    set => this.txtSflowOriginal.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SflowOriginalLabel
  {
    get => this.lbSflowOriginal.Text;
    set => this.lbSflowOriginal.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMSflowOriginal
  {
    get => this.lbUMSflowOriginal.Text;
    set => this.lbUMSflowOriginal.Text = value;
  }

  public double? SFailureOriginalL1
  {
    get => Helpers.ParseNullDouble((object) this.txtSFailureOriginalL1.Text);
    set => this.txtSFailureOriginalL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SFailureOriginalL1Label
  {
    get => this.lbSFailureOriginalL1.Text;
    set => this.lbSFailureOriginalL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMSFailureOriginalL1
  {
    get => this.lbUMSFailureOriginalL1.Text;
    set => this.lbUMSFailureOriginalL1.Text = value;
  }

  public double? PressureOriginalL1
  {
    get => Helpers.ParseNullDouble((object) this.txtPressureOriginalL1.Text);
    set => this.txtPressureOriginalL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string PressureOriginalL1Label
  {
    get => this.lbPressureOriginalL1.Text;
    set => this.lbPressureOriginalL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMPressureOriginalL1
  {
    get => this.lbUMPressureOriginalL1.Text;
    set => this.lbUMPressureOriginalL1.Text = value;
  }

  public bool SFailureCriteriaOriginalL1
  {
    get => Helpers.ParseObjectToBool((object) this.txtSFailureCriteriaOriginalL1.Text);
    set => this.txtSFailureCriteriaOriginalL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SFailureCriteriaOriginalL1Label
  {
    get => this.lbSFailureCriteriaOriginalL1.Text;
    set => this.lbSFailureCriteriaOriginalL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool PressureCriteriaOriginalL1
  {
    get => Helpers.ParseObjectToBool((object) this.txtPressureCriteriaOriginalL1.Text);
    set => this.txtPressureCriteriaOriginalL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string PressureCriteriaOriginalL1Label
  {
    get => this.lbPressureCriteriaOriginalL1.Text;
    set => this.lbPressureCriteriaOriginalL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool Level1OriginalPassed
  {
    get => this._level1OriginalPassed;
    set
    {
      this._level1OriginalPassed = value;
      if (value)
      {
        this.HideLevel2Results();
        this.lbConclusionOriginalLevel1.ForeColor = Color.Green;
      }
      else
        this.lbConclusionOriginalLevel1.ForeColor = Color.DarkRed;
    }
  }

  public string Level1OriginalAssessmentConclusion
  {
    get => this.lbConclusionOriginalLevel1.Text;
    set => this.lbConclusionOriginalLevel1.Text = value;
  }

  public double? SoModified
  {
    get => Helpers.ParseNullDouble((object) this.txtSoModified.Text);
    set => this.txtSoModified.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SoModifiedLabel
  {
    get => this.lbSoModified.Text;
    set => this.lbSoModified.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMSoModified
  {
    get => this.lbUMSoModified.Text;
    set => this.lbUMSoModified.Text = value;
  }

  public double? SflowModified
  {
    get => Helpers.ParseNullDouble((object) this.txtSflowModified.Text);
    set => this.txtSflowModified.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SflowModifiedLabel
  {
    get => this.lbSflowModified.Text;
    set => this.lbSflowModified.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMSflowModified
  {
    get => this.lbUMSflowModified.Text;
    set => this.lbUMSflowModified.Text = value;
  }

  public double? SFailureModifiedL1
  {
    get => Helpers.ParseNullDouble((object) this.txtSFailureModifiedL1.Text);
    set => this.txtSFailureModifiedL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SFailureModifiedL1Label
  {
    get => this.lbSFailureModifiedL1.Text;
    set => this.lbSFailureModifiedL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMSFailureModifiedL1
  {
    get => this.lbUMSFailureModifiedL1.Text;
    set => this.lbUMSFailureModifiedL1.Text = value;
  }

  public double? PressureModifiedL1
  {
    get => Helpers.ParseNullDouble((object) this.txtPressureModifiedL1.Text);
    set => this.txtPressureModifiedL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string PressureModifiedL1Label
  {
    get => this.lbPressureModifiedL1.Text;
    set => this.lbPressureModifiedL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMPressureModifiedL1
  {
    get => this.lbUMPressureModifiedL1.Text;
    set => this.lbUMPressureModifiedL1.Text = value;
  }

  public bool SFailureCriteriaModifiedL1
  {
    get => Helpers.ParseObjectToBool((object) this.txtSFailureCriteriaModifiedL1.Text);
    set => this.txtSFailureCriteriaModifiedL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SFailureCriteriaModifiedL1Label
  {
    get => this.lbSFailureCriteriaModifiedL1.Text;
    set => this.lbSFailureCriteriaModifiedL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool PressureCriteriaModifiedL1
  {
    get => Helpers.ParseObjectToBool((object) this.txtPressureCriteriaModifiedL1.Text);
    set => this.txtPressureCriteriaModifiedL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string PressureCriteriaModifiedL1Label
  {
    get => this.lbPressureCriteriaModifiedL1.Text;
    set => this.lbPressureCriteriaModifiedL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool Level1ModifiedPassed
  {
    get => this._level1ModifiedPassed;
    set
    {
      this._level1ModifiedPassed = value;
      if (value)
        this.lbConclusionModifiedLevel1.ForeColor = Color.Green;
      else
        this.lbConclusionModifiedLevel1.ForeColor = Color.DarkRed;
    }
  }

  public string Level1ModifiedAssessmentConclusion
  {
    get => this.lbConclusionModifiedLevel1.Text;
    set => this.lbConclusionModifiedLevel1.Text = value;
  }

  public double? minRSFiOriginal
  {
    get => Helpers.ParseNullDouble((object) this.txtminRSFiOriginal.Text);
    set => this.txtminRSFiOriginal.Text = Helpers.ParseObjectToString((object) value);
  }

  public string minRSFiOriginalLabel
  {
    get => this.lbminRSFiOriginal.Text;
    set => this.lbminRSFiOriginal.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? SFailureOriginalL2
  {
    get => Helpers.ParseNullDouble((object) this.txtSFailureOriginalL2.Text);
    set => this.txtSFailureOriginalL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SFailureOriginalL2Label
  {
    get => this.lbSFailureOriginalL2.Text;
    set => this.lbSFailureOriginalL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMSFailureOriginalL2
  {
    get => this.lbUMSFailureOriginalL2.Text;
    set => this.lbUMSFailureOriginalL2.Text = value;
  }

  public double? PressureOriginalL2
  {
    get => Helpers.ParseNullDouble((object) this.txtPressureOriginalL2.Text);
    set => this.txtPressureOriginalL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string PressureOriginalL2Label
  {
    get => this.lbPressureOriginalL2.Text;
    set => this.lbPressureOriginalL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMPressureOriginalL2
  {
    get => this.lbUMPressureOriginalL2.Text;
    set => this.lbUMPressureOriginalL2.Text = value;
  }

  public bool SFailureCriteriaOriginalL2
  {
    get => Helpers.ParseObjectToBool((object) this.txtSFailureCriteriaOriginalL2.Text);
    set => this.txtSFailureCriteriaOriginalL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SFailureCriteriaOriginalL2Label
  {
    get => this.lbSFailureCriteriaOriginalL2.Text;
    set => this.lbSFailureCriteriaOriginalL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool PressureCriteriaOriginalL2
  {
    get => Helpers.ParseObjectToBool((object) this.txtPressureCriteriaOriginalL2.Text);
    set => this.txtPressureCriteriaOriginalL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string PressureCriteriaOriginalL2Label
  {
    get => this.lbPressureCriteriaOriginalL2.Text;
    set => this.lbPressureCriteriaOriginalL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool Level2OriginalPassed
  {
    get => this._level2OriginalPassed;
    set
    {
      this._level2OriginalPassed = value;
      if (value)
        this.lbConclusionOriginalLevel2.ForeColor = Color.DarkGreen;
      else
        this.lbConclusionOriginalLevel2.ForeColor = Color.DarkRed;
    }
  }

  public string Level2OriginalAssessmentConclusion
  {
    get => this.lbConclusionOriginalLevel2.Text;
    set => this.lbConclusionOriginalLevel2.Text = value;
  }

  public double? minRSFiModified
  {
    get => Helpers.ParseNullDouble((object) this.txtminRSFiModified.Text);
    set => this.txtminRSFiModified.Text = Helpers.ParseObjectToString((object) value);
  }

  public string minRSFiModifiedLabel
  {
    get => this.lbminRSFiModified.Text;
    set => this.lbminRSFiModified.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? SFailureModifiedL2
  {
    get => Helpers.ParseNullDouble((object) this.txtSFailureModifiedL2.Text);
    set => this.txtSFailureModifiedL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SFailureModifiedL2Label
  {
    get => this.lbSFailureModifiedL2.Text;
    set => this.lbSFailureModifiedL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMSFailureModifiedL2
  {
    get => this.lbUMSFailureModifiedL2.Text;
    set => this.lbUMSFailureModifiedL2.Text = value;
  }

  public double? PressureModifiedL2
  {
    get => Helpers.ParseNullDouble((object) this.txtPressureModifiedL2.Text);
    set => this.txtPressureModifiedL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string PressureModifiedL2Label
  {
    get => this.lbPressureModifiedL2.Text;
    set => this.lbPressureModifiedL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMPressureModifiedL2
  {
    get => this.lbUMPressureModifiedL2.Text;
    set => this.lbUMPressureModifiedL2.Text = value;
  }

  public bool SFailureCriteriaModifiedL2
  {
    get => Helpers.ParseObjectToBool((object) this.txtSFailureCriteriaModifiedL2.Text);
    set => this.txtSFailureCriteriaModifiedL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string SFailureCriteriaModifiedL2Label
  {
    get => this.lbSFailureCriteriaModifiedL2.Text;
    set => this.lbSFailureCriteriaModifiedL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool PressureCriteriaModifiedL2
  {
    get => Helpers.ParseObjectToBool((object) this.txtPressureCriteriaModifiedL2.Text);
    set => this.txtPressureCriteriaModifiedL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string PressureCriteriaModifiedL2Label
  {
    get => this.lbPressureCriteriaModifiedL2.Text;
    set => this.lbPressureCriteriaModifiedL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool Level2ModifiedPassed
  {
    get => this._level2ModifiedPassed;
    set
    {
      this._level2ModifiedPassed = value;
      if (value)
        this.lbConclusionModifiedLevel2.ForeColor = Color.DarkGreen;
      else
        this.lbConclusionModifiedLevel2.ForeColor = Color.DarkRed;
    }
  }

  public string Level2ModifiedAssessmentConclusion
  {
    get => this.lbConclusionModifiedLevel2.Text;
    set => this.lbConclusionModifiedLevel2.Text = value;
  }

  public string ResultMessages
  {
    get => this.txtWarningMessages.Text;
    set => this.txtWarningMessages.Text = value;
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public string CodeEdition => this._presenter.CodeEdition;

  public string Title => "Assessment to ASME B31G: Local Metal Loss.";

  public string Introduction
  {
    get
    {
      return "The B31G-2009 procedure scope includes all pipelines within the scope of the pipeline codes that are part of ANSI/ASME B31 code for pressure piping. The procedure examines the ability of a cylindrical pipe with a corrosion defect to withstand an internal pressure.\n\nA Level 1 evaluation is simple calculation that relies on single measurements of the maximum depth and an axial extent of metal loss. The following assessment calculates the estimated failure stress based on the maximum depth of the corroded area and longitudinal extent of the corroded area.";
    }
  }

  public string CommentsAndAssumptions => string.Empty;

  public string References
  {
    get
    {
      return "ASME B31G - 2009 (Revision of ASME B31G - 1991): 'Manual for Determining the Remaining Strength of Corroded Pipelines. Supplement to ASME B31 Code for Pressure Piping'. The American Society of Mechanical Engineers.";
    }
  }

  public string Limitations
  {
    get
    {
      return "1. The corrosion damage is on weldable pipeline categorised as carbon steel or high strength low alloy steels.\n2. The procedure applies only to defects in the body of linepipe caused by electrolytic or galvanic corrosion. This entails the relatively smooth contour which causes low stress concentrations.\n3. The procedure should not be used to evaluate corrosion which passes through girth or longitudinal welds, or defects caused by mechanical damage.\n4. The criteria are based on internal pressure only.\n5. The procedure does not predict leaks or rupture failure.";
    }
  }

  private void HideLevel2Results()
  {
    this.grcAssessmentResultsOriginalL2.Visible = false;
    this.grcAssessmentCriteriaOriginalL2.Visible = false;
    this.grcAssessmentConclusionOriginalL2.Visible = false;
    this.grcAssessmentResultsModifiedL2.Visible = false;
    this.grcAssessmentCriteriaModifiedL2.Visible = false;
    this.grcAssessmentConclusionModifiedL2.Visible = false;
  }

  private void ShowLevel2Results()
  {
    this.grcAssessmentResultsOriginalL2.Visible = true;
    this.grcAssessmentCriteriaOriginalL2.Visible = true;
    this.grcAssessmentConclusionOriginalL2.Visible = true;
    this.grcAssessmentResultsModifiedL2.Visible = true;
    this.grcAssessmentCriteriaModifiedL2.Visible = true;
    this.grcAssessmentConclusionModifiedL2.Visible = true;
  }

  private void vwResult_Load(object sender, EventArgs e)
  {
    this.txtSoOriginal.ShowOnlyN4();
    this.txtSflowOriginal.ShowOnlyN4();
    this.txtSoModified.ShowOnlyN4();
    this.txtSflowModified.ShowOnlyN4();
    this.txtSFailureOriginalL1.ShowOnlyN4();
    this.txtPressureOriginalL1.ShowOnlyN4();
    this.txtSFailureModifiedL1.ShowOnlyN4();
    this.txtPressureModifiedL1.ShowOnlyN4();
    this.txtminRSFiOriginal.ShowOnlyN4();
    this.txtSFailureOriginalL2.ShowOnlyN4();
    this.txtPressureOriginalL2.ShowOnlyN4();
    this.txtminRSFiModified.ShowOnlyN4();
    this.txtSFailureModifiedL2.ShowOnlyN4();
    this.txtPressureModifiedL2.ShowOnlyN4();
  }

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.lbSoOriginal = new LabelControl();
    this.txtWarningMessages = new MemoEdit();
    this.grcWarnings = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.grcAssessmentResultsOriginal = new GroupControl();
    this.tblpIntermediateResults = new TableLayoutPanel();
    this.txtPressureOriginalL1 = new TextEdit();
    this.txtSFailureOriginalL1 = new TextEdit();
    this.txtSflowOriginal = new TextEdit();
    this.txtSoOriginal = new TextEdit();
    this.lbUMPressureOriginalL1 = new LabelControl();
    this.lbUMSFailureOriginalL1 = new LabelControl();
    this.lbUMSflowOriginal = new LabelControl();
    this.lbUMSoOriginal = new LabelControl();
    this.lbSflowOriginal = new LabelControl();
    this.lbSFailureOriginalL1 = new LabelControl();
    this.lbPressureOriginalL1 = new LabelControl();
    this.grcAssessmentConclusionOriginalL1 = new GroupControl();
    this.tableLayoutPanel22 = new TableLayoutPanel();
    this.lbConclusionOriginalLevel1 = new LabelControl();
    this.grcAssessmentConclusionOriginalL2 = new GroupControl();
    this.tableLayoutPanel27 = new TableLayoutPanel();
    this.lbConclusionOriginalLevel2 = new LabelControl();
    this.grcAssessmentResultsOriginalL2 = new GroupControl();
    this.tableLayoutPanel7 = new TableLayoutPanel();
    this.lbPressureOriginalL2 = new LabelControl();
    this.txtminRSFiOriginal = new TextEdit();
    this.lbminRSFiOriginal = new LabelControl();
    this.lbSFailureOriginalL2 = new LabelControl();
    this.txtSFailureOriginalL2 = new TextEdit();
    this.txtPressureOriginalL2 = new TextEdit();
    this.lbUMSFailureOriginalL2 = new LabelControl();
    this.lbUMPressureOriginalL2 = new LabelControl();
    this.grcAssessmentCriteriaOriginalL1 = new GroupControl();
    this.tbpMawp = new TableLayoutPanel();
    this.lbSFailureCriteriaOriginalL1 = new LabelControl();
    this.lbPressureCriteriaOriginalL1 = new LabelControl();
    this.txtSFailureCriteriaOriginalL1 = new TextEdit();
    this.txtPressureCriteriaOriginalL1 = new TextEdit();
    this.grcAssessmentConclusionModifiedL1 = new GroupControl();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.lbConclusionModifiedLevel1 = new LabelControl();
    this.grcAssessmentCriteriaModifiedL1 = new GroupControl();
    this.tableLayoutPanel5 = new TableLayoutPanel();
    this.lbSFailureCriteriaModifiedL1 = new LabelControl();
    this.lbPressureCriteriaModifiedL1 = new LabelControl();
    this.txtSFailureCriteriaModifiedL1 = new TextEdit();
    this.txtPressureCriteriaModifiedL1 = new TextEdit();
    this.grcAssessmentResultsModified = new GroupControl();
    this.tableLayoutPanel6 = new TableLayoutPanel();
    this.txtPressureModifiedL1 = new TextEdit();
    this.txtSFailureModifiedL1 = new TextEdit();
    this.txtSflowModified = new TextEdit();
    this.txtSoModified = new TextEdit();
    this.lbUMPressureModifiedL1 = new LabelControl();
    this.lbUMSFailureModifiedL1 = new LabelControl();
    this.lbUMSflowModified = new LabelControl();
    this.lbUMSoModified = new LabelControl();
    this.lbSoModified = new LabelControl();
    this.lbSflowModified = new LabelControl();
    this.lbSFailureModifiedL1 = new LabelControl();
    this.lbPressureModifiedL1 = new LabelControl();
    this.grcAssessmentCriteriaOriginalL2 = new GroupControl();
    this.tableLayoutPanel8 = new TableLayoutPanel();
    this.lbSFailureCriteriaOriginalL2 = new LabelControl();
    this.lbPressureCriteriaOriginalL2 = new LabelControl();
    this.txtSFailureCriteriaOriginalL2 = new TextEdit();
    this.txtPressureCriteriaOriginalL2 = new TextEdit();
    this.grcAssessmentConclusionModifiedL2 = new GroupControl();
    this.tableLayoutPanel4 = new TableLayoutPanel();
    this.lbConclusionModifiedLevel2 = new LabelControl();
    this.grcAssessmentCriteriaModifiedL2 = new GroupControl();
    this.tableLayoutPanel9 = new TableLayoutPanel();
    this.lbSFailureCriteriaModifiedL2 = new LabelControl();
    this.lbPressureCriteriaModifiedL2 = new LabelControl();
    this.txtSFailureCriteriaModifiedL2 = new TextEdit();
    this.txtPressureCriteriaModifiedL2 = new TextEdit();
    this.grcAssessmentResultsModifiedL2 = new GroupControl();
    this.tableLayoutPanel10 = new TableLayoutPanel();
    this.lbPressureModifiedL2 = new LabelControl();
    this.txtminRSFiModified = new TextEdit();
    this.lbminRSFiModified = new LabelControl();
    this.lbSFailureModifiedL2 = new LabelControl();
    this.txtSFailureModifiedL2 = new TextEdit();
    this.txtPressureModifiedL2 = new TextEdit();
    this.lbUMSFailureModifiedL2 = new LabelControl();
    this.lbUMPressureModifiedL2 = new LabelControl();
    this.pcBottom = new PanelControl();
    this.txtWarningMessages.Properties.BeginInit();
    this.grcWarnings.BeginInit();
    this.grcWarnings.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.grcAssessmentResultsOriginal.BeginInit();
    this.grcAssessmentResultsOriginal.SuspendLayout();
    this.tblpIntermediateResults.SuspendLayout();
    this.txtPressureOriginalL1.Properties.BeginInit();
    this.txtSFailureOriginalL1.Properties.BeginInit();
    this.txtSflowOriginal.Properties.BeginInit();
    this.txtSoOriginal.Properties.BeginInit();
    this.grcAssessmentConclusionOriginalL1.BeginInit();
    this.grcAssessmentConclusionOriginalL1.SuspendLayout();
    this.tableLayoutPanel22.SuspendLayout();
    this.grcAssessmentConclusionOriginalL2.BeginInit();
    this.grcAssessmentConclusionOriginalL2.SuspendLayout();
    this.tableLayoutPanel27.SuspendLayout();
    this.grcAssessmentResultsOriginalL2.BeginInit();
    this.grcAssessmentResultsOriginalL2.SuspendLayout();
    this.tableLayoutPanel7.SuspendLayout();
    this.txtminRSFiOriginal.Properties.BeginInit();
    this.txtSFailureOriginalL2.Properties.BeginInit();
    this.txtPressureOriginalL2.Properties.BeginInit();
    this.grcAssessmentCriteriaOriginalL1.BeginInit();
    this.grcAssessmentCriteriaOriginalL1.SuspendLayout();
    this.tbpMawp.SuspendLayout();
    this.txtSFailureCriteriaOriginalL1.Properties.BeginInit();
    this.txtPressureCriteriaOriginalL1.Properties.BeginInit();
    this.grcAssessmentConclusionModifiedL1.BeginInit();
    this.grcAssessmentConclusionModifiedL1.SuspendLayout();
    this.tableLayoutPanel2.SuspendLayout();
    this.grcAssessmentCriteriaModifiedL1.BeginInit();
    this.grcAssessmentCriteriaModifiedL1.SuspendLayout();
    this.tableLayoutPanel5.SuspendLayout();
    this.txtSFailureCriteriaModifiedL1.Properties.BeginInit();
    this.txtPressureCriteriaModifiedL1.Properties.BeginInit();
    this.grcAssessmentResultsModified.BeginInit();
    this.grcAssessmentResultsModified.SuspendLayout();
    this.tableLayoutPanel6.SuspendLayout();
    this.txtPressureModifiedL1.Properties.BeginInit();
    this.txtSFailureModifiedL1.Properties.BeginInit();
    this.txtSflowModified.Properties.BeginInit();
    this.txtSoModified.Properties.BeginInit();
    this.grcAssessmentCriteriaOriginalL2.BeginInit();
    this.grcAssessmentCriteriaOriginalL2.SuspendLayout();
    this.tableLayoutPanel8.SuspendLayout();
    this.txtSFailureCriteriaOriginalL2.Properties.BeginInit();
    this.txtPressureCriteriaOriginalL2.Properties.BeginInit();
    this.grcAssessmentConclusionModifiedL2.BeginInit();
    this.grcAssessmentConclusionModifiedL2.SuspendLayout();
    this.tableLayoutPanel4.SuspendLayout();
    this.grcAssessmentCriteriaModifiedL2.BeginInit();
    this.grcAssessmentCriteriaModifiedL2.SuspendLayout();
    this.tableLayoutPanel9.SuspendLayout();
    this.txtSFailureCriteriaModifiedL2.Properties.BeginInit();
    this.txtPressureCriteriaModifiedL2.Properties.BeginInit();
    this.grcAssessmentResultsModifiedL2.BeginInit();
    this.grcAssessmentResultsModifiedL2.SuspendLayout();
    this.tableLayoutPanel10.SuspendLayout();
    this.txtminRSFiModified.Properties.BeginInit();
    this.txtSFailureModifiedL2.Properties.BeginInit();
    this.txtPressureModifiedL2.Properties.BeginInit();
    this.pcBottom.BeginInit();
    this.SuspendLayout();
    this.lbSoOriginal.Location = new Point(13, 13);
    this.lbSoOriginal.Name = "lbSoOriginal";
    this.lbSoOriginal.Size = new Size(48 /*0x30*/, 13);
    this.lbSoOriginal.TabIndex = 10;
    this.lbSoOriginal.Text = "SoOriginal";
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(550, 84);
    this.txtWarningMessages.TabIndex = 4;
    this.grcWarnings.Controls.Add((Control) this.tableLayoutPanel3);
    this.grcWarnings.Dock = DockStyle.Top;
    this.grcWarnings.Location = new Point(0, 1032);
    this.grcWarnings.Name = "grcWarnings";
    this.grcWarnings.Size = new Size(580, 133);
    this.grcWarnings.TabIndex = 8;
    this.grcWarnings.Text = "Warning Messages";
    this.tableLayoutPanel3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel3.Size = new Size(576, 110);
    this.tableLayoutPanel3.TabIndex = 0;
    this.grcAssessmentResultsOriginal.Controls.Add((Control) this.tblpIntermediateResults);
    this.grcAssessmentResultsOriginal.Dock = DockStyle.Top;
    this.grcAssessmentResultsOriginal.Location = new Point(0, 0);
    this.grcAssessmentResultsOriginal.Name = "grcAssessmentResultsOriginal";
    this.grcAssessmentResultsOriginal.Size = new Size(580, (int) sbyte.MaxValue);
    this.grcAssessmentResultsOriginal.TabIndex = 6;
    this.grcAssessmentResultsOriginal.Text = "Original B31G: Level 1 Assessment Results";
    this.tblpIntermediateResults.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpIntermediateResults.ColumnCount = 4;
    this.tblpIntermediateResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpIntermediateResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpIntermediateResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpIntermediateResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpIntermediateResults.Controls.Add((Control) this.txtPressureOriginalL1, 2, 3);
    this.tblpIntermediateResults.Controls.Add((Control) this.txtSFailureOriginalL1, 2, 2);
    this.tblpIntermediateResults.Controls.Add((Control) this.txtSflowOriginal, 2, 1);
    this.tblpIntermediateResults.Controls.Add((Control) this.txtSoOriginal, 2, 0);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbUMPressureOriginalL1, 3, 3);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbUMSFailureOriginalL1, 3, 2);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbUMSflowOriginal, 3, 1);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbUMSoOriginal, 3, 0);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbSoOriginal, 0, 0);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbSflowOriginal, 0, 1);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbSFailureOriginalL1, 0, 2);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbPressureOriginalL1, 0, 3);
    this.tblpIntermediateResults.Dock = DockStyle.Fill;
    this.tblpIntermediateResults.Location = new Point(2, 21);
    this.tblpIntermediateResults.Name = "tblpIntermediateResults";
    this.tblpIntermediateResults.Padding = new Padding(10);
    this.tblpIntermediateResults.RowCount = 4;
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.Size = new Size(576, 104);
    this.tblpIntermediateResults.TabIndex = 0;
    this.txtPressureOriginalL1.Location = new Point(105, 76);
    this.txtPressureOriginalL1.Margin = new Padding(0);
    this.txtPressureOriginalL1.Name = "txtPressureOriginalL1";
    this.txtPressureOriginalL1.Properties.ReadOnly = true;
    this.txtPressureOriginalL1.Size = new Size(100, 20);
    this.txtPressureOriginalL1.TabIndex = 0;
    this.txtSFailureOriginalL1.Location = new Point(105, 54);
    this.txtSFailureOriginalL1.Margin = new Padding(0);
    this.txtSFailureOriginalL1.Name = "txtSFailureOriginalL1";
    this.txtSFailureOriginalL1.Properties.ReadOnly = true;
    this.txtSFailureOriginalL1.Size = new Size(100, 20);
    this.txtSFailureOriginalL1.TabIndex = 0;
    this.txtSflowOriginal.Location = new Point(105, 32 /*0x20*/);
    this.txtSflowOriginal.Margin = new Padding(0);
    this.txtSflowOriginal.Name = "txtSflowOriginal";
    this.txtSflowOriginal.Properties.ReadOnly = true;
    this.txtSflowOriginal.Size = new Size(100, 20);
    this.txtSflowOriginal.TabIndex = 0;
    this.txtSoOriginal.Location = new Point(105, 10);
    this.txtSoOriginal.Margin = new Padding(0);
    this.txtSoOriginal.Name = "txtSoOriginal";
    this.txtSoOriginal.Properties.ReadOnly = true;
    this.txtSoOriginal.Size = new Size(100, 20);
    this.txtSoOriginal.TabIndex = 0;
    this.lbUMPressureOriginalL1.Location = new Point(208 /*0xD0*/, 79);
    this.lbUMPressureOriginalL1.Name = "lbUMPressureOriginalL1";
    this.lbUMPressureOriginalL1.Size = new Size(41, 13);
    this.lbUMPressureOriginalL1.TabIndex = 1;
    this.lbUMPressureOriginalL1.Text = "measure";
    this.lbUMSFailureOriginalL1.Location = new Point(208 /*0xD0*/, 57);
    this.lbUMSFailureOriginalL1.Name = "lbUMSFailureOriginalL1";
    this.lbUMSFailureOriginalL1.Size = new Size(41, 13);
    this.lbUMSFailureOriginalL1.TabIndex = 1;
    this.lbUMSFailureOriginalL1.Text = "measure";
    this.lbUMSflowOriginal.Location = new Point(208 /*0xD0*/, 35);
    this.lbUMSflowOriginal.Name = "lbUMSflowOriginal";
    this.lbUMSflowOriginal.Size = new Size(41, 13);
    this.lbUMSflowOriginal.TabIndex = 1;
    this.lbUMSflowOriginal.Text = "measure";
    this.lbUMSoOriginal.Location = new Point(208 /*0xD0*/, 13);
    this.lbUMSoOriginal.Name = "lbUMSoOriginal";
    this.lbUMSoOriginal.Size = new Size(41, 13);
    this.lbUMSoOriginal.TabIndex = 1;
    this.lbUMSoOriginal.Text = "measure";
    this.lbSflowOriginal.Location = new Point(13, 35);
    this.lbSflowOriginal.Name = "lbSflowOriginal";
    this.lbSflowOriginal.Size = new Size(62, 13);
    this.lbSflowOriginal.TabIndex = 15;
    this.lbSflowOriginal.Text = "SflowOriginal";
    this.lbSFailureOriginalL1.Location = new Point(13, 57);
    this.lbSFailureOriginalL1.Name = "lbSFailureOriginalL1";
    this.lbSFailureOriginalL1.Size = new Size(85, 13);
    this.lbSFailureOriginalL1.TabIndex = 16 /*0x10*/;
    this.lbSFailureOriginalL1.Text = "SFailureOriginalL1";
    this.lbPressureOriginalL1.Location = new Point(13, 79);
    this.lbPressureOriginalL1.Name = "lbPressureOriginalL1";
    this.lbPressureOriginalL1.Size = new Size(89, 13);
    this.lbPressureOriginalL1.TabIndex = 18;
    this.lbPressureOriginalL1.Text = "PressureOriginalL1";
    this.grcAssessmentConclusionOriginalL1.Controls.Add((Control) this.tableLayoutPanel22);
    this.grcAssessmentConclusionOriginalL1.Dock = DockStyle.Top;
    this.grcAssessmentConclusionOriginalL1.Location = new Point(0, 209);
    this.grcAssessmentConclusionOriginalL1.Name = "grcAssessmentConclusionOriginalL1";
    this.grcAssessmentConclusionOriginalL1.Size = new Size(580, 61);
    this.grcAssessmentConclusionOriginalL1.TabIndex = 10;
    this.grcAssessmentConclusionOriginalL1.Text = "Original B31G:  Level1 Assessment Conclusion";
    this.tableLayoutPanel22.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel22.ColumnCount = 2;
    this.tableLayoutPanel22.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel22.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel22.Controls.Add((Control) this.lbConclusionOriginalLevel1, 0, 0);
    this.tableLayoutPanel22.Dock = DockStyle.Fill;
    this.tableLayoutPanel22.Location = new Point(2, 21);
    this.tableLayoutPanel22.Name = "tableLayoutPanel22";
    this.tableLayoutPanel22.Padding = new Padding(10);
    this.tableLayoutPanel22.RowCount = 1;
    this.tableLayoutPanel22.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel22.Size = new Size(576, 38);
    this.tableLayoutPanel22.TabIndex = 0;
    this.lbConclusionOriginalLevel1.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold);
    this.lbConclusionOriginalLevel1.Location = new Point(13, 13);
    this.lbConclusionOriginalLevel1.Name = "lbConclusionOriginalLevel1";
    this.lbConclusionOriginalLevel1.Size = new Size(157, 13);
    this.lbConclusionOriginalLevel1.TabIndex = 16 /*0x10*/;
    this.lbConclusionOriginalLevel1.Text = "The Level 1 Assessment is ..";
    this.grcAssessmentConclusionOriginalL2.Controls.Add((Control) this.tableLayoutPanel27);
    this.grcAssessmentConclusionOriginalL2.Dock = DockStyle.Top;
    this.grcAssessmentConclusionOriginalL2.Location = new Point(0, 718);
    this.grcAssessmentConclusionOriginalL2.Name = "grcAssessmentConclusionOriginalL2";
    this.grcAssessmentConclusionOriginalL2.Size = new Size(580, 68);
    this.grcAssessmentConclusionOriginalL2.TabIndex = 12;
    this.grcAssessmentConclusionOriginalL2.Text = "Original B31G: Level2 Assessment Conclusion";
    this.tableLayoutPanel27.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel27.ColumnCount = 2;
    this.tableLayoutPanel27.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel27.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel27.Controls.Add((Control) this.lbConclusionOriginalLevel2, 0, 0);
    this.tableLayoutPanel27.Dock = DockStyle.Fill;
    this.tableLayoutPanel27.Location = new Point(2, 21);
    this.tableLayoutPanel27.Name = "tableLayoutPanel27";
    this.tableLayoutPanel27.Padding = new Padding(10);
    this.tableLayoutPanel27.RowCount = 1;
    this.tableLayoutPanel27.RowStyles.Add(new RowStyle(SizeType.Absolute, 25f));
    this.tableLayoutPanel27.Size = new Size(576, 45);
    this.tableLayoutPanel27.TabIndex = 0;
    this.lbConclusionOriginalLevel2.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold);
    this.lbConclusionOriginalLevel2.Location = new Point(13, 13);
    this.lbConclusionOriginalLevel2.Name = "lbConclusionOriginalLevel2";
    this.lbConclusionOriginalLevel2.Size = new Size(157, 13);
    this.lbConclusionOriginalLevel2.TabIndex = 18;
    this.lbConclusionOriginalLevel2.Text = "The Level 2 Assessment is ..";
    this.grcAssessmentResultsOriginalL2.Controls.Add((Control) this.tableLayoutPanel7);
    this.grcAssessmentResultsOriginalL2.Dock = DockStyle.Top;
    this.grcAssessmentResultsOriginalL2.Location = new Point(0, 540);
    this.grcAssessmentResultsOriginalL2.Name = "grcAssessmentResultsOriginalL2";
    this.grcAssessmentResultsOriginalL2.Size = new Size(580, 96 /*0x60*/);
    this.grcAssessmentResultsOriginalL2.TabIndex = 17;
    this.grcAssessmentResultsOriginalL2.Text = "Original B31G: Level 2 Assessment Results";
    this.tableLayoutPanel7.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel7.ColumnCount = 3;
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel7.Controls.Add((Control) this.lbPressureOriginalL2, 0, 2);
    this.tableLayoutPanel7.Controls.Add((Control) this.txtminRSFiOriginal, 1, 0);
    this.tableLayoutPanel7.Controls.Add((Control) this.lbminRSFiOriginal, 0, 0);
    this.tableLayoutPanel7.Controls.Add((Control) this.lbSFailureOriginalL2, 0, 1);
    this.tableLayoutPanel7.Controls.Add((Control) this.txtSFailureOriginalL2, 1, 1);
    this.tableLayoutPanel7.Controls.Add((Control) this.txtPressureOriginalL2, 1, 2);
    this.tableLayoutPanel7.Controls.Add((Control) this.lbUMSFailureOriginalL2, 2, 1);
    this.tableLayoutPanel7.Controls.Add((Control) this.lbUMPressureOriginalL2, 2, 2);
    this.tableLayoutPanel7.Dock = DockStyle.Fill;
    this.tableLayoutPanel7.Location = new Point(2, 21);
    this.tableLayoutPanel7.Name = "tableLayoutPanel7";
    this.tableLayoutPanel7.Padding = new Padding(10);
    this.tableLayoutPanel7.RowCount = 3;
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel7.Size = new Size(576, 73);
    this.tableLayoutPanel7.TabIndex = 0;
    this.lbPressureOriginalL2.Location = new Point(13, 53);
    this.lbPressureOriginalL2.Name = "lbPressureOriginalL2";
    this.lbPressureOriginalL2.Size = new Size(89, 13);
    this.lbPressureOriginalL2.TabIndex = 30;
    this.lbPressureOriginalL2.Text = "PressureOriginalL2";
    this.txtminRSFiOriginal.Location = new Point(105, 10);
    this.txtminRSFiOriginal.Margin = new Padding(0);
    this.txtminRSFiOriginal.Name = "txtminRSFiOriginal";
    this.txtminRSFiOriginal.Properties.ReadOnly = true;
    this.txtminRSFiOriginal.Size = new Size(100, 20);
    this.txtminRSFiOriginal.TabIndex = 0;
    this.lbminRSFiOriginal.Location = new Point(13, 13);
    this.lbminRSFiOriginal.Name = "lbminRSFiOriginal";
    this.lbminRSFiOriginal.Size = new Size(73, 13);
    this.lbminRSFiOriginal.TabIndex = 27;
    this.lbminRSFiOriginal.Text = "minRSFiOriginal";
    this.lbSFailureOriginalL2.Location = new Point(13, 33);
    this.lbSFailureOriginalL2.Name = "lbSFailureOriginalL2";
    this.lbSFailureOriginalL2.Size = new Size(85, 13);
    this.lbSFailureOriginalL2.TabIndex = 28;
    this.lbSFailureOriginalL2.Text = "SFailureOriginalL2";
    this.txtSFailureOriginalL2.Location = new Point(105, 30);
    this.txtSFailureOriginalL2.Margin = new Padding(0);
    this.txtSFailureOriginalL2.Name = "txtSFailureOriginalL2";
    this.txtSFailureOriginalL2.Properties.ReadOnly = true;
    this.txtSFailureOriginalL2.Size = new Size(100, 20);
    this.txtSFailureOriginalL2.TabIndex = 29;
    this.txtPressureOriginalL2.Location = new Point(105, 50);
    this.txtPressureOriginalL2.Margin = new Padding(0);
    this.txtPressureOriginalL2.Name = "txtPressureOriginalL2";
    this.txtPressureOriginalL2.Properties.ReadOnly = true;
    this.txtPressureOriginalL2.Size = new Size(100, 20);
    this.txtPressureOriginalL2.TabIndex = 31 /*0x1F*/;
    this.lbUMSFailureOriginalL2.Location = new Point(208 /*0xD0*/, 33);
    this.lbUMSFailureOriginalL2.Name = "lbUMSFailureOriginalL2";
    this.lbUMSFailureOriginalL2.Size = new Size(41, 13);
    this.lbUMSFailureOriginalL2.TabIndex = 32 /*0x20*/;
    this.lbUMSFailureOriginalL2.Text = "measure";
    this.lbUMPressureOriginalL2.Location = new Point(208 /*0xD0*/, 53);
    this.lbUMPressureOriginalL2.Name = "lbUMPressureOriginalL2";
    this.lbUMPressureOriginalL2.Size = new Size(41, 13);
    this.lbUMPressureOriginalL2.TabIndex = 33;
    this.lbUMPressureOriginalL2.Text = "measure";
    this.grcAssessmentCriteriaOriginalL1.Controls.Add((Control) this.tbpMawp);
    this.grcAssessmentCriteriaOriginalL1.Dock = DockStyle.Top;
    this.grcAssessmentCriteriaOriginalL1.Location = new Point(0, (int) sbyte.MaxValue);
    this.grcAssessmentCriteriaOriginalL1.Name = "grcAssessmentCriteriaOriginalL1";
    this.grcAssessmentCriteriaOriginalL1.Size = new Size(580, 82);
    this.grcAssessmentCriteriaOriginalL1.TabIndex = 18;
    this.grcAssessmentCriteriaOriginalL1.Text = "Original B31G: Level 1 Assessment Criteria:";
    this.tbpMawp.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tbpMawp.ColumnCount = 2;
    this.tbpMawp.ColumnStyles.Add(new ColumnStyle());
    this.tbpMawp.ColumnStyles.Add(new ColumnStyle());
    this.tbpMawp.Controls.Add((Control) this.lbSFailureCriteriaOriginalL1, 0, 0);
    this.tbpMawp.Controls.Add((Control) this.lbPressureCriteriaOriginalL1, 0, 1);
    this.tbpMawp.Controls.Add((Control) this.txtSFailureCriteriaOriginalL1, 1, 0);
    this.tbpMawp.Controls.Add((Control) this.txtPressureCriteriaOriginalL1, 1, 1);
    this.tbpMawp.Dock = DockStyle.Fill;
    this.tbpMawp.Location = new Point(2, 21);
    this.tbpMawp.Name = "tbpMawp";
    this.tbpMawp.Padding = new Padding(10);
    this.tbpMawp.RowCount = 2;
    this.tbpMawp.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tbpMawp.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tbpMawp.Size = new Size(576, 59);
    this.tbpMawp.TabIndex = 0;
    this.lbSFailureCriteriaOriginalL1.Location = new Point(13, 13);
    this.lbSFailureCriteriaOriginalL1.Name = "lbSFailureCriteriaOriginalL1";
    this.lbSFailureCriteriaOriginalL1.Size = new Size(120, 13);
    this.lbSFailureCriteriaOriginalL1.TabIndex = 10;
    this.lbSFailureCriteriaOriginalL1.Text = "SFailureCriteriaOriginalL1";
    this.lbPressureCriteriaOriginalL1.Location = new Point(13, 35);
    this.lbPressureCriteriaOriginalL1.Name = "lbPressureCriteriaOriginalL1";
    this.lbPressureCriteriaOriginalL1.Size = new Size(124, 13);
    this.lbPressureCriteriaOriginalL1.TabIndex = 15;
    this.lbPressureCriteriaOriginalL1.Text = "PressureCriteriaOriginalL1";
    this.txtSFailureCriteriaOriginalL1.Location = new Point(140, 10);
    this.txtSFailureCriteriaOriginalL1.Margin = new Padding(0);
    this.txtSFailureCriteriaOriginalL1.Name = "txtSFailureCriteriaOriginalL1";
    this.txtSFailureCriteriaOriginalL1.Properties.ReadOnly = true;
    this.txtSFailureCriteriaOriginalL1.Size = new Size(100, 20);
    this.txtSFailureCriteriaOriginalL1.TabIndex = 0;
    this.txtPressureCriteriaOriginalL1.Location = new Point(140, 32 /*0x20*/);
    this.txtPressureCriteriaOriginalL1.Margin = new Padding(0);
    this.txtPressureCriteriaOriginalL1.Name = "txtPressureCriteriaOriginalL1";
    this.txtPressureCriteriaOriginalL1.Properties.ReadOnly = true;
    this.txtPressureCriteriaOriginalL1.Size = new Size(100, 20);
    this.txtPressureCriteriaOriginalL1.TabIndex = 0;
    this.grcAssessmentConclusionModifiedL1.Controls.Add((Control) this.tableLayoutPanel2);
    this.grcAssessmentConclusionModifiedL1.Dock = DockStyle.Top;
    this.grcAssessmentConclusionModifiedL1.Location = new Point(0, 479);
    this.grcAssessmentConclusionModifiedL1.Name = "grcAssessmentConclusionModifiedL1";
    this.grcAssessmentConclusionModifiedL1.Size = new Size(580, 61);
    this.grcAssessmentConclusionModifiedL1.TabIndex = 20;
    this.grcAssessmentConclusionModifiedL1.Text = "Modified B31G:  Level1 Assessment Conclusion";
    this.tableLayoutPanel2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel2.ColumnCount = 2;
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.Controls.Add((Control) this.lbConclusionModifiedLevel1, 0, 0);
    this.tableLayoutPanel2.Dock = DockStyle.Fill;
    this.tableLayoutPanel2.Location = new Point(2, 21);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.Padding = new Padding(10);
    this.tableLayoutPanel2.RowCount = 1;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel2.Size = new Size(576, 38);
    this.tableLayoutPanel2.TabIndex = 0;
    this.lbConclusionModifiedLevel1.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold);
    this.lbConclusionModifiedLevel1.Location = new Point(13, 13);
    this.lbConclusionModifiedLevel1.Name = "lbConclusionModifiedLevel1";
    this.lbConclusionModifiedLevel1.Size = new Size(157, 13);
    this.lbConclusionModifiedLevel1.TabIndex = 16 /*0x10*/;
    this.lbConclusionModifiedLevel1.Text = "The Level 1 Assessment is ..";
    this.grcAssessmentCriteriaModifiedL1.Controls.Add((Control) this.tableLayoutPanel5);
    this.grcAssessmentCriteriaModifiedL1.Dock = DockStyle.Top;
    this.grcAssessmentCriteriaModifiedL1.Location = new Point(0, 397);
    this.grcAssessmentCriteriaModifiedL1.Name = "grcAssessmentCriteriaModifiedL1";
    this.grcAssessmentCriteriaModifiedL1.Size = new Size(580, 82);
    this.grcAssessmentCriteriaModifiedL1.TabIndex = 21;
    this.grcAssessmentCriteriaModifiedL1.Text = "Modified B31G: Level 1 Assessment Criteria:";
    this.tableLayoutPanel5.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel5.ColumnCount = 2;
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel5.Controls.Add((Control) this.lbSFailureCriteriaModifiedL1, 0, 0);
    this.tableLayoutPanel5.Controls.Add((Control) this.lbPressureCriteriaModifiedL1, 0, 1);
    this.tableLayoutPanel5.Controls.Add((Control) this.txtSFailureCriteriaModifiedL1, 1, 0);
    this.tableLayoutPanel5.Controls.Add((Control) this.txtPressureCriteriaModifiedL1, 1, 1);
    this.tableLayoutPanel5.Dock = DockStyle.Fill;
    this.tableLayoutPanel5.Location = new Point(2, 21);
    this.tableLayoutPanel5.Name = "tableLayoutPanel5";
    this.tableLayoutPanel5.Padding = new Padding(10);
    this.tableLayoutPanel5.RowCount = 2;
    this.tableLayoutPanel5.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel5.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel5.Size = new Size(576, 59);
    this.tableLayoutPanel5.TabIndex = 0;
    this.lbSFailureCriteriaModifiedL1.Location = new Point(13, 13);
    this.lbSFailureCriteriaModifiedL1.Name = "lbSFailureCriteriaModifiedL1";
    this.lbSFailureCriteriaModifiedL1.Size = new Size(124, 13);
    this.lbSFailureCriteriaModifiedL1.TabIndex = 10;
    this.lbSFailureCriteriaModifiedL1.Text = "SFailureCriteriaModifiedL1";
    this.lbPressureCriteriaModifiedL1.Location = new Point(13, 35);
    this.lbPressureCriteriaModifiedL1.Name = "lbPressureCriteriaModifiedL1";
    this.lbPressureCriteriaModifiedL1.Size = new Size(128 /*0x80*/, 13);
    this.lbPressureCriteriaModifiedL1.TabIndex = 15;
    this.lbPressureCriteriaModifiedL1.Text = "PressureCriteriaModifiedL1";
    this.txtSFailureCriteriaModifiedL1.Location = new Point(144 /*0x90*/, 10);
    this.txtSFailureCriteriaModifiedL1.Margin = new Padding(0);
    this.txtSFailureCriteriaModifiedL1.Name = "txtSFailureCriteriaModifiedL1";
    this.txtSFailureCriteriaModifiedL1.Properties.ReadOnly = true;
    this.txtSFailureCriteriaModifiedL1.Size = new Size(100, 20);
    this.txtSFailureCriteriaModifiedL1.TabIndex = 0;
    this.txtPressureCriteriaModifiedL1.Location = new Point(144 /*0x90*/, 32 /*0x20*/);
    this.txtPressureCriteriaModifiedL1.Margin = new Padding(0);
    this.txtPressureCriteriaModifiedL1.Name = "txtPressureCriteriaModifiedL1";
    this.txtPressureCriteriaModifiedL1.Properties.ReadOnly = true;
    this.txtPressureCriteriaModifiedL1.Size = new Size(100, 20);
    this.txtPressureCriteriaModifiedL1.TabIndex = 0;
    this.grcAssessmentResultsModified.Controls.Add((Control) this.tableLayoutPanel6);
    this.grcAssessmentResultsModified.Dock = DockStyle.Top;
    this.grcAssessmentResultsModified.Location = new Point(0, 270);
    this.grcAssessmentResultsModified.Name = "grcAssessmentResultsModified";
    this.grcAssessmentResultsModified.Size = new Size(580, (int) sbyte.MaxValue);
    this.grcAssessmentResultsModified.TabIndex = 19;
    this.grcAssessmentResultsModified.Text = "Modified B31G: Level 1 Assessment Results";
    this.tableLayoutPanel6.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel6.ColumnCount = 4;
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel6.Controls.Add((Control) this.txtPressureModifiedL1, 2, 3);
    this.tableLayoutPanel6.Controls.Add((Control) this.txtSFailureModifiedL1, 2, 2);
    this.tableLayoutPanel6.Controls.Add((Control) this.txtSflowModified, 2, 1);
    this.tableLayoutPanel6.Controls.Add((Control) this.txtSoModified, 2, 0);
    this.tableLayoutPanel6.Controls.Add((Control) this.lbUMPressureModifiedL1, 3, 3);
    this.tableLayoutPanel6.Controls.Add((Control) this.lbUMSFailureModifiedL1, 3, 2);
    this.tableLayoutPanel6.Controls.Add((Control) this.lbUMSflowModified, 3, 1);
    this.tableLayoutPanel6.Controls.Add((Control) this.lbUMSoModified, 3, 0);
    this.tableLayoutPanel6.Controls.Add((Control) this.lbSoModified, 0, 0);
    this.tableLayoutPanel6.Controls.Add((Control) this.lbSflowModified, 0, 1);
    this.tableLayoutPanel6.Controls.Add((Control) this.lbSFailureModifiedL1, 0, 2);
    this.tableLayoutPanel6.Controls.Add((Control) this.lbPressureModifiedL1, 0, 3);
    this.tableLayoutPanel6.Dock = DockStyle.Fill;
    this.tableLayoutPanel6.Location = new Point(2, 21);
    this.tableLayoutPanel6.Name = "tableLayoutPanel6";
    this.tableLayoutPanel6.Padding = new Padding(10);
    this.tableLayoutPanel6.RowCount = 4;
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel6.Size = new Size(576, 104);
    this.tableLayoutPanel6.TabIndex = 0;
    this.txtPressureModifiedL1.Location = new Point(109, 76);
    this.txtPressureModifiedL1.Margin = new Padding(0);
    this.txtPressureModifiedL1.Name = "txtPressureModifiedL1";
    this.txtPressureModifiedL1.Properties.ReadOnly = true;
    this.txtPressureModifiedL1.Size = new Size(100, 20);
    this.txtPressureModifiedL1.TabIndex = 0;
    this.txtSFailureModifiedL1.Location = new Point(109, 54);
    this.txtSFailureModifiedL1.Margin = new Padding(0);
    this.txtSFailureModifiedL1.Name = "txtSFailureModifiedL1";
    this.txtSFailureModifiedL1.Properties.ReadOnly = true;
    this.txtSFailureModifiedL1.Size = new Size(100, 20);
    this.txtSFailureModifiedL1.TabIndex = 0;
    this.txtSflowModified.Location = new Point(109, 32 /*0x20*/);
    this.txtSflowModified.Margin = new Padding(0);
    this.txtSflowModified.Name = "txtSflowModified";
    this.txtSflowModified.Properties.ReadOnly = true;
    this.txtSflowModified.Size = new Size(100, 20);
    this.txtSflowModified.TabIndex = 0;
    this.txtSoModified.Location = new Point(109, 10);
    this.txtSoModified.Margin = new Padding(0);
    this.txtSoModified.Name = "txtSoModified";
    this.txtSoModified.Properties.ReadOnly = true;
    this.txtSoModified.Size = new Size(100, 20);
    this.txtSoModified.TabIndex = 0;
    this.lbUMPressureModifiedL1.Location = new Point(212, 79);
    this.lbUMPressureModifiedL1.Name = "lbUMPressureModifiedL1";
    this.lbUMPressureModifiedL1.Size = new Size(41, 13);
    this.lbUMPressureModifiedL1.TabIndex = 1;
    this.lbUMPressureModifiedL1.Text = "measure";
    this.lbUMSFailureModifiedL1.Location = new Point(212, 57);
    this.lbUMSFailureModifiedL1.Name = "lbUMSFailureModifiedL1";
    this.lbUMSFailureModifiedL1.Size = new Size(41, 13);
    this.lbUMSFailureModifiedL1.TabIndex = 1;
    this.lbUMSFailureModifiedL1.Text = "measure";
    this.lbUMSflowModified.Location = new Point(212, 35);
    this.lbUMSflowModified.Name = "lbUMSflowModified";
    this.lbUMSflowModified.Size = new Size(41, 13);
    this.lbUMSflowModified.TabIndex = 1;
    this.lbUMSflowModified.Text = "measure";
    this.lbUMSoModified.Location = new Point(212, 13);
    this.lbUMSoModified.Name = "lbUMSoModified";
    this.lbUMSoModified.Size = new Size(41, 13);
    this.lbUMSoModified.TabIndex = 1;
    this.lbUMSoModified.Text = "measure";
    this.lbSoModified.Location = new Point(13, 13);
    this.lbSoModified.Name = "lbSoModified";
    this.lbSoModified.Size = new Size(52, 13);
    this.lbSoModified.TabIndex = 10;
    this.lbSoModified.Text = "SoModified";
    this.lbSflowModified.Location = new Point(13, 35);
    this.lbSflowModified.Name = "lbSflowModified";
    this.lbSflowModified.Size = new Size(66, 13);
    this.lbSflowModified.TabIndex = 15;
    this.lbSflowModified.Text = "SflowModified";
    this.lbSFailureModifiedL1.Location = new Point(13, 57);
    this.lbSFailureModifiedL1.Name = "lbSFailureModifiedL1";
    this.lbSFailureModifiedL1.Size = new Size(89, 13);
    this.lbSFailureModifiedL1.TabIndex = 16 /*0x10*/;
    this.lbSFailureModifiedL1.Text = "SFailureModifiedL1";
    this.lbPressureModifiedL1.Location = new Point(13, 79);
    this.lbPressureModifiedL1.Name = "lbPressureModifiedL1";
    this.lbPressureModifiedL1.Size = new Size(93, 13);
    this.lbPressureModifiedL1.TabIndex = 18;
    this.lbPressureModifiedL1.Text = "PressureModifiedL1";
    this.grcAssessmentCriteriaOriginalL2.Controls.Add((Control) this.tableLayoutPanel8);
    this.grcAssessmentCriteriaOriginalL2.Dock = DockStyle.Top;
    this.grcAssessmentCriteriaOriginalL2.Location = new Point(0, 636);
    this.grcAssessmentCriteriaOriginalL2.Name = "grcAssessmentCriteriaOriginalL2";
    this.grcAssessmentCriteriaOriginalL2.Size = new Size(580, 82);
    this.grcAssessmentCriteriaOriginalL2.TabIndex = 22;
    this.grcAssessmentCriteriaOriginalL2.Text = "Original B31G: Level 2 Assessment Criteria:";
    this.tableLayoutPanel8.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel8.ColumnCount = 2;
    this.tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel8.Controls.Add((Control) this.lbSFailureCriteriaOriginalL2, 0, 0);
    this.tableLayoutPanel8.Controls.Add((Control) this.lbPressureCriteriaOriginalL2, 0, 1);
    this.tableLayoutPanel8.Controls.Add((Control) this.txtSFailureCriteriaOriginalL2, 1, 0);
    this.tableLayoutPanel8.Controls.Add((Control) this.txtPressureCriteriaOriginalL2, 1, 1);
    this.tableLayoutPanel8.Dock = DockStyle.Fill;
    this.tableLayoutPanel8.Location = new Point(2, 21);
    this.tableLayoutPanel8.Name = "tableLayoutPanel8";
    this.tableLayoutPanel8.Padding = new Padding(10);
    this.tableLayoutPanel8.RowCount = 2;
    this.tableLayoutPanel8.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel8.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel8.Size = new Size(576, 59);
    this.tableLayoutPanel8.TabIndex = 0;
    this.lbSFailureCriteriaOriginalL2.Location = new Point(13, 13);
    this.lbSFailureCriteriaOriginalL2.Name = "lbSFailureCriteriaOriginalL2";
    this.lbSFailureCriteriaOriginalL2.Size = new Size(120, 13);
    this.lbSFailureCriteriaOriginalL2.TabIndex = 10;
    this.lbSFailureCriteriaOriginalL2.Text = "SFailureCriteriaOriginalL2";
    this.lbPressureCriteriaOriginalL2.Location = new Point(13, 35);
    this.lbPressureCriteriaOriginalL2.Name = "lbPressureCriteriaOriginalL2";
    this.lbPressureCriteriaOriginalL2.Size = new Size(124, 13);
    this.lbPressureCriteriaOriginalL2.TabIndex = 15;
    this.lbPressureCriteriaOriginalL2.Text = "PressureCriteriaOriginalL2";
    this.txtSFailureCriteriaOriginalL2.Location = new Point(140, 10);
    this.txtSFailureCriteriaOriginalL2.Margin = new Padding(0);
    this.txtSFailureCriteriaOriginalL2.Name = "txtSFailureCriteriaOriginalL2";
    this.txtSFailureCriteriaOriginalL2.Properties.ReadOnly = true;
    this.txtSFailureCriteriaOriginalL2.Size = new Size(100, 20);
    this.txtSFailureCriteriaOriginalL2.TabIndex = 0;
    this.txtPressureCriteriaOriginalL2.Location = new Point(140, 32 /*0x20*/);
    this.txtPressureCriteriaOriginalL2.Margin = new Padding(0);
    this.txtPressureCriteriaOriginalL2.Name = "txtPressureCriteriaOriginalL2";
    this.txtPressureCriteriaOriginalL2.Properties.ReadOnly = true;
    this.txtPressureCriteriaOriginalL2.Size = new Size(100, 20);
    this.txtPressureCriteriaOriginalL2.TabIndex = 0;
    this.grcAssessmentConclusionModifiedL2.Controls.Add((Control) this.tableLayoutPanel4);
    this.grcAssessmentConclusionModifiedL2.Dock = DockStyle.Top;
    this.grcAssessmentConclusionModifiedL2.Location = new Point(0, 964);
    this.grcAssessmentConclusionModifiedL2.Name = "grcAssessmentConclusionModifiedL2";
    this.grcAssessmentConclusionModifiedL2.Size = new Size(580, 68);
    this.grcAssessmentConclusionModifiedL2.TabIndex = 23;
    this.grcAssessmentConclusionModifiedL2.Text = "Modified B31G: Level2 Assessment Conclusion";
    this.tableLayoutPanel4.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel4.ColumnCount = 2;
    this.tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel4.Controls.Add((Control) this.lbConclusionModifiedLevel2, 0, 0);
    this.tableLayoutPanel4.Dock = DockStyle.Fill;
    this.tableLayoutPanel4.Location = new Point(2, 21);
    this.tableLayoutPanel4.Name = "tableLayoutPanel4";
    this.tableLayoutPanel4.Padding = new Padding(10);
    this.tableLayoutPanel4.RowCount = 1;
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 25f));
    this.tableLayoutPanel4.Size = new Size(576, 45);
    this.tableLayoutPanel4.TabIndex = 0;
    this.lbConclusionModifiedLevel2.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold);
    this.lbConclusionModifiedLevel2.Location = new Point(13, 13);
    this.lbConclusionModifiedLevel2.Name = "lbConclusionModifiedLevel2";
    this.lbConclusionModifiedLevel2.Size = new Size(157, 13);
    this.lbConclusionModifiedLevel2.TabIndex = 18;
    this.lbConclusionModifiedLevel2.Text = "The Level 2 Assessment is ..";
    this.grcAssessmentCriteriaModifiedL2.Controls.Add((Control) this.tableLayoutPanel9);
    this.grcAssessmentCriteriaModifiedL2.Dock = DockStyle.Top;
    this.grcAssessmentCriteriaModifiedL2.Location = new Point(0, 882);
    this.grcAssessmentCriteriaModifiedL2.Name = "grcAssessmentCriteriaModifiedL2";
    this.grcAssessmentCriteriaModifiedL2.Size = new Size(580, 82);
    this.grcAssessmentCriteriaModifiedL2.TabIndex = 25;
    this.grcAssessmentCriteriaModifiedL2.Text = "Modified B31G: Level 2 Assessment Criteria:";
    this.tableLayoutPanel9.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel9.ColumnCount = 2;
    this.tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel9.Controls.Add((Control) this.lbSFailureCriteriaModifiedL2, 0, 0);
    this.tableLayoutPanel9.Controls.Add((Control) this.lbPressureCriteriaModifiedL2, 0, 1);
    this.tableLayoutPanel9.Controls.Add((Control) this.txtSFailureCriteriaModifiedL2, 1, 0);
    this.tableLayoutPanel9.Controls.Add((Control) this.txtPressureCriteriaModifiedL2, 1, 1);
    this.tableLayoutPanel9.Dock = DockStyle.Fill;
    this.tableLayoutPanel9.Location = new Point(2, 21);
    this.tableLayoutPanel9.Name = "tableLayoutPanel9";
    this.tableLayoutPanel9.Padding = new Padding(10);
    this.tableLayoutPanel9.RowCount = 2;
    this.tableLayoutPanel9.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel9.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel9.Size = new Size(576, 59);
    this.tableLayoutPanel9.TabIndex = 0;
    this.lbSFailureCriteriaModifiedL2.Location = new Point(13, 13);
    this.lbSFailureCriteriaModifiedL2.Name = "lbSFailureCriteriaModifiedL2";
    this.lbSFailureCriteriaModifiedL2.Size = new Size(124, 13);
    this.lbSFailureCriteriaModifiedL2.TabIndex = 10;
    this.lbSFailureCriteriaModifiedL2.Text = "SFailureCriteriaModifiedL2";
    this.lbPressureCriteriaModifiedL2.Location = new Point(13, 35);
    this.lbPressureCriteriaModifiedL2.Name = "lbPressureCriteriaModifiedL2";
    this.lbPressureCriteriaModifiedL2.Size = new Size(128 /*0x80*/, 13);
    this.lbPressureCriteriaModifiedL2.TabIndex = 15;
    this.lbPressureCriteriaModifiedL2.Text = "PressureCriteriaModifiedL2";
    this.txtSFailureCriteriaModifiedL2.Location = new Point(144 /*0x90*/, 10);
    this.txtSFailureCriteriaModifiedL2.Margin = new Padding(0);
    this.txtSFailureCriteriaModifiedL2.Name = "txtSFailureCriteriaModifiedL2";
    this.txtSFailureCriteriaModifiedL2.Properties.ReadOnly = true;
    this.txtSFailureCriteriaModifiedL2.Size = new Size(100, 20);
    this.txtSFailureCriteriaModifiedL2.TabIndex = 0;
    this.txtPressureCriteriaModifiedL2.Location = new Point(144 /*0x90*/, 32 /*0x20*/);
    this.txtPressureCriteriaModifiedL2.Margin = new Padding(0);
    this.txtPressureCriteriaModifiedL2.Name = "txtPressureCriteriaModifiedL2";
    this.txtPressureCriteriaModifiedL2.Properties.ReadOnly = true;
    this.txtPressureCriteriaModifiedL2.Size = new Size(100, 20);
    this.txtPressureCriteriaModifiedL2.TabIndex = 0;
    this.grcAssessmentResultsModifiedL2.Controls.Add((Control) this.tableLayoutPanel10);
    this.grcAssessmentResultsModifiedL2.Dock = DockStyle.Top;
    this.grcAssessmentResultsModifiedL2.Location = new Point(0, 786);
    this.grcAssessmentResultsModifiedL2.Name = "grcAssessmentResultsModifiedL2";
    this.grcAssessmentResultsModifiedL2.Size = new Size(580, 96 /*0x60*/);
    this.grcAssessmentResultsModifiedL2.TabIndex = 24;
    this.grcAssessmentResultsModifiedL2.Text = "Modified B31G: Level 2 Assessment Results";
    this.tableLayoutPanel10.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel10.ColumnCount = 3;
    this.tableLayoutPanel10.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel10.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel10.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel10.Controls.Add((Control) this.lbPressureModifiedL2, 0, 2);
    this.tableLayoutPanel10.Controls.Add((Control) this.txtminRSFiModified, 1, 0);
    this.tableLayoutPanel10.Controls.Add((Control) this.lbminRSFiModified, 0, 0);
    this.tableLayoutPanel10.Controls.Add((Control) this.lbSFailureModifiedL2, 0, 1);
    this.tableLayoutPanel10.Controls.Add((Control) this.txtSFailureModifiedL2, 1, 1);
    this.tableLayoutPanel10.Controls.Add((Control) this.txtPressureModifiedL2, 1, 2);
    this.tableLayoutPanel10.Controls.Add((Control) this.lbUMSFailureModifiedL2, 2, 1);
    this.tableLayoutPanel10.Controls.Add((Control) this.lbUMPressureModifiedL2, 2, 2);
    this.tableLayoutPanel10.Dock = DockStyle.Fill;
    this.tableLayoutPanel10.Location = new Point(2, 21);
    this.tableLayoutPanel10.Name = "tableLayoutPanel10";
    this.tableLayoutPanel10.Padding = new Padding(10);
    this.tableLayoutPanel10.RowCount = 3;
    this.tableLayoutPanel10.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel10.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel10.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel10.Size = new Size(576, 73);
    this.tableLayoutPanel10.TabIndex = 0;
    this.lbPressureModifiedL2.Location = new Point(13, 53);
    this.lbPressureModifiedL2.Name = "lbPressureModifiedL2";
    this.lbPressureModifiedL2.Size = new Size(148, 13);
    this.lbPressureModifiedL2.TabIndex = 30;
    this.lbPressureModifiedL2.Text = "Estimated failure Pressure (PF)";
    this.txtminRSFiModified.Location = new Point(164, 10);
    this.txtminRSFiModified.Margin = new Padding(0);
    this.txtminRSFiModified.Name = "txtminRSFiModified";
    this.txtminRSFiModified.Properties.ReadOnly = true;
    this.txtminRSFiModified.Size = new Size(100, 20);
    this.txtminRSFiModified.TabIndex = 0;
    this.lbminRSFiModified.Location = new Point(13, 13);
    this.lbminRSFiModified.Name = "lbminRSFiModified";
    this.lbminRSFiModified.Size = new Size(77, 13);
    this.lbminRSFiModified.TabIndex = 27;
    this.lbminRSFiModified.Text = "minRSFiModified";
    this.lbSFailureModifiedL2.Location = new Point(13, 33);
    this.lbSFailureModifiedL2.Name = "lbSFailureModifiedL2";
    this.lbSFailureModifiedL2.Size = new Size(136, 13);
    this.lbSFailureModifiedL2.TabIndex = 28;
    this.lbSFailureModifiedL2.Text = "Estimated failure Stress (SF)";
    this.txtSFailureModifiedL2.Location = new Point(164, 30);
    this.txtSFailureModifiedL2.Margin = new Padding(0);
    this.txtSFailureModifiedL2.Name = "txtSFailureModifiedL2";
    this.txtSFailureModifiedL2.Properties.ReadOnly = true;
    this.txtSFailureModifiedL2.Size = new Size(100, 20);
    this.txtSFailureModifiedL2.TabIndex = 29;
    this.txtPressureModifiedL2.Location = new Point(164, 50);
    this.txtPressureModifiedL2.Margin = new Padding(0);
    this.txtPressureModifiedL2.Name = "txtPressureModifiedL2";
    this.txtPressureModifiedL2.Properties.ReadOnly = true;
    this.txtPressureModifiedL2.Size = new Size(100, 20);
    this.txtPressureModifiedL2.TabIndex = 31 /*0x1F*/;
    this.lbUMSFailureModifiedL2.Location = new Point(267, 33);
    this.lbUMSFailureModifiedL2.Name = "lbUMSFailureModifiedL2";
    this.lbUMSFailureModifiedL2.Size = new Size(41, 13);
    this.lbUMSFailureModifiedL2.TabIndex = 32 /*0x20*/;
    this.lbUMSFailureModifiedL2.Text = "measure";
    this.lbUMPressureModifiedL2.Location = new Point(267, 53);
    this.lbUMPressureModifiedL2.Name = "lbUMPressureModifiedL2";
    this.lbUMPressureModifiedL2.Size = new Size(41, 13);
    this.lbUMPressureModifiedL2.TabIndex = 33;
    this.lbUMPressureModifiedL2.Text = "measure";
    this.pcBottom.Dock = DockStyle.Fill;
    this.pcBottom.Location = new Point(0, 1165);
    this.pcBottom.Name = "pcBottom";
    this.pcBottom.Size = new Size(580, 10);
    this.pcBottom.TabIndex = 33;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.Controls.Add((Control) this.pcBottom);
    this.Controls.Add((Control) this.grcWarnings);
    this.Controls.Add((Control) this.grcAssessmentConclusionModifiedL2);
    this.Controls.Add((Control) this.grcAssessmentCriteriaModifiedL2);
    this.Controls.Add((Control) this.grcAssessmentResultsModifiedL2);
    this.Controls.Add((Control) this.grcAssessmentConclusionOriginalL2);
    this.Controls.Add((Control) this.grcAssessmentCriteriaOriginalL2);
    this.Controls.Add((Control) this.grcAssessmentResultsOriginalL2);
    this.Controls.Add((Control) this.grcAssessmentConclusionModifiedL1);
    this.Controls.Add((Control) this.grcAssessmentCriteriaModifiedL1);
    this.Controls.Add((Control) this.grcAssessmentResultsModified);
    this.Controls.Add((Control) this.grcAssessmentConclusionOriginalL1);
    this.Controls.Add((Control) this.grcAssessmentCriteriaOriginalL1);
    this.Controls.Add((Control) this.grcAssessmentResultsOriginal);
    this.Name = nameof (vwResult);
    this.Size = new Size(580, 1175);
    this.Load += new EventHandler(this.vwResult_Load);
    this.txtWarningMessages.Properties.EndInit();
    this.grcWarnings.EndInit();
    this.grcWarnings.ResumeLayout(false);
    this.tableLayoutPanel3.ResumeLayout(false);
    this.grcAssessmentResultsOriginal.EndInit();
    this.grcAssessmentResultsOriginal.ResumeLayout(false);
    this.tblpIntermediateResults.ResumeLayout(false);
    this.tblpIntermediateResults.PerformLayout();
    this.txtPressureOriginalL1.Properties.EndInit();
    this.txtSFailureOriginalL1.Properties.EndInit();
    this.txtSflowOriginal.Properties.EndInit();
    this.txtSoOriginal.Properties.EndInit();
    this.grcAssessmentConclusionOriginalL1.EndInit();
    this.grcAssessmentConclusionOriginalL1.ResumeLayout(false);
    this.tableLayoutPanel22.ResumeLayout(false);
    this.tableLayoutPanel22.PerformLayout();
    this.grcAssessmentConclusionOriginalL2.EndInit();
    this.grcAssessmentConclusionOriginalL2.ResumeLayout(false);
    this.tableLayoutPanel27.ResumeLayout(false);
    this.tableLayoutPanel27.PerformLayout();
    this.grcAssessmentResultsOriginalL2.EndInit();
    this.grcAssessmentResultsOriginalL2.ResumeLayout(false);
    this.tableLayoutPanel7.ResumeLayout(false);
    this.tableLayoutPanel7.PerformLayout();
    this.txtminRSFiOriginal.Properties.EndInit();
    this.txtSFailureOriginalL2.Properties.EndInit();
    this.txtPressureOriginalL2.Properties.EndInit();
    this.grcAssessmentCriteriaOriginalL1.EndInit();
    this.grcAssessmentCriteriaOriginalL1.ResumeLayout(false);
    this.tbpMawp.ResumeLayout(false);
    this.tbpMawp.PerformLayout();
    this.txtSFailureCriteriaOriginalL1.Properties.EndInit();
    this.txtPressureCriteriaOriginalL1.Properties.EndInit();
    this.grcAssessmentConclusionModifiedL1.EndInit();
    this.grcAssessmentConclusionModifiedL1.ResumeLayout(false);
    this.tableLayoutPanel2.ResumeLayout(false);
    this.tableLayoutPanel2.PerformLayout();
    this.grcAssessmentCriteriaModifiedL1.EndInit();
    this.grcAssessmentCriteriaModifiedL1.ResumeLayout(false);
    this.tableLayoutPanel5.ResumeLayout(false);
    this.tableLayoutPanel5.PerformLayout();
    this.txtSFailureCriteriaModifiedL1.Properties.EndInit();
    this.txtPressureCriteriaModifiedL1.Properties.EndInit();
    this.grcAssessmentResultsModified.EndInit();
    this.grcAssessmentResultsModified.ResumeLayout(false);
    this.tableLayoutPanel6.ResumeLayout(false);
    this.tableLayoutPanel6.PerformLayout();
    this.txtPressureModifiedL1.Properties.EndInit();
    this.txtSFailureModifiedL1.Properties.EndInit();
    this.txtSflowModified.Properties.EndInit();
    this.txtSoModified.Properties.EndInit();
    this.grcAssessmentCriteriaOriginalL2.EndInit();
    this.grcAssessmentCriteriaOriginalL2.ResumeLayout(false);
    this.tableLayoutPanel8.ResumeLayout(false);
    this.tableLayoutPanel8.PerformLayout();
    this.txtSFailureCriteriaOriginalL2.Properties.EndInit();
    this.txtPressureCriteriaOriginalL2.Properties.EndInit();
    this.grcAssessmentConclusionModifiedL2.EndInit();
    this.grcAssessmentConclusionModifiedL2.ResumeLayout(false);
    this.tableLayoutPanel4.ResumeLayout(false);
    this.tableLayoutPanel4.PerformLayout();
    this.grcAssessmentCriteriaModifiedL2.EndInit();
    this.grcAssessmentCriteriaModifiedL2.ResumeLayout(false);
    this.tableLayoutPanel9.ResumeLayout(false);
    this.tableLayoutPanel9.PerformLayout();
    this.txtSFailureCriteriaModifiedL2.Properties.EndInit();
    this.txtPressureCriteriaModifiedL2.Properties.EndInit();
    this.grcAssessmentResultsModifiedL2.EndInit();
    this.grcAssessmentResultsModifiedL2.ResumeLayout(false);
    this.tableLayoutPanel10.ResumeLayout(false);
    this.tableLayoutPanel10.PerformLayout();
    this.txtminRSFiModified.Properties.EndInit();
    this.txtSFailureModifiedL2.Properties.EndInit();
    this.txtPressureModifiedL2.Properties.EndInit();
    this.pcBottom.EndInit();
    this.ResumeLayout(false);
  }
}
