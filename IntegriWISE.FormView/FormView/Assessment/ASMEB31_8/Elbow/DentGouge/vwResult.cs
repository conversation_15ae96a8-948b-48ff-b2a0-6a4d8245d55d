// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_8.Elbow.DentGouge.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using IntegriWISE.FormView.Assessment.AbstractView.DentGouge;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.AbstractUserInterface.DentGouge;
using IntegriWISE.UserInterface.Assessment.ASMEB31_8.Elbow.DentGouge;
using IntegriWISE.UserInterface.Record;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_8.Elbow.DentGouge;

public class vwResult : AbstractResultView, IResultView, I<PERSON><PERSON>ultB<PERSON>View, IView
{
  private ResultPresenter _presenter;

  public vwResult(IRecordView recordView)
  {
    this._recordView = recordView;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  public bool ExportToExcel() => false;

  public bool Calculate() => this._presenter.Calculate();
}
