// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.AbstractView.HIC.AbstractHICView
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.Common;
using IntegriWISE.FormView.Properties;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Assessment.AbstractUserInterface.HIC;
using IntegriWISE.UserInterface.Assessment.DesignCode.ASMEB31_3.HIC;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.AbstractView.HIC;

public class AbstractHICView : XtraUserControl, IHICView, IAssessmentBaseView, IView
{
  private IContainer components;
  private TableLayoutPanel tableLayoutPanel1;
  private LabelControl labelControl1;
  private LabelControl umLOSSe;
  private LabelControl umLOSSi;
  private LabelControl labelControl2;
  private LabelControl labelControl3;
  private LabelControl umFCAe;
  private LabelControl umFCAi;
  private LabelControl labelControl4;
  private TextEdit txtFCAi;
  private TextEdit txtFCAe;
  private TextEdit txtLOSSi;
  private TextEdit txtLOSSe;
  private DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider dxErrorProvider1;
  private LabelControl labelControl5;
  private TextEdit txtc;
  private TextEdit txts;
  private TextEdit txtLh;
  private TextEdit txtLw;
  private TextEdit txttmmID;
  private LabelControl labelControl6;
  private LabelControl labelControl7;
  private LabelControl labelControl12;
  private LabelControl labelControl13;
  private LabelControl umc;
  private LabelControl ums;
  private LabelControl umLh;
  private LabelControl umLw;
  private LabelControl umtmmID;
  private LabelControl umtmmOD;
  private TextEdit txttmmOD;
  private LabelControl labelControl8;
  private TextEdit txtLmsd;
  private LabelControl labelControl10;
  private LabelControl umLmsd;
  private PictureEdit pictureGeometry;
  private CheckEdit chkLevel2;
  private CheckEdit chkMAWP;
  private TextEdit txtLHs;
  private LabelControl lblLHs;
  private LabelControl umLHs;
  private HICPresenter _presenter;
  private IRecordView _recordView;
  private FormDirtyTracker _dirtyTracker;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.umLHs = new LabelControl();
    this.txtLHs = new TextEdit();
    this.lblLHs = new LabelControl();
    this.pictureGeometry = new PictureEdit();
    this.labelControl7 = new LabelControl();
    this.txtLh = new TextEdit();
    this.umLh = new LabelControl();
    this.labelControl6 = new LabelControl();
    this.txts = new TextEdit();
    this.ums = new LabelControl();
    this.umc = new LabelControl();
    this.labelControl5 = new LabelControl();
    this.txtc = new TextEdit();
    this.labelControl12 = new LabelControl();
    this.txtLw = new TextEdit();
    this.umLw = new LabelControl();
    this.labelControl4 = new LabelControl();
    this.txtLOSSe = new TextEdit();
    this.umLOSSe = new LabelControl();
    this.umLOSSi = new LabelControl();
    this.txtLOSSi = new TextEdit();
    this.labelControl3 = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.labelControl1 = new LabelControl();
    this.labelControl8 = new LabelControl();
    this.labelControl13 = new LabelControl();
    this.txtFCAe = new TextEdit();
    this.txtFCAi = new TextEdit();
    this.txttmmOD = new TextEdit();
    this.txttmmID = new TextEdit();
    this.umFCAe = new LabelControl();
    this.umFCAi = new LabelControl();
    this.umtmmOD = new LabelControl();
    this.umtmmID = new LabelControl();
    this.txtLmsd = new TextEdit();
    this.labelControl10 = new LabelControl();
    this.umLmsd = new LabelControl();
    this.chkLevel2 = new CheckEdit();
    this.chkMAWP = new CheckEdit();
    this.dxErrorProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(this.components);
    this.tableLayoutPanel1.SuspendLayout();
    this.txtLHs.Properties.BeginInit();
    this.pictureGeometry.Properties.BeginInit();
    this.txtLh.Properties.BeginInit();
    this.txts.Properties.BeginInit();
    this.txtc.Properties.BeginInit();
    this.txtLw.Properties.BeginInit();
    this.txtLOSSe.Properties.BeginInit();
    this.txtLOSSi.Properties.BeginInit();
    this.txtFCAe.Properties.BeginInit();
    this.txtFCAi.Properties.BeginInit();
    this.txttmmOD.Properties.BeginInit();
    this.txttmmID.Properties.BeginInit();
    this.txtLmsd.Properties.BeginInit();
    this.chkLevel2.Properties.BeginInit();
    this.chkMAWP.Properties.BeginInit();
    ((ISupportInitialize) this.dxErrorProvider1).BeginInit();
    this.SuspendLayout();
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.umLHs, 2, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLHs, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblLHs, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.pictureGeometry, 0, 14);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl7, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLh, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLh, 2, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl6, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txts, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.ums, 2, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.umc, 2, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl5, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtc, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl12, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLw, 1, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLw, 2, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl4, 0, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSe, 1, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSe, 2, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSi, 2, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSi, 1, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl3, 0, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 0, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl8, 0, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl13, 0, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAe, 1, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAi, 1, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.txttmmOD, 1, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.txttmmID, 1, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAe, 2, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAi, 2, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.umtmmOD, 2, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.umtmmID, 2, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLmsd, 1, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl10, 0, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLmsd, 2, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkLevel2, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkMAWP, 0, 0);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 15;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.Size = new Size(730, 493);
    this.tableLayoutPanel1.TabIndex = 0;
    this.umLHs.Location = new Point(480, 55);
    this.umLHs.Name = "umLHs";
    this.umLHs.Size = new Size(41, 13);
    this.umLHs.TabIndex = 4;
    this.umLHs.Text = "measure";
    this.umLHs.Visible = false;
    this.txtLHs.Location = new Point(376, 53);
    this.txtLHs.Margin = new Padding(1);
    this.txtLHs.Name = "txtLHs";
    this.txtLHs.Size = new Size(100, 20);
    this.txtLHs.TabIndex = 3;
    this.txtLHs.Visible = false;
    this.lblLHs.Location = new Point(13, 55);
    this.lblLHs.Name = "lblLHs";
    this.lblLHs.Size = new Size(248, 13);
    this.lblLHs.TabIndex = 2;
    this.lblLHs.Text = "HIC-to-HIC spacing in the longitudinal direction, LHs";
    this.lblLHs.Visible = false;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.pictureGeometry, 3);
    this.pictureGeometry.EditValue = (object) Resources.IW_HIC;
    this.pictureGeometry.Location = new Point(20, 326);
    this.pictureGeometry.Margin = new Padding(10);
    this.pictureGeometry.Name = "pictureGeometry";
    this.pictureGeometry.Properties.AllowFocused = false;
    this.pictureGeometry.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry.Properties.ReadOnly = true;
    this.pictureGeometry.Properties.ShowMenu = false;
    this.pictureGeometry.Size = new Size(220, 131);
    this.pictureGeometry.TabIndex = 38;
    this.labelControl7.Location = new Point(13, 121);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(196, 13);
    this.labelControl7.TabIndex = 11;
    this.labelControl7.Text = "HIC Damage-to-HIC Damage Spacing, Lh";
    this.txtLh.Location = new Point(376, 119);
    this.txtLh.Margin = new Padding(1);
    this.txtLh.Name = "txtLh";
    this.txtLh.Size = new Size(100, 20);
    this.txtLh.TabIndex = 12;
    this.umLh.Location = new Point(480, 121);
    this.umLh.Name = "umLh";
    this.umLh.Size = new Size(41, 13);
    this.umLh.TabIndex = 13;
    this.umLh.Text = "measure";
    this.labelControl6.Location = new Point(13, 99);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(216, 13);
    this.labelControl6.TabIndex = 8;
    this.labelControl6.Text = "HIC Dimension in the Longitudinal Direction, s";
    this.txts.Location = new Point(376, 97);
    this.txts.Margin = new Padding(1);
    this.txts.Name = "txts";
    this.txts.Size = new Size(100, 20);
    this.txts.TabIndex = 9;
    this.ums.Location = new Point(480, 99);
    this.ums.Name = "ums";
    this.ums.Size = new Size(41, 13);
    this.ums.TabIndex = 10;
    this.ums.Text = "measure";
    this.umc.Location = new Point(480, 77);
    this.umc.Name = "umc";
    this.umc.Size = new Size(41, 13);
    this.umc.TabIndex = 7;
    this.umc.Text = "measure";
    this.labelControl5.Location = new Point(13, 77);
    this.labelControl5.Name = "labelControl5";
    this.labelControl5.Size = new Size(231, 13);
    this.labelControl5.TabIndex = 5;
    this.labelControl5.Text = "HIC Dimension in the Circumferential Direction, c";
    this.txtc.Location = new Point(376, 75);
    this.txtc.Margin = new Padding(1);
    this.txtc.Name = "txtc";
    this.txtc.Size = new Size(100, 20);
    this.txtc.TabIndex = 6;
    this.labelControl12.Location = new Point(13, 143);
    this.labelControl12.Name = "labelControl12";
    this.labelControl12.Size = new Size(164, 13);
    this.labelControl12.TabIndex = 14;
    this.labelControl12.Text = "Spacing to Nearest Weld Joint, Lw";
    this.txtLw.Location = new Point(376, 141);
    this.txtLw.Margin = new Padding(1);
    this.txtLw.Name = "txtLw";
    this.txtLw.Size = new Size(100, 20);
    this.txtLw.TabIndex = 15;
    this.umLw.Location = new Point(480, 143);
    this.umLw.Name = "umLw";
    this.umLw.Size = new Size(41, 13);
    this.umLw.TabIndex = 16 /*0x10*/;
    this.umLw.Text = "measure";
    this.labelControl4.Location = new Point(13, 297);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(171, 13);
    this.labelControl4.TabIndex = 35;
    this.labelControl4.Text = "External Uniform Metal Loss, LOSSe";
    this.txtLOSSe.Location = new Point(376, 295);
    this.txtLOSSe.Margin = new Padding(1);
    this.txtLOSSe.Name = "txtLOSSe";
    this.txtLOSSe.Size = new Size(100, 20);
    this.txtLOSSe.TabIndex = 36;
    this.umLOSSe.Location = new Point(480, 297);
    this.umLOSSe.Name = "umLOSSe";
    this.umLOSSe.Size = new Size(41, 13);
    this.umLOSSe.TabIndex = 37;
    this.umLOSSe.Text = "measure";
    this.umLOSSi.Location = new Point(480, 275);
    this.umLOSSi.Name = "umLOSSi";
    this.umLOSSi.Size = new Size(41, 13);
    this.umLOSSi.TabIndex = 34;
    this.umLOSSi.Text = "measure";
    this.txtLOSSi.Location = new Point(376, 273);
    this.txtLOSSi.Margin = new Padding(1);
    this.txtLOSSi.Name = "txtLOSSi";
    this.txtLOSSi.Size = new Size(100, 20);
    this.txtLOSSi.TabIndex = 33;
    this.labelControl3.Location = new Point(13, 275);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(165, 13);
    this.labelControl3.TabIndex = 32 /*0x20*/;
    this.labelControl3.Text = "Internal Uniform Metal Loss, LOSSi";
    this.labelControl2.Location = new Point(13, 253);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(208 /*0xD0*/, 13);
    this.labelControl2.TabIndex = 29;
    this.labelControl2.Text = "External Future Corrosion Allowance, FCAe";
    this.labelControl1.Location = new Point(13, 231);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(202, 13);
    this.labelControl1.TabIndex = 26;
    this.labelControl1.Text = "Internal Future Corrosion Allowance, FCAi";
    this.labelControl8.Location = new Point(13, 209);
    this.labelControl8.Name = "labelControl8";
    this.labelControl8.Size = new Size(359, 13);
    this.labelControl8.TabIndex = 23;
    this.labelControl8.Text = "Minimum Measured Thickness of the External Side of HIC Damage, tmm-OD";
    this.labelControl13.Location = new Point(13, 187);
    this.labelControl13.Name = "labelControl13";
    this.labelControl13.Size = new Size(353, 13);
    this.labelControl13.TabIndex = 20;
    this.labelControl13.Text = "Minimum Measured Thickness of the Internal Side of HIC Damage, tmm-ID";
    this.txtFCAe.Location = new Point(376, 251);
    this.txtFCAe.Margin = new Padding(1);
    this.txtFCAe.Name = "txtFCAe";
    this.txtFCAe.Size = new Size(100, 20);
    this.txtFCAe.TabIndex = 30;
    this.txtFCAi.Location = new Point(376, 229);
    this.txtFCAi.Margin = new Padding(1);
    this.txtFCAi.Name = "txtFCAi";
    this.txtFCAi.Size = new Size(100, 20);
    this.txtFCAi.TabIndex = 27;
    this.txttmmOD.Location = new Point(376, 207);
    this.txttmmOD.Margin = new Padding(1);
    this.txttmmOD.Name = "txttmmOD";
    this.txttmmOD.Size = new Size(100, 20);
    this.txttmmOD.TabIndex = 24;
    this.txttmmID.Location = new Point(376, 185);
    this.txttmmID.Margin = new Padding(1);
    this.txttmmID.Name = "txttmmID";
    this.txttmmID.Size = new Size(100, 20);
    this.txttmmID.TabIndex = 21;
    this.umFCAe.Location = new Point(480, 253);
    this.umFCAe.Name = "umFCAe";
    this.umFCAe.Size = new Size(41, 13);
    this.umFCAe.TabIndex = 31 /*0x1F*/;
    this.umFCAe.Text = "measure";
    this.umFCAi.Location = new Point(480, 231);
    this.umFCAi.Name = "umFCAi";
    this.umFCAi.Size = new Size(41, 13);
    this.umFCAi.TabIndex = 28;
    this.umFCAi.Text = "measure";
    this.umtmmOD.Location = new Point(480, 209);
    this.umtmmOD.Name = "umtmmOD";
    this.umtmmOD.Size = new Size(41, 13);
    this.umtmmOD.TabIndex = 25;
    this.umtmmOD.Text = "measure";
    this.umtmmID.Location = new Point(480, 187);
    this.umtmmID.Name = "umtmmID";
    this.umtmmID.Size = new Size(41, 13);
    this.umtmmID.TabIndex = 22;
    this.umtmmID.Text = "measure";
    this.txtLmsd.Location = new Point(376, 163);
    this.txtLmsd.Margin = new Padding(1);
    this.txtLmsd.Name = "txtLmsd";
    this.txtLmsd.Size = new Size(100, 20);
    this.txtLmsd.TabIndex = 18;
    this.labelControl10.Location = new Point(13, 165);
    this.labelControl10.Name = "labelControl10";
    this.labelControl10.Size = new Size(225, 13);
    this.labelControl10.TabIndex = 17;
    this.labelControl10.Text = "Spacing to Major Structural Discontinuity, Lmsd";
    this.umLmsd.Location = new Point(480, 165);
    this.umLmsd.Name = "umLmsd";
    this.umLmsd.Size = new Size(41, 13);
    this.umLmsd.TabIndex = 19;
    this.umLmsd.Text = "measure";
    this.chkLevel2.Location = new Point(11, 32 /*0x20*/);
    this.chkLevel2.Margin = new Padding(1);
    this.chkLevel2.Name = "chkLevel2";
    this.chkLevel2.Properties.Caption = "Level 2";
    this.chkLevel2.Size = new Size(120, 19);
    this.chkLevel2.TabIndex = 1;
    this.chkLevel2.CheckedChanged += new EventHandler(this.chkLevel2_CheckedChanged);
    this.chkMAWP.Location = new Point(11, 11);
    this.chkMAWP.Margin = new Padding(1);
    this.chkMAWP.Name = "chkMAWP";
    this.chkMAWP.Properties.AutoWidth = true;
    this.chkMAWP.Properties.Caption = "MAWP";
    this.chkMAWP.Size = new Size(54, 19);
    this.chkMAWP.TabIndex = 0;
    this.dxErrorProvider1.ContainerControl = (ContainerControl) this;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = "vwHIC";
    this.Size = new Size(730, 493);
    this.Load += new EventHandler(this.AbstractHICView_Load);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.txtLHs.Properties.EndInit();
    this.pictureGeometry.Properties.EndInit();
    this.txtLh.Properties.EndInit();
    this.txts.Properties.EndInit();
    this.txtc.Properties.EndInit();
    this.txtLw.Properties.EndInit();
    this.txtLOSSe.Properties.EndInit();
    this.txtLOSSi.Properties.EndInit();
    this.txtFCAe.Properties.EndInit();
    this.txtFCAi.Properties.EndInit();
    this.txttmmOD.Properties.EndInit();
    this.txttmmID.Properties.EndInit();
    this.txtLmsd.Properties.EndInit();
    this.chkLevel2.Properties.EndInit();
    this.chkMAWP.Properties.EndInit();
    ((ISupportInitialize) this.dxErrorProvider1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public AbstractHICView(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.AssessmentView = (IAssessmentBaseView) this;
    this._presenter = new HICPresenter(this._recordView, (IHICView) this);
  }

  private void AbstractHICView_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView);
    this._dirtyTracker.IsHandled = true;
    this.txtc.AllowOnlyN4();
    this.txts.AllowOnlyN4();
    this.txtLh.AllowOnlyN4();
    this.txtLmsd.AllowOnlyN4();
    this.txttmmOD.AllowOnlyN4();
    this.txtLw.AllowOnlyN4();
    this.txttmmID.AllowOnlyN4();
    this.txtFCAi.AllowOnlyN4();
    this.txtFCAe.AllowOnlyN4();
    this.txtLOSSi.AllowOnlyN4();
    this.txtLOSSe.AllowOnlyN4();
    this.txtLHs.AllowOnlyN4();
    this._presenter.LoadAssessment();
    this._dirtyTracker.IsHandled = false;
  }

  public int? AssessmentID { get; set; }

  public bool Level2
  {
    get => this.chkLevel2.Checked;
    set => this.chkLevel2.Checked = value;
  }

  public bool MAWP
  {
    get => this.chkMAWP.Checked;
    set => this.chkMAWP.Checked = value;
  }

  public double? c
  {
    get => Helpers.ParseNullDouble((object) this.txtc.Text);
    set => this.txtc.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? s
  {
    get => Helpers.ParseNullDouble((object) this.txts.Text);
    set => this.txts.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Lh
  {
    get => Helpers.ParseNullDouble((object) this.txtLh.Text);
    set => this.txtLh.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Lw
  {
    get => Helpers.ParseNullDouble((object) this.txtLw.Text);
    set => this.txtLw.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Lmsd
  {
    get => Helpers.ParseNullDouble((object) this.txtLmsd.Text);
    set => this.txtLmsd.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? tmmMinusID
  {
    get => Helpers.ParseNullDouble((object) this.txttmmID.Text);
    set => this.txttmmID.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? tmmMinusOD
  {
    get => Helpers.ParseNullDouble((object) this.txttmmOD.Text);
    set => this.txttmmOD.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? FCAi
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAi.Text);
    set => this.txtFCAi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? FCAe
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAe.Text);
    set => this.txtFCAe.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSi
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSi.Text);
    set => this.txtLOSSi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSe
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSe.Text);
    set => this.txtLOSSe.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LHs
  {
    get => Helpers.ParseNullDouble((object) this.txtLHs.Text);
    set => this.txtLHs.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool Level2InputVisible
  {
    set
    {
      this.lblLHs.Visible = value;
      this.txtLHs.Visible = value;
      this.umLHs.Visible = value;
    }
  }

  public string UMc
  {
    set => this.umc.Text = value;
  }

  public string UMs
  {
    set => this.ums.Text = value;
  }

  public string UMLh
  {
    set => this.umLh.Text = value;
  }

  public string UMLw
  {
    set => this.umLw.Text = value;
  }

  public string UMLmsd
  {
    set => this.umLmsd.Text = value;
  }

  public string UMtmmMinusID
  {
    set => this.umtmmID.Text = value;
  }

  public string UMtmmMinusOD
  {
    set => this.umtmmOD.Text = value;
  }

  public string UMFCAi
  {
    set => this.umFCAi.Text = value;
  }

  public string UMFCAe
  {
    set => this.umFCAe.Text = value;
  }

  public string UMLOSSi
  {
    set => this.umLOSSi.Text = value;
  }

  public string UMLOSSe
  {
    set => this.umLOSSe.Text = value;
  }

  public string UMLHs
  {
    set => this.umLHs.Text = value;
  }

  public string cErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtc, value);
  }

  public string sErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txts, value);
  }

  public string LhErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLh, value);
  }

  public string LwErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLw, value);
  }

  public string LmsdErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLmsd, value);
  }

  public string tmmMinusIDErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txttmmID, value);
  }

  public string tmmMinusODErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txttmmOD, value);
  }

  public string FCAiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAi, value);
  }

  public string FCAeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAe, value);
  }

  public string LOSSiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSi, value);
  }

  public string LOSSeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSe, value);
  }

  public string LHsErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLHs, value);
  }

  public string cInfo
  {
    set => this.txtc.ToolTip = value;
  }

  public string sInfo
  {
    set => this.txts.ToolTip = value;
  }

  public string LhInfo
  {
    set => this.txtLh.ToolTip = value;
  }

  public string LwInfo
  {
    set => this.txtLw.ToolTip = value;
  }

  public string LmsdInfo
  {
    set => this.txtLmsd.ToolTip = value;
  }

  public string tmmMinusIDInfo
  {
    set => this.txttmmID.ToolTip = value;
  }

  public string tmmMinusODInfo
  {
    set => this.txttmmOD.ToolTip = value;
  }

  public string FCAiInfo
  {
    set => this.txtFCAi.ToolTip = value;
  }

  public string FCAeInfo
  {
    set => this.txtFCAe.ToolTip = value;
  }

  public string LOSSiInfo
  {
    set => this.txtLOSSi.ToolTip = value;
  }

  public string LOSSeInfo
  {
    set => this.txtLOSSe.ToolTip = value;
  }

  public string LHsInfo
  {
    set => this.txtLHs.ToolTip = value;
  }

  public bool ValidateAssessment() => this._presenter.Validate();

  public void Save() => this._presenter.Save();

  public void ClearErrors() => this.dxErrorProvider1.ClearErrors();

  private void chkLevel2_CheckedChanged(object sender, EventArgs e)
  {
    this._presenter.Level2CheckChanged();
  }
}
