// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.AbstractView.Blister.AbstractBlisterView
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.Common;
using IntegriWISE.DataTransferObjects.AssessmentData;
using IntegriWISE.FormView.Properties;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Assessment.AbstractUserInterface.Blister;
using IntegriWISE.UserInterface.Assessment.DesignCode.ASMEB31_3.Blister;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.AbstractView.Blister;

public abstract class AbstractBlisterView : XtraUserControl, IBlisterView, IAssessmentBaseView, IView
{
  private IContainer components;
  private TableLayoutPanel tableLayoutPanel1;
  private LabelControl labelControl1;
  private LabelControl umLOSSe;
  private LabelControl umLOSSi;
  private LabelControl labelControl2;
  private LabelControl labelControl3;
  private LabelControl umFCAe;
  private LabelControl umFCAi;
  private LabelControl labelControl4;
  private TextEdit txtFCAi;
  private TextEdit txtFCAe;
  private TextEdit txtLOSSi;
  private TextEdit txtLOSSe;
  private DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider dxErrorProvider1;
  private LabelControl labelControl5;
  private TextEdit txtc;
  private TextEdit txts;
  private TextEdit txtLb;
  private TextEdit txtBp;
  private TextEdit txttmm;
  private TextEdit txtSc;
  private TextEdit txtLw;
  private TextEdit txtLmsd;
  private LabelControl labelControl6;
  private LabelControl labelControl7;
  private LabelControl labelControl8;
  private LabelControl labelControl9;
  private LabelControl LabelControl10;
  private LabelControl lblSc;
  private LabelControl labelControl12;
  private LabelControl labelControl13;
  private LabelControl umc;
  private LabelControl ums;
  private LabelControl umLb;
  private LabelControl umBp;
  private LabelControl umtmm;
  private LabelControl umSc;
  private LabelControl umLw;
  private LabelControl umLmsd;
  private PictureEdit pictureGeometry1;
  private PictureEdit pictureGeometry2;
  private TableLayoutPanel tableLayoutPanel2;
  private ComboBoxEdit cboVent;
  private CheckEdit chkMAWP;
  private CheckEdit chkLevel2;
  private BlisterPresenter _presenter;
  private IRecordView _recordView;
  private FormDirtyTracker _dirtyTracker;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.chkLevel2 = new CheckEdit();
    this.cboVent = new ComboBoxEdit();
    this.labelControl4 = new LabelControl();
    this.txtLOSSe = new TextEdit();
    this.umLOSSe = new LabelControl();
    this.labelControl3 = new LabelControl();
    this.txtLOSSi = new TextEdit();
    this.umLOSSi = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.txtFCAe = new TextEdit();
    this.txtFCAi = new TextEdit();
    this.umFCAe = new LabelControl();
    this.umFCAi = new LabelControl();
    this.labelControl1 = new LabelControl();
    this.labelControl5 = new LabelControl();
    this.txtc = new TextEdit();
    this.txts = new TextEdit();
    this.txtLb = new TextEdit();
    this.txtBp = new TextEdit();
    this.txttmm = new TextEdit();
    this.txtSc = new TextEdit();
    this.txtLw = new TextEdit();
    this.txtLmsd = new TextEdit();
    this.labelControl6 = new LabelControl();
    this.labelControl7 = new LabelControl();
    this.labelControl8 = new LabelControl();
    this.labelControl9 = new LabelControl();
    this.LabelControl10 = new LabelControl();
    this.lblSc = new LabelControl();
    this.labelControl12 = new LabelControl();
    this.labelControl13 = new LabelControl();
    this.umc = new LabelControl();
    this.ums = new LabelControl();
    this.umLb = new LabelControl();
    this.umBp = new LabelControl();
    this.umtmm = new LabelControl();
    this.umSc = new LabelControl();
    this.umLw = new LabelControl();
    this.umLmsd = new LabelControl();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.pictureGeometry1 = new PictureEdit();
    this.pictureGeometry2 = new PictureEdit();
    this.chkMAWP = new CheckEdit();
    this.dxErrorProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(this.components);
    this.tableLayoutPanel1.SuspendLayout();
    this.chkLevel2.Properties.BeginInit();
    this.cboVent.Properties.BeginInit();
    this.txtLOSSe.Properties.BeginInit();
    this.txtLOSSi.Properties.BeginInit();
    this.txtFCAe.Properties.BeginInit();
    this.txtFCAi.Properties.BeginInit();
    this.txtc.Properties.BeginInit();
    this.txts.Properties.BeginInit();
    this.txtLb.Properties.BeginInit();
    this.txtBp.Properties.BeginInit();
    this.txttmm.Properties.BeginInit();
    this.txtSc.Properties.BeginInit();
    this.txtLw.Properties.BeginInit();
    this.txtLmsd.Properties.BeginInit();
    this.tableLayoutPanel2.SuspendLayout();
    this.pictureGeometry1.Properties.BeginInit();
    this.pictureGeometry2.Properties.BeginInit();
    this.chkMAWP.Properties.BeginInit();
    ((ISupportInitialize) this.dxErrorProvider1).BeginInit();
    this.SuspendLayout();
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.chkLevel2, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.cboVent, 1, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl4, 0, 14);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSe, 1, 14);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSe, 2, 14);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl3, 0, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSi, 1, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSi, 2, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAe, 1, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAi, 1, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAe, 2, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAi, 2, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 0, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl5, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtc, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txts, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLb, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtBp, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txttmm, 1, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtSc, 1, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLw, 1, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLmsd, 1, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl6, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl7, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl8, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl9, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.LabelControl10, 0, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblSc, 0, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl12, 0, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl13, 0, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.umc, 2, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.ums, 2, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLb, 2, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.umBp, 2, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.umtmm, 2, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.umSc, 2, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLw, 2, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLmsd, 2, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel2, 0, 15);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkMAWP, 0, 0);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 16 /*0x10*/;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.Size = new Size(525, 518);
    this.tableLayoutPanel1.TabIndex = 0;
    this.chkLevel2.Location = new Point(11, 31 /*0x1F*/);
    this.chkLevel2.Margin = new Padding(1);
    this.chkLevel2.Name = "chkLevel2";
    this.chkLevel2.Properties.Caption = "Level 2";
    this.chkLevel2.Size = new Size(120, 19);
    this.chkLevel2.TabIndex = 1;
    this.cboVent.Location = new Point(316, 161);
    this.cboVent.Margin = new Padding(1);
    this.cboVent.Name = "cboVent";
    this.cboVent.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Combo)
    });
    this.cboVent.Properties.Items.AddRange(new object[3]
    {
      (object) "No",
      (object) "Vent",
      (object) "Crown"
    });
    this.cboVent.Size = new Size(100, 20);
    this.cboVent.TabIndex = 18;
    this.cboVent.SelectedIndexChanged += new EventHandler(this.cboVent_SelectedIndexChanged);
    this.labelControl4.Location = new Point(13, 317);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(171, 13);
    this.labelControl4.TabIndex = 37;
    this.labelControl4.Text = "External Uniform Metal Loss, LOSSe";
    this.txtLOSSe.Location = new Point(316, 315);
    this.txtLOSSe.Margin = new Padding(1);
    this.txtLOSSe.Name = "txtLOSSe";
    this.txtLOSSe.Size = new Size(100, 20);
    this.txtLOSSe.TabIndex = 38;
    this.umLOSSe.Location = new Point(420, 317);
    this.umLOSSe.Name = "umLOSSe";
    this.umLOSSe.Size = new Size(41, 13);
    this.umLOSSe.TabIndex = 39;
    this.umLOSSe.Text = "measure";
    this.labelControl3.Location = new Point(13, 295);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(165, 13);
    this.labelControl3.TabIndex = 34;
    this.labelControl3.Text = "Internal Uniform Metal Loss, LOSSi";
    this.txtLOSSi.Location = new Point(316, 293);
    this.txtLOSSi.Margin = new Padding(1);
    this.txtLOSSi.Name = "txtLOSSi";
    this.txtLOSSi.Size = new Size(100, 20);
    this.txtLOSSi.TabIndex = 35;
    this.umLOSSi.Location = new Point(420, 295);
    this.umLOSSi.Name = "umLOSSi";
    this.umLOSSi.Size = new Size(41, 13);
    this.umLOSSi.TabIndex = 36;
    this.umLOSSi.Text = "measure";
    this.labelControl2.Location = new Point(13, 273);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(208 /*0xD0*/, 13);
    this.labelControl2.TabIndex = 31 /*0x1F*/;
    this.labelControl2.Text = "External Future Corrosion Allowance, FCAe";
    this.txtFCAe.Location = new Point(316, 271);
    this.txtFCAe.Margin = new Padding(1);
    this.txtFCAe.Name = "txtFCAe";
    this.txtFCAe.Size = new Size(100, 20);
    this.txtFCAe.TabIndex = 32 /*0x20*/;
    this.txtFCAi.Location = new Point(316, 249);
    this.txtFCAi.Margin = new Padding(1);
    this.txtFCAi.Name = "txtFCAi";
    this.txtFCAi.Size = new Size(100, 20);
    this.txtFCAi.TabIndex = 29;
    this.umFCAe.Location = new Point(420, 273);
    this.umFCAe.Name = "umFCAe";
    this.umFCAe.Size = new Size(41, 13);
    this.umFCAe.TabIndex = 33;
    this.umFCAe.Text = "measure";
    this.umFCAi.Location = new Point(420, 251);
    this.umFCAi.Name = "umFCAi";
    this.umFCAi.Size = new Size(41, 13);
    this.umFCAi.TabIndex = 30;
    this.umFCAi.Text = "measure";
    this.labelControl1.Location = new Point(13, 251);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(202, 13);
    this.labelControl1.TabIndex = 28;
    this.labelControl1.Text = "Internal Future Corrosion Allowance, FCAi";
    this.labelControl5.Location = new Point(13, 53);
    this.labelControl5.Name = "labelControl5";
    this.labelControl5.Size = new Size(242, 13);
    this.labelControl5.TabIndex = 2;
    this.labelControl5.Text = "Blister Dimension in the Circumferential Direction, c";
    this.txtc.Location = new Point(316, 51);
    this.txtc.Margin = new Padding(1);
    this.txtc.Name = "txtc";
    this.txtc.Size = new Size(100, 20);
    this.txtc.TabIndex = 3;
    this.txts.Location = new Point(316, 73);
    this.txts.Margin = new Padding(1);
    this.txts.Name = "txts";
    this.txts.Size = new Size(100, 20);
    this.txts.TabIndex = 6;
    this.txtLb.Location = new Point(316, 95);
    this.txtLb.Margin = new Padding(1);
    this.txtLb.Name = "txtLb";
    this.txtLb.Size = new Size(100, 20);
    this.txtLb.TabIndex = 9;
    this.txtBp.Location = new Point(316, 117);
    this.txtBp.Margin = new Padding(1);
    this.txtBp.Name = "txtBp";
    this.txtBp.Size = new Size(100, 20);
    this.txtBp.TabIndex = 12;
    this.txttmm.Location = new Point(316, 139);
    this.txttmm.Margin = new Padding(1);
    this.txttmm.Name = "txttmm";
    this.txttmm.Size = new Size(100, 20);
    this.txttmm.TabIndex = 15;
    this.txtSc.Location = new Point(316, 183);
    this.txtSc.Margin = new Padding(1);
    this.txtSc.Name = "txtSc";
    this.txtSc.Size = new Size(100, 20);
    this.txtSc.TabIndex = 20;
    this.txtLw.Location = new Point(316, 205);
    this.txtLw.Margin = new Padding(1);
    this.txtLw.Name = "txtLw";
    this.txtLw.Size = new Size(100, 20);
    this.txtLw.TabIndex = 23;
    this.txtLmsd.Location = new Point(316, 227);
    this.txtLmsd.Margin = new Padding(1);
    this.txtLmsd.Name = "txtLmsd";
    this.txtLmsd.Size = new Size(100, 20);
    this.txtLmsd.TabIndex = 26;
    this.labelControl6.Location = new Point(13, 75);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(227, 13);
    this.labelControl6.TabIndex = 5;
    this.labelControl6.Text = "Blister Dimension in the Longitudinal Direction, s";
    this.labelControl7.Location = new Point(13, 97);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(133, 13);
    this.labelControl7.TabIndex = 8;
    this.labelControl7.Text = "Blister-to-blister spacing, Lb";
    this.labelControl8.Location = new Point(13, 119);
    this.labelControl8.Name = "labelControl8";
    this.labelControl8.Size = new Size(128 /*0x80*/, 13);
    this.labelControl8.TabIndex = 11;
    this.labelControl8.Text = "Blister Bulge Projection, Bp";
    this.labelControl9.Location = new Point(13, 141);
    this.labelControl9.Name = "labelControl9";
    this.labelControl9.Size = new Size(166, 13);
    this.labelControl9.TabIndex = 14;
    this.labelControl9.Text = "Minimum Measured Thickness, tmm";
    this.LabelControl10.Location = new Point(13, 163);
    this.LabelControl10.Name = "LabelControl10";
    this.LabelControl10.Size = new Size(231, 13);
    this.LabelControl10.TabIndex = 17;
    this.LabelControl10.Text = "Cracks or Vent Holes on the Crown of the Blister";
    this.lblSc.Location = new Point(13, 185);
    this.lblSc.Name = "lblSc";
    this.lblSc.Size = new Size(299, 13);
    this.lblSc.TabIndex = 19;
    this.lblSc.Text = "Length of a Blister Crown Crack or Diameter of a Vent Hole, Sc";
    this.labelControl12.Location = new Point(13, 207);
    this.labelControl12.Name = "labelControl12";
    this.labelControl12.Size = new Size(164, 13);
    this.labelControl12.TabIndex = 22;
    this.labelControl12.Text = "Spacing to Nearest Weld Joint, Lw";
    this.labelControl13.Location = new Point(13, 229);
    this.labelControl13.Name = "labelControl13";
    this.labelControl13.Size = new Size(225, 13);
    this.labelControl13.TabIndex = 25;
    this.labelControl13.Text = "Spacing to Major Structural Discontinuity, Lmsd";
    this.umc.Location = new Point(420, 53);
    this.umc.Name = "umc";
    this.umc.Size = new Size(41, 13);
    this.umc.TabIndex = 4;
    this.umc.Text = "measure";
    this.ums.Location = new Point(420, 75);
    this.ums.Name = "ums";
    this.ums.Size = new Size(41, 13);
    this.ums.TabIndex = 7;
    this.ums.Text = "measure";
    this.umLb.Location = new Point(420, 97);
    this.umLb.Name = "umLb";
    this.umLb.Size = new Size(41, 13);
    this.umLb.TabIndex = 10;
    this.umLb.Text = "measure";
    this.umBp.Location = new Point(420, 119);
    this.umBp.Name = "umBp";
    this.umBp.Size = new Size(41, 13);
    this.umBp.TabIndex = 13;
    this.umBp.Text = "measure";
    this.umtmm.Location = new Point(420, 141);
    this.umtmm.Name = "umtmm";
    this.umtmm.Size = new Size(41, 13);
    this.umtmm.TabIndex = 16 /*0x10*/;
    this.umtmm.Text = "measure";
    this.umSc.Location = new Point(420, 185);
    this.umSc.Name = "umSc";
    this.umSc.Size = new Size(41, 13);
    this.umSc.TabIndex = 21;
    this.umSc.Text = "measure";
    this.umLw.Location = new Point(420, 207);
    this.umLw.Name = "umLw";
    this.umLw.Size = new Size(41, 13);
    this.umLw.TabIndex = 24;
    this.umLw.Text = "measure";
    this.umLmsd.Location = new Point(420, 229);
    this.umLmsd.Name = "umLmsd";
    this.umLmsd.Size = new Size(41, 13);
    this.umLmsd.TabIndex = 27;
    this.umLmsd.Text = "measure";
    this.tableLayoutPanel2.AutoSize = true;
    this.tableLayoutPanel2.ColumnCount = 2;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.tableLayoutPanel2, 3);
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel2.Controls.Add((Control) this.pictureGeometry1, 0, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.pictureGeometry2, 1, 0);
    this.tableLayoutPanel2.Location = new Point(13, 339);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.RowCount = 1;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Absolute, 151f));
    this.tableLayoutPanel2.Size = new Size(480, 151);
    this.tableLayoutPanel2.TabIndex = 40;
    this.pictureGeometry1.EditValue = (object) Resources.IW_Blister_1;
    this.pictureGeometry1.Location = new Point(10, 10);
    this.pictureGeometry1.Margin = new Padding(10);
    this.pictureGeometry1.Name = "pictureGeometry1";
    this.pictureGeometry1.Properties.AllowFocused = false;
    this.pictureGeometry1.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry1.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry1.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry1.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry1.Properties.ReadOnly = true;
    this.pictureGeometry1.Properties.ShowMenu = false;
    this.pictureGeometry1.Size = new Size(220, 131);
    this.pictureGeometry1.TabIndex = 0;
    this.pictureGeometry2.EditValue = (object) Resources.IW_Blister_2;
    this.pictureGeometry2.Location = new Point(250, 10);
    this.pictureGeometry2.Margin = new Padding(10);
    this.pictureGeometry2.Name = "pictureGeometry2";
    this.pictureGeometry2.Properties.AllowFocused = false;
    this.pictureGeometry2.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry2.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry2.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry2.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry2.Properties.ReadOnly = true;
    this.pictureGeometry2.Properties.ShowMenu = false;
    this.pictureGeometry2.Size = new Size(220, 131);
    this.pictureGeometry2.TabIndex = 1;
    this.chkMAWP.Location = new Point(11, 11);
    this.chkMAWP.Margin = new Padding(1);
    this.chkMAWP.Name = "chkMAWP";
    this.chkMAWP.Properties.AutoWidth = true;
    this.chkMAWP.Properties.Caption = "MAWP";
    this.chkMAWP.Size = new Size(54, 19);
    this.chkMAWP.TabIndex = 0;
    this.dxErrorProvider1.ContainerControl = (ContainerControl) this;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = nameof (AbstractBlisterView);
    this.Size = new Size(525, 518);
    this.Load += new EventHandler(this.AbstractBlisterView_Load);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.chkLevel2.Properties.EndInit();
    this.cboVent.Properties.EndInit();
    this.txtLOSSe.Properties.EndInit();
    this.txtLOSSi.Properties.EndInit();
    this.txtFCAe.Properties.EndInit();
    this.txtFCAi.Properties.EndInit();
    this.txtc.Properties.EndInit();
    this.txts.Properties.EndInit();
    this.txtLb.Properties.EndInit();
    this.txtBp.Properties.EndInit();
    this.txttmm.Properties.EndInit();
    this.txtSc.Properties.EndInit();
    this.txtLw.Properties.EndInit();
    this.txtLmsd.Properties.EndInit();
    this.tableLayoutPanel2.ResumeLayout(false);
    this.pictureGeometry1.Properties.EndInit();
    this.pictureGeometry2.Properties.EndInit();
    this.chkMAWP.Properties.EndInit();
    ((ISupportInitialize) this.dxErrorProvider1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public AbstractBlisterView(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.AssessmentView = (IAssessmentBaseView) this;
    this._presenter = new BlisterPresenter(this._recordView, (IBlisterView) this);
    this.umSc.Visible = false;
    this.txtSc.Visible = false;
    this.lblSc.Visible = false;
  }

  private void AbstractBlisterView_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView)
    {
      IsHandled = true
    };
    this.txtc.AllowOnlyN4();
    this.txts.AllowOnlyN4();
    this.txtLb.AllowOnlyN4();
    this.txtBp.AllowOnlyN4();
    this.txttmm.AllowOnlyN4();
    this.txtSc.AllowOnlyN4();
    this.txtLw.AllowOnlyN4();
    this.txtLmsd.AllowOnlyN4();
    this.txtFCAi.AllowOnlyN4();
    this.txtFCAe.AllowOnlyN4();
    this.txtLOSSi.AllowOnlyN4();
    this.txtLOSSe.AllowOnlyN4();
    this._presenter.LoadAssessment();
    this._dirtyTracker.IsHandled = false;
  }

  public bool EnableSc
  {
    set
    {
      this.umSc.Visible = value;
      this.txtSc.Visible = value;
      this.lblSc.Visible = value;
    }
  }

  public bool Level2
  {
    get => this.chkLevel2.Checked;
    set => this.chkLevel2.Checked = value;
  }

  public bool MAWP
  {
    get => this.chkMAWP.Checked;
    set => this.chkMAWP.Checked = value;
  }

  public int? AssessmentID { get; set; }

  public double? c
  {
    get => Helpers.ParseNullDouble((object) this.txtc.Text);
    set => this.txtc.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? s
  {
    get => Helpers.ParseNullDouble((object) this.txts.Text);
    set => this.txts.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LB
  {
    get => Helpers.ParseNullDouble((object) this.txtLb.Text);
    set => this.txtLb.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Bp
  {
    get => Helpers.ParseNullDouble((object) this.txtBp.Text);
    set => this.txtBp.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? tmm
  {
    get => Helpers.ParseNullDouble((object) this.txttmm.Text);
    set => this.txttmm.Text = Helpers.ParseObjectToString((object) value);
  }

  public VentType Vented
  {
    get => (VentType) this.cboVent.SelectedIndex;
    set => this.cboVent.SelectedIndex = (int) value;
  }

  public double? Sc
  {
    get => Helpers.ParseNullDouble((object) this.txtSc.Text);
    set => this.txtSc.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Lw
  {
    get => Helpers.ParseNullDouble((object) this.txtLw.Text);
    set => this.txtLw.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Lmsd
  {
    get => Helpers.ParseNullDouble((object) this.txtLmsd.Text);
    set => this.txtLmsd.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? FCAi
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAi.Text);
    set => this.txtFCAi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? FCAe
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAe.Text);
    set => this.txtFCAe.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSi
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSi.Text);
    set => this.txtLOSSi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSe
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSe.Text);
    set => this.txtLOSSe.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMc
  {
    set => this.umc.Text = value;
  }

  public string UMs
  {
    set => this.ums.Text = value;
  }

  public string UMLB
  {
    set => this.umLb.Text = value;
  }

  public string UMBp
  {
    set => this.umBp.Text = value;
  }

  public string UMtmm
  {
    set => this.umtmm.Text = value;
  }

  public string UMSc
  {
    set => this.umSc.Text = value;
  }

  public string UMLw
  {
    set => this.umLw.Text = value;
  }

  public string UMLmsd
  {
    set => this.umLmsd.Text = value;
  }

  public string UMFCAi
  {
    set => this.umFCAi.Text = value;
  }

  public string UMFCAe
  {
    set => this.umFCAe.Text = value;
  }

  public string UMLOSSi
  {
    set => this.umLOSSi.Text = value;
  }

  public string UMLOSSe
  {
    set => this.umLOSSe.Text = value;
  }

  public string cErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtc, value);
  }

  public string sErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txts, value);
  }

  public string LBErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLb, value);
  }

  public string BpErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtBp, value);
  }

  public string tmmErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txttmm, value);
  }

  public string ScErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtSc, value);
  }

  public string LwErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLw, value);
  }

  public string LmsdErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLmsd, value);
  }

  public string VentedErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.cboVent, value);
  }

  public string FCAiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAi, value);
  }

  public string FCAeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAe, value);
  }

  public string LOSSiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSi, value);
  }

  public string LOSSeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSe, value);
  }

  public string cInfo
  {
    set => this.txtc.ToolTip = value;
  }

  public string sInfo
  {
    set => this.txts.ToolTip = value;
  }

  public string LBInfo
  {
    set => this.txtLb.ToolTip = value;
  }

  public string BpInfo
  {
    set => this.txtBp.ToolTip = value;
  }

  public string tmmInfo
  {
    set => this.txttmm.ToolTip = value;
  }

  public string ScInfo
  {
    set => this.txtSc.ToolTip = value;
  }

  public string LwInfo
  {
    set => this.txtLw.ToolTip = value;
  }

  public string LmsdInfo
  {
    set => this.txtLmsd.ToolTip = value;
  }

  public string VentedInfo
  {
    set => this.cboVent.ToolTip = value;
  }

  public string FCAiInfo
  {
    set => this.txtFCAi.ToolTip = value;
  }

  public string FCAeInfo
  {
    set => this.txtFCAe.ToolTip = value;
  }

  public string LOSSiInfo
  {
    set => this.txtLOSSi.ToolTip = value;
  }

  public string LOSSeInfo
  {
    set => this.txtLOSSe.ToolTip = value;
  }

  public bool ValidateAssessment() => this._presenter.Validate();

  public void Save() => this._presenter.Save();

  public void ClearErrors() => this.dxErrorProvider1.ClearErrors();

  private void cboVent_SelectedIndexChanged(object sender, EventArgs e)
  {
    this._presenter.EnableSc();
  }
}
