// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.AbstractView.Dent.AbstractResultView
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.UserInterface.Record;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.AbstractView.Dent;

public abstract class AbstractResultView : XtraUserControl
{
  protected IRecordView _recordView;
  private bool _diameterCriteria;
  private bool _SMYSCriteria;
  private bool _UTSCriteria;
  private bool _thicknessCriteria;
  private bool _spacingToDiscontinuityCriteria;
  private bool _spacingToWeldJointCriteria;
  private bool _MAWPCriteria;
  private bool _dentDepthCriteria;
  private bool _cyclicLoadingCriteria;
  private double _corrodedWallThickness;
  private double _MAWP;
  private double _dentDepth;
  private double _amplitudeCyclicCircMembraneStress;
  private double _amplitudeAdjustedCyclicCircMembraneStress;
  private double _stressConcentrationDent;
  private int _allowableNumberPressureCycles;
  private string _resultMessages;
  private bool _level1Passed;
  private bool _level2Passed;
  private IContainer components;
  private GroupControl groupControl1;
  private TableLayoutPanel tableLayoutPanel3;
  private LabelControl umMAWP;
  private LabelControl umAmplitudeAdjustedCyclicCircMembraneStress;
  private LabelControl umAmplitudeCyclicCircMembraneStress;
  private LabelControl umDentDepth;
  private LabelControl lblStressConcentrationDent;
  private LabelControl lblAmplitudeAdjustedCyclicCircMembraneStress;
  private LabelControl lblAmplitudeCyclicCircMembraneStress;
  private LabelControl lblCylicLoading;
  private LabelControl labelControl9;
  private LabelControl labelControl8;
  private LabelControl labelControl7;
  private LabelControl labelControl6;
  private LabelControl lblAllowableNumberPressureCycles;
  private TextEdit txtAllowableNumberPressureCycles;
  private TextEdit txtDentDepth;
  private TextEdit txtMAWPCriteria;
  private TextEdit txtStressConcentrationDent;
  private TextEdit txtDentDepthCriteria;
  private TextEdit txtAmplitudeAdjustedCyclicCircMembraneStress;
  private TextEdit txtAmplitudeCyclicCircMembraneStress;
  private TextEdit txtCyclicLoading;
  private TextEdit txtMAWP;
  private GroupControl groupControl2;
  private TableLayoutPanel tableLayoutPanel1;
  private LabelControl labelControl5;
  private TextEdit txtDiameterCriteria;
  private LabelControl lblAxialSpacingBetweenAdjacentDefectsCriteria;
  private LabelControl lblCircAngularSpacingBetweenAdjacentDefectsCriteria;
  private LabelControl lblDefectsInteractionCriteria;
  private TextEdit txtUTSCriteria;
  private TextEdit txtSMYSCriteria;
  private TextEdit txtThicknessCriteria;
  private LabelControl lblExternalLoadWithinLimit;
  private TextEdit txtSpacingToDiscontinuityCriteria;
  private GroupControl groupControl4;
  private TableLayoutPanel tableLayoutPanel7;
  private LabelControl lblConclusionL1;
  private GroupControl groupControl5;
  private TableLayoutPanel tableLayoutPanel9;
  private MemoEdit txtWarningMessages;
  private LabelControl labelControl1;
  private TextEdit txtSpacingToWeldJointCriteria;
  private LabelControl lblCyclicLoadingCriteria;
  private TextEdit txtCyclicLoadingCriteria;
  private TextEdit txtCorrodedWallThickness;
  private LabelControl umCorrodedWallThickness;
  private LabelControl labelControl4;
  private GroupControl grpLevel2Conclusion;
  private TableLayoutPanel tableLayoutPanel2;
  private LabelControl lblConclusionL2;

  public AbstractResultView() => this.InitializeComponent();

  public bool DiameterCriteria
  {
    get => this._diameterCriteria;
    set
    {
      this.txtDiameterCriteria.Text = value ? "True" : "False";
      this._diameterCriteria = value;
    }
  }

  public bool SMYSCriteria
  {
    get => this._SMYSCriteria;
    set
    {
      this.txtSMYSCriteria.Text = value ? "True" : "False";
      this._SMYSCriteria = value;
    }
  }

  public bool UTSCriteria
  {
    get => this._UTSCriteria;
    set
    {
      this.txtUTSCriteria.Text = value ? "True" : "False";
      this._UTSCriteria = value;
    }
  }

  public bool ThicknessCriteria
  {
    get => this._thicknessCriteria;
    set
    {
      this.txtThicknessCriteria.Text = value ? "True" : "False";
      this._thicknessCriteria = value;
    }
  }

  public bool SpacingToDiscontinuityCriteria
  {
    get => this._spacingToDiscontinuityCriteria;
    set
    {
      this.txtSpacingToDiscontinuityCriteria.Text = value ? "True" : "False";
      this._spacingToDiscontinuityCriteria = value;
    }
  }

  public bool SpacingToWeldJointCriteria
  {
    get => this._spacingToWeldJointCriteria;
    set
    {
      this.txtSpacingToWeldJointCriteria.Text = value ? "True" : "False";
      this._spacingToWeldJointCriteria = value;
    }
  }

  public bool MAWPCriteria
  {
    get => this._MAWPCriteria;
    set
    {
      this.txtMAWPCriteria.Text = value ? "True" : "False";
      this._MAWPCriteria = value;
    }
  }

  public bool DentDepthCriteria
  {
    get => this._dentDepthCriteria;
    set
    {
      this.txtDentDepthCriteria.Text = value ? "True" : "False";
      this._dentDepthCriteria = value;
    }
  }

  public bool CyclicLoadingCriteria
  {
    get => this._cyclicLoadingCriteria;
    set
    {
      this.txtCyclicLoadingCriteria.Text = value ? "True" : "False";
      this._cyclicLoadingCriteria = value;
    }
  }

  public double CorrodedWallThickness
  {
    get => this._corrodedWallThickness;
    set
    {
      this.txtCorrodedWallThickness.Text = Helpers.ParseObjectToString((object) value);
      this._corrodedWallThickness = value;
    }
  }

  public double MAWP
  {
    get => this._MAWP;
    set
    {
      this.txtMAWP.Text = Helpers.ParseObjectToString((object) value);
      this._MAWP = value;
    }
  }

  public double DentDepth
  {
    get => this._dentDepth;
    set
    {
      this.txtDentDepth.Text = Helpers.ParseObjectToString((object) value);
      this._dentDepth = value;
    }
  }

  public double AmplitudeCyclicCircMembraneStress
  {
    get => this._amplitudeCyclicCircMembraneStress;
    set
    {
      this.txtAmplitudeCyclicCircMembraneStress.Text = Helpers.ParseObjectToString((object) value);
      this._amplitudeCyclicCircMembraneStress = value;
    }
  }

  public double AmplitudeAdjustedCyclicCircMembraneStress
  {
    get => this._amplitudeAdjustedCyclicCircMembraneStress;
    set
    {
      this.txtAmplitudeAdjustedCyclicCircMembraneStress.Text = Helpers.ParseObjectToString((object) value);
      this._amplitudeAdjustedCyclicCircMembraneStress = value;
    }
  }

  public double StressConcentrationDent
  {
    get => this._stressConcentrationDent;
    set
    {
      this.txtStressConcentrationDent.Text = Helpers.ParseObjectToString((object) value);
      this._stressConcentrationDent = value;
    }
  }

  public int AllowableNumberPressureCycles
  {
    get => this._allowableNumberPressureCycles;
    set
    {
      this.txtAllowableNumberPressureCycles.Text = Helpers.ParseObjectToString((object) value);
      this._allowableNumberPressureCycles = value;
    }
  }

  public bool CyclicLoading { get; set; }

  public string CyclicLoadingText
  {
    get => this.txtCyclicLoading.Text;
    set => this.txtCyclicLoading.Text = value;
  }

  public string UM_CorrodedWallThickness
  {
    set => this.umCorrodedWallThickness.Text = value;
  }

  public string UM_MAWP
  {
    set => this.umMAWP.Text = value;
  }

  public string UM_DentDepth
  {
    set => this.umDentDepth.Text = value;
  }

  public string UM_AmplitudeCyclicCircMembraneStress
  {
    set => this.umAmplitudeCyclicCircMembraneStress.Text = value;
  }

  public string UM_AmplitudeAdjustedCyclicCircMembraneStress
  {
    set => this.umAmplitudeAdjustedCyclicCircMembraneStress.Text = value;
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show((IWin32Window) this, message);
  }

  public string ResultMessages
  {
    get => this._resultMessages;
    set
    {
      this._resultMessages = value;
      this.txtWarningMessages.Text = value;
    }
  }

  public string ConclusionL1
  {
    set => this.lblConclusionL1.Text = value;
  }

  public string ConclusionL2
  {
    set => this.lblConclusionL2.Text = value;
  }

  public bool Level1Passed
  {
    get => this._level1Passed;
    set
    {
      this._level1Passed = value;
      this.lblConclusionL1.Appearance.ForeColor = this._level1Passed ? Color.Green : Color.Red;
    }
  }

  public bool Level2Passed
  {
    get => this._level2Passed;
    set
    {
      this._level2Passed = value;
      this.lblConclusionL2.Appearance.ForeColor = this._level2Passed ? Color.Green : Color.Red;
    }
  }

  public bool EnableCyclicLoadingControls
  {
    set
    {
      this.lblCylicLoading.Visible = value;
      this.txtCyclicLoading.Visible = value;
      this.lblAmplitudeCyclicCircMembraneStress.Visible = value;
      this.txtAmplitudeCyclicCircMembraneStress.Visible = value;
      this.umAmplitudeCyclicCircMembraneStress.Visible = value;
      this.lblAmplitudeAdjustedCyclicCircMembraneStress.Visible = value;
      this.txtAmplitudeAdjustedCyclicCircMembraneStress.Visible = value;
      this.umAmplitudeAdjustedCyclicCircMembraneStress.Visible = value;
      this.lblStressConcentrationDent.Visible = value;
      this.txtStressConcentrationDent.Visible = value;
      this.lblAllowableNumberPressureCycles.Visible = value;
      this.txtAllowableNumberPressureCycles.Visible = value;
      this.lblCyclicLoadingCriteria.Visible = value;
      this.txtCyclicLoadingCriteria.Visible = value;
    }
  }

  public bool EnableLevel2Controls
  {
    set => this.grpLevel2Conclusion.Visible = value;
  }

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.groupControl1 = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.lblStressConcentrationDent = new LabelControl();
    this.txtStressConcentrationDent = new TextEdit();
    this.txtAmplitudeAdjustedCyclicCircMembraneStress = new TextEdit();
    this.umAmplitudeAdjustedCyclicCircMembraneStress = new LabelControl();
    this.txtAmplitudeCyclicCircMembraneStress = new TextEdit();
    this.umAmplitudeCyclicCircMembraneStress = new LabelControl();
    this.txtCyclicLoading = new TextEdit();
    this.txtDentDepth = new TextEdit();
    this.umDentDepth = new LabelControl();
    this.txtMAWP = new TextEdit();
    this.umMAWP = new LabelControl();
    this.txtCorrodedWallThickness = new TextEdit();
    this.umCorrodedWallThickness = new LabelControl();
    this.lblAmplitudeAdjustedCyclicCircMembraneStress = new LabelControl();
    this.lblAmplitudeCyclicCircMembraneStress = new LabelControl();
    this.lblCylicLoading = new LabelControl();
    this.labelControl6 = new LabelControl();
    this.labelControl8 = new LabelControl();
    this.labelControl4 = new LabelControl();
    this.lblAllowableNumberPressureCycles = new LabelControl();
    this.txtAllowableNumberPressureCycles = new TextEdit();
    this.labelControl9 = new LabelControl();
    this.labelControl7 = new LabelControl();
    this.txtMAWPCriteria = new TextEdit();
    this.txtDentDepthCriteria = new TextEdit();
    this.groupControl2 = new GroupControl();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.labelControl1 = new LabelControl();
    this.labelControl5 = new LabelControl();
    this.txtDiameterCriteria = new TextEdit();
    this.lblAxialSpacingBetweenAdjacentDefectsCriteria = new LabelControl();
    this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria = new LabelControl();
    this.lblDefectsInteractionCriteria = new LabelControl();
    this.txtUTSCriteria = new TextEdit();
    this.txtSMYSCriteria = new TextEdit();
    this.txtThicknessCriteria = new TextEdit();
    this.lblExternalLoadWithinLimit = new LabelControl();
    this.txtSpacingToDiscontinuityCriteria = new TextEdit();
    this.txtSpacingToWeldJointCriteria = new TextEdit();
    this.lblCyclicLoadingCriteria = new LabelControl();
    this.txtCyclicLoadingCriteria = new TextEdit();
    this.groupControl4 = new GroupControl();
    this.tableLayoutPanel7 = new TableLayoutPanel();
    this.lblConclusionL1 = new LabelControl();
    this.groupControl5 = new GroupControl();
    this.tableLayoutPanel9 = new TableLayoutPanel();
    this.txtWarningMessages = new MemoEdit();
    this.grpLevel2Conclusion = new GroupControl();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.lblConclusionL2 = new LabelControl();
    this.groupControl1.BeginInit();
    this.groupControl1.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.txtStressConcentrationDent.Properties.BeginInit();
    this.txtAmplitudeAdjustedCyclicCircMembraneStress.Properties.BeginInit();
    this.txtAmplitudeCyclicCircMembraneStress.Properties.BeginInit();
    this.txtCyclicLoading.Properties.BeginInit();
    this.txtDentDepth.Properties.BeginInit();
    this.txtMAWP.Properties.BeginInit();
    this.txtCorrodedWallThickness.Properties.BeginInit();
    this.txtAllowableNumberPressureCycles.Properties.BeginInit();
    this.txtMAWPCriteria.Properties.BeginInit();
    this.txtDentDepthCriteria.Properties.BeginInit();
    this.groupControl2.BeginInit();
    this.groupControl2.SuspendLayout();
    this.tableLayoutPanel1.SuspendLayout();
    this.txtDiameterCriteria.Properties.BeginInit();
    this.txtUTSCriteria.Properties.BeginInit();
    this.txtSMYSCriteria.Properties.BeginInit();
    this.txtThicknessCriteria.Properties.BeginInit();
    this.txtSpacingToDiscontinuityCriteria.Properties.BeginInit();
    this.txtSpacingToWeldJointCriteria.Properties.BeginInit();
    this.txtCyclicLoadingCriteria.Properties.BeginInit();
    this.groupControl4.BeginInit();
    this.groupControl4.SuspendLayout();
    this.tableLayoutPanel7.SuspendLayout();
    this.groupControl5.BeginInit();
    this.groupControl5.SuspendLayout();
    this.tableLayoutPanel9.SuspendLayout();
    this.txtWarningMessages.Properties.BeginInit();
    this.grpLevel2Conclusion.BeginInit();
    this.grpLevel2Conclusion.SuspendLayout();
    this.tableLayoutPanel2.SuspendLayout();
    this.SuspendLayout();
    this.groupControl1.AppearanceCaption.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.groupControl1.AppearanceCaption.Options.UseFont = true;
    this.groupControl1.AutoSize = true;
    this.groupControl1.Controls.Add((Control) this.tableLayoutPanel3);
    this.groupControl1.Dock = DockStyle.Top;
    this.groupControl1.Location = new Point(0, 241);
    this.groupControl1.Name = "groupControl1";
    this.groupControl1.Size = new Size(681, 219);
    this.groupControl1.TabIndex = 1;
    this.groupControl1.Text = "Intermediate Result";
    this.tableLayoutPanel3.AutoSize = true;
    this.tableLayoutPanel3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel3.ColumnCount = 3;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 300f));
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.lblStressConcentrationDent, 0, 6);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtStressConcentrationDent, 1, 6);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtAmplitudeAdjustedCyclicCircMembraneStress, 1, 5);
    this.tableLayoutPanel3.Controls.Add((Control) this.umAmplitudeAdjustedCyclicCircMembraneStress, 2, 5);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtAmplitudeCyclicCircMembraneStress, 1, 4);
    this.tableLayoutPanel3.Controls.Add((Control) this.umAmplitudeCyclicCircMembraneStress, 2, 4);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtCyclicLoading, 1, 3);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtDentDepth, 1, 2);
    this.tableLayoutPanel3.Controls.Add((Control) this.umDentDepth, 2, 2);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtMAWP, 1, 1);
    this.tableLayoutPanel3.Controls.Add((Control) this.umMAWP, 2, 1);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtCorrodedWallThickness, 1, 0);
    this.tableLayoutPanel3.Controls.Add((Control) this.umCorrodedWallThickness, 2, 0);
    this.tableLayoutPanel3.Controls.Add((Control) this.lblAmplitudeAdjustedCyclicCircMembraneStress, 0, 5);
    this.tableLayoutPanel3.Controls.Add((Control) this.lblAmplitudeCyclicCircMembraneStress, 0, 4);
    this.tableLayoutPanel3.Controls.Add((Control) this.lblCylicLoading, 0, 3);
    this.tableLayoutPanel3.Controls.Add((Control) this.labelControl6, 0, 2);
    this.tableLayoutPanel3.Controls.Add((Control) this.labelControl8, 0, 1);
    this.tableLayoutPanel3.Controls.Add((Control) this.labelControl4, 0, 0);
    this.tableLayoutPanel3.Controls.Add((Control) this.lblAllowableNumberPressureCycles, 0, 7);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtAllowableNumberPressureCycles, 1, 7);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 8;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel3.Size = new Size(677, 196);
    this.tableLayoutPanel3.TabIndex = 0;
    this.lblStressConcentrationDent.Location = new Point(13, 145);
    this.lblStressConcentrationDent.Name = "lblStressConcentrationDent";
    this.lblStressConcentrationDent.Size = new Size(203, 13);
    this.lblStressConcentrationDent.TabIndex = 17;
    this.lblStressConcentrationDent.Text = "Stress concentration parameter for a dent";
    this.txtStressConcentrationDent.Location = new Point(311, 143);
    this.txtStressConcentrationDent.Margin = new Padding(1);
    this.txtStressConcentrationDent.Name = "txtStressConcentrationDent";
    this.txtStressConcentrationDent.Properties.ReadOnly = true;
    this.txtStressConcentrationDent.Size = new Size(100, 20);
    this.txtStressConcentrationDent.TabIndex = 18;
    this.txtAmplitudeAdjustedCyclicCircMembraneStress.Location = new Point(311, 121);
    this.txtAmplitudeAdjustedCyclicCircMembraneStress.Margin = new Padding(1);
    this.txtAmplitudeAdjustedCyclicCircMembraneStress.Name = "txtAmplitudeAdjustedCyclicCircMembraneStress";
    this.txtAmplitudeAdjustedCyclicCircMembraneStress.Properties.ReadOnly = true;
    this.txtAmplitudeAdjustedCyclicCircMembraneStress.Size = new Size(100, 20);
    this.txtAmplitudeAdjustedCyclicCircMembraneStress.TabIndex = 15;
    this.umAmplitudeAdjustedCyclicCircMembraneStress.Location = new Point(415, 123);
    this.umAmplitudeAdjustedCyclicCircMembraneStress.Name = "umAmplitudeAdjustedCyclicCircMembraneStress";
    this.umAmplitudeAdjustedCyclicCircMembraneStress.Size = new Size(41, 13);
    this.umAmplitudeAdjustedCyclicCircMembraneStress.TabIndex = 16 /*0x10*/;
    this.umAmplitudeAdjustedCyclicCircMembraneStress.Text = "measure";
    this.txtAmplitudeCyclicCircMembraneStress.Location = new Point(311, 99);
    this.txtAmplitudeCyclicCircMembraneStress.Margin = new Padding(1);
    this.txtAmplitudeCyclicCircMembraneStress.Name = "txtAmplitudeCyclicCircMembraneStress";
    this.txtAmplitudeCyclicCircMembraneStress.Properties.ReadOnly = true;
    this.txtAmplitudeCyclicCircMembraneStress.Size = new Size(100, 20);
    this.txtAmplitudeCyclicCircMembraneStress.TabIndex = 12;
    this.umAmplitudeCyclicCircMembraneStress.Location = new Point(415, 101);
    this.umAmplitudeCyclicCircMembraneStress.Name = "umAmplitudeCyclicCircMembraneStress";
    this.umAmplitudeCyclicCircMembraneStress.Size = new Size(41, 13);
    this.umAmplitudeCyclicCircMembraneStress.TabIndex = 13;
    this.umAmplitudeCyclicCircMembraneStress.Text = "measure";
    this.txtCyclicLoading.Location = new Point(311, 77);
    this.txtCyclicLoading.Margin = new Padding(1);
    this.txtCyclicLoading.Name = "txtCyclicLoading";
    this.txtCyclicLoading.Properties.ReadOnly = true;
    this.txtCyclicLoading.Size = new Size(100, 20);
    this.txtCyclicLoading.TabIndex = 10;
    this.txtDentDepth.Location = new Point(311, 55);
    this.txtDentDepth.Margin = new Padding(1);
    this.txtDentDepth.Name = "txtDentDepth";
    this.txtDentDepth.Properties.ReadOnly = true;
    this.txtDentDepth.Size = new Size(100, 20);
    this.txtDentDepth.TabIndex = 7;
    this.umDentDepth.Location = new Point(415, 57);
    this.umDentDepth.Name = "umDentDepth";
    this.umDentDepth.Size = new Size(41, 13);
    this.umDentDepth.TabIndex = 8;
    this.umDentDepth.Text = "measure";
    this.txtMAWP.Location = new Point(311, 33);
    this.txtMAWP.Margin = new Padding(1);
    this.txtMAWP.Name = "txtMAWP";
    this.txtMAWP.Properties.ReadOnly = true;
    this.txtMAWP.Size = new Size(100, 20);
    this.txtMAWP.TabIndex = 4;
    this.umMAWP.Location = new Point(415, 35);
    this.umMAWP.Name = "umMAWP";
    this.umMAWP.Size = new Size(41, 13);
    this.umMAWP.TabIndex = 5;
    this.umMAWP.Text = "measure";
    this.txtCorrodedWallThickness.Location = new Point(311, 11);
    this.txtCorrodedWallThickness.Margin = new Padding(1);
    this.txtCorrodedWallThickness.Name = "txtCorrodedWallThickness";
    this.txtCorrodedWallThickness.Properties.ReadOnly = true;
    this.txtCorrodedWallThickness.Size = new Size(100, 20);
    this.txtCorrodedWallThickness.TabIndex = 1;
    this.umCorrodedWallThickness.Location = new Point(415, 13);
    this.umCorrodedWallThickness.Name = "umCorrodedWallThickness";
    this.umCorrodedWallThickness.Size = new Size(41, 13);
    this.umCorrodedWallThickness.TabIndex = 2;
    this.umCorrodedWallThickness.Text = "measure";
    this.lblAmplitudeAdjustedCyclicCircMembraneStress.Location = new Point(13, 123);
    this.lblAmplitudeAdjustedCyclicCircMembraneStress.Name = "lblAmplitudeAdjustedCyclicCircMembraneStress";
    this.lblAmplitudeAdjustedCyclicCircMembraneStress.Size = new Size(291, 13);
    this.lblAmplitudeAdjustedCyclicCircMembraneStress.TabIndex = 14;
    this.lblAmplitudeAdjustedCyclicCircMembraneStress.Text = "Amplitude of adjusted cyclic circumferential membrane stress";
    this.lblAmplitudeCyclicCircMembraneStress.Location = new Point(13, 101);
    this.lblAmplitudeCyclicCircMembraneStress.Name = "lblAmplitudeCyclicCircMembraneStress";
    this.lblAmplitudeCyclicCircMembraneStress.Size = new Size(246, 13);
    this.lblAmplitudeCyclicCircMembraneStress.TabIndex = 11;
    this.lblAmplitudeCyclicCircMembraneStress.Text = "Amplitude of cyclic circumferential membrane stress";
    this.lblCylicLoading.Location = new Point(13, 79);
    this.lblCylicLoading.Name = "lblCylicLoading";
    this.lblCylicLoading.Size = new Size(67, 13);
    this.lblCylicLoading.TabIndex = 9;
    this.lblCylicLoading.Text = "Cyclic Loading";
    this.labelControl6.Location = new Point(13, 57);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(54, 13);
    this.labelControl6.TabIndex = 6;
    this.labelControl6.Text = "Dent depth";
    this.labelControl8.Location = new Point(13, 35);
    this.labelControl8.Name = "labelControl8";
    this.labelControl8.Size = new Size(31 /*0x1F*/, 13);
    this.labelControl8.TabIndex = 3;
    this.labelControl8.Text = "MAWP";
    this.labelControl4.Location = new Point(13, 13);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(113, 13);
    this.labelControl4.TabIndex = 0;
    this.labelControl4.Text = "Corroded wall thickness";
    this.lblAllowableNumberPressureCycles.Location = new Point(13, 167);
    this.lblAllowableNumberPressureCycles.Name = "lblAllowableNumberPressureCycles";
    this.lblAllowableNumberPressureCycles.Size = new Size(225, 13);
    this.lblAllowableNumberPressureCycles.TabIndex = 19;
    this.lblAllowableNumberPressureCycles.Text = "Allowable number of pressure cycles for a dent";
    this.txtAllowableNumberPressureCycles.Location = new Point(311, 165);
    this.txtAllowableNumberPressureCycles.Margin = new Padding(1);
    this.txtAllowableNumberPressureCycles.Name = "txtAllowableNumberPressureCycles";
    this.txtAllowableNumberPressureCycles.Properties.ReadOnly = true;
    this.txtAllowableNumberPressureCycles.Size = new Size(100, 20);
    this.txtAllowableNumberPressureCycles.TabIndex = 20;
    this.labelControl9.Location = new Point(13, 145);
    this.labelControl9.Name = "labelControl9";
    this.labelControl9.Size = new Size(69, 13);
    this.labelControl9.TabIndex = 12;
    this.labelControl9.Text = "MAWP Criteria";
    this.labelControl7.Location = new Point(13, 167);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(90, 13);
    this.labelControl7.TabIndex = 14;
    this.labelControl7.Text = "Dent depth criteria";
    this.txtMAWPCriteria.Location = new Point(311, 143);
    this.txtMAWPCriteria.Margin = new Padding(1);
    this.txtMAWPCriteria.Name = "txtMAWPCriteria";
    this.txtMAWPCriteria.Properties.ReadOnly = true;
    this.txtMAWPCriteria.Size = new Size(100, 20);
    this.txtMAWPCriteria.TabIndex = 13;
    this.txtDentDepthCriteria.Location = new Point(311, 165);
    this.txtDentDepthCriteria.Margin = new Padding(1);
    this.txtDentDepthCriteria.Name = "txtDentDepthCriteria";
    this.txtDentDepthCriteria.Properties.ReadOnly = true;
    this.txtDentDepthCriteria.Size = new Size(100, 20);
    this.txtDentDepthCriteria.TabIndex = 15;
    this.groupControl2.AppearanceCaption.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.groupControl2.AppearanceCaption.Options.UseFont = true;
    this.groupControl2.AutoSize = true;
    this.groupControl2.Controls.Add((Control) this.tableLayoutPanel1);
    this.groupControl2.Dock = DockStyle.Top;
    this.groupControl2.Location = new Point(0, 0);
    this.groupControl2.Name = "groupControl2";
    this.groupControl2.Size = new Size(681, 241);
    this.groupControl2.TabIndex = 0;
    this.groupControl2.Text = "Assessment Criteria";
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 2;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 300f));
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl5, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtDiameterCriteria, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblAxialSpacingBetweenAdjacentDefectsCriteria, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblDefectsInteractionCriteria, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtUTSCriteria, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl9, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl7, 0, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtSMYSCriteria, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtThicknessCriteria, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblExternalLoadWithinLimit, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtSpacingToDiscontinuityCriteria, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtDentDepthCriteria, 1, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtSpacingToWeldJointCriteria, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtMAWPCriteria, 1, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblCyclicLoadingCriteria, 0, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtCyclicLoadingCriteria, 1, 8);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(2, 21);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 9;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.Size = new Size(677, 218);
    this.tableLayoutPanel1.TabIndex = 0;
    this.labelControl1.Location = new Point(13, 123);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(135, 13);
    this.labelControl1.TabIndex = 10;
    this.labelControl1.Text = "Spacing to weld joint criteria";
    this.labelControl5.Location = new Point(13, 79);
    this.labelControl5.Name = "labelControl5";
    this.labelControl5.Size = new Size(164, 13);
    this.labelControl5.TabIndex = 6;
    this.labelControl5.Text = "Thickness criteria: 5mm<tc<19mm";
    this.txtDiameterCriteria.Location = new Point(311, 11);
    this.txtDiameterCriteria.Margin = new Padding(1);
    this.txtDiameterCriteria.Name = "txtDiameterCriteria";
    this.txtDiameterCriteria.Properties.ReadOnly = true;
    this.txtDiameterCriteria.Size = new Size(100, 20);
    this.txtDiameterCriteria.TabIndex = 1;
    this.lblAxialSpacingBetweenAdjacentDefectsCriteria.Location = new Point(13, 35);
    this.lblAxialSpacingBetweenAdjacentDefectsCriteria.Name = "lblAxialSpacingBetweenAdjacentDefectsCriteria";
    this.lblAxialSpacingBetweenAdjacentDefectsCriteria.Size = new Size(286, 13);
    this.lblAxialSpacingBetweenAdjacentDefectsCriteria.TabIndex = 2;
    this.lblAxialSpacingBetweenAdjacentDefectsCriteria.Text = "SMYS criteria: SMYS =< 482 MPa for static assessment only";
    this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria.Location = new Point(13, 13);
    this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria.Name = "lblCircAngularSpacingBetweenAdjacentDefectsCriteria";
    this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria.Size = new Size(204, 13);
    this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria.TabIndex = 0;
    this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria.Text = "Diameter criteria : 168mm < Do < 1050mm";
    this.lblDefectsInteractionCriteria.Location = new Point(13, 57);
    this.lblDefectsInteractionCriteria.Name = "lblDefectsInteractionCriteria";
    this.lblDefectsInteractionCriteria.Size = new Size(280, 13);
    this.lblDefectsInteractionCriteria.TabIndex = 4;
    this.lblDefectsInteractionCriteria.Text = "UTS criteria: UTS =< 711 MPa for fatigue assessment only";
    this.txtUTSCriteria.Location = new Point(311, 55);
    this.txtUTSCriteria.Margin = new Padding(1);
    this.txtUTSCriteria.Name = "txtUTSCriteria";
    this.txtUTSCriteria.Properties.ReadOnly = true;
    this.txtUTSCriteria.Size = new Size(100, 20);
    this.txtUTSCriteria.TabIndex = 5;
    this.txtSMYSCriteria.Location = new Point(311, 33);
    this.txtSMYSCriteria.Margin = new Padding(1);
    this.txtSMYSCriteria.Name = "txtSMYSCriteria";
    this.txtSMYSCriteria.Properties.ReadOnly = true;
    this.txtSMYSCriteria.Size = new Size(100, 20);
    this.txtSMYSCriteria.TabIndex = 3;
    this.txtThicknessCriteria.Location = new Point(311, 77);
    this.txtThicknessCriteria.Margin = new Padding(1);
    this.txtThicknessCriteria.Name = "txtThicknessCriteria";
    this.txtThicknessCriteria.Properties.ReadOnly = true;
    this.txtThicknessCriteria.Size = new Size(100, 20);
    this.txtThicknessCriteria.TabIndex = 7;
    this.lblExternalLoadWithinLimit.Location = new Point(13, 101);
    this.lblExternalLoadWithinLimit.Name = "lblExternalLoadWithinLimit";
    this.lblExternalLoadWithinLimit.Size = new Size(115, 13);
    this.lblExternalLoadWithinLimit.TabIndex = 8;
    this.lblExternalLoadWithinLimit.Text = "Spacing to LMSD criteria";
    this.txtSpacingToDiscontinuityCriteria.Location = new Point(311, 99);
    this.txtSpacingToDiscontinuityCriteria.Margin = new Padding(1);
    this.txtSpacingToDiscontinuityCriteria.Name = "txtSpacingToDiscontinuityCriteria";
    this.txtSpacingToDiscontinuityCriteria.Properties.ReadOnly = true;
    this.txtSpacingToDiscontinuityCriteria.Size = new Size(100, 20);
    this.txtSpacingToDiscontinuityCriteria.TabIndex = 9;
    this.txtSpacingToWeldJointCriteria.Location = new Point(311, 121);
    this.txtSpacingToWeldJointCriteria.Margin = new Padding(1);
    this.txtSpacingToWeldJointCriteria.Name = "txtSpacingToWeldJointCriteria";
    this.txtSpacingToWeldJointCriteria.Properties.ReadOnly = true;
    this.txtSpacingToWeldJointCriteria.Size = new Size(100, 20);
    this.txtSpacingToWeldJointCriteria.TabIndex = 11;
    this.lblCyclicLoadingCriteria.Location = new Point(13, 189);
    this.lblCyclicLoadingCriteria.Name = "lblCyclicLoadingCriteria";
    this.lblCyclicLoadingCriteria.Size = new Size(100, 13);
    this.lblCyclicLoadingCriteria.TabIndex = 16 /*0x10*/;
    this.lblCyclicLoadingCriteria.Text = "Cyclic loading criteria";
    this.txtCyclicLoadingCriteria.Location = new Point(311, 187);
    this.txtCyclicLoadingCriteria.Margin = new Padding(1);
    this.txtCyclicLoadingCriteria.Name = "txtCyclicLoadingCriteria";
    this.txtCyclicLoadingCriteria.Properties.ReadOnly = true;
    this.txtCyclicLoadingCriteria.Size = new Size(100, 20);
    this.txtCyclicLoadingCriteria.TabIndex = 17;
    this.groupControl4.AppearanceCaption.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.groupControl4.AppearanceCaption.Options.UseFont = true;
    this.groupControl4.AutoSize = true;
    this.groupControl4.Controls.Add((Control) this.tableLayoutPanel7);
    this.groupControl4.Dock = DockStyle.Top;
    this.groupControl4.Location = new Point(0, 460);
    this.groupControl4.Name = "groupControl4";
    this.groupControl4.Size = new Size(681, 62);
    this.groupControl4.TabIndex = 2;
    this.groupControl4.Text = "Level 1 Assessment Conclusion";
    this.tableLayoutPanel7.AutoSize = true;
    this.tableLayoutPanel7.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel7.ColumnCount = 1;
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 799f));
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel7.Controls.Add((Control) this.lblConclusionL1, 0, 0);
    this.tableLayoutPanel7.Dock = DockStyle.Fill;
    this.tableLayoutPanel7.Location = new Point(2, 21);
    this.tableLayoutPanel7.Name = "tableLayoutPanel7";
    this.tableLayoutPanel7.Padding = new Padding(10);
    this.tableLayoutPanel7.RowCount = 1;
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel7.Size = new Size(677, 39);
    this.tableLayoutPanel7.TabIndex = 0;
    this.lblConclusionL1.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblConclusionL1.Dock = DockStyle.Fill;
    this.lblConclusionL1.Location = new Point(13, 13);
    this.lblConclusionL1.Name = "lblConclusionL1";
    this.lblConclusionL1.Size = new Size(60, 13);
    this.lblConclusionL1.TabIndex = 0;
    this.lblConclusionL1.Text = "Conclusion";
    this.groupControl5.AppearanceCaption.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.groupControl5.AppearanceCaption.Options.UseFont = true;
    this.groupControl5.AutoSize = true;
    this.groupControl5.Controls.Add((Control) this.tableLayoutPanel9);
    this.groupControl5.Dock = DockStyle.Top;
    this.groupControl5.Location = new Point(0, 584);
    this.groupControl5.Name = "groupControl5";
    this.groupControl5.Size = new Size(681, 100);
    this.groupControl5.TabIndex = 4;
    this.groupControl5.Text = "Warning Messages";
    this.tableLayoutPanel9.AutoSize = true;
    this.tableLayoutPanel9.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel9.ColumnCount = 1;
    this.tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel9.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel9.Dock = DockStyle.Fill;
    this.tableLayoutPanel9.Location = new Point(2, 21);
    this.tableLayoutPanel9.Name = "tableLayoutPanel9";
    this.tableLayoutPanel9.Padding = new Padding(10);
    this.tableLayoutPanel9.RowCount = 1;
    this.tableLayoutPanel9.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel9.Size = new Size(677, 77);
    this.tableLayoutPanel9.TabIndex = 0;
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(651, 51);
    this.txtWarningMessages.TabIndex = 0;
    this.grpLevel2Conclusion.AppearanceCaption.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.grpLevel2Conclusion.AppearanceCaption.Options.UseFont = true;
    this.grpLevel2Conclusion.AutoSize = true;
    this.grpLevel2Conclusion.Controls.Add((Control) this.tableLayoutPanel2);
    this.grpLevel2Conclusion.Dock = DockStyle.Top;
    this.grpLevel2Conclusion.Location = new Point(0, 522);
    this.grpLevel2Conclusion.Name = "grpLevel2Conclusion";
    this.grpLevel2Conclusion.Size = new Size(681, 62);
    this.grpLevel2Conclusion.TabIndex = 3;
    this.grpLevel2Conclusion.Text = "Level 2 Assessment Conclusion";
    this.tableLayoutPanel2.AutoSize = true;
    this.tableLayoutPanel2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel2.ColumnCount = 1;
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 799f));
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel2.Controls.Add((Control) this.lblConclusionL2, 0, 0);
    this.tableLayoutPanel2.Dock = DockStyle.Fill;
    this.tableLayoutPanel2.Location = new Point(2, 21);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.Padding = new Padding(10);
    this.tableLayoutPanel2.RowCount = 1;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.Size = new Size(677, 39);
    this.tableLayoutPanel2.TabIndex = 0;
    this.lblConclusionL2.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblConclusionL2.Dock = DockStyle.Fill;
    this.lblConclusionL2.Location = new Point(13, 13);
    this.lblConclusionL2.Name = "lblConclusionL2";
    this.lblConclusionL2.Size = new Size(60, 13);
    this.lblConclusionL2.TabIndex = 0;
    this.lblConclusionL2.Text = "Conclusion";
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.groupControl5);
    this.Controls.Add((Control) this.grpLevel2Conclusion);
    this.Controls.Add((Control) this.groupControl4);
    this.Controls.Add((Control) this.groupControl1);
    this.Controls.Add((Control) this.groupControl2);
    this.Name = nameof (AbstractResultView);
    this.Size = new Size(681, 684);
    this.groupControl1.EndInit();
    this.groupControl1.ResumeLayout(false);
    this.groupControl1.PerformLayout();
    this.tableLayoutPanel3.ResumeLayout(false);
    this.tableLayoutPanel3.PerformLayout();
    this.txtStressConcentrationDent.Properties.EndInit();
    this.txtAmplitudeAdjustedCyclicCircMembraneStress.Properties.EndInit();
    this.txtAmplitudeCyclicCircMembraneStress.Properties.EndInit();
    this.txtCyclicLoading.Properties.EndInit();
    this.txtDentDepth.Properties.EndInit();
    this.txtMAWP.Properties.EndInit();
    this.txtCorrodedWallThickness.Properties.EndInit();
    this.txtAllowableNumberPressureCycles.Properties.EndInit();
    this.txtMAWPCriteria.Properties.EndInit();
    this.txtDentDepthCriteria.Properties.EndInit();
    this.groupControl2.EndInit();
    this.groupControl2.ResumeLayout(false);
    this.groupControl2.PerformLayout();
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.txtDiameterCriteria.Properties.EndInit();
    this.txtUTSCriteria.Properties.EndInit();
    this.txtSMYSCriteria.Properties.EndInit();
    this.txtThicknessCriteria.Properties.EndInit();
    this.txtSpacingToDiscontinuityCriteria.Properties.EndInit();
    this.txtSpacingToWeldJointCriteria.Properties.EndInit();
    this.txtCyclicLoadingCriteria.Properties.EndInit();
    this.groupControl4.EndInit();
    this.groupControl4.ResumeLayout(false);
    this.groupControl4.PerformLayout();
    this.tableLayoutPanel7.ResumeLayout(false);
    this.tableLayoutPanel7.PerformLayout();
    this.groupControl5.EndInit();
    this.groupControl5.ResumeLayout(false);
    this.groupControl5.PerformLayout();
    this.tableLayoutPanel9.ResumeLayout(false);
    this.txtWarningMessages.Properties.EndInit();
    this.grpLevel2Conclusion.EndInit();
    this.grpLevel2Conclusion.ResumeLayout(false);
    this.grpLevel2Conclusion.PerformLayout();
    this.tableLayoutPanel2.ResumeLayout(false);
    this.tableLayoutPanel2.PerformLayout();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
