// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.AbstractView.Lamination.AbstractLaminationView
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.Common;
using IntegriWISE.FormView.Properties;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Assessment.AbstractUserInterface.Lamination;
using IntegriWISE.UserInterface.Assessment.DesignCode.ASMEB31_3.Lamination;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.AbstractView.Lamination;

public class AbstractLaminationView : XtraUserControl, ILaminationView, IAssessmentBaseView, IView
{
  private LaminationPresenter _presenter;
  private IRecordView _recordView;
  private FormDirtyTracker _dirtyTracker;
  private IContainer components;
  private TableLayoutPanel tableLayoutPanel1;
  private LabelControl labelControl1;
  private LabelControl umLOSSe;
  private LabelControl umLOSSi;
  private LabelControl labelControl2;
  private LabelControl labelControl3;
  private LabelControl umFCAe;
  private LabelControl umFCAi;
  private LabelControl labelControl4;
  private TextEdit txtFCAi;
  private TextEdit txtFCAe;
  private TextEdit txtLOSSi;
  private TextEdit txtLOSSe;
  private DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider dxErrorProvider1;
  private LabelControl labelControl5;
  private TextEdit txtc;
  private TextEdit txts;
  private TextEdit txtLh;
  private TextEdit txtLw;
  private TextEdit txttmm;
  private LabelControl labelControl6;
  private LabelControl labelControl7;
  private LabelControl labelControl12;
  private LabelControl labelControl13;
  private LabelControl umc;
  private LabelControl ums;
  private LabelControl umLh;
  private LabelControl umLw;
  private LabelControl umtmm;
  private TextEdit txtLmsd;
  private LabelControl labelControl10;
  private LabelControl umLmsd;
  private LabelControl lblLs;
  private ComboBoxEdit cboMultipleLamination;
  private TextEdit txtLs;
  private LabelControl labelControl8;
  private LabelControl umLs;
  private CheckEdit chkOpInHydrogen;
  private LabelControl labelControl9;
  private CheckEdit chkMAWP;
  private TableLayoutPanel tableLayoutPanel2;
  private PictureEdit pictureGeometry1;
  private PictureEdit pictureGeometry2;
  private CheckEdit chkLevel2;

  public AbstractLaminationView(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.AssessmentView = (IAssessmentBaseView) this;
    this._presenter = new LaminationPresenter(this._recordView, (ILaminationView) this);
  }

  private void vwLamination_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView);
    this._dirtyTracker.IsHandled = true;
    this.txtc.AllowOnlyN4();
    this.txts.AllowOnlyN4();
    this.txtLh.AllowOnlyN4();
    this.txtLmsd.AllowOnlyN4();
    this.txtLw.AllowOnlyN4();
    this.txttmm.AllowOnlyN4();
    this.txtFCAi.AllowOnlyN4();
    this.txtFCAe.AllowOnlyN4();
    this.txtLOSSi.AllowOnlyN4();
    this.txtLOSSe.AllowOnlyN4();
    this.txtLs.AllowOnlyN4();
    this._presenter.LoadAssessment();
    this._dirtyTracker.IsHandled = false;
  }

  private void cboMultipleLamination_SelectedIndexChanged(object sender, EventArgs e)
  {
    this._presenter.EnableLs();
  }

  public bool Level2
  {
    get => this.chkLevel2.Checked;
    set => this.chkLevel2.Checked = value;
  }

  public int? AssessmentID { get; set; }

  public double? c
  {
    get => Helpers.ParseNullDouble((object) this.txtc.Text);
    set => this.txtc.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? s
  {
    get => Helpers.ParseNullDouble((object) this.txts.Text);
    set => this.txts.Text = Helpers.ParseObjectToString((object) value);
  }

  public string Multiple
  {
    get => this.cboMultipleLamination.Text;
    set => this.cboMultipleLamination.Text = value;
  }

  public double? Ls
  {
    get => Helpers.ParseNullDouble((object) this.txtLs.Text);
    set => this.txtLs.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Lh
  {
    get => Helpers.ParseNullDouble((object) this.txtLh.Text);
    set => this.txtLh.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Lw
  {
    get => Helpers.ParseNullDouble((object) this.txtLw.Text);
    set => this.txtLw.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Lmsd
  {
    get => Helpers.ParseNullDouble((object) this.txtLmsd.Text);
    set => this.txtLmsd.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? tmm
  {
    get => Helpers.ParseNullDouble((object) this.txttmm.Text);
    set => this.txttmm.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool OpInHydrogen
  {
    get => this.chkOpInHydrogen.Checked;
    set => this.chkOpInHydrogen.Checked = value;
  }

  public double? FCAi
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAi.Text);
    set => this.txtFCAi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? FCAe
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAe.Text);
    set => this.txtFCAe.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSi
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSi.Text);
    set => this.txtLOSSi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSe
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSe.Text);
    set => this.txtLOSSe.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMc
  {
    set => this.umc.Text = value;
  }

  public string UMs
  {
    set => this.ums.Text = value;
  }

  public string UMLh
  {
    set => this.umLh.Text = value;
  }

  public string UMLw
  {
    set => this.umLw.Text = value;
  }

  public string UMLmsd
  {
    set => this.umLmsd.Text = value;
  }

  public string UMtmm
  {
    set => this.umtmm.Text = value;
  }

  public string UMLs
  {
    set => this.umLs.Text = value;
  }

  public string UMFCAi
  {
    set => this.umFCAi.Text = value;
  }

  public string UMFCAe
  {
    set => this.umFCAe.Text = value;
  }

  public string UMLOSSi
  {
    set => this.umLOSSi.Text = value;
  }

  public string UMLOSSe
  {
    set => this.umLOSSe.Text = value;
  }

  public string cErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtc, value);
  }

  public string sErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txts, value);
  }

  public string LhErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLh, value);
  }

  public string LwErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLw, value);
  }

  public string LsErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLs, value);
  }

  public string LmsdErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLmsd, value);
  }

  public string tmmErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txttmm, value);
  }

  public string FCAiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAi, value);
  }

  public string FCAeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAe, value);
  }

  public string LOSSiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSi, value);
  }

  public string LOSSeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSe, value);
  }

  public string OpInHydrogenErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.chkOpInHydrogen, value);
  }

  public string MultipleErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.cboMultipleLamination, value);
  }

  public string cInfo
  {
    set => this.txtc.ToolTip = value;
  }

  public string sInfo
  {
    set => this.txts.ToolTip = value;
  }

  public string LhInfo
  {
    set => this.txtLh.ToolTip = value;
  }

  public string LwInfo
  {
    set => this.txtLw.ToolTip = value;
  }

  public string LsInfo
  {
    set => this.txtLs.ToolTip = value;
  }

  public string LmsdInfo
  {
    set => this.txtLmsd.ToolTip = value;
  }

  public string tmmInfo
  {
    set => this.txttmm.ToolTip = value;
  }

  public string OpInHydrogenInfo
  {
    set => this.chkOpInHydrogen.ToolTip = value;
  }

  public string MultipleInfo
  {
    set => this.cboMultipleLamination.ToolTip = value;
  }

  public string FCAiInfo
  {
    set => this.txtFCAi.ToolTip = value;
  }

  public string FCAeInfo
  {
    set => this.txtFCAe.ToolTip = value;
  }

  public string LOSSiInfo
  {
    set => this.txtLOSSi.ToolTip = value;
  }

  public string LOSSeInfo
  {
    set => this.txtLOSSe.ToolTip = value;
  }

  public bool EnableLs
  {
    set
    {
      this.lblLs.Visible = value;
      this.txtLs.Visible = value;
      this.umLs.Visible = value;
    }
  }

  public bool MAWP
  {
    get => this.chkMAWP.Checked;
    set => this.chkMAWP.Checked = value;
  }

  public bool ValidateAssessment() => this._presenter.Validate();

  public void Save() => this._presenter.Save();

  public void ClearErrors() => this.dxErrorProvider1.ClearErrors();

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.chkMAWP = new CheckEdit();
    this.labelControl6 = new LabelControl();
    this.txts = new TextEdit();
    this.ums = new LabelControl();
    this.umc = new LabelControl();
    this.labelControl5 = new LabelControl();
    this.txtc = new TextEdit();
    this.labelControl13 = new LabelControl();
    this.txttmm = new TextEdit();
    this.umtmm = new LabelControl();
    this.labelControl10 = new LabelControl();
    this.txtLmsd = new TextEdit();
    this.umLmsd = new LabelControl();
    this.labelControl12 = new LabelControl();
    this.txtLw = new TextEdit();
    this.umLw = new LabelControl();
    this.labelControl7 = new LabelControl();
    this.txtLh = new TextEdit();
    this.umLh = new LabelControl();
    this.lblLs = new LabelControl();
    this.cboMultipleLamination = new ComboBoxEdit();
    this.txtLs = new TextEdit();
    this.labelControl8 = new LabelControl();
    this.umLs = new LabelControl();
    this.labelControl4 = new LabelControl();
    this.txtLOSSe = new TextEdit();
    this.umLOSSe = new LabelControl();
    this.labelControl3 = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.labelControl1 = new LabelControl();
    this.txtLOSSi = new TextEdit();
    this.txtFCAe = new TextEdit();
    this.txtFCAi = new TextEdit();
    this.umLOSSi = new LabelControl();
    this.umFCAe = new LabelControl();
    this.umFCAi = new LabelControl();
    this.chkOpInHydrogen = new CheckEdit();
    this.labelControl9 = new LabelControl();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.pictureGeometry1 = new PictureEdit();
    this.pictureGeometry2 = new PictureEdit();
    this.chkLevel2 = new CheckEdit();
    this.dxErrorProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider();
    this.tableLayoutPanel1.SuspendLayout();
    this.chkMAWP.Properties.BeginInit();
    this.txts.Properties.BeginInit();
    this.txtc.Properties.BeginInit();
    this.txttmm.Properties.BeginInit();
    this.txtLmsd.Properties.BeginInit();
    this.txtLw.Properties.BeginInit();
    this.txtLh.Properties.BeginInit();
    this.cboMultipleLamination.Properties.BeginInit();
    this.txtLs.Properties.BeginInit();
    this.txtLOSSe.Properties.BeginInit();
    this.txtLOSSi.Properties.BeginInit();
    this.txtFCAe.Properties.BeginInit();
    this.txtFCAi.Properties.BeginInit();
    this.chkOpInHydrogen.Properties.BeginInit();
    this.tableLayoutPanel2.SuspendLayout();
    this.pictureGeometry1.Properties.BeginInit();
    this.pictureGeometry2.Properties.BeginInit();
    this.chkLevel2.Properties.BeginInit();
    ((ISupportInitialize) this.dxErrorProvider1).BeginInit();
    this.SuspendLayout();
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.chkMAWP, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl6, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txts, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.ums, 2, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.umc, 2, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl5, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtc, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl13, 0, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.txttmm, 1, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.umtmm, 2, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl10, 0, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLmsd, 1, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLmsd, 2, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl12, 0, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLw, 1, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLw, 2, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl7, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLh, 1, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLh, 2, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblLs, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.cboMultipleLamination, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLs, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl8, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLs, 2, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl4, 0, 14);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSe, 1, 14);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSe, 2, 14);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl3, 0, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 0, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSi, 1, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAe, 1, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAi, 1, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSi, 2, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAe, 2, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAi, 2, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkOpInHydrogen, 1, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl9, 0, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel2, 0, 15);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkLevel2, 0, 0);
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 16 /*0x10*/;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.Size = new Size(506, 503);
    this.tableLayoutPanel1.TabIndex = 0;
    this.chkMAWP.Location = new Point(11, 31 /*0x1F*/);
    this.chkMAWP.Margin = new Padding(1);
    this.chkMAWP.Name = "chkMAWP";
    this.chkMAWP.Properties.AutoWidth = true;
    this.chkMAWP.Properties.Caption = "MAWP";
    this.chkMAWP.Size = new Size(54, 19);
    this.chkMAWP.TabIndex = 1;
    this.labelControl6.Location = new Point(13, 76);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(249, 13);
    this.labelControl6.TabIndex = 4;
    this.labelControl6.Text = "Lamination Dimension in the Longitudinal Direction, s";
    this.txts.Location = new Point(287, 74);
    this.txts.Margin = new Padding(1);
    this.txts.Name = "txts";
    this.txts.Size = new Size(100, 20);
    this.txts.TabIndex = 5;
    this.ums.Location = new Point(391, 76);
    this.ums.Name = "ums";
    this.ums.Size = new Size(41, 13);
    this.ums.TabIndex = 6;
    this.ums.Text = "measure";
    this.umc.Location = new Point(391, 54);
    this.umc.Name = "umc";
    this.umc.Size = new Size(41, 13);
    this.umc.TabIndex = 3;
    this.umc.Text = "measure";
    this.labelControl5.Location = new Point(13, 54);
    this.labelControl5.Name = "labelControl5";
    this.labelControl5.Size = new Size(264, 13);
    this.labelControl5.TabIndex = 1;
    this.labelControl5.Text = "Lamination Dimension in the Circumferential Direction, c";
    this.txtc.Location = new Point(287, 52);
    this.txtc.Margin = new Padding(1);
    this.txtc.Name = "txtc";
    this.txtc.Size = new Size(100, 20);
    this.txtc.TabIndex = 2;
    this.labelControl13.Location = new Point(13, 208 /*0xD0*/);
    this.labelControl13.Name = "labelControl13";
    this.labelControl13.Size = new Size(166, 13);
    this.labelControl13.TabIndex = 21;
    this.labelControl13.Text = "Minimum Measured Thickness, tmm";
    this.txttmm.Location = new Point(287, 206);
    this.txttmm.Margin = new Padding(1);
    this.txttmm.Name = "txttmm";
    this.txttmm.Size = new Size(100, 20);
    this.txttmm.TabIndex = 22;
    this.umtmm.Location = new Point(391, 208 /*0xD0*/);
    this.umtmm.Name = "umtmm";
    this.umtmm.Size = new Size(41, 13);
    this.umtmm.TabIndex = 23;
    this.umtmm.Text = "measure";
    this.labelControl10.Location = new Point(13, 186);
    this.labelControl10.Name = "labelControl10";
    this.labelControl10.Size = new Size(225, 13);
    this.labelControl10.TabIndex = 18;
    this.labelControl10.Text = "Spacing to Major Structural Discontinuity, Lmsd";
    this.txtLmsd.Location = new Point(287, 184);
    this.txtLmsd.Margin = new Padding(1);
    this.txtLmsd.Name = "txtLmsd";
    this.txtLmsd.Size = new Size(100, 20);
    this.txtLmsd.TabIndex = 19;
    this.umLmsd.Location = new Point(391, 186);
    this.umLmsd.Name = "umLmsd";
    this.umLmsd.Size = new Size(41, 13);
    this.umLmsd.TabIndex = 20;
    this.umLmsd.Text = "measure";
    this.labelControl12.Location = new Point(13, 164);
    this.labelControl12.Name = "labelControl12";
    this.labelControl12.Size = new Size(164, 13);
    this.labelControl12.TabIndex = 15;
    this.labelControl12.Text = "Spacing to Nearest Weld Joint, Lw";
    this.txtLw.Location = new Point(287, 162);
    this.txtLw.Margin = new Padding(1);
    this.txtLw.Name = "txtLw";
    this.txtLw.Size = new Size(100, 20);
    this.txtLw.TabIndex = 16 /*0x10*/;
    this.umLw.Location = new Point(391, 164);
    this.umLw.Name = "umLw";
    this.umLw.Size = new Size(41, 13);
    this.umLw.TabIndex = 17;
    this.umLw.Text = "measure";
    this.labelControl7.Location = new Point(13, 142);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(103, 13);
    this.labelControl7.TabIndex = 12;
    this.labelControl7.Text = "Lamination Height, Lh";
    this.txtLh.Location = new Point(287, 140);
    this.txtLh.Margin = new Padding(1);
    this.txtLh.Name = "txtLh";
    this.txtLh.Size = new Size(100, 20);
    this.txtLh.TabIndex = 13;
    this.umLh.Location = new Point(391, 142);
    this.umLh.Name = "umLh";
    this.umLh.Size = new Size(41, 13);
    this.umLh.TabIndex = 14;
    this.umLh.Text = "measure";
    this.lblLs.Location = new Point(13, 120);
    this.lblLs.Name = "lblLs";
    this.lblLs.Size = new Size(174, 13);
    this.lblLs.TabIndex = 9;
    this.lblLs.Text = "Lamination-to-lamination Spacing, Ls";
    this.lblLs.Visible = false;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.cboMultipleLamination, 2);
    this.cboMultipleLamination.Location = new Point(287, 96 /*0x60*/);
    this.cboMultipleLamination.Margin = new Padding(1);
    this.cboMultipleLamination.Name = "cboMultipleLamination";
    this.cboMultipleLamination.Properties.Buttons.AddRange(new EditorButton[1]
    {
      new EditorButton(ButtonPredefines.Combo)
    });
    this.cboMultipleLamination.Properties.Items.AddRange(new object[3]
    {
      (object) "No",
      (object) "Yes, in the same plane",
      (object) "Yes, in different depths"
    });
    this.cboMultipleLamination.Properties.NullText = "No";
    this.cboMultipleLamination.Properties.TextEditStyle = TextEditStyles.DisableTextEditor;
    this.cboMultipleLamination.Size = new Size(200, 20);
    this.cboMultipleLamination.TabIndex = 8;
    this.cboMultipleLamination.SelectedIndexChanged += new EventHandler(this.cboMultipleLamination_SelectedIndexChanged);
    this.txtLs.Location = new Point(287, 118);
    this.txtLs.Margin = new Padding(1);
    this.txtLs.Name = "txtLs";
    this.txtLs.Size = new Size(100, 20);
    this.txtLs.TabIndex = 10;
    this.txtLs.Visible = false;
    this.labelControl8.Location = new Point(13, 98);
    this.labelControl8.Name = "labelControl8";
    this.labelControl8.Size = new Size(100, 13);
    this.labelControl8.TabIndex = 7;
    this.labelControl8.Text = "Multiple Laminations?";
    this.umLs.Location = new Point(391, 120);
    this.umLs.Name = "umLs";
    this.umLs.Size = new Size(41, 13);
    this.umLs.TabIndex = 11;
    this.umLs.Text = "measure";
    this.umLs.Visible = false;
    this.labelControl4.Location = new Point(13, 317);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(171, 13);
    this.labelControl4.TabIndex = 35;
    this.labelControl4.Text = "External Uniform Metal Loss, LOSSe";
    this.txtLOSSe.Location = new Point(287, 315);
    this.txtLOSSe.Margin = new Padding(1);
    this.txtLOSSe.Name = "txtLOSSe";
    this.txtLOSSe.Size = new Size(100, 20);
    this.txtLOSSe.TabIndex = 36;
    this.umLOSSe.Location = new Point(391, 317);
    this.umLOSSe.Name = "umLOSSe";
    this.umLOSSe.Size = new Size(41, 13);
    this.umLOSSe.TabIndex = 37;
    this.umLOSSe.Text = "measure";
    this.labelControl3.Location = new Point(13, 295);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(165, 13);
    this.labelControl3.TabIndex = 32 /*0x20*/;
    this.labelControl3.Text = "Internal Uniform Metal Loss, LOSSi";
    this.labelControl2.Location = new Point(13, 273);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(208 /*0xD0*/, 13);
    this.labelControl2.TabIndex = 29;
    this.labelControl2.Text = "External Future Corrosion Allowance, FCAe";
    this.labelControl1.Location = new Point(13, 251);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(202, 13);
    this.labelControl1.TabIndex = 26;
    this.labelControl1.Text = "Internal Future Corrosion Allowance, FCAi";
    this.txtLOSSi.Location = new Point(287, 293);
    this.txtLOSSi.Margin = new Padding(1);
    this.txtLOSSi.Name = "txtLOSSi";
    this.txtLOSSi.Size = new Size(100, 20);
    this.txtLOSSi.TabIndex = 33;
    this.txtFCAe.Location = new Point(287, 271);
    this.txtFCAe.Margin = new Padding(1);
    this.txtFCAe.Name = "txtFCAe";
    this.txtFCAe.Size = new Size(100, 20);
    this.txtFCAe.TabIndex = 30;
    this.txtFCAi.Location = new Point(287, 249);
    this.txtFCAi.Margin = new Padding(1);
    this.txtFCAi.Name = "txtFCAi";
    this.txtFCAi.Size = new Size(100, 20);
    this.txtFCAi.TabIndex = 27;
    this.umLOSSi.Location = new Point(391, 295);
    this.umLOSSi.Name = "umLOSSi";
    this.umLOSSi.Size = new Size(41, 13);
    this.umLOSSi.TabIndex = 34;
    this.umLOSSi.Text = "measure";
    this.umFCAe.Location = new Point(391, 273);
    this.umFCAe.Name = "umFCAe";
    this.umFCAe.Size = new Size(41, 13);
    this.umFCAe.TabIndex = 31 /*0x1F*/;
    this.umFCAe.Text = "measure";
    this.umFCAi.Location = new Point(391, 251);
    this.umFCAi.Name = "umFCAi";
    this.umFCAi.Size = new Size(41, 13);
    this.umFCAi.TabIndex = 28;
    this.umFCAi.Text = "measure";
    this.chkOpInHydrogen.Location = new Point(287, 228);
    this.chkOpInHydrogen.Margin = new Padding(1);
    this.chkOpInHydrogen.Name = "chkOpInHydrogen";
    this.chkOpInHydrogen.Properties.Caption = "";
    this.chkOpInHydrogen.Properties.GlyphAlignment = HorzAlignment.Center;
    this.chkOpInHydrogen.Size = new Size(100, 19);
    this.chkOpInHydrogen.TabIndex = 25;
    this.labelControl9.Location = new Point(13, 230);
    this.labelControl9.Name = "labelControl9";
    this.labelControl9.Size = new Size(270, 13);
    this.labelControl9.TabIndex = 24;
    this.labelControl9.Text = "Component is Operating in a Hydrogen Charging Service";
    this.tableLayoutPanel2.AutoSize = true;
    this.tableLayoutPanel2.ColumnCount = 2;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.tableLayoutPanel2, 3);
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50f));
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel2.Controls.Add((Control) this.pictureGeometry1, 0, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.pictureGeometry2, 1, 0);
    this.tableLayoutPanel2.Location = new Point(13, 339);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.RowCount = 1;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Absolute, 151f));
    this.tableLayoutPanel2.Size = new Size(480, 151);
    this.tableLayoutPanel2.TabIndex = 41;
    this.pictureGeometry1.EditValue = (object) Resources.IW_Lamination_1;
    this.pictureGeometry1.Location = new Point(10, 10);
    this.pictureGeometry1.Margin = new Padding(10);
    this.pictureGeometry1.Name = "pictureGeometry1";
    this.pictureGeometry1.Properties.AllowFocused = false;
    this.pictureGeometry1.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry1.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry1.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry1.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry1.Properties.ReadOnly = true;
    this.pictureGeometry1.Properties.ShowMenu = false;
    this.pictureGeometry1.Size = new Size(220, 131);
    this.pictureGeometry1.TabIndex = 39;
    this.pictureGeometry2.EditValue = (object) Resources.IW_Lamination_2;
    this.pictureGeometry2.Location = new Point(250, 10);
    this.pictureGeometry2.Margin = new Padding(10);
    this.pictureGeometry2.Name = "pictureGeometry2";
    this.pictureGeometry2.Properties.AllowFocused = false;
    this.pictureGeometry2.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry2.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry2.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry2.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry2.Properties.ReadOnly = true;
    this.pictureGeometry2.Properties.ShowMenu = false;
    this.pictureGeometry2.Size = new Size(220, 131);
    this.pictureGeometry2.TabIndex = 38;
    this.chkLevel2.Location = new Point(11, 11);
    this.chkLevel2.Margin = new Padding(1);
    this.chkLevel2.Name = "chkLevel2";
    this.chkLevel2.Properties.Caption = "Level 2";
    this.chkLevel2.Size = new Size(120, 19);
    this.chkLevel2.TabIndex = 0;
    this.dxErrorProvider1.ContainerControl = (ContainerControl) this;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = "vwLamination";
    this.Size = new Size(730, 506);
    this.Load += new EventHandler(this.vwLamination_Load);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.chkMAWP.Properties.EndInit();
    this.txts.Properties.EndInit();
    this.txtc.Properties.EndInit();
    this.txttmm.Properties.EndInit();
    this.txtLmsd.Properties.EndInit();
    this.txtLw.Properties.EndInit();
    this.txtLh.Properties.EndInit();
    this.cboMultipleLamination.Properties.EndInit();
    this.txtLs.Properties.EndInit();
    this.txtLOSSe.Properties.EndInit();
    this.txtLOSSi.Properties.EndInit();
    this.txtFCAe.Properties.EndInit();
    this.txtFCAi.Properties.EndInit();
    this.chkOpInHydrogen.Properties.EndInit();
    this.tableLayoutPanel2.ResumeLayout(false);
    this.pictureGeometry1.Properties.EndInit();
    this.pictureGeometry2.Properties.EndInit();
    this.chkLevel2.Properties.EndInit();
    ((ISupportInitialize) this.dxErrorProvider1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
