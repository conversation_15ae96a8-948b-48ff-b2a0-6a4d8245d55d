// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.AbstractView.GeneralMetalLossThicknessProfile.AbstractGeneralMetalLossThicknessProfileView
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.AbstractView.GeneralMetalLossThicknessProfile;

public abstract class AbstractGeneralMetalLossThicknessProfileView : XtraUserControl
{
  private IContainer components;
  private LabelControl umLOSSi;
  private LabelControl labelControl2;
  private TableLayoutPanel tableLayoutPanel1;
  private LabelControl labelControl1;
  private LabelControl labelControl3;
  private LabelControl umFCAe;
  private LabelControl umFCAi;
  private LabelControl labelControl4;
  private TextEdit txtFCAi;
  private TextEdit txtFCAe;
  private TextEdit txtLOSSi;
  private TextEdit txtLOSSe;
  private LabelControl umLOSSe;
  private DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider dxErrorProvider1;
  private LabelControl labelControl6;
  private LabelControl labelControl5;
  private LabelControl lbUMLm;
  private LabelControl lbUMLc;
  private TextEdit txtLm;
  private TextEdit txtLc;
  private CheckEdit chkLevel2;
  private LabelControl labelControl7;
  protected PictureEdit pictureAssessment;
  private IRecordView _recordView;
  private FormDirtyTracker _dirtyTracker;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    this.umLOSSi = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.pictureAssessment = new PictureEdit();
    this.labelControl1 = new LabelControl();
    this.labelControl3 = new LabelControl();
    this.umFCAe = new LabelControl();
    this.umFCAi = new LabelControl();
    this.labelControl4 = new LabelControl();
    this.txtFCAi = new TextEdit();
    this.txtFCAe = new TextEdit();
    this.txtLOSSi = new TextEdit();
    this.txtLOSSe = new TextEdit();
    this.umLOSSe = new LabelControl();
    this.labelControl5 = new LabelControl();
    this.lbUMLm = new LabelControl();
    this.lbUMLc = new LabelControl();
    this.txtLc = new TextEdit();
    this.labelControl6 = new LabelControl();
    this.txtLm = new TextEdit();
    this.labelControl7 = new LabelControl();
    this.chkLevel2 = new CheckEdit();
    this.dxErrorProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(this.components);
    this.tableLayoutPanel1.SuspendLayout();
    this.pictureAssessment.Properties.BeginInit();
    this.txtFCAi.Properties.BeginInit();
    this.txtFCAe.Properties.BeginInit();
    this.txtLOSSi.Properties.BeginInit();
    this.txtLOSSe.Properties.BeginInit();
    this.txtLc.Properties.BeginInit();
    this.txtLm.Properties.BeginInit();
    this.chkLevel2.Properties.BeginInit();
    ((ISupportInitialize) this.dxErrorProvider1).BeginInit();
    this.SuspendLayout();
    this.umLOSSi.Location = new Point(347, 142);
    this.umLOSSi.Name = "umLOSSi";
    this.umLOSSi.Size = new Size(41, 13);
    this.umLOSSi.TabIndex = 16 /*0x10*/;
    this.umLOSSi.Text = "measure";
    this.labelControl2.Location = new Point(13, 120);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(208 /*0xD0*/, 13);
    this.labelControl2.TabIndex = 11;
    this.labelControl2.Text = "External Future Corrosion Allowance, FCAe";
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.pictureAssessment, 0, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSi, 2, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl3, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAe, 2, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAi, 2, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl4, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAi, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAe, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSi, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSe, 1, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSe, 2, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl5, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbUMLm, 2, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbUMLc, 2, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLc, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl6, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLm, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl7, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkLevel2, 1, 0);
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 10;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(401, 363);
    this.tableLayoutPanel1.TabIndex = 0;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.pictureAssessment, 3);
    this.pictureAssessment.Location = new Point(20, 193);
    this.pictureAssessment.Margin = new Padding(10);
    this.pictureAssessment.Name = "pictureAssessment";
    this.pictureAssessment.Properties.AllowFocused = false;
    this.pictureAssessment.Properties.AllowScrollViaMouseDrag = false;
    this.pictureAssessment.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureAssessment.Properties.Appearance.Options.UseBackColor = true;
    this.pictureAssessment.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureAssessment.Properties.ReadOnly = true;
    this.pictureAssessment.Properties.ShowMenu = false;
    this.pictureAssessment.Size = new Size(220, 150);
    this.pictureAssessment.TabIndex = 20;
    this.labelControl1.Location = new Point(13, 98);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(202, 13);
    this.labelControl1.TabIndex = 8;
    this.labelControl1.Text = "Internal Future Corrosion Allowance, FCAi";
    this.labelControl3.Location = new Point(13, 142);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(165, 13);
    this.labelControl3.TabIndex = 14;
    this.labelControl3.Text = "Internal Uniform Metal Loss, LOSSi";
    this.umFCAe.Location = new Point(347, 120);
    this.umFCAe.Name = "umFCAe";
    this.umFCAe.Size = new Size(41, 13);
    this.umFCAe.TabIndex = 13;
    this.umFCAe.Text = "measure";
    this.umFCAi.Location = new Point(347, 98);
    this.umFCAi.Name = "umFCAi";
    this.umFCAi.Size = new Size(41, 13);
    this.umFCAi.TabIndex = 10;
    this.umFCAi.Text = "measure";
    this.labelControl4.Location = new Point(13, 164);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(171, 13);
    this.labelControl4.TabIndex = 17;
    this.labelControl4.Text = "External Uniform Metal Loss, LOSSe";
    this.txtFCAi.Location = new Point(243, 96 /*0x60*/);
    this.txtFCAi.Margin = new Padding(1);
    this.txtFCAi.Name = "txtFCAi";
    this.txtFCAi.Size = new Size(100, 20);
    this.txtFCAi.TabIndex = 9;
    this.txtFCAe.Location = new Point(243, 118);
    this.txtFCAe.Margin = new Padding(1);
    this.txtFCAe.Name = "txtFCAe";
    this.txtFCAe.Size = new Size(100, 20);
    this.txtFCAe.TabIndex = 12;
    this.txtLOSSi.Location = new Point(243, 140);
    this.txtLOSSi.Margin = new Padding(1);
    this.txtLOSSi.Name = "txtLOSSi";
    this.txtLOSSi.Size = new Size(100, 20);
    this.txtLOSSi.TabIndex = 15;
    this.txtLOSSe.Location = new Point(243, 162);
    this.txtLOSSe.Margin = new Padding(1);
    this.txtLOSSe.Name = "txtLOSSe";
    this.txtLOSSe.Size = new Size(100, 20);
    this.txtLOSSe.TabIndex = 18;
    this.umLOSSe.Location = new Point(347, 164);
    this.umLOSSe.Name = "umLOSSe";
    this.umLOSSe.Size = new Size(41, 13);
    this.umLOSSe.TabIndex = 19;
    this.umLOSSe.Text = "measure";
    this.labelControl5.AutoSizeMode = LabelAutoSizeMode.Vertical;
    this.labelControl5.Location = new Point(13, 34);
    this.labelControl5.Name = "labelControl5";
    this.labelControl5.Size = new Size(208 /*0xD0*/, 26);
    this.labelControl5.TabIndex = 2;
    this.labelControl5.Text = "Spacing Distance for Thickness Readings along the Longitudinal Inspection Plane - Lc";
    this.lbUMLm.Dock = DockStyle.Bottom;
    this.lbUMLm.Location = new Point(347, 79);
    this.lbUMLm.Name = "lbUMLm";
    this.lbUMLm.Size = new Size(41, 13);
    this.lbUMLm.TabIndex = 7;
    this.lbUMLm.Text = "measure";
    this.lbUMLc.Dock = DockStyle.Bottom;
    this.lbUMLc.Location = new Point(347, 47);
    this.lbUMLc.Name = "lbUMLc";
    this.lbUMLc.Size = new Size(41, 13);
    this.lbUMLc.TabIndex = 4;
    this.lbUMLc.Text = "measure";
    this.txtLc.Dock = DockStyle.Bottom;
    this.txtLc.Location = new Point(243, 42);
    this.txtLc.Margin = new Padding(1);
    this.txtLc.Name = "txtLc";
    this.txtLc.Size = new Size(100, 20);
    this.txtLc.TabIndex = 3;
    this.labelControl6.AutoSizeMode = LabelAutoSizeMode.Vertical;
    this.labelControl6.Location = new Point(13, 66);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(226, 26);
    this.labelControl6.TabIndex = 5;
    this.labelControl6.Text = "Spacing Distance for Thickness Readings along the Circumferential Inspection Plane - Lm";
    this.txtLm.Dock = DockStyle.Bottom;
    this.txtLm.Location = new Point(243, 74);
    this.txtLm.Margin = new Padding(1);
    this.txtLm.Name = "txtLm";
    this.txtLm.Size = new Size(100, 20);
    this.txtLm.TabIndex = 6;
    this.labelControl7.Location = new Point(13, 13);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(34, 13);
    this.labelControl7.TabIndex = 0;
    this.labelControl7.Text = "Level 2";
    this.chkLevel2.Location = new Point(243, 11);
    this.chkLevel2.Margin = new Padding(1);
    this.chkLevel2.Name = "chkLevel2";
    this.chkLevel2.Properties.Caption = "";
    this.chkLevel2.Size = new Size(24, 19);
    this.chkLevel2.TabIndex = 1;
    this.dxErrorProvider1.ContainerControl = (ContainerControl) this;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = nameof (AbstractGeneralMetalLossThicknessProfileView);
    this.Size = new Size(589, 451);
    this.Load += new EventHandler(this.vwGeneralMetalLossThicknessProfile_Load);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.pictureAssessment.Properties.EndInit();
    this.txtFCAi.Properties.EndInit();
    this.txtFCAe.Properties.EndInit();
    this.txtLOSSi.Properties.EndInit();
    this.txtLOSSe.Properties.EndInit();
    this.txtLc.Properties.EndInit();
    this.txtLm.Properties.EndInit();
    this.chkLevel2.Properties.EndInit();
    ((ISupportInitialize) this.dxErrorProvider1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public AbstractGeneralMetalLossThicknessProfileView(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.EnableThicknessReadings = true;
  }

  private void vwGeneralMetalLossThicknessProfile_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView);
    this._dirtyTracker.IsHandled = true;
    this.txtLc.AllowOnlyN4();
    this.txtLm.AllowOnlyN4();
    this.txtFCAi.AllowOnlyN4();
    this.txtFCAe.AllowOnlyN4();
    this.txtLOSSi.AllowOnlyN4();
    this.txtLOSSe.AllowOnlyN4();
    this._dirtyTracker.IsHandled = false;
  }

  public int? AssessmentID { get; set; }

  public double? Lc
  {
    get => Helpers.ParseNullDouble((object) this.txtLc.Text);
    set => this.txtLc.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Lm
  {
    get => Helpers.ParseNullDouble((object) this.txtLm.Text);
    set => this.txtLm.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool Level2
  {
    get => this.chkLevel2.Checked;
    set => this.chkLevel2.Checked = value;
  }

  public double? FCAi
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAi.Text);
    set => this.txtFCAi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? FCAe
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAe.Text);
    set => this.txtFCAe.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSi
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSi.Text);
    set => this.txtLOSSi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSe
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSe.Text);
    set => this.txtLOSSe.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMLc
  {
    set => this.lbUMLc.Text = value;
  }

  public string UMLm
  {
    set => this.lbUMLm.Text = value;
  }

  public string UMFCAi
  {
    set => this.umFCAi.Text = value;
  }

  public string UMFCAe
  {
    set => this.umFCAe.Text = value;
  }

  public string UMLOSSi
  {
    set => this.umLOSSi.Text = value;
  }

  public string UMLOSSe
  {
    set => this.umLOSSe.Text = value;
  }

  public string LcErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAi, value);
  }

  public string LmErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAi, value);
  }

  public string FCAiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAi, value);
  }

  public string FCAeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAe, value);
  }

  public string LOSSiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSi, value);
  }

  public string LOSSeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSe, value);
  }

  public string LcInfo
  {
    set => this.txtLc.ToolTip = value;
  }

  public string LmInfo
  {
    set => this.txtLm.ToolTip = value;
  }

  public string FCAiInfo
  {
    set => this.txtFCAi.ToolTip = value;
  }

  public string FCAeInfo
  {
    set => this.txtFCAe.ToolTip = value;
  }

  public string LOSSiInfo
  {
    set => this.txtLOSSi.ToolTip = value;
  }

  public string LOSSeInfo
  {
    set => this.txtLOSSe.ToolTip = value;
  }

  public void ClearErrors() => this.dxErrorProvider1.ClearErrors();
}
