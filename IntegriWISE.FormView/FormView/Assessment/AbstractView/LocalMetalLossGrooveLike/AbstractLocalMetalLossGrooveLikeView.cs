// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.AbstractView.LocalMetalLossGrooveLike.AbstractLocalMetalLossGrooveLikeView
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.AbstractView.LocalMetalLossGrooveLike;

public abstract class AbstractLocalMetalLossGrooveLikeView : XtraUserControl
{
  private IRecordView _recordView;
  private FormDirtyTracker _dirtyTracker;
  private IContainer components;
  private TableLayoutPanel tableLayoutPanel1;
  private TableLayoutPanel tableLayoutPanel2;
  private PictureEdit pictureGeometry1;
  private PictureEdit pictureGeometry2;
  private TextEdit txtFCAe;
  private TextEdit txtFCAi;
  private LabelControl umFCAe;
  private LabelControl umFCAi;
  private TextEdit txtdg;
  private TextEdit txtdl;
  private TextEdit txtdw;
  private TextEdit txtdr;
  private TextEdit txtdAngle;
  private TextEdit txtLmsd;
  private TextEdit txtLOSSi;
  private TextEdit txtLOSSe;
  private LabelControl umdg;
  private LabelControl umdl;
  private LabelControl umdw;
  private LabelControl umdr;
  private LabelControl umdangle;
  private LabelControl umLmsd;
  private LabelControl umLOSSi;
  private LabelControl umLOSSe;
  private DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider dxErrorProvider1;
  private PictureEdit pictureGeometry3;
  private LabelControl labelControl1;
  private CheckEdit chkMAWP;
  private LabelControl labelControl2;
  private LabelControl labelControl3;
  private LabelControl labelControl4;
  private LabelControl labelControl5;
  private LabelControl labelControl6;
  private LabelControl labelControl7;
  private LabelControl labelControl8;
  private LabelControl labelControl9;
  private LabelControl labelControl10;
  private LabelControl labelControl11;

  public AbstractLocalMetalLossGrooveLikeView(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
  }

  private void AbstractLocalMetalLossGrooveLikeView_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView)
    {
      IsHandled = true
    };
    this.txtdg.AllowOnlyN4();
    this.txtdl.AllowOnlyN4();
    this.txtdr.AllowOnlyN4();
    this.txtdAngle.AllowOnlyN4();
    this.txtdw.AllowOnlyN4();
    this.txtLmsd.AllowOnlyN4();
    this.txtFCAi.AllowOnlyN4();
    this.txtFCAe.AllowOnlyN4();
    this.txtLOSSi.AllowOnlyN4();
    this.txtLOSSe.AllowOnlyN4();
    this._dirtyTracker.IsHandled = false;
  }

  public int? AssessmentID { get; set; }

  public bool MAWP
  {
    get => this.chkMAWP.EditValue.ParseObjectToBool();
    set => this.chkMAWP.EditValue = (object) value.ParseObjectToBool();
  }

  public double? dg
  {
    get => this.txtdg.EditValue.ParseNullDouble();
    set => this.txtdg.EditValue = (object) value;
  }

  public double? dl
  {
    get => this.txtdl.EditValue.ParseNullDouble();
    set => this.txtdl.EditValue = (object) value;
  }

  public double? dw
  {
    get => this.txtdw.EditValue.ParseNullDouble();
    set => this.txtdw.EditValue = (object) value;
  }

  public double? dr
  {
    get => this.txtdr.EditValue.ParseNullDouble();
    set => this.txtdr.EditValue = (object) value;
  }

  public double? dAngle
  {
    get => this.txtdAngle.EditValue.ParseNullDouble();
    set => this.txtdAngle.EditValue = (object) value;
  }

  public double? Lmsd
  {
    get => this.txtLmsd.EditValue.ParseNullDouble();
    set => this.txtLmsd.EditValue = (object) value;
  }

  public double? FCAi
  {
    get => this.txtFCAi.EditValue.ParseNullDouble();
    set => this.txtFCAi.EditValue = (object) value;
  }

  public double? FCAe
  {
    get => this.txtFCAe.EditValue.ParseNullDouble();
    set => this.txtFCAe.EditValue = (object) value;
  }

  public double? LOSSi
  {
    get => this.txtLOSSi.EditValue.ParseNullDouble();
    set => this.txtLOSSi.EditValue = (object) value;
  }

  public double? LOSSe
  {
    get => this.txtLOSSe.EditValue.ParseNullDouble();
    set => this.txtLOSSe.EditValue = (object) value;
  }

  public string UMdg
  {
    set => this.umdg.Text = value;
  }

  public string UMdl
  {
    set => this.umdl.Text = value;
  }

  public string UMdw
  {
    set => this.umdw.Text = value;
  }

  public string UMdr
  {
    set => this.umdr.Text = value;
  }

  public string UMdAngle
  {
    set => this.umdangle.Text = value;
  }

  public string UMLmsd
  {
    set => this.umLmsd.Text = value;
  }

  public string UMFCAi
  {
    set => this.umFCAi.Text = value;
  }

  public string UMFCAe
  {
    set => this.umFCAe.Text = value;
  }

  public string UMLOSSi
  {
    set => this.umLOSSi.Text = value;
  }

  public string UMLOSSe
  {
    set => this.umLOSSe.Text = value;
  }

  public string dgInfo
  {
    set => this.txtdg.ToolTip = value;
  }

  public string dlInfo
  {
    set => this.txtdl.ToolTip = value;
  }

  public string dwInfo
  {
    set => this.txtdw.ToolTip = value;
  }

  public string drInfo
  {
    set => this.txtdr.ToolTip = value;
  }

  public string dAngleInfo
  {
    set => this.txtdAngle.ToolTip = value;
  }

  public string LmsdInfo
  {
    set => this.txtLmsd.ToolTip = value;
  }

  public string FCAiInfo
  {
    set => this.txtFCAi.ToolTip = value;
  }

  public string FCAeInfo
  {
    set => this.txtFCAe.ToolTip = value;
  }

  public string LOSSiInfo
  {
    set => this.txtLOSSi.ToolTip = value;
  }

  public string LOSSeInfo
  {
    set => this.txtLOSSe.ToolTip = value;
  }

  public string dgErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtdg, value);
  }

  public string dlErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtdl, value);
  }

  public string dwErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtdw, value);
  }

  public string drErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtdr, value);
  }

  public string dAngleErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtdAngle, value);
  }

  public string LmsdErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLmsd, value);
  }

  public string FCAiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAi, value);
  }

  public string FCAeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAe, value);
  }

  public string LOSSiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSi, value);
  }

  public string LOSSeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSe, value);
  }

  public void ClearErrors() => this.dxErrorProvider1.ClearErrors();

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    ComponentResourceManager componentResourceManager = new ComponentResourceManager(typeof (AbstractLocalMetalLossGrooveLikeView));
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.labelControl11 = new LabelControl();
    this.labelControl10 = new LabelControl();
    this.labelControl9 = new LabelControl();
    this.labelControl8 = new LabelControl();
    this.labelControl7 = new LabelControl();
    this.labelControl6 = new LabelControl();
    this.labelControl5 = new LabelControl();
    this.labelControl4 = new LabelControl();
    this.labelControl3 = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.labelControl1 = new LabelControl();
    this.chkMAWP = new CheckEdit();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.pictureGeometry3 = new PictureEdit();
    this.pictureGeometry1 = new PictureEdit();
    this.pictureGeometry2 = new PictureEdit();
    this.txtFCAe = new TextEdit();
    this.txtFCAi = new TextEdit();
    this.umFCAe = new LabelControl();
    this.umFCAi = new LabelControl();
    this.txtdg = new TextEdit();
    this.txtdl = new TextEdit();
    this.txtdw = new TextEdit();
    this.txtdr = new TextEdit();
    this.txtdAngle = new TextEdit();
    this.txtLmsd = new TextEdit();
    this.txtLOSSi = new TextEdit();
    this.txtLOSSe = new TextEdit();
    this.umdg = new LabelControl();
    this.umdl = new LabelControl();
    this.umdw = new LabelControl();
    this.umdr = new LabelControl();
    this.umdangle = new LabelControl();
    this.umLmsd = new LabelControl();
    this.umLOSSi = new LabelControl();
    this.umLOSSe = new LabelControl();
    this.dxErrorProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(this.components);
    this.tableLayoutPanel1.SuspendLayout();
    this.chkMAWP.Properties.BeginInit();
    this.tableLayoutPanel2.SuspendLayout();
    this.pictureGeometry3.Properties.BeginInit();
    this.pictureGeometry1.Properties.BeginInit();
    this.pictureGeometry2.Properties.BeginInit();
    this.txtFCAe.Properties.BeginInit();
    this.txtFCAi.Properties.BeginInit();
    this.txtdg.Properties.BeginInit();
    this.txtdl.Properties.BeginInit();
    this.txtdw.Properties.BeginInit();
    this.txtdr.Properties.BeginInit();
    this.txtdAngle.Properties.BeginInit();
    this.txtLmsd.Properties.BeginInit();
    this.txtLOSSi.Properties.BeginInit();
    this.txtLOSSe.Properties.BeginInit();
    ((ISupportInitialize) this.dxErrorProvider1).BeginInit();
    this.SuspendLayout();
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl11, 0, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl10, 0, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl9, 0, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl8, 0, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl7, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl6, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl5, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl4, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl3, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkMAWP, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel2, 0, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAe, 1, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAi, 1, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAe, 2, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAi, 2, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtdg, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtdl, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtdw, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtdr, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtdAngle, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLmsd, 1, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSi, 1, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSe, 1, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.umdg, 2, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.umdl, 2, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.umdw, 2, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.umdr, 2, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.umdangle, 2, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLmsd, 2, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSi, 2, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSe, 2, 8);
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 12;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.Size = new Size(743, 418);
    this.tableLayoutPanel1.TabIndex = 0;
    this.labelControl11.Location = new Point(13, 232);
    this.labelControl11.Name = "labelControl11";
    this.labelControl11.Size = new Size(211, 13);
    this.labelControl11.TabIndex = 29;
    this.labelControl11.Text = "External Future Corrosion Allowance - FCAe";
    this.labelControl10.Location = new Point(13, 210);
    this.labelControl10.Name = "labelControl10";
    this.labelControl10.Size = new Size(205, 13);
    this.labelControl10.TabIndex = 26;
    this.labelControl10.Text = "Internal Future Corrosion Allowance - FCAi";
    this.labelControl9.Location = new Point(13, 188);
    this.labelControl9.Name = "labelControl9";
    this.labelControl9.Size = new Size(174, 13);
    this.labelControl9.TabIndex = 23;
    this.labelControl9.Text = "External Uniform Metal Loss - LOSSe";
    this.labelControl8.Location = new Point(13, 166);
    this.labelControl8.Name = "labelControl8";
    this.labelControl8.Size = new Size(168, 13);
    this.labelControl8.TabIndex = 20;
    this.labelControl8.Text = "Internal Uniform Metal Loss - LOSSi";
    this.labelControl7.Location = new Point(13, 144 /*0x90*/);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(226, 13);
    this.labelControl7.TabIndex = 17;
    this.labelControl7.Text = "Spacing to major structural discontinuity - Lmsd";
    this.labelControl6.Location = new Point(13, 122);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(129, 13);
    this.labelControl6.TabIndex = 14;
    this.labelControl6.Text = "Groove-Like-Flaw Angle - ß";
    this.labelControl5.Location = new Point(13, 100);
    this.labelControl5.Name = "labelControl5";
    this.labelControl5.Size = new Size(138, 13);
    this.labelControl5.TabIndex = 11;
    this.labelControl5.Text = "Groove-Like-Flaw Radius - gr";
    this.labelControl4.Location = new Point(13, 78);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(138, 13);
    this.labelControl4.TabIndex = 8;
    this.labelControl4.Text = "Groove-Like-Flaw Width - gw";
    this.labelControl3.Location = new Point(13, 56);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(137, 13);
    this.labelControl3.TabIndex = 5;
    this.labelControl3.Text = "Groove-Like-Flaw Length - gl";
    this.labelControl2.Location = new Point(13, 34);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(137, 13);
    this.labelControl2.TabIndex = 2;
    this.labelControl2.Text = "Groove-Like-Flaw Depth - dg";
    this.labelControl1.Location = new Point(13, 13);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(31 /*0x1F*/, 13);
    this.labelControl1.TabIndex = 0;
    this.labelControl1.Text = "MAWP";
    this.chkMAWP.Location = new Point(243, 11);
    this.chkMAWP.Margin = new Padding(1);
    this.chkMAWP.Name = "chkMAWP";
    this.chkMAWP.Properties.AutoWidth = true;
    this.chkMAWP.Properties.Caption = "";
    this.chkMAWP.Size = new Size(19, 19);
    this.chkMAWP.TabIndex = 1;
    this.tableLayoutPanel2.AutoSize = true;
    this.tableLayoutPanel2.ColumnCount = 3;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.tableLayoutPanel2, 3);
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333f));
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333f));
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 33.33333f));
    this.tableLayoutPanel2.Controls.Add((Control) this.pictureGeometry3, 2, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.pictureGeometry1, 0, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.pictureGeometry2, 1, 0);
    this.tableLayoutPanel2.Location = new Point(13, 254);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.RowCount = 1;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.Size = new Size(717, 151);
    this.tableLayoutPanel2.TabIndex = 32 /*0x20*/;
    this.pictureGeometry3.EditValue = componentResourceManager.GetObject("pictureGeometry3.EditValue");
    this.pictureGeometry3.Location = new Point(488, 10);
    this.pictureGeometry3.Margin = new Padding(10);
    this.pictureGeometry3.Name = "pictureGeometry3";
    this.pictureGeometry3.Properties.AllowFocused = false;
    this.pictureGeometry3.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry3.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry3.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry3.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry3.Properties.ReadOnly = true;
    this.pictureGeometry3.Properties.ShowMenu = false;
    this.pictureGeometry3.Size = new Size(219, 131);
    this.pictureGeometry3.TabIndex = 2;
    this.pictureGeometry1.EditValue = componentResourceManager.GetObject("pictureGeometry1.EditValue");
    this.pictureGeometry1.Location = new Point(10, 10);
    this.pictureGeometry1.Margin = new Padding(10);
    this.pictureGeometry1.Name = "pictureGeometry1";
    this.pictureGeometry1.Properties.AllowFocused = false;
    this.pictureGeometry1.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry1.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry1.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry1.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry1.Properties.ReadOnly = true;
    this.pictureGeometry1.Properties.ShowMenu = false;
    this.pictureGeometry1.Size = new Size(219, 131);
    this.pictureGeometry1.TabIndex = 0;
    this.pictureGeometry2.EditValue = componentResourceManager.GetObject("pictureGeometry2.EditValue");
    this.pictureGeometry2.Location = new Point(249, 10);
    this.pictureGeometry2.Margin = new Padding(10);
    this.pictureGeometry2.Name = "pictureGeometry2";
    this.pictureGeometry2.Properties.AllowFocused = false;
    this.pictureGeometry2.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry2.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry2.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry2.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry2.Properties.ReadOnly = true;
    this.pictureGeometry2.Properties.ShowMenu = false;
    this.pictureGeometry2.Size = new Size(219, 131);
    this.pictureGeometry2.TabIndex = 1;
    this.txtFCAe.Location = new Point(243, 230);
    this.txtFCAe.Margin = new Padding(1);
    this.txtFCAe.Name = "txtFCAe";
    this.txtFCAe.Size = new Size(100, 20);
    this.txtFCAe.TabIndex = 30;
    this.txtFCAi.Location = new Point(243, 208 /*0xD0*/);
    this.txtFCAi.Margin = new Padding(1);
    this.txtFCAi.Name = "txtFCAi";
    this.txtFCAi.Size = new Size(100, 20);
    this.txtFCAi.TabIndex = 27;
    this.umFCAe.Location = new Point(347, 232);
    this.umFCAe.Name = "umFCAe";
    this.umFCAe.Size = new Size(41, 13);
    this.umFCAe.TabIndex = 31 /*0x1F*/;
    this.umFCAe.Text = "measure";
    this.umFCAi.Location = new Point(347, 210);
    this.umFCAi.Name = "umFCAi";
    this.umFCAi.Size = new Size(41, 13);
    this.umFCAi.TabIndex = 28;
    this.umFCAi.Text = "measure";
    this.txtdg.Location = new Point(243, 32 /*0x20*/);
    this.txtdg.Margin = new Padding(1);
    this.txtdg.Name = "txtdg";
    this.txtdg.Size = new Size(100, 20);
    this.txtdg.TabIndex = 3;
    this.txtdl.Location = new Point(243, 54);
    this.txtdl.Margin = new Padding(1);
    this.txtdl.Name = "txtdl";
    this.txtdl.Size = new Size(100, 20);
    this.txtdl.TabIndex = 6;
    this.txtdw.Location = new Point(243, 76);
    this.txtdw.Margin = new Padding(1);
    this.txtdw.Name = "txtdw";
    this.txtdw.Size = new Size(100, 20);
    this.txtdw.TabIndex = 9;
    this.txtdr.Location = new Point(243, 98);
    this.txtdr.Margin = new Padding(1);
    this.txtdr.Name = "txtdr";
    this.txtdr.Size = new Size(100, 20);
    this.txtdr.TabIndex = 12;
    this.txtdAngle.Location = new Point(243, 120);
    this.txtdAngle.Margin = new Padding(1);
    this.txtdAngle.Name = "txtdAngle";
    this.txtdAngle.Size = new Size(100, 20);
    this.txtdAngle.TabIndex = 15;
    this.txtLmsd.Location = new Point(243, 142);
    this.txtLmsd.Margin = new Padding(1);
    this.txtLmsd.Name = "txtLmsd";
    this.txtLmsd.Size = new Size(100, 20);
    this.txtLmsd.TabIndex = 18;
    this.txtLOSSi.Location = new Point(243, 164);
    this.txtLOSSi.Margin = new Padding(1);
    this.txtLOSSi.Name = "txtLOSSi";
    this.txtLOSSi.Size = new Size(100, 20);
    this.txtLOSSi.TabIndex = 21;
    this.txtLOSSe.Location = new Point(243, 186);
    this.txtLOSSe.Margin = new Padding(1);
    this.txtLOSSe.Name = "txtLOSSe";
    this.txtLOSSe.Size = new Size(100, 20);
    this.txtLOSSe.TabIndex = 24;
    this.umdg.Location = new Point(347, 34);
    this.umdg.Name = "umdg";
    this.umdg.Size = new Size(41, 13);
    this.umdg.TabIndex = 4;
    this.umdg.Text = "measure";
    this.umdl.Location = new Point(347, 56);
    this.umdl.Name = "umdl";
    this.umdl.Size = new Size(41, 13);
    this.umdl.TabIndex = 7;
    this.umdl.Text = "measure";
    this.umdw.Location = new Point(347, 78);
    this.umdw.Name = "umdw";
    this.umdw.Size = new Size(41, 13);
    this.umdw.TabIndex = 10;
    this.umdw.Text = "measure";
    this.umdr.Location = new Point(347, 100);
    this.umdr.Name = "umdr";
    this.umdr.Size = new Size(41, 13);
    this.umdr.TabIndex = 13;
    this.umdr.Text = "measure";
    this.umdangle.Location = new Point(347, 122);
    this.umdangle.Name = "umdangle";
    this.umdangle.Size = new Size(41, 13);
    this.umdangle.TabIndex = 16 /*0x10*/;
    this.umdangle.Text = "measure";
    this.umLmsd.Location = new Point(347, 144 /*0x90*/);
    this.umLmsd.Name = "umLmsd";
    this.umLmsd.Size = new Size(41, 13);
    this.umLmsd.TabIndex = 19;
    this.umLmsd.Text = "measure";
    this.umLOSSi.Location = new Point(347, 166);
    this.umLOSSi.Name = "umLOSSi";
    this.umLOSSi.Size = new Size(41, 13);
    this.umLOSSi.TabIndex = 22;
    this.umLOSSi.Text = "measure";
    this.umLOSSe.Location = new Point(347, 188);
    this.umLOSSe.Name = "umLOSSe";
    this.umLOSSe.Size = new Size(41, 13);
    this.umLOSSe.TabIndex = 25;
    this.umLOSSe.Text = "measure";
    this.dxErrorProvider1.ContainerControl = (ContainerControl) this;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = "vwLocalMetalLossGrooveLike";
    this.Size = new Size(746, 421);
    this.Load += new EventHandler(this.AbstractLocalMetalLossGrooveLikeView_Load);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.chkMAWP.Properties.EndInit();
    this.tableLayoutPanel2.ResumeLayout(false);
    this.pictureGeometry3.Properties.EndInit();
    this.pictureGeometry1.Properties.EndInit();
    this.pictureGeometry2.Properties.EndInit();
    this.txtFCAe.Properties.EndInit();
    this.txtFCAi.Properties.EndInit();
    this.txtdg.Properties.EndInit();
    this.txtdl.Properties.EndInit();
    this.txtdw.Properties.EndInit();
    this.txtdr.Properties.EndInit();
    this.txtdAngle.Properties.EndInit();
    this.txtLmsd.Properties.EndInit();
    this.txtLOSSi.Properties.EndInit();
    this.txtLOSSe.Properties.EndInit();
    ((ISupportInitialize) this.dxErrorProvider1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
