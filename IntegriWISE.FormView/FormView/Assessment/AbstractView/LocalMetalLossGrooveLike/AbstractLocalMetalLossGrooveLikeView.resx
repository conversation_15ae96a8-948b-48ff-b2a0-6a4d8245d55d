<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="pictureGeometry1.EditValue" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>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</value>
  </data>
  <data name="pictureGeometry2.EditValue" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>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</value>
  </data>
  <data name="pictureGeometry3.EditValue" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>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</value>
  </data>
</root>