// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.AbstractView.MAWP.AbstractMAWPView
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Assessment.AbstractUserInterface.MAWP;
using IntegriWISE.UserInterface.Assessment.DesignCode.ASMEB31_3.MAWP;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.AbstractView.MAWP;

public abstract class AbstractMAWPView : XtraUserControl, IMAWPView, IAssessmentBaseView, IView
{
  private MAWPPresenter _presenter;
  private IRecordView _recordView;
  private FormDirtyTracker _dirtyTracker;
  private IContainer components;
  private TableLayoutPanel tableLayoutPanel1;
  private LabelControl labelControl1;
  private LabelControl umLOSSe;
  private LabelControl umLOSSi;
  private LabelControl labelControl2;
  private LabelControl labelControl3;
  private LabelControl umFCAe;
  private LabelControl umFCAi;
  private LabelControl labelControl4;
  private TextEdit txtFCAi;
  private TextEdit txtFCAe;
  private TextEdit txtLOSSi;
  private TextEdit txtLOSSe;
  private DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider dxErrorProvider1;

  public AbstractMAWPView(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.AssessmentView = (IAssessmentBaseView) this;
    this._presenter = new MAWPPresenter(this._recordView, (IMAWPView) this);
  }

  private void AbstractMAWPView_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView);
    this._dirtyTracker.IsHandled = true;
    this.txtFCAi.AllowOnlyN4();
    this.txtFCAe.AllowOnlyN4();
    this.txtLOSSi.AllowOnlyN4();
    this.txtLOSSe.AllowOnlyN4();
    this._presenter.LoadAssessment();
    this._dirtyTracker.IsHandled = false;
  }

  public int? AssessmentID { get; set; }

  public double? FCAi
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAi.Text);
    set => this.txtFCAi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? FCAe
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAe.Text);
    set => this.txtFCAe.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSi
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSi.Text);
    set => this.txtLOSSi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSe
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSe.Text);
    set => this.txtLOSSe.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMFCAi
  {
    set => this.umFCAi.Text = value;
  }

  public string UMFCAe
  {
    set => this.umFCAe.Text = value;
  }

  public string UMLOSSi
  {
    set => this.umLOSSi.Text = value;
  }

  public string UMLOSSe
  {
    set => this.umLOSSe.Text = value;
  }

  public string FCAiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAi, value);
  }

  public string FCAeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtFCAe, value);
  }

  public string LOSSiErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSi, value);
  }

  public string LOSSeErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtLOSSe, value);
  }

  public string FCAiInfo
  {
    set => this.txtFCAi.ToolTip = value;
  }

  public string FCAeInfo
  {
    set => this.txtFCAe.ToolTip = value;
  }

  public string LOSSiInfo
  {
    set => this.txtLOSSi.ToolTip = value;
  }

  public string LOSSeInfo
  {
    set => this.txtLOSSe.ToolTip = value;
  }

  public bool ValidateAssessment() => this._presenter.Validate();

  public void Save() => this._presenter.Save();

  public void ClearErrors() => this.dxErrorProvider1.ClearErrors();

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.labelControl1 = new LabelControl();
    this.umLOSSi = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.labelControl3 = new LabelControl();
    this.umFCAe = new LabelControl();
    this.umFCAi = new LabelControl();
    this.labelControl4 = new LabelControl();
    this.txtFCAi = new TextEdit();
    this.txtFCAe = new TextEdit();
    this.txtLOSSi = new TextEdit();
    this.txtLOSSe = new TextEdit();
    this.umLOSSe = new LabelControl();
    this.dxErrorProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(this.components);
    this.tableLayoutPanel1.SuspendLayout();
    this.txtFCAi.Properties.BeginInit();
    this.txtFCAe.Properties.BeginInit();
    this.txtLOSSi.Properties.BeginInit();
    this.txtLOSSe.Properties.BeginInit();
    ((ISupportInitialize) this.dxErrorProvider1).BeginInit();
    this.SuspendLayout();
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSi, 2, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl3, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAe, 2, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAi, 2, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl4, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAi, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAe, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSi, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSe, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSe, 2, 3);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 6;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(425, 110);
    this.tableLayoutPanel1.TabIndex = 0;
    this.labelControl1.Location = new Point(13, 13);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(202, 13);
    this.labelControl1.TabIndex = 0;
    this.labelControl1.Text = "Internal Future Corrosion Allowance, FCAi";
    this.umLOSSi.Location = new Point(329, 57);
    this.umLOSSi.Name = "umLOSSi";
    this.umLOSSi.Size = new Size(41, 13);
    this.umLOSSi.TabIndex = 8;
    this.umLOSSi.Text = "measure";
    this.labelControl2.Location = new Point(13, 35);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(208 /*0xD0*/, 13);
    this.labelControl2.TabIndex = 3;
    this.labelControl2.Text = "External Future Corrosion Allowance, FCAe";
    this.labelControl3.Location = new Point(13, 57);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(165, 13);
    this.labelControl3.TabIndex = 6;
    this.labelControl3.Text = "Internal Uniform Metal Loss, LOSSi";
    this.umFCAe.Location = new Point(329, 35);
    this.umFCAe.Name = "umFCAe";
    this.umFCAe.Size = new Size(41, 13);
    this.umFCAe.TabIndex = 5;
    this.umFCAe.Text = "measure";
    this.umFCAi.Location = new Point(329, 13);
    this.umFCAi.Name = "umFCAi";
    this.umFCAi.Size = new Size(41, 13);
    this.umFCAi.TabIndex = 2;
    this.umFCAi.Text = "measure";
    this.labelControl4.Location = new Point(13, 79);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(171, 13);
    this.labelControl4.TabIndex = 9;
    this.labelControl4.Text = "External Uniform Metal Loss, LOSSe";
    this.txtFCAi.Location = new Point(225, 11);
    this.txtFCAi.Margin = new Padding(1);
    this.txtFCAi.Name = "txtFCAi";
    this.txtFCAi.Size = new Size(100, 20);
    this.txtFCAi.TabIndex = 1;
    this.txtFCAe.Location = new Point(225, 33);
    this.txtFCAe.Margin = new Padding(1);
    this.txtFCAe.Name = "txtFCAe";
    this.txtFCAe.Size = new Size(100, 20);
    this.txtFCAe.TabIndex = 4;
    this.txtLOSSi.Location = new Point(225, 55);
    this.txtLOSSi.Margin = new Padding(1);
    this.txtLOSSi.Name = "txtLOSSi";
    this.txtLOSSi.Size = new Size(100, 20);
    this.txtLOSSi.TabIndex = 7;
    this.txtLOSSe.Location = new Point(225, 77);
    this.txtLOSSe.Margin = new Padding(1);
    this.txtLOSSe.Name = "txtLOSSe";
    this.txtLOSSe.Size = new Size(100, 20);
    this.txtLOSSe.TabIndex = 10;
    this.umLOSSe.Location = new Point(329, 79);
    this.umLOSSe.Name = "umLOSSe";
    this.umLOSSe.Size = new Size(41, 13);
    this.umLOSSe.TabIndex = 11;
    this.umLOSSe.Text = "measure";
    this.dxErrorProvider1.ContainerControl = (ContainerControl) this;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = "vwMAWP";
    this.Size = new Size(425, 110);
    this.Load += new EventHandler(this.AbstractMAWPView_Load);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.txtFCAi.Properties.EndInit();
    this.txtFCAe.Properties.EndInit();
    this.txtLOSSi.Properties.EndInit();
    this.txtLOSSe.Properties.EndInit();
    ((ISupportInitialize) this.dxErrorProvider1).EndInit();
    this.ResumeLayout(false);
  }
}
