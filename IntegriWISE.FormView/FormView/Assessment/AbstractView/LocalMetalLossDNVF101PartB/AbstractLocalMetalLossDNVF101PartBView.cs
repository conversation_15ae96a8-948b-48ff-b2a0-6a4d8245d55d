// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.AbstractView.LocalMetalLossDNVF101PartB.AbstractLocalMetalLossDNVF101PartBView
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Assessment.AbstractUserInterface.LocalMetalLossDNVF101PartB;
using IntegriWISE.UserInterface.Assessment.DesignCode.ASMEB31_8.LocalMetalLossDNVF101PartB;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.AbstractView.LocalMetalLossDNVF101PartB;

public abstract class AbstractLocalMetalLossDNVF101PartBView : 
  XtraUserControl,
  ILocalMetalLossDNVF101PartBView,
  IAssessmentBaseView,
  IView
{
  private IContainer components;
  private TableLayoutPanel tableLayoutPanel1;
  private TextEdit txtCircExtentOfCorrodedArea;
  private LabelControl umLongExtentOfCorrodedArea;
  private TextEdit txtLongExtentOfCorrodedArea;
  private LabelControl umDepthOfCorrodedArea;
  private TextEdit txtDepthOfCorrodedArea;
  private LabelControl lblCircAngularSpacingBetweenDefects;
  private LabelControl umExternalLongitudinalForce;
  private LabelControl umCircAngularSpacingBetweenDefects;
  private TextEdit txtCircAngularSpacingBetweenDefects;
  private LabelControl lblExternalLongitudinalForce;
  private TextEdit txtExternalLongitudinalForce;
  private LabelControl lblCombinedLoading;
  private CheckEdit chkCombinedLoading;
  private LabelControl lblDepthOfCorrodedArea;
  private LabelControl lblLongExtentOfCorrodedArea;
  private LabelControl lblCircExtentOfCorrodedArea;
  private LabelControl lblLongSpacingBetweenDefects;
  private LabelControl umExternalBendingMoment;
  private LabelControl lblExternalBendingMoment;
  private TextEdit txtLongSpacingBetweenDefects;
  private TextEdit txtExternalBendingMoment;
  private LabelControl umLongSpacingBetweenDefects;
  private LabelControl umCircExtentOfCorrodedArea;
  private LabelControl lblModellingFactor;
  private TextEdit txtModellingFactor;
  private LabelControl lblAdjacentDefectPresent;
  private CheckEdit chkAdjacentDefectPresent;
  private LocalMetalLossDNVF101PartBPresenter _presenter;
  protected IRecordView _recordView;
  private FormDirtyTracker _dirtyTracker;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.lblModellingFactor = new LabelControl();
    this.lblCircExtentOfCorrodedArea = new LabelControl();
    this.lblLongExtentOfCorrodedArea = new LabelControl();
    this.lblDepthOfCorrodedArea = new LabelControl();
    this.txtCircExtentOfCorrodedArea = new TextEdit();
    this.txtLongExtentOfCorrodedArea = new TextEdit();
    this.txtDepthOfCorrodedArea = new TextEdit();
    this.txtModellingFactor = new TextEdit();
    this.umCircExtentOfCorrodedArea = new LabelControl();
    this.umLongExtentOfCorrodedArea = new LabelControl();
    this.umDepthOfCorrodedArea = new LabelControl();
    this.lblCircAngularSpacingBetweenDefects = new LabelControl();
    this.txtCircAngularSpacingBetweenDefects = new TextEdit();
    this.umCircAngularSpacingBetweenDefects = new LabelControl();
    this.lblLongSpacingBetweenDefects = new LabelControl();
    this.txtLongSpacingBetweenDefects = new TextEdit();
    this.umLongSpacingBetweenDefects = new LabelControl();
    this.lblAdjacentDefectPresent = new LabelControl();
    this.chkAdjacentDefectPresent = new CheckEdit();
    this.lblCombinedLoading = new LabelControl();
    this.chkCombinedLoading = new CheckEdit();
    this.lblExternalLongitudinalForce = new LabelControl();
    this.lblExternalBendingMoment = new LabelControl();
    this.txtExternalLongitudinalForce = new TextEdit();
    this.txtExternalBendingMoment = new TextEdit();
    this.umExternalLongitudinalForce = new LabelControl();
    this.umExternalBendingMoment = new LabelControl();
    this.tableLayoutPanel1.SuspendLayout();
    this.txtCircExtentOfCorrodedArea.Properties.BeginInit();
    this.txtLongExtentOfCorrodedArea.Properties.BeginInit();
    this.txtDepthOfCorrodedArea.Properties.BeginInit();
    this.txtModellingFactor.Properties.BeginInit();
    this.txtCircAngularSpacingBetweenDefects.Properties.BeginInit();
    this.txtLongSpacingBetweenDefects.Properties.BeginInit();
    this.chkAdjacentDefectPresent.Properties.BeginInit();
    this.chkCombinedLoading.Properties.BeginInit();
    this.txtExternalLongitudinalForce.Properties.BeginInit();
    this.txtExternalBendingMoment.Properties.BeginInit();
    this.SuspendLayout();
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.lblModellingFactor, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblCircExtentOfCorrodedArea, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblLongExtentOfCorrodedArea, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblDepthOfCorrodedArea, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtCircExtentOfCorrodedArea, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLongExtentOfCorrodedArea, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtDepthOfCorrodedArea, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtModellingFactor, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.umCircExtentOfCorrodedArea, 2, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLongExtentOfCorrodedArea, 2, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.umDepthOfCorrodedArea, 2, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblCircAngularSpacingBetweenDefects, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtCircAngularSpacingBetweenDefects, 1, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.umCircAngularSpacingBetweenDefects, 2, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblLongSpacingBetweenDefects, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLongSpacingBetweenDefects, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLongSpacingBetweenDefects, 2, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblAdjacentDefectPresent, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkAdjacentDefectPresent, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblCombinedLoading, 0, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkCombinedLoading, 1, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblExternalLongitudinalForce, 0, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblExternalBendingMoment, 0, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtExternalLongitudinalForce, 1, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtExternalBendingMoment, 1, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.umExternalLongitudinalForce, 2, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.umExternalBendingMoment, 2, 10);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 11;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(552, 468);
    this.tableLayoutPanel1.TabIndex = 0;
    this.lblModellingFactor.Location = new Point(13, 13);
    this.lblModellingFactor.Name = "lblModellingFactor";
    this.lblModellingFactor.Size = new Size(78, 13);
    this.lblModellingFactor.TabIndex = 0;
    this.lblModellingFactor.Text = "Modelling Factor";
    this.lblCircExtentOfCorrodedArea.Location = new Point(13, 79);
    this.lblCircExtentOfCorrodedArea.Name = "lblCircExtentOfCorrodedArea";
    this.lblCircExtentOfCorrodedArea.Size = new Size(206, 13);
    this.lblCircExtentOfCorrodedArea.TabIndex = 8;
    this.lblCircExtentOfCorrodedArea.Text = "Circumferential Extent of Corroded Area, c";
    this.lblLongExtentOfCorrodedArea.Location = new Point(13, 57);
    this.lblLongExtentOfCorrodedArea.Name = "lblLongExtentOfCorrodedArea";
    this.lblLongExtentOfCorrodedArea.Size = new Size(188, 13);
    this.lblLongExtentOfCorrodedArea.TabIndex = 5;
    this.lblLongExtentOfCorrodedArea.Text = "Longitudinal Extent of Corroded Area, l";
    this.lblDepthOfCorrodedArea.Location = new Point(13, 35);
    this.lblDepthOfCorrodedArea.Name = "lblDepthOfCorrodedArea";
    this.lblDepthOfCorrodedArea.Size = new Size(129, 13);
    this.lblDepthOfCorrodedArea.TabIndex = 2;
    this.lblDepthOfCorrodedArea.Text = "Depth of Corroded Area, d";
    this.txtCircExtentOfCorrodedArea.Location = new Point(265, 77);
    this.txtCircExtentOfCorrodedArea.Margin = new Padding(1);
    this.txtCircExtentOfCorrodedArea.Name = "txtCircExtentOfCorrodedArea";
    this.txtCircExtentOfCorrodedArea.Size = new Size(130, 20);
    this.txtCircExtentOfCorrodedArea.TabIndex = 9;
    this.txtLongExtentOfCorrodedArea.Location = new Point(265, 55);
    this.txtLongExtentOfCorrodedArea.Margin = new Padding(1);
    this.txtLongExtentOfCorrodedArea.Name = "txtLongExtentOfCorrodedArea";
    this.txtLongExtentOfCorrodedArea.Size = new Size(130, 20);
    this.txtLongExtentOfCorrodedArea.TabIndex = 6;
    this.txtDepthOfCorrodedArea.Location = new Point(265, 33);
    this.txtDepthOfCorrodedArea.Margin = new Padding(1);
    this.txtDepthOfCorrodedArea.Name = "txtDepthOfCorrodedArea";
    this.txtDepthOfCorrodedArea.Size = new Size(130, 20);
    this.txtDepthOfCorrodedArea.TabIndex = 3;
    this.txtModellingFactor.EditValue = (object) "0.9";
    this.txtModellingFactor.Location = new Point(265, 11);
    this.txtModellingFactor.Margin = new Padding(1);
    this.txtModellingFactor.Name = "txtModellingFactor";
    this.txtModellingFactor.Properties.ReadOnly = true;
    this.txtModellingFactor.Size = new Size(130, 20);
    this.txtModellingFactor.TabIndex = 1;
    this.txtModellingFactor.TabStop = false;
    this.umCircExtentOfCorrodedArea.Location = new Point(399, 79);
    this.umCircExtentOfCorrodedArea.Name = "umCircExtentOfCorrodedArea";
    this.umCircExtentOfCorrodedArea.Size = new Size(41, 13);
    this.umCircExtentOfCorrodedArea.TabIndex = 10;
    this.umCircExtentOfCorrodedArea.Text = "measure";
    this.umLongExtentOfCorrodedArea.Location = new Point(399, 57);
    this.umLongExtentOfCorrodedArea.Name = "umLongExtentOfCorrodedArea";
    this.umLongExtentOfCorrodedArea.Size = new Size(41, 13);
    this.umLongExtentOfCorrodedArea.TabIndex = 7;
    this.umLongExtentOfCorrodedArea.Text = "measure";
    this.umDepthOfCorrodedArea.Location = new Point(399, 35);
    this.umDepthOfCorrodedArea.Name = "umDepthOfCorrodedArea";
    this.umDepthOfCorrodedArea.Size = new Size(41, 13);
    this.umDepthOfCorrodedArea.TabIndex = 4;
    this.umDepthOfCorrodedArea.Text = "measure";
    this.lblCircAngularSpacingBetweenDefects.AutoSizeMode = LabelAutoSizeMode.Vertical;
    this.lblCircAngularSpacingBetweenDefects.Location = new Point(13, 148);
    this.lblCircAngularSpacingBetweenDefects.Name = "lblCircAngularSpacingBetweenDefects";
    this.lblCircAngularSpacingBetweenDefects.Size = new Size(208 /*0xD0*/, 26);
    this.lblCircAngularSpacingBetweenDefects.TabIndex = 16 /*0x10*/;
    this.lblCircAngularSpacingBetweenDefects.Text = "Circumferential Angular Spacing Between Adjacent Defects, ɸ";
    this.lblCircAngularSpacingBetweenDefects.Visible = false;
    this.txtCircAngularSpacingBetweenDefects.Location = new Point(265, 146);
    this.txtCircAngularSpacingBetweenDefects.Margin = new Padding(1);
    this.txtCircAngularSpacingBetweenDefects.Name = "txtCircAngularSpacingBetweenDefects";
    this.txtCircAngularSpacingBetweenDefects.Size = new Size(130, 20);
    this.txtCircAngularSpacingBetweenDefects.TabIndex = 17;
    this.txtCircAngularSpacingBetweenDefects.Visible = false;
    this.umCircAngularSpacingBetweenDefects.Location = new Point(399, 148);
    this.umCircAngularSpacingBetweenDefects.Name = "umCircAngularSpacingBetweenDefects";
    this.umCircAngularSpacingBetweenDefects.Size = new Size(41, 13);
    this.umCircAngularSpacingBetweenDefects.TabIndex = 18;
    this.umCircAngularSpacingBetweenDefects.Text = "measure";
    this.umCircAngularSpacingBetweenDefects.Visible = false;
    this.lblLongSpacingBetweenDefects.Location = new Point(13, 126);
    this.lblLongSpacingBetweenDefects.Name = "lblLongSpacingBetweenDefects";
    this.lblLongSpacingBetweenDefects.Size = new Size(194, 13);
    this.lblLongSpacingBetweenDefects.TabIndex = 13;
    this.lblLongSpacingBetweenDefects.Text = "Longitudinal Spacing Between Defects, s";
    this.lblLongSpacingBetweenDefects.Visible = false;
    this.txtLongSpacingBetweenDefects.Location = new Point(265, 124);
    this.txtLongSpacingBetweenDefects.Margin = new Padding(1);
    this.txtLongSpacingBetweenDefects.Name = "txtLongSpacingBetweenDefects";
    this.txtLongSpacingBetweenDefects.Size = new Size(130, 20);
    this.txtLongSpacingBetweenDefects.TabIndex = 14;
    this.txtLongSpacingBetweenDefects.Visible = false;
    this.umLongSpacingBetweenDefects.Location = new Point(399, 126);
    this.umLongSpacingBetweenDefects.Name = "umLongSpacingBetweenDefects";
    this.umLongSpacingBetweenDefects.Size = new Size(41, 13);
    this.umLongSpacingBetweenDefects.TabIndex = 15;
    this.umLongSpacingBetweenDefects.Text = "measure";
    this.umLongSpacingBetweenDefects.Visible = false;
    this.lblAdjacentDefectPresent.Location = new Point(13, 101);
    this.lblAdjacentDefectPresent.Name = "lblAdjacentDefectPresent";
    this.lblAdjacentDefectPresent.Size = new Size(131, 13);
    this.lblAdjacentDefectPresent.TabIndex = 11;
    this.lblAdjacentDefectPresent.Text = "Adjacent Defects Present? ";
    this.chkAdjacentDefectPresent.Location = new Point(267, 101);
    this.chkAdjacentDefectPresent.Name = "chkAdjacentDefectPresent";
    this.chkAdjacentDefectPresent.Properties.Caption = "";
    this.chkAdjacentDefectPresent.Size = new Size(24, 19);
    this.chkAdjacentDefectPresent.TabIndex = 12;
    this.chkAdjacentDefectPresent.CheckedChanged += new EventHandler(this.chkAdjacentDefectPresent_CheckedChanged);
    this.lblCombinedLoading.Location = new Point(13, 180);
    this.lblCombinedLoading.Name = "lblCombinedLoading";
    this.lblCombinedLoading.Size = new Size(248, 13);
    this.lblCombinedLoading.TabIndex = 19;
    this.lblCombinedLoading.Text = "Internal Pressure + Combined Compressive Loading";
    this.chkCombinedLoading.Location = new Point(267, 180);
    this.chkCombinedLoading.Name = "chkCombinedLoading";
    this.chkCombinedLoading.Properties.Caption = "";
    this.chkCombinedLoading.Size = new Size(24, 19);
    this.chkCombinedLoading.TabIndex = 20;
    this.chkCombinedLoading.CheckedChanged += new EventHandler(this.chkCombinedLoading_CheckedChanged);
    this.lblExternalLongitudinalForce.AutoSizeMode = LabelAutoSizeMode.Vertical;
    this.lblExternalLongitudinalForce.Location = new Point(13, 205);
    this.lblExternalLongitudinalForce.Name = "lblExternalLongitudinalForce";
    this.lblExternalLongitudinalForce.Size = new Size(226, 13);
    this.lblExternalLongitudinalForce.TabIndex = 21;
    this.lblExternalLongitudinalForce.Text = "External Applied Longitudinal Force, F";
    this.lblExternalLongitudinalForce.Visible = false;
    this.lblExternalBendingMoment.AutoSizeMode = LabelAutoSizeMode.Vertical;
    this.lblExternalBendingMoment.Location = new Point(13, 227);
    this.lblExternalBendingMoment.Name = "lblExternalBendingMoment";
    this.lblExternalBendingMoment.Size = new Size(226, 13);
    this.lblExternalBendingMoment.TabIndex = 24;
    this.lblExternalBendingMoment.Text = "External Applied Bending Moment, M";
    this.lblExternalBendingMoment.Visible = false;
    this.txtExternalLongitudinalForce.Location = new Point(265, 203);
    this.txtExternalLongitudinalForce.Margin = new Padding(1);
    this.txtExternalLongitudinalForce.Name = "txtExternalLongitudinalForce";
    this.txtExternalLongitudinalForce.Size = new Size(130, 20);
    this.txtExternalLongitudinalForce.TabIndex = 22;
    this.txtExternalLongitudinalForce.Visible = false;
    this.txtExternalBendingMoment.Location = new Point(265, 225);
    this.txtExternalBendingMoment.Margin = new Padding(1);
    this.txtExternalBendingMoment.Name = "txtExternalBendingMoment";
    this.txtExternalBendingMoment.Size = new Size(130, 20);
    this.txtExternalBendingMoment.TabIndex = 25;
    this.txtExternalBendingMoment.Visible = false;
    this.umExternalLongitudinalForce.Location = new Point(399, 205);
    this.umExternalLongitudinalForce.Name = "umExternalLongitudinalForce";
    this.umExternalLongitudinalForce.Size = new Size(41, 13);
    this.umExternalLongitudinalForce.TabIndex = 23;
    this.umExternalLongitudinalForce.Text = "measure";
    this.umExternalLongitudinalForce.Visible = false;
    this.umExternalBendingMoment.Location = new Point(399, 227);
    this.umExternalBendingMoment.Name = "umExternalBendingMoment";
    this.umExternalBendingMoment.Size = new Size(41, 13);
    this.umExternalBendingMoment.TabIndex = 26;
    this.umExternalBendingMoment.Text = "measure";
    this.umExternalBendingMoment.Visible = false;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = nameof (AbstractLocalMetalLossDNVF101PartBView);
    this.Size = new Size(552, 468);
    this.Load += new EventHandler(this.vwLocalMetalLossDNVF101PartB_Load);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.txtCircExtentOfCorrodedArea.Properties.EndInit();
    this.txtLongExtentOfCorrodedArea.Properties.EndInit();
    this.txtDepthOfCorrodedArea.Properties.EndInit();
    this.txtModellingFactor.Properties.EndInit();
    this.txtCircAngularSpacingBetweenDefects.Properties.EndInit();
    this.txtLongSpacingBetweenDefects.Properties.EndInit();
    this.chkAdjacentDefectPresent.Properties.EndInit();
    this.chkCombinedLoading.Properties.EndInit();
    this.txtExternalLongitudinalForce.Properties.EndInit();
    this.txtExternalBendingMoment.Properties.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public AbstractLocalMetalLossDNVF101PartBView(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.AssessmentView = (IAssessmentBaseView) this;
    this._presenter = new LocalMetalLossDNVF101PartBPresenter(this._recordView, (ILocalMetalLossDNVF101PartBView) this);
  }

  protected void vwLocalMetalLossDNVF101PartB_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView);
    this._dirtyTracker.IsHandled = true;
    this.txtDepthOfCorrodedArea.AllowOnlyN4();
    this.txtLongExtentOfCorrodedArea.AllowOnlyN4();
    this.txtCircExtentOfCorrodedArea.AllowOnlyN4();
    this.txtLongSpacingBetweenDefects.AllowOnlyN4();
    this.txtCircAngularSpacingBetweenDefects.AllowOnlyN4();
    this.txtExternalLongitudinalForce.AllowOnlyN4AndNeg();
    this.txtExternalBendingMoment.AllowOnlyN4AndNeg();
    this._presenter.LoadAssessment();
    this._dirtyTracker.IsHandled = false;
  }

  private void chkCombinedLoading_CheckedChanged(object sender, EventArgs e)
  {
    this._presenter.EnableCombinedLoadControls();
  }

  private void chkAdjacentDefectPresent_CheckedChanged(object sender, EventArgs e)
  {
    this._presenter.EnableAdjacentDefectControls();
  }

  public int? AssessmentID { get; set; }

  public bool IntPressureAndCombinedCompLoading
  {
    get => this.chkCombinedLoading.Checked;
    set => this.chkCombinedLoading.Checked = value;
  }

  public bool AdjacentDefectPresent
  {
    get => this.chkAdjacentDefectPresent.Checked;
    set => this.chkAdjacentDefectPresent.Checked = value;
  }

  public double? DepthOfCorrodedArea
  {
    get => Helpers.ParseNullDouble((object) this.txtDepthOfCorrodedArea.Text);
    set => this.txtDepthOfCorrodedArea.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LongitudinalExtentOfCorrodedArea
  {
    get => Helpers.ParseNullDouble((object) this.txtLongExtentOfCorrodedArea.Text);
    set => this.txtLongExtentOfCorrodedArea.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? CircumferentialExtentOfCorrodedArea
  {
    get => Helpers.ParseNullDouble((object) this.txtCircExtentOfCorrodedArea.Text);
    set => this.txtCircExtentOfCorrodedArea.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LongitudinalSpacingBetweenDefects
  {
    get => Helpers.ParseNullDouble((object) this.txtLongSpacingBetweenDefects.Text);
    set => this.txtLongSpacingBetweenDefects.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? CircAngularSpacingBetweenAdjacentDefects
  {
    get => Helpers.ParseNullDouble((object) this.txtCircAngularSpacingBetweenDefects.Text);
    set
    {
      this.txtCircAngularSpacingBetweenDefects.Text = Helpers.ParseObjectToString((object) value);
    }
  }

  public double? ExternalLongitudinalForce
  {
    get => Helpers.ParseNullDouble((object) this.txtExternalLongitudinalForce.Text);
    set => this.txtExternalLongitudinalForce.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? ExternalBendingMoment
  {
    get => Helpers.ParseNullDouble((object) this.txtExternalBendingMoment.Text);
    set => this.txtExternalBendingMoment.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMMaxCorrodedDepth
  {
    set => this.umDepthOfCorrodedArea.Text = value;
  }

  public string UMl
  {
    set => this.umLongExtentOfCorrodedArea.Text = value;
  }

  public string UMc
  {
    set => this.umCircExtentOfCorrodedArea.Text = value;
  }

  public string UMs
  {
    set => this.umLongSpacingBetweenDefects.Text = value;
  }

  public string UMPhi
  {
    set => this.umCircAngularSpacingBetweenDefects.Text = value;
  }

  public string UMFX
  {
    set => this.umExternalLongitudinalForce.Text = value;
  }

  public string UMMY
  {
    set => this.umExternalBendingMoment.Text = value;
  }

  public string CombinedLoadInfo
  {
    set => this.chkCombinedLoading.ToolTip = value;
  }

  public string ModellingFactorInfo
  {
    set => this.txtModellingFactor.ToolTip = value;
  }

  public string MaxCorrodedDepthInfo
  {
    set => this.txtDepthOfCorrodedArea.ToolTip = value;
  }

  public string lInfo
  {
    set => this.txtLongExtentOfCorrodedArea.ToolTip = value;
  }

  public string cInfo
  {
    set => this.txtCircExtentOfCorrodedArea.ToolTip = value;
  }

  public string sInfo
  {
    set => this.txtLongSpacingBetweenDefects.ToolTip = value;
  }

  public string PhiInfo
  {
    set => this.txtCircAngularSpacingBetweenDefects.ToolTip = value;
  }

  public string FXInfo
  {
    set => this.txtExternalLongitudinalForce.ToolTip = value;
  }

  public string MYInfo
  {
    set => this.txtExternalBendingMoment.ToolTip = value;
  }

  public bool EnableCombinedLoadControls
  {
    set
    {
      this.lblExternalLongitudinalForce.Visible = value;
      this.txtExternalLongitudinalForce.Visible = value;
      this.umExternalLongitudinalForce.Visible = value;
      this.lblExternalBendingMoment.Visible = value;
      this.txtExternalBendingMoment.Visible = value;
      this.umExternalBendingMoment.Visible = value;
    }
  }

  public bool EnableAdjacentDefectControls
  {
    set
    {
      this.lblLongSpacingBetweenDefects.Visible = value;
      this.txtLongSpacingBetweenDefects.Visible = value;
      this.umLongSpacingBetweenDefects.Visible = value;
      this.lblCircAngularSpacingBetweenDefects.Visible = value;
      this.txtCircAngularSpacingBetweenDefects.Visible = value;
      this.umCircAngularSpacingBetweenDefects.Visible = value;
    }
  }

  public bool ValidateAssessment() => this._presenter.Validate();

  public void Save() => this._presenter.Save();

  public void ClearErrors()
  {
  }
}
