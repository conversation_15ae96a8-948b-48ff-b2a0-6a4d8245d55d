// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.AbstractView.LocalMetalLossDNVF101PartB.AbstractResultView
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.UserInterface.Record;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.AbstractView.LocalMetalLossDNVF101PartB;

public abstract class AbstractResultView : XtraUserControl
{
  private IContainer components;
  private GroupControl groupControl2;
  private TableLayoutPanel tableLayoutPanel1;
  private TextEdit txtDesignFactor;
  private LabelControl lblCircAngularSpacingBetweenAdjacentDefectsCriteria;
  private LabelControl lblAxialSpacingBetweenAdjacentDefectsCriteria;
  private LabelControl lblDefectsInteractionCriteria;
  private GroupControl groupControl1;
  private TableLayoutPanel tableLayoutPanel3;
  private LabelControl labelControl4;
  private TextEdit txtModellingFactor;
  private LabelControl labelControl6;
  private LabelControl labelControl7;
  private LabelControl labelControl8;
  private LabelControl lblExternalLoadWithinLimit;
  private LabelControl lblLowerLimitExternalLoads;
  private LabelControl lblCombinedLongitudinalStress;
  private LabelControl lblLongitudinalStressBendingForceWallThickness;
  private LabelControl lblLongitudinalStressAxialForceWallThickness;
  private LabelControl labelControl9;
  private LabelControl lblFactorForCompressiveLongitudinalStresses;
  private GroupControl groupControl3;
  private TableLayoutPanel tableLayoutPanel5;
  private LabelControl lblFailurePressureOfCorrodedPipe;
  private LabelControl umFailurePressureIntPressureCompLongitudinalStress;
  private TextEdit txtFailurePressureIntPressureCompLongitudinalStress;
  private LabelControl lblFailurePressureIntPressureCompLongitudinalStress;
  private LabelControl labelControl19;
  private GroupControl groupControl4;
  private TableLayoutPanel tableLayoutPanel7;
  private LabelControl lblConclusion;
  private TextEdit txtCircAngularSpacingBetweenAdjacentDefectsCriteria;
  private TextEdit txtAxialSpacingBetweenAdjacentDefectsCriteria;
  private TextEdit txtDefectsInteractionCriteria;
  private TextEdit txtFactorForCompressiveLongitudinalStresses;
  private TextEdit txtExternalLoadWithinLimit;
  private LabelControl umLowerLimitExternalLoads;
  private TextEdit txtLowerLimitExternalLoads;
  private LabelControl umCombinedLongitudinalStress;
  private TextEdit txtCombinedLongitudinalStress;
  private LabelControl umLongitudinalStressBendingForceWallThickness;
  private TextEdit txtLongitudinalStressBendingForceWallThickness;
  private LabelControl umLongitudinalStressAxialForceWallThickness;
  private TextEdit txtLongitudinalStressAxialForceWallThickness;
  private TextEdit txtTotalUsageFactor;
  private TextEdit txtLengthCorrectionFactor;
  private LabelControl umSafeWorkingPressure;
  private TextEdit txtSafeWorkingPressure;
  private LabelControl umFailurePressureOfCorrodedPipe;
  private TextEdit txtFailurePressureOfCorrodedPipe;
  private LabelControl labelControl5;
  private TextEdit txtDefectDepthCriteria;
  private LabelControl umFailurePressureUnderInternalPressure;
  private TextEdit txtFailurePressureUnderInternalPressure;
  private GroupControl groupControl5;
  private TableLayoutPanel tableLayoutPanel9;
  private MemoEdit txtWarningMessages;
  protected IRecordView _recordView;
  private bool _circAngularSpacingBetweenAdjacentDefectsCriteria;
  private bool _axialSpacingBetweenAdjacentDefectsCriteria;
  private bool _defectsInteractionCriteria;
  private bool _defectDepthCriteria;
  private double _designFactor;
  private double _lengthCorrectionFactor;
  private double _totalUsageFactor;
  private double _longitudinalStressAxialForceWallThickness;
  private double _longitudinalStressBendingForceWallThickness;
  private double _combinedLongitudinalStress;
  private double _lowerLimitExternalLoads;
  private bool _externalLoadWithinLimit;
  private double _factorForCompressiveLongitudinalStresses;
  private double _failurePressureUnderInternalPressure;
  private double _failurePressureIntPressureCompLongitudinalStress;
  private double _failurePressureOfCorrodedPipe;
  private double _safeWorkingPressure;
  private string _resultMessages;
  private bool _level1Passed;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.groupControl2 = new GroupControl();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.labelControl5 = new LabelControl();
    this.txtCircAngularSpacingBetweenAdjacentDefectsCriteria = new TextEdit();
    this.lblAxialSpacingBetweenAdjacentDefectsCriteria = new LabelControl();
    this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria = new LabelControl();
    this.lblDefectsInteractionCriteria = new LabelControl();
    this.txtDefectsInteractionCriteria = new TextEdit();
    this.txtAxialSpacingBetweenAdjacentDefectsCriteria = new TextEdit();
    this.txtDefectDepthCriteria = new TextEdit();
    this.lblExternalLoadWithinLimit = new LabelControl();
    this.txtExternalLoadWithinLimit = new TextEdit();
    this.txtDesignFactor = new TextEdit();
    this.groupControl1 = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.umLowerLimitExternalLoads = new LabelControl();
    this.umCombinedLongitudinalStress = new LabelControl();
    this.umLongitudinalStressBendingForceWallThickness = new LabelControl();
    this.umLongitudinalStressAxialForceWallThickness = new LabelControl();
    this.lblLowerLimitExternalLoads = new LabelControl();
    this.lblCombinedLongitudinalStress = new LabelControl();
    this.lblLongitudinalStressBendingForceWallThickness = new LabelControl();
    this.lblLongitudinalStressAxialForceWallThickness = new LabelControl();
    this.labelControl9 = new LabelControl();
    this.labelControl8 = new LabelControl();
    this.labelControl7 = new LabelControl();
    this.labelControl6 = new LabelControl();
    this.lblFactorForCompressiveLongitudinalStresses = new LabelControl();
    this.txtFactorForCompressiveLongitudinalStresses = new TextEdit();
    this.txtLengthCorrectionFactor = new TextEdit();
    this.txtLowerLimitExternalLoads = new TextEdit();
    this.txtTotalUsageFactor = new TextEdit();
    this.txtCombinedLongitudinalStress = new TextEdit();
    this.txtLongitudinalStressBendingForceWallThickness = new TextEdit();
    this.txtLongitudinalStressAxialForceWallThickness = new TextEdit();
    this.txtModellingFactor = new TextEdit();
    this.labelControl4 = new LabelControl();
    this.groupControl3 = new GroupControl();
    this.tableLayoutPanel5 = new TableLayoutPanel();
    this.txtFailurePressureIntPressureCompLongitudinalStress = new TextEdit();
    this.umFailurePressureIntPressureCompLongitudinalStress = new LabelControl();
    this.umFailurePressureOfCorrodedPipe = new LabelControl();
    this.umSafeWorkingPressure = new LabelControl();
    this.umFailurePressureUnderInternalPressure = new LabelControl();
    this.lblFailurePressureOfCorrodedPipe = new LabelControl();
    this.lblFailurePressureIntPressureCompLongitudinalStress = new LabelControl();
    this.labelControl19 = new LabelControl();
    this.txtFailurePressureUnderInternalPressure = new TextEdit();
    this.txtSafeWorkingPressure = new TextEdit();
    this.txtFailurePressureOfCorrodedPipe = new TextEdit();
    this.groupControl4 = new GroupControl();
    this.tableLayoutPanel7 = new TableLayoutPanel();
    this.lblConclusion = new LabelControl();
    this.groupControl5 = new GroupControl();
    this.tableLayoutPanel9 = new TableLayoutPanel();
    this.txtWarningMessages = new MemoEdit();
    this.groupControl2.BeginInit();
    this.groupControl2.SuspendLayout();
    this.tableLayoutPanel1.SuspendLayout();
    this.txtCircAngularSpacingBetweenAdjacentDefectsCriteria.Properties.BeginInit();
    this.txtDefectsInteractionCriteria.Properties.BeginInit();
    this.txtAxialSpacingBetweenAdjacentDefectsCriteria.Properties.BeginInit();
    this.txtDefectDepthCriteria.Properties.BeginInit();
    this.txtExternalLoadWithinLimit.Properties.BeginInit();
    this.txtDesignFactor.Properties.BeginInit();
    this.groupControl1.BeginInit();
    this.groupControl1.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.txtFactorForCompressiveLongitudinalStresses.Properties.BeginInit();
    this.txtLengthCorrectionFactor.Properties.BeginInit();
    this.txtLowerLimitExternalLoads.Properties.BeginInit();
    this.txtTotalUsageFactor.Properties.BeginInit();
    this.txtCombinedLongitudinalStress.Properties.BeginInit();
    this.txtLongitudinalStressBendingForceWallThickness.Properties.BeginInit();
    this.txtLongitudinalStressAxialForceWallThickness.Properties.BeginInit();
    this.txtModellingFactor.Properties.BeginInit();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.tableLayoutPanel5.SuspendLayout();
    this.txtFailurePressureIntPressureCompLongitudinalStress.Properties.BeginInit();
    this.txtFailurePressureUnderInternalPressure.Properties.BeginInit();
    this.txtSafeWorkingPressure.Properties.BeginInit();
    this.txtFailurePressureOfCorrodedPipe.Properties.BeginInit();
    this.groupControl4.BeginInit();
    this.groupControl4.SuspendLayout();
    this.tableLayoutPanel7.SuspendLayout();
    this.groupControl5.BeginInit();
    this.groupControl5.SuspendLayout();
    this.tableLayoutPanel9.SuspendLayout();
    this.txtWarningMessages.Properties.BeginInit();
    this.SuspendLayout();
    this.groupControl2.AppearanceCaption.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.groupControl2.AppearanceCaption.Options.UseFont = true;
    this.groupControl2.AutoSize = true;
    this.groupControl2.Controls.Add((Control) this.tableLayoutPanel1);
    this.groupControl2.Dock = DockStyle.Top;
    this.groupControl2.Location = new Point(0, 271);
    this.groupControl2.Name = "groupControl2";
    this.groupControl2.Size = new Size(647, 153);
    this.groupControl2.TabIndex = 1;
    this.groupControl2.Text = "Assessment Criteria";
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 2;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 300f));
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl5, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtCircAngularSpacingBetweenAdjacentDefectsCriteria, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblAxialSpacingBetweenAdjacentDefectsCriteria, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblDefectsInteractionCriteria, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtDefectsInteractionCriteria, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtAxialSpacingBetweenAdjacentDefectsCriteria, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtDefectDepthCriteria, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblExternalLoadWithinLimit, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtExternalLoadWithinLimit, 1, 4);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(2, 21);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 5;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(643, 130);
    this.tableLayoutPanel1.TabIndex = 0;
    this.labelControl5.Location = new Point(13, 79);
    this.labelControl5.Name = "labelControl5";
    this.labelControl5.Size = new Size(99, 13);
    this.labelControl5.TabIndex = 6;
    this.labelControl5.Text = "Defect depth criteria";
    this.txtCircAngularSpacingBetweenAdjacentDefectsCriteria.Location = new Point(311, 11);
    this.txtCircAngularSpacingBetweenAdjacentDefectsCriteria.Margin = new Padding(1);
    this.txtCircAngularSpacingBetweenAdjacentDefectsCriteria.Name = "txtCircAngularSpacingBetweenAdjacentDefectsCriteria";
    this.txtCircAngularSpacingBetweenAdjacentDefectsCriteria.Properties.ReadOnly = true;
    this.txtCircAngularSpacingBetweenAdjacentDefectsCriteria.Size = new Size(100, 20);
    this.txtCircAngularSpacingBetweenAdjacentDefectsCriteria.TabIndex = 1;
    this.lblAxialSpacingBetweenAdjacentDefectsCriteria.Location = new Point(13, 35);
    this.lblAxialSpacingBetweenAdjacentDefectsCriteria.Name = "lblAxialSpacingBetweenAdjacentDefectsCriteria";
    this.lblAxialSpacingBetweenAdjacentDefectsCriteria.Size = new Size(191, 13);
    this.lblAxialSpacingBetweenAdjacentDefectsCriteria.TabIndex = 2;
    this.lblAxialSpacingBetweenAdjacentDefectsCriteria.Text = "Axial spacing between adjacent defects";
    this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria.Location = new Point(13, 13);
    this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria.Name = "lblCircAngularSpacingBetweenAdjacentDefectsCriteria";
    this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria.Size = new Size(279, 13);
    this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria.TabIndex = 0;
    this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria.Text = "Circumferential angular spacing between adjacent defects";
    this.lblDefectsInteractionCriteria.Location = new Point(13, 57);
    this.lblDefectsInteractionCriteria.Name = "lblDefectsInteractionCriteria";
    this.lblDefectsInteractionCriteria.Size = new Size(122, 13);
    this.lblDefectsInteractionCriteria.TabIndex = 4;
    this.lblDefectsInteractionCriteria.Text = "Defect interaction criteria";
    this.txtDefectsInteractionCriteria.Location = new Point(311, 55);
    this.txtDefectsInteractionCriteria.Margin = new Padding(1);
    this.txtDefectsInteractionCriteria.Name = "txtDefectsInteractionCriteria";
    this.txtDefectsInteractionCriteria.Properties.ReadOnly = true;
    this.txtDefectsInteractionCriteria.Size = new Size(100, 20);
    this.txtDefectsInteractionCriteria.TabIndex = 5;
    this.txtAxialSpacingBetweenAdjacentDefectsCriteria.Location = new Point(311, 33);
    this.txtAxialSpacingBetweenAdjacentDefectsCriteria.Margin = new Padding(1);
    this.txtAxialSpacingBetweenAdjacentDefectsCriteria.Name = "txtAxialSpacingBetweenAdjacentDefectsCriteria";
    this.txtAxialSpacingBetweenAdjacentDefectsCriteria.Properties.ReadOnly = true;
    this.txtAxialSpacingBetweenAdjacentDefectsCriteria.Size = new Size(100, 20);
    this.txtAxialSpacingBetweenAdjacentDefectsCriteria.TabIndex = 3;
    this.txtDefectDepthCriteria.Location = new Point(311, 77);
    this.txtDefectDepthCriteria.Margin = new Padding(1);
    this.txtDefectDepthCriteria.Name = "txtDefectDepthCriteria";
    this.txtDefectDepthCriteria.Properties.ReadOnly = true;
    this.txtDefectDepthCriteria.Size = new Size(100, 20);
    this.txtDefectDepthCriteria.TabIndex = 7;
    this.lblExternalLoadWithinLimit.Location = new Point(13, 101);
    this.lblExternalLoadWithinLimit.Name = "lblExternalLoadWithinLimit";
    this.lblExternalLoadWithinLimit.Size = new Size(115, 13);
    this.lblExternalLoadWithinLimit.TabIndex = 8;
    this.lblExternalLoadWithinLimit.Text = "External load within limit";
    this.txtExternalLoadWithinLimit.Location = new Point(311, 99);
    this.txtExternalLoadWithinLimit.Margin = new Padding(1);
    this.txtExternalLoadWithinLimit.Name = "txtExternalLoadWithinLimit";
    this.txtExternalLoadWithinLimit.Properties.ReadOnly = true;
    this.txtExternalLoadWithinLimit.Size = new Size(100, 20);
    this.txtExternalLoadWithinLimit.TabIndex = 9;
    this.txtDesignFactor.Location = new Point(311, 33);
    this.txtDesignFactor.Margin = new Padding(1);
    this.txtDesignFactor.Name = "txtDesignFactor";
    this.txtDesignFactor.Properties.ReadOnly = true;
    this.txtDesignFactor.Size = new Size(100, 20);
    this.txtDesignFactor.TabIndex = 3;
    this.groupControl1.AppearanceCaption.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.groupControl1.AppearanceCaption.Options.UseFont = true;
    this.groupControl1.AutoSize = true;
    this.groupControl1.Controls.Add((Control) this.tableLayoutPanel3);
    this.groupControl1.Dock = DockStyle.Top;
    this.groupControl1.Location = new Point(0, 0);
    this.groupControl1.Name = "groupControl1";
    this.groupControl1.Size = new Size(647, 271);
    this.groupControl1.TabIndex = 0;
    this.groupControl1.Text = "Intermediate Result";
    this.tableLayoutPanel3.AutoSize = true;
    this.tableLayoutPanel3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel3.ColumnCount = 3;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 300f));
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.umLowerLimitExternalLoads, 2, 7);
    this.tableLayoutPanel3.Controls.Add((Control) this.umCombinedLongitudinalStress, 2, 6);
    this.tableLayoutPanel3.Controls.Add((Control) this.umLongitudinalStressBendingForceWallThickness, 2, 5);
    this.tableLayoutPanel3.Controls.Add((Control) this.umLongitudinalStressAxialForceWallThickness, 2, 4);
    this.tableLayoutPanel3.Controls.Add((Control) this.lblLowerLimitExternalLoads, 0, 7);
    this.tableLayoutPanel3.Controls.Add((Control) this.lblCombinedLongitudinalStress, 0, 6);
    this.tableLayoutPanel3.Controls.Add((Control) this.lblLongitudinalStressBendingForceWallThickness, 0, 5);
    this.tableLayoutPanel3.Controls.Add((Control) this.lblLongitudinalStressAxialForceWallThickness, 0, 4);
    this.tableLayoutPanel3.Controls.Add((Control) this.labelControl9, 0, 1);
    this.tableLayoutPanel3.Controls.Add((Control) this.labelControl8, 0, 0);
    this.tableLayoutPanel3.Controls.Add((Control) this.labelControl7, 0, 3);
    this.tableLayoutPanel3.Controls.Add((Control) this.labelControl6, 0, 2);
    this.tableLayoutPanel3.Controls.Add((Control) this.lblFactorForCompressiveLongitudinalStresses, 0, 8);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtFactorForCompressiveLongitudinalStresses, 1, 8);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtLengthCorrectionFactor, 1, 2);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtDesignFactor, 1, 1);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtLowerLimitExternalLoads, 1, 7);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtTotalUsageFactor, 1, 3);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtCombinedLongitudinalStress, 1, 6);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtLongitudinalStressBendingForceWallThickness, 1, 5);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtLongitudinalStressAxialForceWallThickness, 1, 4);
    this.tableLayoutPanel3.Controls.Add((Control) this.txtModellingFactor, 1, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 9;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel3.Size = new Size(643, 248);
    this.tableLayoutPanel3.TabIndex = 0;
    this.umLowerLimitExternalLoads.Location = new Point(415, 197);
    this.umLowerLimitExternalLoads.Name = "umLowerLimitExternalLoads";
    this.umLowerLimitExternalLoads.Size = new Size(41, 13);
    this.umLowerLimitExternalLoads.TabIndex = 19;
    this.umLowerLimitExternalLoads.Text = "measure";
    this.umCombinedLongitudinalStress.Location = new Point(415, 165);
    this.umCombinedLongitudinalStress.Name = "umCombinedLongitudinalStress";
    this.umCombinedLongitudinalStress.Size = new Size(41, 13);
    this.umCombinedLongitudinalStress.TabIndex = 16 /*0x10*/;
    this.umCombinedLongitudinalStress.Text = "measure";
    this.umLongitudinalStressBendingForceWallThickness.Location = new Point(415, 133);
    this.umLongitudinalStressBendingForceWallThickness.Name = "umLongitudinalStressBendingForceWallThickness";
    this.umLongitudinalStressBendingForceWallThickness.Size = new Size(41, 13);
    this.umLongitudinalStressBendingForceWallThickness.TabIndex = 13;
    this.umLongitudinalStressBendingForceWallThickness.Text = "measure";
    this.umLongitudinalStressAxialForceWallThickness.Location = new Point(415, 101);
    this.umLongitudinalStressAxialForceWallThickness.Name = "umLongitudinalStressAxialForceWallThickness";
    this.umLongitudinalStressAxialForceWallThickness.Size = new Size(41, 13);
    this.umLongitudinalStressAxialForceWallThickness.TabIndex = 10;
    this.umLongitudinalStressAxialForceWallThickness.Text = "measure";
    this.lblLowerLimitExternalLoads.Location = new Point(13, 197);
    this.lblLowerLimitExternalLoads.Name = "lblLowerLimitExternalLoads";
    this.lblLowerLimitExternalLoads.Size = new Size(225, 13);
    this.lblLowerLimitExternalLoads.TabIndex = 17;
    this.lblLowerLimitExternalLoads.Text = "Lower bound limit on external applied loads, σ1";
    this.lblCombinedLongitudinalStress.Location = new Point(13, 165);
    this.lblCombinedLongitudinalStress.Name = "lblCombinedLongitudinalStress";
    this.lblCombinedLongitudinalStress.Size = new Size(213, 26);
    this.lblCombinedLongitudinalStress.TabIndex = 14;
    this.lblCombinedLongitudinalStress.Text = "Combined longitudinal stress due to external\r\napplied loads, σL";
    this.lblLongitudinalStressBendingForceWallThickness.Location = new Point(13, 133);
    this.lblLongitudinalStressBendingForceWallThickness.Name = "lblLongitudinalStressBendingForceWallThickness";
    this.lblLongitudinalStressBendingForceWallThickness.Size = new Size(276, 26);
    this.lblLongitudinalStressBendingForceWallThickness.TabIndex = 11;
    this.lblLongitudinalStressBendingForceWallThickness.Text = "Longitudinal stress due to external applied bending force,\r\nbased on the nominal wall thickness, σB";
    this.lblLongitudinalStressAxialForceWallThickness.Location = new Point(13, 101);
    this.lblLongitudinalStressAxialForceWallThickness.Name = "lblLongitudinalStressAxialForceWallThickness";
    this.lblLongitudinalStressAxialForceWallThickness.Size = new Size(260, 26);
    this.lblLongitudinalStressAxialForceWallThickness.TabIndex = 8;
    this.lblLongitudinalStressAxialForceWallThickness.Text = "Longitudinal stress due to external applied axial force,\r\nbased on the nominal wall thickness, σA";
    this.labelControl9.Location = new Point(13, 35);
    this.labelControl9.Name = "labelControl9";
    this.labelControl9.Size = new Size(64 /*0x40*/, 13);
    this.labelControl9.TabIndex = 2;
    this.labelControl9.Text = "Design factor";
    this.labelControl8.Location = new Point(13, 13);
    this.labelControl8.Name = "labelControl8";
    this.labelControl8.Size = new Size(76, 13);
    this.labelControl8.TabIndex = 0;
    this.labelControl8.Text = "Modelling factor";
    this.labelControl7.Location = new Point(13, 79);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(88, 13);
    this.labelControl7.TabIndex = 6;
    this.labelControl7.Text = "Total usage factor";
    this.labelControl6.Location = new Point(13, 57);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(131, 13);
    this.labelControl6.TabIndex = 4;
    this.labelControl6.Text = "Length correction factor, Q";
    this.lblFactorForCompressiveLongitudinalStresses.Location = new Point(13, 219);
    this.lblFactorForCompressiveLongitudinalStresses.Name = "lblFactorForCompressiveLongitudinalStresses";
    this.lblFactorForCompressiveLongitudinalStresses.Size = new Size(284, 13);
    this.lblFactorForCompressiveLongitudinalStresses.TabIndex = 20;
    this.lblFactorForCompressiveLongitudinalStresses.Text = "Factor to account for compressive longitudinal stresses, H1";
    this.txtFactorForCompressiveLongitudinalStresses.Location = new Point(311, 217);
    this.txtFactorForCompressiveLongitudinalStresses.Margin = new Padding(1);
    this.txtFactorForCompressiveLongitudinalStresses.Name = "txtFactorForCompressiveLongitudinalStresses";
    this.txtFactorForCompressiveLongitudinalStresses.Properties.ReadOnly = true;
    this.txtFactorForCompressiveLongitudinalStresses.Size = new Size(100, 20);
    this.txtFactorForCompressiveLongitudinalStresses.TabIndex = 21;
    this.txtLengthCorrectionFactor.Location = new Point(311, 55);
    this.txtLengthCorrectionFactor.Margin = new Padding(1);
    this.txtLengthCorrectionFactor.Name = "txtLengthCorrectionFactor";
    this.txtLengthCorrectionFactor.Properties.ReadOnly = true;
    this.txtLengthCorrectionFactor.Size = new Size(100, 20);
    this.txtLengthCorrectionFactor.TabIndex = 5;
    this.txtLowerLimitExternalLoads.Location = new Point(311, 195);
    this.txtLowerLimitExternalLoads.Margin = new Padding(1);
    this.txtLowerLimitExternalLoads.Name = "txtLowerLimitExternalLoads";
    this.txtLowerLimitExternalLoads.Properties.ReadOnly = true;
    this.txtLowerLimitExternalLoads.Size = new Size(100, 20);
    this.txtLowerLimitExternalLoads.TabIndex = 18;
    this.txtTotalUsageFactor.Location = new Point(311, 77);
    this.txtTotalUsageFactor.Margin = new Padding(1);
    this.txtTotalUsageFactor.Name = "txtTotalUsageFactor";
    this.txtTotalUsageFactor.Properties.ReadOnly = true;
    this.txtTotalUsageFactor.Size = new Size(100, 20);
    this.txtTotalUsageFactor.TabIndex = 7;
    this.txtCombinedLongitudinalStress.Location = new Point(311, 163);
    this.txtCombinedLongitudinalStress.Margin = new Padding(1);
    this.txtCombinedLongitudinalStress.Name = "txtCombinedLongitudinalStress";
    this.txtCombinedLongitudinalStress.Properties.ReadOnly = true;
    this.txtCombinedLongitudinalStress.Size = new Size(100, 20);
    this.txtCombinedLongitudinalStress.TabIndex = 15;
    this.txtLongitudinalStressBendingForceWallThickness.Location = new Point(311, 131);
    this.txtLongitudinalStressBendingForceWallThickness.Margin = new Padding(1);
    this.txtLongitudinalStressBendingForceWallThickness.Name = "txtLongitudinalStressBendingForceWallThickness";
    this.txtLongitudinalStressBendingForceWallThickness.Properties.ReadOnly = true;
    this.txtLongitudinalStressBendingForceWallThickness.Size = new Size(100, 20);
    this.txtLongitudinalStressBendingForceWallThickness.TabIndex = 12;
    this.txtLongitudinalStressAxialForceWallThickness.Location = new Point(311, 99);
    this.txtLongitudinalStressAxialForceWallThickness.Margin = new Padding(1);
    this.txtLongitudinalStressAxialForceWallThickness.Name = "txtLongitudinalStressAxialForceWallThickness";
    this.txtLongitudinalStressAxialForceWallThickness.Properties.ReadOnly = true;
    this.txtLongitudinalStressAxialForceWallThickness.Size = new Size(100, 20);
    this.txtLongitudinalStressAxialForceWallThickness.TabIndex = 9;
    this.txtModellingFactor.Location = new Point(311, 11);
    this.txtModellingFactor.Margin = new Padding(1);
    this.txtModellingFactor.Name = "txtModellingFactor";
    this.txtModellingFactor.Properties.ReadOnly = true;
    this.txtModellingFactor.Size = new Size(100, 20);
    this.txtModellingFactor.TabIndex = 1;
    this.labelControl4.Location = new Point(13, 13);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(192 /*0xC0*/, 13);
    this.labelControl4.TabIndex = 0;
    this.labelControl4.Text = "Failure pressure under internal pressure";
    this.groupControl3.AppearanceCaption.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.groupControl3.AppearanceCaption.Options.UseFont = true;
    this.groupControl3.AutoSize = true;
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel5);
    this.groupControl3.Dock = DockStyle.Top;
    this.groupControl3.Location = new Point(0, 424);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(647, 141);
    this.groupControl3.TabIndex = 2;
    this.groupControl3.Text = "Assessment Results";
    this.tableLayoutPanel5.AutoSize = true;
    this.tableLayoutPanel5.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel5.ColumnCount = 3;
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 300f));
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel5.Controls.Add((Control) this.txtFailurePressureIntPressureCompLongitudinalStress, 1, 1);
    this.tableLayoutPanel5.Controls.Add((Control) this.umFailurePressureIntPressureCompLongitudinalStress, 2, 1);
    this.tableLayoutPanel5.Controls.Add((Control) this.umFailurePressureOfCorrodedPipe, 2, 2);
    this.tableLayoutPanel5.Controls.Add((Control) this.umSafeWorkingPressure, 2, 3);
    this.tableLayoutPanel5.Controls.Add((Control) this.umFailurePressureUnderInternalPressure, 2, 0);
    this.tableLayoutPanel5.Controls.Add((Control) this.lblFailurePressureOfCorrodedPipe, 0, 2);
    this.tableLayoutPanel5.Controls.Add((Control) this.lblFailurePressureIntPressureCompLongitudinalStress, 0, 1);
    this.tableLayoutPanel5.Controls.Add((Control) this.labelControl4, 0, 0);
    this.tableLayoutPanel5.Controls.Add((Control) this.labelControl19, 0, 3);
    this.tableLayoutPanel5.Controls.Add((Control) this.txtFailurePressureUnderInternalPressure, 1, 0);
    this.tableLayoutPanel5.Controls.Add((Control) this.txtSafeWorkingPressure, 1, 3);
    this.tableLayoutPanel5.Controls.Add((Control) this.txtFailurePressureOfCorrodedPipe, 1, 2);
    this.tableLayoutPanel5.Dock = DockStyle.Fill;
    this.tableLayoutPanel5.Location = new Point(2, 21);
    this.tableLayoutPanel5.Name = "tableLayoutPanel5";
    this.tableLayoutPanel5.Padding = new Padding(10);
    this.tableLayoutPanel5.RowCount = 4;
    this.tableLayoutPanel5.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel5.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel5.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel5.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel5.Size = new Size(643, 118);
    this.tableLayoutPanel5.TabIndex = 0;
    this.txtFailurePressureIntPressureCompLongitudinalStress.Location = new Point(311, 33);
    this.txtFailurePressureIntPressureCompLongitudinalStress.Margin = new Padding(1);
    this.txtFailurePressureIntPressureCompLongitudinalStress.Name = "txtFailurePressureIntPressureCompLongitudinalStress";
    this.txtFailurePressureIntPressureCompLongitudinalStress.Properties.ReadOnly = true;
    this.txtFailurePressureIntPressureCompLongitudinalStress.Size = new Size(100, 20);
    this.txtFailurePressureIntPressureCompLongitudinalStress.TabIndex = 4;
    this.umFailurePressureIntPressureCompLongitudinalStress.Location = new Point(415, 35);
    this.umFailurePressureIntPressureCompLongitudinalStress.Name = "umFailurePressureIntPressureCompLongitudinalStress";
    this.umFailurePressureIntPressureCompLongitudinalStress.Size = new Size(41, 13);
    this.umFailurePressureIntPressureCompLongitudinalStress.TabIndex = 5;
    this.umFailurePressureIntPressureCompLongitudinalStress.Text = "measure";
    this.umFailurePressureOfCorrodedPipe.Location = new Point(415, 67);
    this.umFailurePressureOfCorrodedPipe.Name = "umFailurePressureOfCorrodedPipe";
    this.umFailurePressureOfCorrodedPipe.Size = new Size(41, 13);
    this.umFailurePressureOfCorrodedPipe.TabIndex = 8;
    this.umFailurePressureOfCorrodedPipe.Text = "measure";
    this.umSafeWorkingPressure.Location = new Point(415, 89);
    this.umSafeWorkingPressure.Name = "umSafeWorkingPressure";
    this.umSafeWorkingPressure.Size = new Size(41, 13);
    this.umSafeWorkingPressure.TabIndex = 11;
    this.umSafeWorkingPressure.Text = "measure";
    this.umFailurePressureUnderInternalPressure.Location = new Point(415, 13);
    this.umFailurePressureUnderInternalPressure.Name = "umFailurePressureUnderInternalPressure";
    this.umFailurePressureUnderInternalPressure.Size = new Size(41, 13);
    this.umFailurePressureUnderInternalPressure.TabIndex = 2;
    this.umFailurePressureUnderInternalPressure.Text = "measure";
    this.lblFailurePressureOfCorrodedPipe.Location = new Point(13, 67);
    this.lblFailurePressureOfCorrodedPipe.Name = "lblFailurePressureOfCorrodedPipe";
    this.lblFailurePressureOfCorrodedPipe.Size = new Size(180, 13);
    this.lblFailurePressureOfCorrodedPipe.TabIndex = 6;
    this.lblFailurePressureOfCorrodedPipe.Text = "Failure pressure of corroded pipe, P f";
    this.lblFailurePressureIntPressureCompLongitudinalStress.Location = new Point(13, 35);
    this.lblFailurePressureIntPressureCompLongitudinalStress.Name = "lblFailurePressureIntPressureCompLongitudinalStress";
    this.lblFailurePressureIntPressureCompLongitudinalStress.Size = new Size(213, 26);
    this.lblFailurePressureIntPressureCompLongitudinalStress.TabIndex = 3;
    this.lblFailurePressureIntPressureCompLongitudinalStress.Text = "Failure pressure under internal pressure and\r\ncompressive longitudinal stresses, P comp";
    this.labelControl19.Location = new Point(13, 89);
    this.labelControl19.Name = "labelControl19";
    this.labelControl19.Size = new Size(136, 13);
    this.labelControl19.TabIndex = 9;
    this.labelControl19.Text = "Safe working pressure, P sw";
    this.txtFailurePressureUnderInternalPressure.Location = new Point(311, 11);
    this.txtFailurePressureUnderInternalPressure.Margin = new Padding(1);
    this.txtFailurePressureUnderInternalPressure.Name = "txtFailurePressureUnderInternalPressure";
    this.txtFailurePressureUnderInternalPressure.Properties.ReadOnly = true;
    this.txtFailurePressureUnderInternalPressure.Size = new Size(100, 20);
    this.txtFailurePressureUnderInternalPressure.TabIndex = 1;
    this.txtSafeWorkingPressure.Location = new Point(311, 87);
    this.txtSafeWorkingPressure.Margin = new Padding(1);
    this.txtSafeWorkingPressure.Name = "txtSafeWorkingPressure";
    this.txtSafeWorkingPressure.Properties.ReadOnly = true;
    this.txtSafeWorkingPressure.Size = new Size(100, 20);
    this.txtSafeWorkingPressure.TabIndex = 10;
    this.txtFailurePressureOfCorrodedPipe.Location = new Point(311, 65);
    this.txtFailurePressureOfCorrodedPipe.Margin = new Padding(1);
    this.txtFailurePressureOfCorrodedPipe.Name = "txtFailurePressureOfCorrodedPipe";
    this.txtFailurePressureOfCorrodedPipe.Properties.ReadOnly = true;
    this.txtFailurePressureOfCorrodedPipe.Size = new Size(100, 20);
    this.txtFailurePressureOfCorrodedPipe.TabIndex = 7;
    this.groupControl4.AppearanceCaption.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.groupControl4.AppearanceCaption.Options.UseFont = true;
    this.groupControl4.AutoSize = true;
    this.groupControl4.Controls.Add((Control) this.tableLayoutPanel7);
    this.groupControl4.Dock = DockStyle.Top;
    this.groupControl4.Location = new Point(0, 565);
    this.groupControl4.Name = "groupControl4";
    this.groupControl4.Size = new Size(647, 62);
    this.groupControl4.TabIndex = 3;
    this.groupControl4.Text = "Assessment Conclusion";
    this.tableLayoutPanel7.AutoSize = true;
    this.tableLayoutPanel7.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel7.ColumnCount = 1;
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 623f));
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel7.Controls.Add((Control) this.lblConclusion, 0, 0);
    this.tableLayoutPanel7.Dock = DockStyle.Fill;
    this.tableLayoutPanel7.Location = new Point(2, 21);
    this.tableLayoutPanel7.Name = "tableLayoutPanel7";
    this.tableLayoutPanel7.Padding = new Padding(10);
    this.tableLayoutPanel7.RowCount = 1;
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel7.Size = new Size(643, 39);
    this.tableLayoutPanel7.TabIndex = 0;
    this.lblConclusion.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.lblConclusion.Dock = DockStyle.Fill;
    this.lblConclusion.Location = new Point(13, 13);
    this.lblConclusion.Name = "lblConclusion";
    this.lblConclusion.Size = new Size(60, 13);
    this.lblConclusion.TabIndex = 0;
    this.lblConclusion.Text = "Conclusion";
    this.groupControl5.AppearanceCaption.Font = new Font("Tahoma", 8.25f, FontStyle.Bold, GraphicsUnit.Point, (byte) 0);
    this.groupControl5.AppearanceCaption.Options.UseFont = true;
    this.groupControl5.AutoSize = true;
    this.groupControl5.Controls.Add((Control) this.tableLayoutPanel9);
    this.groupControl5.Dock = DockStyle.Top;
    this.groupControl5.Location = new Point(0, 627);
    this.groupControl5.Name = "groupControl5";
    this.groupControl5.Size = new Size(647, 100);
    this.groupControl5.TabIndex = 4;
    this.groupControl5.Text = "Warning Messages";
    this.tableLayoutPanel9.AutoSize = true;
    this.tableLayoutPanel9.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel9.ColumnCount = 1;
    this.tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel9.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel9.Dock = DockStyle.Fill;
    this.tableLayoutPanel9.Location = new Point(2, 21);
    this.tableLayoutPanel9.Name = "tableLayoutPanel9";
    this.tableLayoutPanel9.Padding = new Padding(10);
    this.tableLayoutPanel9.RowCount = 1;
    this.tableLayoutPanel9.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel9.Size = new Size(643, 77);
    this.tableLayoutPanel9.TabIndex = 0;
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(617, 51);
    this.txtWarningMessages.TabIndex = 0;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.groupControl5);
    this.Controls.Add((Control) this.groupControl4);
    this.Controls.Add((Control) this.groupControl3);
    this.Controls.Add((Control) this.groupControl2);
    this.Controls.Add((Control) this.groupControl1);
    this.Name = nameof (AbstractResultView);
    this.Size = new Size(647, 767 /*0x02FF*/);
    this.groupControl2.EndInit();
    this.groupControl2.ResumeLayout(false);
    this.groupControl2.PerformLayout();
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.txtCircAngularSpacingBetweenAdjacentDefectsCriteria.Properties.EndInit();
    this.txtDefectsInteractionCriteria.Properties.EndInit();
    this.txtAxialSpacingBetweenAdjacentDefectsCriteria.Properties.EndInit();
    this.txtDefectDepthCriteria.Properties.EndInit();
    this.txtExternalLoadWithinLimit.Properties.EndInit();
    this.txtDesignFactor.Properties.EndInit();
    this.groupControl1.EndInit();
    this.groupControl1.ResumeLayout(false);
    this.groupControl1.PerformLayout();
    this.tableLayoutPanel3.ResumeLayout(false);
    this.tableLayoutPanel3.PerformLayout();
    this.txtFactorForCompressiveLongitudinalStresses.Properties.EndInit();
    this.txtLengthCorrectionFactor.Properties.EndInit();
    this.txtLowerLimitExternalLoads.Properties.EndInit();
    this.txtTotalUsageFactor.Properties.EndInit();
    this.txtCombinedLongitudinalStress.Properties.EndInit();
    this.txtLongitudinalStressBendingForceWallThickness.Properties.EndInit();
    this.txtLongitudinalStressAxialForceWallThickness.Properties.EndInit();
    this.txtModellingFactor.Properties.EndInit();
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.groupControl3.PerformLayout();
    this.tableLayoutPanel5.ResumeLayout(false);
    this.tableLayoutPanel5.PerformLayout();
    this.txtFailurePressureIntPressureCompLongitudinalStress.Properties.EndInit();
    this.txtFailurePressureUnderInternalPressure.Properties.EndInit();
    this.txtSafeWorkingPressure.Properties.EndInit();
    this.txtFailurePressureOfCorrodedPipe.Properties.EndInit();
    this.groupControl4.EndInit();
    this.groupControl4.ResumeLayout(false);
    this.groupControl4.PerformLayout();
    this.tableLayoutPanel7.ResumeLayout(false);
    this.tableLayoutPanel7.PerformLayout();
    this.groupControl5.EndInit();
    this.groupControl5.ResumeLayout(false);
    this.groupControl5.PerformLayout();
    this.tableLayoutPanel9.ResumeLayout(false);
    this.txtWarningMessages.Properties.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public AbstractResultView() => this.InitializeComponent();

  public bool CircAngularSpacingBetweenAdjacentDefectsCriteria
  {
    get => this._circAngularSpacingBetweenAdjacentDefectsCriteria;
    set
    {
      this.txtCircAngularSpacingBetweenAdjacentDefectsCriteria.Text = value ? "True" : "False";
      this._circAngularSpacingBetweenAdjacentDefectsCriteria = value;
    }
  }

  public bool AxialSpacingBetweenAdjacentDefectsCriteria
  {
    get => this._axialSpacingBetweenAdjacentDefectsCriteria;
    set
    {
      this.txtAxialSpacingBetweenAdjacentDefectsCriteria.Text = value ? "True" : "False";
      this._axialSpacingBetweenAdjacentDefectsCriteria = value;
    }
  }

  public bool DefectsInteractionCriteria
  {
    get => this._defectsInteractionCriteria;
    set
    {
      this.txtDefectsInteractionCriteria.Text = value ? "True" : "False";
      this._defectsInteractionCriteria = value;
    }
  }

  public bool DefectDepthCriteria
  {
    get => this._defectDepthCriteria;
    set
    {
      this.txtDefectDepthCriteria.Text = value ? "True" : "False";
      this._defectDepthCriteria = value;
    }
  }

  public double ModellingFactor
  {
    set => this.txtModellingFactor.Text = Helpers.ParseObjectToString((object) value);
  }

  public double DesignFactor
  {
    get => this._designFactor;
    set
    {
      this.txtDesignFactor.Text = Helpers.ParseObjectToString((object) value);
      this._designFactor = value;
    }
  }

  public double LengthCorrectionFactor
  {
    get => this._lengthCorrectionFactor;
    set
    {
      this.txtLengthCorrectionFactor.Text = Helpers.ParseObjectToString((object) value);
      this._lengthCorrectionFactor = value;
    }
  }

  public double TotalUsageFactor
  {
    get => this._totalUsageFactor;
    set
    {
      this.txtTotalUsageFactor.Text = Helpers.ParseObjectToString((object) value);
      this._totalUsageFactor = value;
    }
  }

  public double LongitudinalStressAxialForceWallThickness
  {
    get => this._longitudinalStressAxialForceWallThickness;
    set
    {
      this.txtLongitudinalStressAxialForceWallThickness.Text = Helpers.ParseObjectToString((object) value);
      this._longitudinalStressAxialForceWallThickness = value;
    }
  }

  public double LongitudinalStressBendingForceWallThickness
  {
    get => this._longitudinalStressBendingForceWallThickness;
    set
    {
      this.txtLongitudinalStressBendingForceWallThickness.Text = Helpers.ParseObjectToString((object) value);
      this._longitudinalStressBendingForceWallThickness = value;
    }
  }

  public double CombinedLongitudinalStress
  {
    get => this._combinedLongitudinalStress;
    set
    {
      this.txtCombinedLongitudinalStress.Text = Helpers.ParseObjectToString((object) value);
      this._combinedLongitudinalStress = value;
    }
  }

  public double LowerLimitExternalLoads
  {
    get => this._lowerLimitExternalLoads;
    set
    {
      this.txtLowerLimitExternalLoads.Text = Helpers.ParseObjectToString((object) value);
      this._lowerLimitExternalLoads = value;
    }
  }

  public bool ExternalLoadWithinLimit
  {
    get => this._externalLoadWithinLimit;
    set
    {
      this.txtExternalLoadWithinLimit.Text = value ? "True" : "False";
      this._externalLoadWithinLimit = value;
    }
  }

  public double FactorForCompressiveLongitudinalStresses
  {
    get => this._factorForCompressiveLongitudinalStresses;
    set
    {
      this.txtFactorForCompressiveLongitudinalStresses.Text = Helpers.ParseObjectToString((object) value);
      this._factorForCompressiveLongitudinalStresses = value;
    }
  }

  public double FailurePressureUnderInternalPressure
  {
    get => this._failurePressureUnderInternalPressure;
    set
    {
      this.txtFailurePressureUnderInternalPressure.Text = Helpers.ParseObjectToString((object) value);
      this._failurePressureUnderInternalPressure = value;
    }
  }

  public double FailurePressureIntPressureCompLongitudinalStress
  {
    get => this._failurePressureIntPressureCompLongitudinalStress;
    set
    {
      this.txtFailurePressureIntPressureCompLongitudinalStress.Text = Helpers.ParseObjectToString((object) value);
      this._failurePressureIntPressureCompLongitudinalStress = value;
    }
  }

  public double FailurePressureOfCorrodedPipe
  {
    get => this._failurePressureOfCorrodedPipe;
    set
    {
      this.txtFailurePressureOfCorrodedPipe.Text = Helpers.ParseObjectToString((object) value);
      this._failurePressureOfCorrodedPipe = value;
    }
  }

  public double SafeWorkingPressure
  {
    get => this._safeWorkingPressure;
    set
    {
      this.txtSafeWorkingPressure.Text = Helpers.ParseObjectToString((object) value);
      this._safeWorkingPressure = value;
    }
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show((IWin32Window) this, message);
  }

  public string ResultMessages
  {
    get => this._resultMessages;
    set
    {
      this._resultMessages = value;
      this.txtWarningMessages.Text = value;
    }
  }

  public string UM_LongitudinalStressAxialForceWallThickness
  {
    set => this.umLongitudinalStressAxialForceWallThickness.Text = value;
  }

  public string UM_LongitudinalStressBendingForceWallThickness
  {
    set => this.umLongitudinalStressBendingForceWallThickness.Text = value;
  }

  public string UM_CombinedLongitudinalStress
  {
    set => this.umCombinedLongitudinalStress.Text = value;
  }

  public string UM_LowerLimitExternalLoads
  {
    set => this.umLowerLimitExternalLoads.Text = value;
  }

  public string UM_FailurePressureUnderInternalPressure
  {
    set => this.umFailurePressureUnderInternalPressure.Text = value;
  }

  public string UM_FailurePressureIntPressureCompLongitudinalStress
  {
    set => this.umFailurePressureIntPressureCompLongitudinalStress.Text = value;
  }

  public string UM_FailurePressureOfCorrodedPipe
  {
    set => this.umFailurePressureOfCorrodedPipe.Text = value;
  }

  public string UM_SafeWorkingPressure
  {
    set => this.umSafeWorkingPressure.Text = value;
  }

  public bool EnableCombinedLoads
  {
    set
    {
      this.umLongitudinalStressAxialForceWallThickness.Visible = value;
      this.umLongitudinalStressBendingForceWallThickness.Visible = value;
      this.umCombinedLongitudinalStress.Visible = value;
      this.umLowerLimitExternalLoads.Visible = value;
      this.umFailurePressureIntPressureCompLongitudinalStress.Visible = value;
      this.umFailurePressureOfCorrodedPipe.Visible = value;
      this.txtLongitudinalStressAxialForceWallThickness.Visible = value;
      this.txtLongitudinalStressBendingForceWallThickness.Visible = value;
      this.txtCombinedLongitudinalStress.Visible = value;
      this.txtLowerLimitExternalLoads.Visible = value;
      this.txtFailurePressureIntPressureCompLongitudinalStress.Visible = value;
      this.txtFailurePressureOfCorrodedPipe.Visible = value;
      this.txtExternalLoadWithinLimit.Visible = value;
      this.txtFactorForCompressiveLongitudinalStresses.Visible = value;
      this.lblLongitudinalStressAxialForceWallThickness.Visible = value;
      this.lblLongitudinalStressBendingForceWallThickness.Visible = value;
      this.lblCombinedLongitudinalStress.Visible = value;
      this.lblLowerLimitExternalLoads.Visible = value;
      this.lblFailurePressureIntPressureCompLongitudinalStress.Visible = value;
      this.lblFailurePressureOfCorrodedPipe.Visible = value;
      this.lblExternalLoadWithinLimit.Visible = value;
      this.lblFactorForCompressiveLongitudinalStresses.Visible = value;
    }
  }

  public bool EnableAdjacentDefect
  {
    set
    {
      this.txtCircAngularSpacingBetweenAdjacentDefectsCriteria.Visible = value;
      this.lblCircAngularSpacingBetweenAdjacentDefectsCriteria.Visible = value;
      this.txtAxialSpacingBetweenAdjacentDefectsCriteria.Visible = value;
      this.lblAxialSpacingBetweenAdjacentDefectsCriteria.Visible = value;
      this.txtDefectsInteractionCriteria.Visible = value;
      this.lblDefectsInteractionCriteria.Visible = value;
    }
  }

  public string Conclusion
  {
    set => this.lblConclusion.Text = value;
  }

  public bool Level1Passed
  {
    get => this._level1Passed;
    set
    {
      this._level1Passed = value;
      this.lblConclusion.Appearance.ForeColor = this._level1Passed ? Color.Green : Color.Red;
    }
  }
}
