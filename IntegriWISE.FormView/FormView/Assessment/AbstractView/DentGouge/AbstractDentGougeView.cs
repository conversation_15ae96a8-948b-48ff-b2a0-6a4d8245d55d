// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.AbstractView.DentGouge.AbstractDentGougeView
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Assessment.AbstractUserInterface.DentGouge;
using IntegriWISE.UserInterface.Assessment.DesignCode.ASMEB31_4.DentGouge;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.AbstractView.DentGouge;

public abstract class AbstractDentGougeView : 
  XtraUserControl,
  IDentGougeView,
  IAssessmentBaseView,
  IView
{
  private DentGougePresenter _presenter;
  protected IRecordView _recordView;
  private FormDirtyTracker _dirtyTracker;
  private IContainer components;
  private TableLayoutPanel tableLayoutPanel1;
  private LabelControl umLOSSi;
  private LabelControl lblFCAe;
  private LabelControl lblLOSSi;
  private LabelControl umFCAe;
  private LabelControl lblLOSSe;
  private TextEdit txtFCAe;
  private TextEdit txtLOSSi;
  private TextEdit txtLOSSe;
  private LabelControl umLOSSe;
  private CheckEdit chkLevel2;
  private LabelControl lblDentDepthInPressurisedCondition;
  private LabelControl lblDentDepthInUnpressurisedCondition;
  private LabelControl lblSpacingToDiscontinuity;
  private LabelControl lblSpacingToWeldJoint;
  private LabelControl lblRadiusBaseDent;
  private LabelControl lblCyclicLoading;
  private CheckEdit chkCyclicLoading;
  private LabelControl lblMaxPressureCyclic;
  private LabelControl lblMinPressureCyclic;
  private LabelControl lblTotalDesignPressureCycles;
  private LabelControl lblFCAi;
  private TextEdit txtFCAi;
  private LabelControl umFCAi;
  private TextEdit txtDentDepthInPressurisedCondition;
  private TextEdit txtDentDepthInUnpressurisedCondition;
  private TextEdit txtRadiusBaseDent;
  private TextEdit txtSpacingToWeldJoint;
  private TextEdit txtSpacingToDiscontinuity;
  private TextEdit txtMaxPressureCyclic;
  private TextEdit txtMinPressureCyclic;
  private TextEdit txtTotalDesignPressureCycles;
  private LabelControl umDentDepthInPressurisedCondition;
  private LabelControl umDentDepthInUnpressurisedCondition;
  private LabelControl umRadiusBaseDent;
  private LabelControl umSpacingToWeldJoint;
  private LabelControl umSpacingToDiscontinuity;
  private LabelControl umMaxPressureCyclic;
  private LabelControl umMinPressureCyclic;
  private LabelControl lblDepthGougeInCorrodedCondition;
  private TextEdit txtDepthGougeInCorrodedCondition;
  private LabelControl umDepthGougeInCorrodedCondition;
  private LabelControl lblModulusElasticity;
  private LabelControl lblCharpyVNotch;
  private LabelControl umCharpyVNotch;
  private LabelControl umModulusElasticity;
  private TextEdit txtCharpyVNotch;
  private TextEdit txtModulusElasticity;
  private CheckEdit chkDentDepthMeasuredInPressurised;
  private LabelControl lblDentDepthMeasuredInPressurised;

  public AbstractDentGougeView(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.AssessmentView = (IAssessmentBaseView) this;
    this._presenter = new DentGougePresenter(this._recordView, (IDentGougeView) this);
  }

  private void AbstractDentGougeView_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView);
    this._dirtyTracker.IsHandled = true;
    this.txtModulusElasticity.AllowOnlyN4();
    this.txtCharpyVNotch.AllowOnlyN4();
    this.txtDepthGougeInCorrodedCondition.AllowOnlyN4();
    this.txtDentDepthInPressurisedCondition.AllowOnlyN4();
    this.txtDentDepthInUnpressurisedCondition.AllowOnlyN4();
    this.txtRadiusBaseDent.AllowOnlyN4();
    this.txtSpacingToWeldJoint.AllowOnlyN4();
    this.txtSpacingToDiscontinuity.AllowOnlyN4();
    this.txtFCAi.AllowOnlyN4();
    this.txtFCAe.AllowOnlyN4();
    this.txtLOSSi.AllowOnlyN4();
    this.txtLOSSe.AllowOnlyN4();
    this.txtMaxPressureCyclic.AllowOnlyN4();
    this.txtMinPressureCyclic.AllowOnlyN4();
    this.txtTotalDesignPressureCycles.AllowOnlyInt();
    this._presenter.LoadAssessment();
    this._dirtyTracker.IsHandled = false;
  }

  private void chkLevel2_CheckedChanged(object sender, EventArgs e)
  {
    this._presenter.EnableLevel2Controls();
  }

  private void chkCyclicLoading_CheckedChanged(object sender, EventArgs e)
  {
    this._presenter.EnableCyclicLoadingControls();
  }

  private void chkDentDepthMeasuredInPressurised_CheckedChanged(object sender, EventArgs e)
  {
    this._presenter.EnableDentDepthMeasureInPressuriedControls();
  }

  public int? AssessmentID { get; set; }

  public bool Level2
  {
    get => this.chkLevel2.Checked;
    set => this.chkLevel2.Checked = value;
  }

  public double? ModulusElasticity
  {
    get => Helpers.ParseNullDouble((object) this.txtModulusElasticity.Text);
    set => this.txtModulusElasticity.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? CharpyVNotch
  {
    get => Helpers.ParseNullDouble((object) this.txtCharpyVNotch.Text);
    set => this.txtCharpyVNotch.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? DepthGougeInCorrodedCondition
  {
    get => Helpers.ParseNullDouble((object) this.txtDepthGougeInCorrodedCondition.Text);
    set => this.txtDepthGougeInCorrodedCondition.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool DentDepthMeasuredInPressurisedCondition
  {
    get => this.chkDentDepthMeasuredInPressurised.Checked;
    set => this.chkDentDepthMeasuredInPressurised.Checked = value;
  }

  public double? DentDepthInPressurisedCondition
  {
    get => Helpers.ParseNullDouble((object) this.txtDentDepthInPressurisedCondition.Text);
    set
    {
      this.txtDentDepthInPressurisedCondition.Text = Helpers.ParseObjectToString((object) value);
    }
  }

  public double? DentDepthInUnpressurisedCondition
  {
    get => Helpers.ParseNullDouble((object) this.txtDentDepthInUnpressurisedCondition.Text);
    set
    {
      this.txtDentDepthInUnpressurisedCondition.Text = Helpers.ParseObjectToString((object) value);
    }
  }

  public double? RadiusBaseDent
  {
    get => Helpers.ParseNullDouble((object) this.txtRadiusBaseDent.Text);
    set => this.txtRadiusBaseDent.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? SpacingToWeldJoint
  {
    get => Helpers.ParseNullDouble((object) this.txtSpacingToWeldJoint.Text);
    set => this.txtSpacingToWeldJoint.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? SpacingToDiscontinuity
  {
    get => Helpers.ParseNullDouble((object) this.txtSpacingToDiscontinuity.Text);
    set => this.txtSpacingToDiscontinuity.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool CyclicLoading
  {
    get => this.chkCyclicLoading.Checked;
    set => this.chkCyclicLoading.Checked = value;
  }

  public double? MaxPressureCyclic
  {
    get => Helpers.ParseNullDouble((object) this.txtMaxPressureCyclic.Text);
    set => this.txtMaxPressureCyclic.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? MinPressureCyclic
  {
    get => Helpers.ParseNullDouble((object) this.txtMinPressureCyclic.Text);
    set => this.txtMinPressureCyclic.Text = Helpers.ParseObjectToString((object) value);
  }

  public int? TotalDesignPressureCycles
  {
    get => Helpers.ParseNullInt32((object) this.txtTotalDesignPressureCycles.Text);
    set => this.txtTotalDesignPressureCycles.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? FCAi
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAi.Text);
    set => this.txtFCAi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? FCAe
  {
    get => Helpers.ParseNullDouble((object) this.txtFCAe.Text);
    set => this.txtFCAe.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSi
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSi.Text);
    set => this.txtLOSSi.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LOSSe
  {
    get => Helpers.ParseNullDouble((object) this.txtLOSSe.Text);
    set => this.txtLOSSe.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMModulusElasticity
  {
    set => this.umModulusElasticity.Text = value;
  }

  public string UMDepthGougeInCorrodedCondition
  {
    set => this.umDepthGougeInCorrodedCondition.Text = value;
  }

  public string UMDentDepthInPressurisedCondition
  {
    set => this.umDentDepthInPressurisedCondition.Text = value;
  }

  public string UMDentDepthInUnpressurisedCondition
  {
    set => this.umDentDepthInUnpressurisedCondition.Text = value;
  }

  public string UMRadiusBaseDent
  {
    set => this.umRadiusBaseDent.Text = value;
  }

  public string UMSpacingToWeldJoint
  {
    set => this.umSpacingToWeldJoint.Text = value;
  }

  public string UMSpacingToDiscontinuity
  {
    set => this.umSpacingToDiscontinuity.Text = value;
  }

  public string UMMaxPressureCyclic
  {
    set => this.umMaxPressureCyclic.Text = value;
  }

  public string UMMinPressureCyclic
  {
    set => this.umMinPressureCyclic.Text = value;
  }

  public string UMFCAi
  {
    set => this.umFCAi.Text = value;
  }

  public string UMFCAe
  {
    set => this.umFCAe.Text = value;
  }

  public string UMLOSSi
  {
    set => this.umLOSSi.Text = value;
  }

  public string UMLOSSe
  {
    set => this.umLOSSe.Text = value;
  }

  public string ModulusElasticityInfo
  {
    set => this.txtModulusElasticity.ToolTip = value;
  }

  public string CharpyVNotchInfo
  {
    set => this.txtCharpyVNotch.ToolTip = value;
  }

  public string DepthGougeInCorrodedConditionInfo
  {
    set => this.txtDepthGougeInCorrodedCondition.ToolTip = value;
  }

  public string DentDepthInPressurisedConditionInfo
  {
    set => this.txtDentDepthInPressurisedCondition.ToolTip = value;
  }

  public string DentDepthInUnpressurisedConditionInfo
  {
    set => this.txtDentDepthInUnpressurisedCondition.ToolTip = value;
  }

  public string RadiusBaseDentInfo
  {
    set => this.txtRadiusBaseDent.ToolTip = value;
  }

  public string SpacingToWeldJointInfo
  {
    set => this.txtSpacingToWeldJoint.ToolTip = value;
  }

  public string SpacingToDiscontinuityInfo
  {
    set => this.txtSpacingToDiscontinuity.ToolTip = value;
  }

  public string CyclicLoadingInfo
  {
    set => this.chkCyclicLoading.ToolTip = value;
  }

  public string MaxPressureCyclicInfo
  {
    set => this.txtMaxPressureCyclic.ToolTip = value;
  }

  public string MinPressureCyclicInfo
  {
    set => this.txtMinPressureCyclic.ToolTip = value;
  }

  public string TotalDesignPressureCyclesInfo
  {
    set => this.txtTotalDesignPressureCycles.ToolTip = value;
  }

  public string FCAiInfo
  {
    set => this.txtFCAi.ToolTip = value;
  }

  public string FCAeInfo
  {
    set => this.txtFCAe.ToolTip = value;
  }

  public string LOSSiInfo
  {
    set => this.txtLOSSi.ToolTip = value;
  }

  public string LOSSeInfo
  {
    set => this.txtLOSSe.ToolTip = value;
  }

  public bool EnableLevel2Controls
  {
    set
    {
      this.txtModulusElasticity.Visible = value;
      this.lblModulusElasticity.Visible = value;
      this.umModulusElasticity.Visible = value;
      this.txtCharpyVNotch.Visible = value;
      this.lblCharpyVNotch.Visible = value;
      this.umCharpyVNotch.Visible = value;
      this.txtRadiusBaseDent.Visible = value;
      this.lblRadiusBaseDent.Visible = value;
      this.umRadiusBaseDent.Visible = value;
      this.chkCyclicLoading.Visible = value;
      this.lblCyclicLoading.Visible = value;
      this.txtMaxPressureCyclic.Visible = this.chkCyclicLoading.Visible && this.chkCyclicLoading.Checked;
      this.lblMaxPressureCyclic.Visible = this.chkCyclicLoading.Visible && this.chkCyclicLoading.Checked;
      this.umMaxPressureCyclic.Visible = this.chkCyclicLoading.Visible && this.chkCyclicLoading.Checked;
      this.txtMinPressureCyclic.Visible = this.chkCyclicLoading.Visible && this.chkCyclicLoading.Checked;
      this.lblMinPressureCyclic.Visible = this.chkCyclicLoading.Visible && this.chkCyclicLoading.Checked;
      this.umMinPressureCyclic.Visible = this.chkCyclicLoading.Visible && this.chkCyclicLoading.Checked;
      this.txtTotalDesignPressureCycles.Visible = this.chkCyclicLoading.Visible && this.chkCyclicLoading.Checked;
      this.lblTotalDesignPressureCycles.Visible = this.chkCyclicLoading.Visible && this.chkCyclicLoading.Checked;
    }
  }

  public bool EnableCyclicLoadingControls
  {
    set
    {
      this.txtMaxPressureCyclic.Visible = value;
      this.lblMaxPressureCyclic.Visible = value;
      this.umMaxPressureCyclic.Visible = value;
      this.txtMinPressureCyclic.Visible = value;
      this.lblMinPressureCyclic.Visible = value;
      this.umMinPressureCyclic.Visible = value;
      this.txtTotalDesignPressureCycles.Visible = value;
      this.lblTotalDesignPressureCycles.Visible = value;
    }
  }

  public bool EnableDentDepthMeasureInPressuriedControls
  {
    set
    {
      this.lblDentDepthInPressurisedCondition.Visible = value;
      this.txtDentDepthInPressurisedCondition.Visible = value;
      this.umDentDepthInPressurisedCondition.Visible = value;
    }
  }

  public bool ValidateAssessment() => this._presenter.Validate();

  public void Save() => this._presenter.Save();

  public void ClearErrors()
  {
  }

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.chkLevel2 = new CheckEdit();
    this.lblDepthGougeInCorrodedCondition = new LabelControl();
    this.umDepthGougeInCorrodedCondition = new LabelControl();
    this.lblModulusElasticity = new LabelControl();
    this.lblCharpyVNotch = new LabelControl();
    this.umCharpyVNotch = new LabelControl();
    this.umModulusElasticity = new LabelControl();
    this.txtCharpyVNotch = new TextEdit();
    this.txtModulusElasticity = new TextEdit();
    this.lblTotalDesignPressureCycles = new LabelControl();
    this.lblMinPressureCyclic = new LabelControl();
    this.lblMaxPressureCyclic = new LabelControl();
    this.lblCyclicLoading = new LabelControl();
    this.lblLOSSe = new LabelControl();
    this.lblLOSSi = new LabelControl();
    this.lblFCAe = new LabelControl();
    this.lblFCAi = new LabelControl();
    this.lblSpacingToDiscontinuity = new LabelControl();
    this.lblSpacingToWeldJoint = new LabelControl();
    this.lblRadiusBaseDent = new LabelControl();
    this.lblDentDepthInUnpressurisedCondition = new LabelControl();
    this.lblDentDepthInPressurisedCondition = new LabelControl();
    this.txtTotalDesignPressureCycles = new TextEdit();
    this.txtMinPressureCyclic = new TextEdit();
    this.txtMaxPressureCyclic = new TextEdit();
    this.chkCyclicLoading = new CheckEdit();
    this.txtLOSSe = new TextEdit();
    this.txtLOSSi = new TextEdit();
    this.txtFCAe = new TextEdit();
    this.txtFCAi = new TextEdit();
    this.txtSpacingToDiscontinuity = new TextEdit();
    this.txtSpacingToWeldJoint = new TextEdit();
    this.txtRadiusBaseDent = new TextEdit();
    this.txtDentDepthInUnpressurisedCondition = new TextEdit();
    this.txtDentDepthInPressurisedCondition = new TextEdit();
    this.txtDepthGougeInCorrodedCondition = new TextEdit();
    this.chkDentDepthMeasuredInPressurised = new CheckEdit();
    this.lblDentDepthMeasuredInPressurised = new LabelControl();
    this.umMinPressureCyclic = new LabelControl();
    this.umMaxPressureCyclic = new LabelControl();
    this.umLOSSe = new LabelControl();
    this.umLOSSi = new LabelControl();
    this.umFCAe = new LabelControl();
    this.umFCAi = new LabelControl();
    this.umSpacingToDiscontinuity = new LabelControl();
    this.umSpacingToWeldJoint = new LabelControl();
    this.umRadiusBaseDent = new LabelControl();
    this.umDentDepthInUnpressurisedCondition = new LabelControl();
    this.umDentDepthInPressurisedCondition = new LabelControl();
    this.tableLayoutPanel1.SuspendLayout();
    this.chkLevel2.Properties.BeginInit();
    this.txtCharpyVNotch.Properties.BeginInit();
    this.txtModulusElasticity.Properties.BeginInit();
    this.txtTotalDesignPressureCycles.Properties.BeginInit();
    this.txtMinPressureCyclic.Properties.BeginInit();
    this.txtMaxPressureCyclic.Properties.BeginInit();
    this.chkCyclicLoading.Properties.BeginInit();
    this.txtLOSSe.Properties.BeginInit();
    this.txtLOSSi.Properties.BeginInit();
    this.txtFCAe.Properties.BeginInit();
    this.txtFCAi.Properties.BeginInit();
    this.txtSpacingToDiscontinuity.Properties.BeginInit();
    this.txtSpacingToWeldJoint.Properties.BeginInit();
    this.txtRadiusBaseDent.Properties.BeginInit();
    this.txtDentDepthInUnpressurisedCondition.Properties.BeginInit();
    this.txtDentDepthInPressurisedCondition.Properties.BeginInit();
    this.txtDepthGougeInCorrodedCondition.Properties.BeginInit();
    this.chkDentDepthMeasuredInPressurised.Properties.BeginInit();
    this.SuspendLayout();
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.chkLevel2, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblDepthGougeInCorrodedCondition, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.umDepthGougeInCorrodedCondition, 2, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblModulusElasticity, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblCharpyVNotch, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.umCharpyVNotch, 2, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.umModulusElasticity, 2, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtCharpyVNotch, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtModulusElasticity, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblTotalDesignPressureCycles, 0, 17);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblMinPressureCyclic, 0, 16 /*0x10*/);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblMaxPressureCyclic, 0, 15);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblCyclicLoading, 0, 14);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblLOSSe, 0, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblLOSSi, 0, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblFCAe, 0, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblFCAi, 0, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblSpacingToDiscontinuity, 0, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblSpacingToWeldJoint, 0, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblRadiusBaseDent, 0, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblDentDepthInUnpressurisedCondition, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblDentDepthInPressurisedCondition, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtTotalDesignPressureCycles, 1, 17);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtMinPressureCyclic, 1, 16 /*0x10*/);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtMaxPressureCyclic, 1, 15);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkCyclicLoading, 1, 14);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSe, 1, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtLOSSi, 1, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAe, 1, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtFCAi, 1, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtSpacingToDiscontinuity, 1, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtSpacingToWeldJoint, 1, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtRadiusBaseDent, 1, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtDentDepthInUnpressurisedCondition, 1, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtDentDepthInPressurisedCondition, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtDepthGougeInCorrodedCondition, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.chkDentDepthMeasuredInPressurised, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.lblDentDepthMeasuredInPressurised, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.umMinPressureCyclic, 2, 16 /*0x10*/);
    this.tableLayoutPanel1.Controls.Add((Control) this.umMaxPressureCyclic, 2, 15);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSe, 2, 13);
    this.tableLayoutPanel1.Controls.Add((Control) this.umLOSSi, 2, 12);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAe, 2, 11);
    this.tableLayoutPanel1.Controls.Add((Control) this.umFCAi, 2, 10);
    this.tableLayoutPanel1.Controls.Add((Control) this.umSpacingToDiscontinuity, 2, 9);
    this.tableLayoutPanel1.Controls.Add((Control) this.umSpacingToWeldJoint, 2, 8);
    this.tableLayoutPanel1.Controls.Add((Control) this.umRadiusBaseDent, 2, 7);
    this.tableLayoutPanel1.Controls.Add((Control) this.umDentDepthInUnpressurisedCondition, 2, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.umDentDepthInPressurisedCondition, 2, 5);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 18;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(624, 643);
    this.tableLayoutPanel1.TabIndex = 0;
    this.chkLevel2.Location = new Point(11, 11);
    this.chkLevel2.Margin = new Padding(1);
    this.chkLevel2.Name = "chkLevel2";
    this.chkLevel2.Properties.Caption = "Level 2";
    this.chkLevel2.Size = new Size(120, 19);
    this.chkLevel2.TabIndex = 0;
    this.chkLevel2.CheckedChanged += new EventHandler(this.chkLevel2_CheckedChanged);
    this.lblDepthGougeInCorrodedCondition.Location = new Point(13, 78);
    this.lblDepthGougeInCorrodedCondition.Name = "lblDepthGougeInCorrodedCondition";
    this.lblDepthGougeInCorrodedCondition.Size = new Size(197, 13);
    this.lblDepthGougeInCorrodedCondition.TabIndex = 7;
    this.lblDepthGougeInCorrodedCondition.Text = "Depth of gouge in corroded condition, dg";
    this.umDepthGougeInCorrodedCondition.Location = new Point(419, 78);
    this.umDepthGougeInCorrodedCondition.Name = "umDepthGougeInCorrodedCondition";
    this.umDepthGougeInCorrodedCondition.Size = new Size(41, 13);
    this.umDepthGougeInCorrodedCondition.TabIndex = 9;
    this.umDepthGougeInCorrodedCondition.Text = "measure";
    this.lblModulusElasticity.Location = new Point(13, 34);
    this.lblModulusElasticity.Name = "lblModulusElasticity";
    this.lblModulusElasticity.Size = new Size(97, 13);
    this.lblModulusElasticity.TabIndex = 1;
    this.lblModulusElasticity.Text = "Modulus of Elasticity";
    this.lblModulusElasticity.Visible = false;
    this.lblCharpyVNotch.Location = new Point(13, 56);
    this.lblCharpyVNotch.Name = "lblCharpyVNotch";
    this.lblCharpyVNotch.Size = new Size(74, 13);
    this.lblCharpyVNotch.TabIndex = 4;
    this.lblCharpyVNotch.Text = "Charpy V notch";
    this.lblCharpyVNotch.Visible = false;
    this.umCharpyVNotch.Location = new Point(419, 56);
    this.umCharpyVNotch.Name = "umCharpyVNotch";
    this.umCharpyVNotch.Size = new Size(5, 13);
    this.umCharpyVNotch.TabIndex = 6;
    this.umCharpyVNotch.Text = "J";
    this.umCharpyVNotch.Visible = false;
    this.umModulusElasticity.Location = new Point(419, 34);
    this.umModulusElasticity.Name = "umModulusElasticity";
    this.umModulusElasticity.Size = new Size(41, 13);
    this.umModulusElasticity.TabIndex = 3;
    this.umModulusElasticity.Text = "measure";
    this.umModulusElasticity.Visible = false;
    this.txtCharpyVNotch.Location = new Point(315, 54);
    this.txtCharpyVNotch.Margin = new Padding(1);
    this.txtCharpyVNotch.Name = "txtCharpyVNotch";
    this.txtCharpyVNotch.Size = new Size(100, 20);
    this.txtCharpyVNotch.TabIndex = 5;
    this.txtCharpyVNotch.Visible = false;
    this.txtModulusElasticity.EditValue = (object) "";
    this.txtModulusElasticity.Location = new Point(315, 32 /*0x20*/);
    this.txtModulusElasticity.Margin = new Padding(1);
    this.txtModulusElasticity.Name = "txtModulusElasticity";
    this.txtModulusElasticity.Size = new Size(100, 20);
    this.txtModulusElasticity.TabIndex = 2;
    this.txtModulusElasticity.Visible = false;
    this.lblTotalDesignPressureCycles.Location = new Point(13, 384);
    this.lblTotalDesignPressureCycles.Name = "lblTotalDesignPressureCycles";
    this.lblTotalDesignPressureCycles.Size = new Size(298, 13);
    this.lblTotalDesignPressureCycles.TabIndex = 47;
    this.lblTotalDesignPressureCycles.Text = "Total Design Pressure Cycles Experienced by Component, Npf";
    this.lblTotalDesignPressureCycles.Visible = false;
    this.lblMinPressureCyclic.Location = new Point(13, 362);
    this.lblMinPressureCyclic.Name = "lblMinPressureCyclic";
    this.lblMinPressureCyclic.Size = new Size(227, 13);
    this.lblMinPressureCyclic.TabIndex = 44;
    this.lblMinPressureCyclic.Text = "Minimum Pressure Under Cyclic Operation, Pmin";
    this.lblMinPressureCyclic.Visible = false;
    this.lblMaxPressureCyclic.Location = new Point(13, 340);
    this.lblMaxPressureCyclic.Name = "lblMaxPressureCyclic";
    this.lblMaxPressureCyclic.Size = new Size(235, 13);
    this.lblMaxPressureCyclic.TabIndex = 41;
    this.lblMaxPressureCyclic.Text = "Maximum Pressure Under Cyclic Operation, Pmax";
    this.lblMaxPressureCyclic.Visible = false;
    this.lblCyclicLoading.Location = new Point(13, 319);
    this.lblCyclicLoading.Name = "lblCyclicLoading";
    this.lblCyclicLoading.Size = new Size(157, 13);
    this.lblCyclicLoading.TabIndex = 39;
    this.lblCyclicLoading.Text = "Component Under Cyclic Loading";
    this.lblCyclicLoading.Visible = false;
    this.lblLOSSe.Location = new Point(13, 297);
    this.lblLOSSe.Name = "lblLOSSe";
    this.lblLOSSe.Size = new Size(171, 13);
    this.lblLOSSe.TabIndex = 36;
    this.lblLOSSe.Text = "External Uniform Metal Loss, LOSSe";
    this.lblLOSSi.Location = new Point(13, 275);
    this.lblLOSSi.Name = "lblLOSSi";
    this.lblLOSSi.Size = new Size(165, 13);
    this.lblLOSSi.TabIndex = 33;
    this.lblLOSSi.Text = "Internal Uniform Metal Loss, LOSSi";
    this.lblFCAe.Location = new Point(13, 253);
    this.lblFCAe.Name = "lblFCAe";
    this.lblFCAe.Size = new Size(208 /*0xD0*/, 13);
    this.lblFCAe.TabIndex = 30;
    this.lblFCAe.Text = "External Future Corrosion Allowance, FCAe";
    this.lblFCAi.Location = new Point(13, 231);
    this.lblFCAi.Name = "lblFCAi";
    this.lblFCAi.Size = new Size(202, 13);
    this.lblFCAi.TabIndex = 27;
    this.lblFCAi.Text = "Internal Future Corrosion Allowance, FCAi";
    this.lblSpacingToDiscontinuity.Location = new Point(13, 209);
    this.lblSpacingToDiscontinuity.Name = "lblSpacingToDiscontinuity";
    this.lblSpacingToDiscontinuity.Size = new Size(268, 13);
    this.lblSpacingToDiscontinuity.TabIndex = 24;
    this.lblSpacingToDiscontinuity.Text = "Spacing To Nearest Major Structural Discontinuity, Lmsd";
    this.lblSpacingToWeldJoint.Location = new Point(13, 187);
    this.lblSpacingToWeldJoint.Name = "lblSpacingToWeldJoint";
    this.lblSpacingToWeldJoint.Size = new Size(166, 13);
    this.lblSpacingToWeldJoint.TabIndex = 21;
    this.lblSpacingToWeldJoint.Text = "Spacing To Nearest Weld Joint, Lw";
    this.lblRadiusBaseDent.Location = new Point(13, 165);
    this.lblRadiusBaseDent.Name = "lblRadiusBaseDent";
    this.lblRadiusBaseDent.Size = new Size(170, 13);
    this.lblRadiusBaseDent.TabIndex = 18;
    this.lblRadiusBaseDent.Text = "Radius At The Base of The Dent, rd";
    this.lblRadiusBaseDent.Visible = false;
    this.lblDentDepthInUnpressurisedCondition.Location = new Point(13, 143);
    this.lblDentDepthInUnpressurisedCondition.Name = "lblDentDepthInUnpressurisedCondition";
    this.lblDentDepthInUnpressurisedCondition.Size = new Size(263, 13);
    this.lblDentDepthInUnpressurisedCondition.TabIndex = 15;
    this.lblDentDepthInUnpressurisedCondition.Text = "Measured Dent Depth in Unpressurised Condition, d d0";
    this.lblDentDepthInPressurisedCondition.Location = new Point(13, 121);
    this.lblDentDepthInPressurisedCondition.Name = "lblDentDepthInPressurisedCondition";
    this.lblDentDepthInPressurisedCondition.Size = new Size(258, 13);
    this.lblDentDepthInPressurisedCondition.TabIndex = 12;
    this.lblDentDepthInPressurisedCondition.Text = "Measured Dent Depth in Pressurised Condition, d dpm";
    this.lblDentDepthInPressurisedCondition.Visible = false;
    this.txtTotalDesignPressureCycles.Location = new Point(315, 382);
    this.txtTotalDesignPressureCycles.Margin = new Padding(1);
    this.txtTotalDesignPressureCycles.Name = "txtTotalDesignPressureCycles";
    this.txtTotalDesignPressureCycles.Size = new Size(100, 20);
    this.txtTotalDesignPressureCycles.TabIndex = 48 /*0x30*/;
    this.txtTotalDesignPressureCycles.Visible = false;
    this.txtMinPressureCyclic.Location = new Point(315, 360);
    this.txtMinPressureCyclic.Margin = new Padding(1);
    this.txtMinPressureCyclic.Name = "txtMinPressureCyclic";
    this.txtMinPressureCyclic.Size = new Size(100, 20);
    this.txtMinPressureCyclic.TabIndex = 45;
    this.txtMinPressureCyclic.Visible = false;
    this.txtMaxPressureCyclic.Location = new Point(315, 338);
    this.txtMaxPressureCyclic.Margin = new Padding(1);
    this.txtMaxPressureCyclic.Name = "txtMaxPressureCyclic";
    this.txtMaxPressureCyclic.Size = new Size(100, 20);
    this.txtMaxPressureCyclic.TabIndex = 42;
    this.txtMaxPressureCyclic.Visible = false;
    this.chkCyclicLoading.Location = new Point(315, 317);
    this.chkCyclicLoading.Margin = new Padding(1);
    this.chkCyclicLoading.Name = "chkCyclicLoading";
    this.chkCyclicLoading.Properties.Caption = "";
    this.chkCyclicLoading.Size = new Size(24, 19);
    this.chkCyclicLoading.TabIndex = 40;
    this.chkCyclicLoading.Visible = false;
    this.chkCyclicLoading.CheckedChanged += new EventHandler(this.chkCyclicLoading_CheckedChanged);
    this.txtLOSSe.Location = new Point(315, 295);
    this.txtLOSSe.Margin = new Padding(1);
    this.txtLOSSe.Name = "txtLOSSe";
    this.txtLOSSe.Size = new Size(100, 20);
    this.txtLOSSe.TabIndex = 37;
    this.txtLOSSi.Location = new Point(315, 273);
    this.txtLOSSi.Margin = new Padding(1);
    this.txtLOSSi.Name = "txtLOSSi";
    this.txtLOSSi.Size = new Size(100, 20);
    this.txtLOSSi.TabIndex = 34;
    this.txtFCAe.Location = new Point(315, 251);
    this.txtFCAe.Margin = new Padding(1);
    this.txtFCAe.Name = "txtFCAe";
    this.txtFCAe.Size = new Size(100, 20);
    this.txtFCAe.TabIndex = 31 /*0x1F*/;
    this.txtFCAi.Location = new Point(315, 229);
    this.txtFCAi.Margin = new Padding(1);
    this.txtFCAi.Name = "txtFCAi";
    this.txtFCAi.Size = new Size(100, 20);
    this.txtFCAi.TabIndex = 28;
    this.txtSpacingToDiscontinuity.Location = new Point(315, 207);
    this.txtSpacingToDiscontinuity.Margin = new Padding(1);
    this.txtSpacingToDiscontinuity.Name = "txtSpacingToDiscontinuity";
    this.txtSpacingToDiscontinuity.Size = new Size(100, 20);
    this.txtSpacingToDiscontinuity.TabIndex = 25;
    this.txtSpacingToWeldJoint.Location = new Point(315, 185);
    this.txtSpacingToWeldJoint.Margin = new Padding(1);
    this.txtSpacingToWeldJoint.Name = "txtSpacingToWeldJoint";
    this.txtSpacingToWeldJoint.Size = new Size(100, 20);
    this.txtSpacingToWeldJoint.TabIndex = 22;
    this.txtRadiusBaseDent.Location = new Point(315, 163);
    this.txtRadiusBaseDent.Margin = new Padding(1);
    this.txtRadiusBaseDent.Name = "txtRadiusBaseDent";
    this.txtRadiusBaseDent.Size = new Size(100, 20);
    this.txtRadiusBaseDent.TabIndex = 19;
    this.txtRadiusBaseDent.Visible = false;
    this.txtDentDepthInUnpressurisedCondition.Location = new Point(315, 141);
    this.txtDentDepthInUnpressurisedCondition.Margin = new Padding(1);
    this.txtDentDepthInUnpressurisedCondition.Name = "txtDentDepthInUnpressurisedCondition";
    this.txtDentDepthInUnpressurisedCondition.Size = new Size(100, 20);
    this.txtDentDepthInUnpressurisedCondition.TabIndex = 16 /*0x10*/;
    this.txtDentDepthInPressurisedCondition.Location = new Point(315, 119);
    this.txtDentDepthInPressurisedCondition.Margin = new Padding(1);
    this.txtDentDepthInPressurisedCondition.Name = "txtDentDepthInPressurisedCondition";
    this.txtDentDepthInPressurisedCondition.Size = new Size(100, 20);
    this.txtDentDepthInPressurisedCondition.TabIndex = 13;
    this.txtDentDepthInPressurisedCondition.Visible = false;
    this.txtDepthGougeInCorrodedCondition.Location = new Point(315, 76);
    this.txtDepthGougeInCorrodedCondition.Margin = new Padding(1);
    this.txtDepthGougeInCorrodedCondition.Name = "txtDepthGougeInCorrodedCondition";
    this.txtDepthGougeInCorrodedCondition.Size = new Size(100, 20);
    this.txtDepthGougeInCorrodedCondition.TabIndex = 8;
    this.chkDentDepthMeasuredInPressurised.Location = new Point(315, 98);
    this.chkDentDepthMeasuredInPressurised.Margin = new Padding(1);
    this.chkDentDepthMeasuredInPressurised.Name = "chkDentDepthMeasuredInPressurised";
    this.chkDentDepthMeasuredInPressurised.Properties.Caption = "";
    this.chkDentDepthMeasuredInPressurised.Size = new Size(24, 19);
    this.chkDentDepthMeasuredInPressurised.TabIndex = 11;
    this.chkDentDepthMeasuredInPressurised.CheckedChanged += new EventHandler(this.chkDentDepthMeasuredInPressurised_CheckedChanged);
    this.lblDentDepthMeasuredInPressurised.Location = new Point(13, 100);
    this.lblDentDepthMeasuredInPressurised.Name = "lblDentDepthMeasuredInPressurised";
    this.lblDentDepthMeasuredInPressurised.Size = new Size(239, 13);
    this.lblDentDepthMeasuredInPressurised.TabIndex = 10;
    this.lblDentDepthMeasuredInPressurised.Text = "Is Dent Depth Measured in Pressurised Condition?";
    this.umMinPressureCyclic.Location = new Point(419, 362);
    this.umMinPressureCyclic.Name = "umMinPressureCyclic";
    this.umMinPressureCyclic.Size = new Size(41, 13);
    this.umMinPressureCyclic.TabIndex = 46;
    this.umMinPressureCyclic.Text = "measure";
    this.umMinPressureCyclic.Visible = false;
    this.umMaxPressureCyclic.Location = new Point(419, 340);
    this.umMaxPressureCyclic.Name = "umMaxPressureCyclic";
    this.umMaxPressureCyclic.Size = new Size(41, 13);
    this.umMaxPressureCyclic.TabIndex = 43;
    this.umMaxPressureCyclic.Text = "measure";
    this.umMaxPressureCyclic.Visible = false;
    this.umLOSSe.Location = new Point(419, 297);
    this.umLOSSe.Name = "umLOSSe";
    this.umLOSSe.Size = new Size(41, 13);
    this.umLOSSe.TabIndex = 38;
    this.umLOSSe.Text = "measure";
    this.umLOSSi.Location = new Point(419, 275);
    this.umLOSSi.Name = "umLOSSi";
    this.umLOSSi.Size = new Size(41, 13);
    this.umLOSSi.TabIndex = 35;
    this.umLOSSi.Text = "measure";
    this.umFCAe.Location = new Point(419, 253);
    this.umFCAe.Name = "umFCAe";
    this.umFCAe.Size = new Size(41, 13);
    this.umFCAe.TabIndex = 32 /*0x20*/;
    this.umFCAe.Text = "measure";
    this.umFCAi.Location = new Point(419, 231);
    this.umFCAi.Name = "umFCAi";
    this.umFCAi.Size = new Size(41, 13);
    this.umFCAi.TabIndex = 29;
    this.umFCAi.Text = "measure";
    this.umSpacingToDiscontinuity.Location = new Point(419, 209);
    this.umSpacingToDiscontinuity.Name = "umSpacingToDiscontinuity";
    this.umSpacingToDiscontinuity.Size = new Size(41, 13);
    this.umSpacingToDiscontinuity.TabIndex = 26;
    this.umSpacingToDiscontinuity.Text = "measure";
    this.umSpacingToWeldJoint.Location = new Point(419, 187);
    this.umSpacingToWeldJoint.Name = "umSpacingToWeldJoint";
    this.umSpacingToWeldJoint.Size = new Size(41, 13);
    this.umSpacingToWeldJoint.TabIndex = 23;
    this.umSpacingToWeldJoint.Text = "measure";
    this.umRadiusBaseDent.Location = new Point(419, 165);
    this.umRadiusBaseDent.Name = "umRadiusBaseDent";
    this.umRadiusBaseDent.Size = new Size(41, 13);
    this.umRadiusBaseDent.TabIndex = 20;
    this.umRadiusBaseDent.Text = "measure";
    this.umRadiusBaseDent.Visible = false;
    this.umDentDepthInUnpressurisedCondition.Location = new Point(419, 143);
    this.umDentDepthInUnpressurisedCondition.Name = "umDentDepthInUnpressurisedCondition";
    this.umDentDepthInUnpressurisedCondition.Size = new Size(41, 13);
    this.umDentDepthInUnpressurisedCondition.TabIndex = 17;
    this.umDentDepthInUnpressurisedCondition.Text = "measure";
    this.umDentDepthInPressurisedCondition.Location = new Point(419, 121);
    this.umDentDepthInPressurisedCondition.Name = "umDentDepthInPressurisedCondition";
    this.umDentDepthInPressurisedCondition.Size = new Size(41, 13);
    this.umDentDepthInPressurisedCondition.TabIndex = 14;
    this.umDentDepthInPressurisedCondition.Text = "measure";
    this.umDentDepthInPressurisedCondition.Visible = false;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = nameof (AbstractDentGougeView);
    this.Size = new Size(624, 643);
    this.Load += new EventHandler(this.AbstractDentGougeView_Load);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.chkLevel2.Properties.EndInit();
    this.txtCharpyVNotch.Properties.EndInit();
    this.txtModulusElasticity.Properties.EndInit();
    this.txtTotalDesignPressureCycles.Properties.EndInit();
    this.txtMinPressureCyclic.Properties.EndInit();
    this.txtMaxPressureCyclic.Properties.EndInit();
    this.chkCyclicLoading.Properties.EndInit();
    this.txtLOSSe.Properties.EndInit();
    this.txtLOSSi.Properties.EndInit();
    this.txtFCAe.Properties.EndInit();
    this.txtFCAi.Properties.EndInit();
    this.txtSpacingToDiscontinuity.Properties.EndInit();
    this.txtSpacingToWeldJoint.Properties.EndInit();
    this.txtRadiusBaseDent.Properties.EndInit();
    this.txtDentDepthInUnpressurisedCondition.Properties.EndInit();
    this.txtDentDepthInPressurisedCondition.Properties.EndInit();
    this.txtDepthGougeInCorrodedCondition.Properties.EndInit();
    this.chkDentDepthMeasuredInPressurised.Properties.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
