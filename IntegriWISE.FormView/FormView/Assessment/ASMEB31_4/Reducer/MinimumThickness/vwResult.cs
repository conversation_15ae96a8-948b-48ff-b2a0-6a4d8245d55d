// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_4.Reducer.MinimumThickness.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEB31_4.Reducer.MinimumThickness;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_4.Reducer.MinimumThickness;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private IContainer components;
  private GroupControl groupControl2;
  private TableLayoutPanel tableLayoutPanel1;
  private TableLayoutPanel tableLayoutPanel2;
  private LabelControl umAllowableStrength;
  private TextEdit txtAllowableStrength;
  private LabelControl labelControl2;
  private GroupControl groupControl1;
  private TableLayoutPanel tableLayoutPanel6;
  private LabelControl labelControl12;
  private TableLayoutPanel tableLayoutPanel9;
  private TextEdit txtMinimumThickness;
  private LabelControl umMinimumThickness;
  private MemoEdit txtWarningMessages;
  private GroupControl groupControl3;
  private TableLayoutPanel tableLayoutPanel3;
  private IRecordView _recordView;
  private ResultPresenter _presenter;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.groupControl2 = new GroupControl();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.umAllowableStrength = new LabelControl();
    this.txtAllowableStrength = new TextEdit();
    this.labelControl2 = new LabelControl();
    this.groupControl1 = new GroupControl();
    this.tableLayoutPanel6 = new TableLayoutPanel();
    this.labelControl12 = new LabelControl();
    this.tableLayoutPanel9 = new TableLayoutPanel();
    this.txtMinimumThickness = new TextEdit();
    this.umMinimumThickness = new LabelControl();
    this.txtWarningMessages = new MemoEdit();
    this.groupControl3 = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.groupControl2.BeginInit();
    this.groupControl2.SuspendLayout();
    this.tableLayoutPanel1.SuspendLayout();
    this.tableLayoutPanel2.SuspendLayout();
    this.txtAllowableStrength.Properties.BeginInit();
    this.groupControl1.BeginInit();
    this.groupControl1.SuspendLayout();
    this.tableLayoutPanel6.SuspendLayout();
    this.tableLayoutPanel9.SuspendLayout();
    this.txtMinimumThickness.Properties.BeginInit();
    this.txtWarningMessages.Properties.BeginInit();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.SuspendLayout();
    this.groupControl2.Controls.Add((Control) this.tableLayoutPanel1);
    this.groupControl2.Dock = DockStyle.Top;
    this.groupControl2.Location = new Point(0, 0);
    this.groupControl2.Name = "groupControl2";
    this.groupControl2.Size = new Size(580, 65);
    this.groupControl2.TabIndex = 2;
    this.groupControl2.Text = "Intermediate Result";
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 2;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 170f));
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel2, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 0);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(2, 21);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 1;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel1.Size = new Size(576, 42);
    this.tableLayoutPanel1.TabIndex = 0;
    this.tableLayoutPanel2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel2.ColumnCount = 2;
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.Controls.Add((Control) this.umAllowableStrength, 1, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.txtAllowableStrength, 0, 0);
    this.tableLayoutPanel2.Location = new Point(181, 11);
    this.tableLayoutPanel2.Margin = new Padding(1);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.RowCount = 1;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.Size = new Size(196, 20);
    this.tableLayoutPanel2.TabIndex = 11;
    this.umAllowableStrength.Location = new Point(103, 3);
    this.umAllowableStrength.Name = "umAllowableStrength";
    this.umAllowableStrength.Size = new Size(41, 13);
    this.umAllowableStrength.TabIndex = 1;
    this.umAllowableStrength.Text = "measure";
    this.txtAllowableStrength.Location = new Point(0, 0);
    this.txtAllowableStrength.Margin = new Padding(0);
    this.txtAllowableStrength.Name = "txtAllowableStrength";
    this.txtAllowableStrength.Properties.ReadOnly = true;
    this.txtAllowableStrength.Size = new Size(100, 20);
    this.txtAllowableStrength.TabIndex = 0;
    this.labelControl2.Location = new Point(13, 13);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(78, 13);
    this.labelControl2.TabIndex = 10;
    this.labelControl2.Text = "SMYS x E x F";
    this.groupControl1.Controls.Add((Control) this.tableLayoutPanel6);
    this.groupControl1.Dock = DockStyle.Top;
    this.groupControl1.Location = new Point(0, 65);
    this.groupControl1.Name = "groupControl1";
    this.groupControl1.Size = new Size(580, 65);
    this.groupControl1.TabIndex = 3;
    this.groupControl1.Text = "Assessment Result";
    this.tableLayoutPanel6.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel6.ColumnCount = 2;
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 170f));
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel6.Controls.Add((Control) this.labelControl12, 0, 0);
    this.tableLayoutPanel6.Controls.Add((Control) this.tableLayoutPanel9, 1, 0);
    this.tableLayoutPanel6.Dock = DockStyle.Fill;
    this.tableLayoutPanel6.Location = new Point(2, 21);
    this.tableLayoutPanel6.Name = "tableLayoutPanel6";
    this.tableLayoutPanel6.Padding = new Padding(10);
    this.tableLayoutPanel6.RowCount = 1;
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel6.Size = new Size(576, 42);
    this.tableLayoutPanel6.TabIndex = 0;
    this.labelControl12.Location = new Point(13, 13);
    this.labelControl12.Name = "labelControl12";
    this.labelControl12.Size = new Size(135, 13);
    this.labelControl12.TabIndex = 16 /*0x10*/;
    this.labelControl12.Text = "Minimum Required Thickness";
    this.tableLayoutPanel9.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel9.ColumnCount = 2;
    this.tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel9.Controls.Add((Control) this.txtMinimumThickness, 0, 0);
    this.tableLayoutPanel9.Controls.Add((Control) this.umMinimumThickness, 1, 0);
    this.tableLayoutPanel9.Location = new Point(181, 11);
    this.tableLayoutPanel9.Margin = new Padding(1);
    this.tableLayoutPanel9.Name = "tableLayoutPanel9";
    this.tableLayoutPanel9.RowCount = 1;
    this.tableLayoutPanel9.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel9.Size = new Size(196, 20);
    this.tableLayoutPanel9.TabIndex = 17;
    this.txtMinimumThickness.Location = new Point(0, 0);
    this.txtMinimumThickness.Margin = new Padding(0);
    this.txtMinimumThickness.Name = "txtMinimumThickness";
    this.txtMinimumThickness.Properties.ReadOnly = true;
    this.txtMinimumThickness.Size = new Size(100, 20);
    this.txtMinimumThickness.TabIndex = 0;
    this.umMinimumThickness.Location = new Point(103, 3);
    this.umMinimumThickness.Name = "umMinimumThickness";
    this.umMinimumThickness.Size = new Size(41, 13);
    this.umMinimumThickness.TabIndex = 1;
    this.umMinimumThickness.Text = "measure";
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(550, 51);
    this.txtWarningMessages.TabIndex = 4;
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel3);
    this.groupControl3.Dock = DockStyle.Fill;
    this.groupControl3.Location = new Point(0, 130);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(580, 100);
    this.groupControl3.TabIndex = 5;
    this.groupControl3.Text = "Warning Messages";
    this.tableLayoutPanel3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel3.Size = new Size(576, 77);
    this.tableLayoutPanel3.TabIndex = 0;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.Controls.Add((Control) this.groupControl3);
    this.Controls.Add((Control) this.groupControl1);
    this.Controls.Add((Control) this.groupControl2);
    this.Name = nameof (vwResult);
    this.Size = new Size(580, 230);
    this.Load += new EventHandler(this.vwResult_Load);
    this.groupControl2.EndInit();
    this.groupControl2.ResumeLayout(false);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.tableLayoutPanel2.ResumeLayout(false);
    this.tableLayoutPanel2.PerformLayout();
    this.txtAllowableStrength.Properties.EndInit();
    this.groupControl1.EndInit();
    this.groupControl1.ResumeLayout(false);
    this.tableLayoutPanel6.ResumeLayout(false);
    this.tableLayoutPanel6.PerformLayout();
    this.tableLayoutPanel9.ResumeLayout(false);
    this.tableLayoutPanel9.PerformLayout();
    this.txtMinimumThickness.Properties.EndInit();
    this.txtWarningMessages.Properties.EndInit();
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.tableLayoutPanel3.ResumeLayout(false);
    this.ResumeLayout(false);
  }

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.ResultView = (IResultBaseView) this;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  private void vwResult_Load(object sender, EventArgs e)
  {
    this.txtMinimumThickness.AllowOnlyN4();
    this.txtAllowableStrength.AllowOnlyN4();
  }

  public bool Calculate() => this._presenter.Calculate();

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public double? AllowableStrength
  {
    get => Helpers.ParseNullDouble((object) this.txtAllowableStrength.Text);
    set => this.txtAllowableStrength.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? MinimumThickness
  {
    get => Helpers.ParseNullDouble((object) this.txtMinimumThickness.Text);
    set => this.txtMinimumThickness.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMAllowableStrength
  {
    set => this.umAllowableStrength.Text = value;
  }

  public string UMMinimumThickness
  {
    set => this.umMinimumThickness.Text = value;
  }

  public string ResultMessages
  {
    get => this.txtWarningMessages.Text;
    set => this.txtWarningMessages.Text = value;
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public string CodeEdition => this._presenter.CodeEdition;

  public string Title => "Minimum Required Wall Thickness";

  public string Introduction
  {
    get
    {
      return "This assessment calculates the minimum required wall thickness for pipe reducer designed to ASME B31.4. The code calculation is taken from ASME B31.4 clause 404.1 (\"Pressure design of piping components\").";
    }
  }

  public string CommentsAndAssumptions
  {
    get => "The Calculations are based on data from ASME B31.4 2002.";
  }

  public string References
  {
    get
    {
      return "ASME B31.4 - 2002, \"Pipeline Transportation Systems for liquid hydrocarbons and other liquids\", The American Society of Mechanical Engineers.";
    }
  }

  public string Limitations => string.Empty;
}
