// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_4.Reducer.LocalMetalLossThinArea.vwLocalMetalLossThinArea
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using IntegriWISE.FormView.Assessment.AbstractView.LocalMetalLossThinArea;
using IntegriWISE.FormView.Properties;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Assessment.AbstractUserInterface.LocalMetalLossThinArea;
using IntegriWISE.UserInterface.Assessment.ASMEB31_4.Reducer.LocalMetalLossThinArea;
using IntegriWISE.UserInterface.Record;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_4.Reducer.LocalMetalLossThinArea;

public class vwLocalMetalLossThinArea : 
  AbstractLocalMetalLossThinAreaView,
  ILocalMetalLossThinAreaView,
  IThicknessAssessmentBaseView,
  IAssessmentBaseView,
  IView,
  ILongitudinalSpacingView,
  ICircumferentialSpacingView
{
  private LocalMetalLossThinAreaPresenter _presenter;

  public vwLocalMetalLossThinArea(IRecordView recordView)
    : base(recordView)
  {
    this.pictureAssessment.EditValue = (object) Resources.IW_GML_Vessels_Reducers;
    recordView.AssessmentView = (IAssessmentBaseView) this;
    this._presenter = new LocalMetalLossThinAreaPresenter(recordView, (ILocalMetalLossThinAreaView) this);
    this._presenter.LoadAssessment();
  }

  public void CalculateTRD() => this._presenter.CalculateTRD();

  public bool ValidateAssessment() => this._presenter.Validate();

  public void Save() => this._presenter.Save();
}
