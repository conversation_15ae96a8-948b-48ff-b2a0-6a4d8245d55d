// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_4.Reducer.GeneralMetalLossThicknessProfile.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEB31_4.Reducer.GeneralMetalLossThicknessProfile;
using IntegriWISE.UserInterface.Assessment.DesignCode.ASMEB31_4.GeneralMetalLossThicknessProfile;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_4.Reducer.GeneralMetalLossThicknessProfile;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private IRecordView _recordView;
  private ResultPresenter _presenter;
  private bool _level2;
  private bool _level1Passed;
  private bool _level2Passed;
  private IContainer components;
  private LabelControl labelControl2;
  private TableLayoutPanel tableLayoutPanel9;
  private TextEdit txtWallThickTsam;
  private LabelControl umWallThickTsam;
  private MemoEdit txtWarningMessages;
  private GroupControl grcWarnings;
  private TableLayoutPanel tableLayoutPanel3;
  private LabelControl labelControl12;
  private TableLayoutPanel tblpAssessmentResults;
  private LabelControl labelControl9;
  private GroupControl grcAssessmentResults;
  private GroupControl grcIntermediateResults;
  private TableLayoutPanel tblpIntermediateResults;
  private TableLayoutPanel tableLayoutPanel5;
  private LabelControl umMinMeasuredThickness;
  private TextEdit txtMinMeasuredThickness;
  private TableLayoutPanel tableLayoutPanel4;
  private LabelControl umTmin;
  private TextEdit txtTmin;
  private LabelControl labelControl1;
  private TableLayoutPanel tableLayoutPanel2;
  private LabelControl umAllowableStrength;
  private TextEdit txtAllowableStrength;
  private LabelControl labelControl4;
  private LabelControl labelControl5;
  private LabelControl labelControl6;
  private LabelControl labelControl7;
  private LabelControl labelControl8;
  private TableLayoutPanel tableLayoutPanel13;
  private TextEdit txtWallThickTcam;
  private LabelControl umWallThickTcam;
  private TableLayoutPanel tableLayoutPanel8;
  private LabelControl umTrd;
  private TextEdit txtTrd;
  private TableLayoutPanel tableLayoutPanel10;
  private TextEdit txtRemainingThickRationRT;
  private TableLayoutPanel tableLayoutPanel11;
  private TextEdit txtParameterQ;
  private TableLayoutPanel tableLayoutPanel12;
  private LabelControl umThickAveragingLengthL;
  private TextEdit txtThickAveragingLengthL;
  private LabelControl lbMAWPLL2;
  private LabelControl labelControl19;
  private LabelControl labelControl20;
  private LabelControl lbMAWPCL2;
  private TableLayoutPanel tableLayoutPanel14;
  private TextEdit txtMawpCLevel1;
  private LabelControl umMawpC1;
  private TableLayoutPanel tableLayoutPanel15;
  private TextEdit txtMawpLLevel1;
  private LabelControl umMawpL1;
  private TableLayoutPanel tblLMAWPCL2;
  private TextEdit txtMawpCLevel2;
  private LabelControl umMawpC2;
  private TableLayoutPanel tblLMAWPLL2;
  private TextEdit txtMawpLLevel2;
  private LabelControl umMawpL2;
  private GroupControl grcAssessmentCriteriaL1;
  private GroupControl grcAssessmentConclusionL1;
  private TableLayoutPanel tableLayoutPanel22;
  private LabelControl lbConclusionLevel1;
  private GroupControl grcAssessmentCriteriaL2;
  private GroupControl grcAssessmentConclusionL2;
  private TableLayoutPanel tableLayoutPanel27;
  private LabelControl lbConclusionLevel2;
  private TableLayoutPanel tblpAssessmentCriteriaL1;
  private LabelControl labelControl29;
  private TableLayoutPanel tableLayoutPanel20;
  private TextEdit txtAverageMThicknessL1;
  private TableLayoutPanel tableLayoutPanel21;
  private TextEdit txtMinMThicknessL1;
  private LabelControl labelControl32;
  private TableLayoutPanel tableLayoutPanel19;
  private TextEdit txtMaxAllowWorkPressureL1;
  private LabelControl labelControl31;
  private TableLayoutPanel tblpAssessmentCriteriaL2;
  private LabelControl labelControl34;
  private TableLayoutPanel tableLayoutPanel25;
  private TextEdit txtAverageMThicknessL2;
  private LabelControl labelControl37;
  private LabelControl labelControl38;
  private TableLayoutPanel tableLayoutPanel26;
  private TextEdit txtMinMThicknessL2;
  private TableLayoutPanel tableLayoutPanel24;
  private TextEdit txtMaxAllowWorkPressureL2;
  private GroupControl grcInspectionDataSummary;
  private TableLayoutPanel tableLayoutPanel1;
  private TableLayoutPanel tableLayoutPanel6;
  private LabelControl umLm;
  private TextEdit txtLm;
  private LabelControl labelControl11;
  private TableLayoutPanel tableLayoutPanel16;
  private LabelControl umLc;
  private TextEdit txtLc;
  private LabelControl labelControl14;
  private LabelControl labelControl15;
  private LabelControl labelControl17;
  private TableLayoutPanel tableLayoutPanel17;
  private LabelControl umS;
  private TextEdit txtS;
  private TableLayoutPanel tableLayoutPanel18;
  private LabelControl umC;
  private TextEdit txtC;

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  public bool Calculate() => this._presenter.Calculate();

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public bool Level2
  {
    get => this._level2;
    set
    {
      this._level2 = value;
      if (!value)
        this.HideLevel2Results();
      else
        this.ShowLevel2Results();
    }
  }

  public double? LongitudinalThicknessReadingSpacingLc
  {
    get => Helpers.ParseNullDouble((object) this.txtLc.Text);
    set => this.txtLc.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? CircumferentialThicknessReadingSpacingLm
  {
    get => Helpers.ParseNullDouble((object) this.txtLm.Text);
    set => this.txtLm.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? LongitudinalMetalLossExtentS
  {
    get => Helpers.ParseNullDouble((object) this.txtS.Text);
    set => this.txtS.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? CircumferentiallMetalLossExtentC
  {
    get => Helpers.ParseNullDouble((object) this.txtC.Text);
    set => this.txtC.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? AllowableStrength
  {
    get => Helpers.ParseNullDouble((object) this.txtAllowableStrength.Text);
    set => this.txtAllowableStrength.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Tcmin
  {
    get => Helpers.ParseNullDouble((object) this.txtTmin.Text);
    set => this.txtTmin.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Tmin
  {
    get => Helpers.ParseNullDouble((object) this.txtTmin.Text);
    set => this.txtTmin.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Tmm
  {
    get => Helpers.ParseNullDouble((object) this.txtMinMeasuredThickness.Text);
    set => this.txtMinMeasuredThickness.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Trd
  {
    get => Helpers.ParseNullDouble((object) this.txtTrd.Text);
    set => this.txtTrd.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Rt
  {
    get => Helpers.ParseNullDouble((object) this.txtRemainingThickRationRT.Text);
    set => this.txtRemainingThickRationRT.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Q
  {
    get => Helpers.ParseNullDouble((object) this.txtParameterQ.Text);
    set => this.txtParameterQ.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? L
  {
    get => Helpers.ParseNullDouble((object) this.txtThickAveragingLengthL.Text);
    set => this.txtThickAveragingLengthL.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? TSam
  {
    get => Helpers.ParseNullDouble((object) this.txtWallThickTsam.Text);
    set => this.txtWallThickTsam.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? TCam
  {
    get => Helpers.ParseNullDouble((object) this.txtWallThickTcam.Text);
    set => this.txtWallThickTcam.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? MAWPrcTSamMinusFCA
  {
    get => Helpers.ParseNullDouble((object) this.txtMawpCLevel1.Text);
    set => this.txtMawpCLevel1.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? MAWPrlTCamMinusFCA
  {
    get => Helpers.ParseNullDouble((object) this.txtMawpLLevel1.Text);
    set => this.txtMawpLLevel1.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? MAWPrcTsamMinusFCAOverRSFa
  {
    get => Helpers.ParseNullDouble((object) this.txtMawpCLevel2.Text);
    set => this.txtMawpCLevel2.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? MAWPrlTcamMinusTslMinusFCAOverRSFa
  {
    get => Helpers.ParseNullDouble((object) this.txtMawpLLevel2.Text);
    set => this.txtMawpLLevel2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool AverageMeasuredThicknessL1
  {
    get => Helpers.ParseObjectToBool((object) this.txtAverageMThicknessL1.Text);
    set => this.txtAverageMThicknessL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool MawpL1
  {
    get => Helpers.ParseObjectToBool((object) this.txtMaxAllowWorkPressureL1.Text);
    set => this.txtMaxAllowWorkPressureL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool MinimumMeasuredThicknessL1
  {
    get => Helpers.ParseObjectToBool((object) this.txtMinMThicknessL1.Text);
    set => this.txtMinMThicknessL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool Level1Passed
  {
    get => this._level1Passed;
    set
    {
      this._level1Passed = value;
      if (value)
      {
        this.HideLevel2Results();
        this.lbConclusionLevel1.ForeColor = Color.Green;
      }
      else
        this.lbConclusionLevel1.ForeColor = Color.DarkRed;
    }
  }

  public string Level1AssessmentConclusion
  {
    get => this.lbConclusionLevel1.Text;
    set => this.lbConclusionLevel1.Text = value;
  }

  public bool AverageMeasuredThicknessL2
  {
    get => Helpers.ParseObjectToBool((object) this.txtAverageMThicknessL2.Text);
    set => this.txtAverageMThicknessL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool MawpL2
  {
    get => Helpers.ParseObjectToBool((object) this.txtMaxAllowWorkPressureL2.Text);
    set => this.txtMaxAllowWorkPressureL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool MinimumMeasuredThicknessL2
  {
    get => Helpers.ParseObjectToBool((object) this.txtMinMThicknessL2.Text);
    set => this.txtMinMThicknessL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool Level2Passed
  {
    get => this._level2Passed;
    set
    {
      this._level2Passed = value;
      if (value)
        this.lbConclusionLevel2.ForeColor = Color.DarkGreen;
      else
        this.lbConclusionLevel2.ForeColor = Color.DarkRed;
    }
  }

  public string Level2AssessmentConclusion
  {
    get => this.lbConclusionLevel2.Text;
    set => this.lbConclusionLevel2.Text = value;
  }

  public string UMLc
  {
    set => this.umLc.Text = value;
  }

  public string UMLm
  {
    set => this.umLm.Text = value;
  }

  public string UMS
  {
    set => this.umS.Text = value;
  }

  public string UMC
  {
    set => this.umC.Text = value;
  }

  public string UMAllowableStrength
  {
    set => this.umAllowableStrength.Text = value;
  }

  public string UMTmin
  {
    set => this.umTmin.Text = value;
  }

  public string UMMinMeasuredThickness
  {
    set => this.umMinMeasuredThickness.Text = value;
  }

  public string UMTrd
  {
    set => this.umTrd.Text = value;
  }

  public string UMThickAveragingLengthL
  {
    set => this.umThickAveragingLengthL.Text = value;
  }

  public string UMWallThickTsam
  {
    set => this.umWallThickTsam.Text = value;
  }

  public string UMWallThickTcam
  {
    set => this.umWallThickTcam.Text = value;
  }

  public string UMMawpC1
  {
    set => this.umMawpC1.Text = value;
  }

  public string UMMawpL1
  {
    set => this.umMawpL1.Text = value;
  }

  public string UMMawpC2
  {
    set => this.umMawpC2.Text = value;
  }

  public string UMMawpL2
  {
    set => this.umMawpL2.Text = value;
  }

  public string ResultMessages
  {
    get => this.txtWarningMessages.Text;
    set => this.txtWarningMessages.Text = value;
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public string CodeEdition => this._presenter.CodeEdition;

  public string Title => "Assessment to API 579 Section 4. General Metal Loss. Thickness Profiles";

  public string Introduction
  {
    get
    {
      return "The assessment procedure in Part 4 can be used to evaluated general metal loss (uniform or local) that exceeds or is predicted to exceed the corrosion allowance before the next scheduled inspection. The assessment procedure to be used in an evaluation depends on the type of thickness data available (point thickness readings or thickness profiles), the characteristics of the metal loss and the degree of conservatism acceptable for the assessment.  \n\nThis module conducts the procedure for assessing local metal loss based on thickness profiles. Thickness profiles are used to characterize metal loss in a component. The Critical Thickness Profile is determined by projecting the minimum remaining thickness along parallel inspection planes onto a common plane. Then, the average remaining wall thickness over a prescribed length is assessed with respect to the original construction code minimum required wall thickness (tmin).  \n\nAn assessment to the Level 1 and Level 2 local metal loss procedures in Section 4 of API 579 has been conducted.";
    }
  }

  public string CommentsAndAssumptions
  {
    get
    {
      return "If the corroded surface is not accessible for visual inspection, then the recommended spacing distance for thickness readings along each inspection plane is given by the equation 4.1 API 579  \n\nA minimum of five thickness readings is recommended for each inspection plane.   \n\nIf there are multiple flaws in close proximity to one another, then the size of the flaw to be used in the assessment is established considering the effects of neighbouring flaws using the methodology shown in Figure 4.7";
    }
  }

  public string References
  {
    get
    {
      return "API 579 'Fitness-for-Service', Second Edition, The American Society of Mechanical Engineers. Part 4: Assessment of General Metal Loss.";
    }
  }

  public string Limitations => string.Empty;

  private void HideLevel2Results()
  {
    this.lbMAWPCL2.Visible = false;
    this.txtMawpCLevel2.Visible = false;
    this.umMawpC2.Visible = false;
    this.tblLMAWPCL2.Visible = false;
    this.lbMAWPLL2.Visible = false;
    this.txtMawpLLevel2.Visible = false;
    this.umMawpL2.Visible = false;
    this.tblLMAWPLL2.Visible = false;
    this.grcAssessmentCriteriaL2.Visible = false;
    this.grcAssessmentConclusionL2.Visible = false;
  }

  private void ShowLevel2Results()
  {
    this.lbMAWPCL2.Visible = true;
    this.txtMawpCLevel2.Visible = true;
    this.umMawpC2.Visible = true;
    this.tblLMAWPCL2.Visible = true;
    this.lbMAWPLL2.Visible = true;
    this.txtMawpLLevel2.Visible = true;
    this.umMawpL2.Visible = true;
    this.tblLMAWPLL2.Visible = true;
    this.grcAssessmentCriteriaL2.Visible = true;
    this.grcAssessmentConclusionL2.Visible = true;
  }

  private void vwResult_Load(object sender, EventArgs e)
  {
    this.txtLc.ShowOnlyN4();
    this.txtLm.ShowOnlyN4();
    this.txtS.ShowOnlyN4();
    this.txtC.ShowOnlyN4();
    this.txtAllowableStrength.ShowOnlyN4();
    this.txtTmin.ShowOnlyN4();
    this.txtTmin.ShowOnlyN4();
    this.txtMinMeasuredThickness.ShowOnlyN4();
    this.txtTrd.ShowOnlyN4();
    this.txtRemainingThickRationRT.ShowOnlyN4();
    this.txtParameterQ.ShowOnlyN4();
    this.txtThickAveragingLengthL.ShowOnlyN4();
    this.txtWallThickTsam.ShowOnlyN4();
    this.txtWallThickTcam.ShowOnlyN4();
    this.txtMawpCLevel1.ShowOnlyN4();
    this.txtMawpLLevel1.ShowOnlyN4();
    this.txtMawpCLevel2.ShowOnlyN4();
    this.txtMawpLLevel2.ShowOnlyN4();
    this.txtAllowableStrength.ShowOnlyN4();
    this.txtAllowableStrength.ShowOnlyN4();
    this.txtAllowableStrength.ShowOnlyN4();
  }

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.labelControl2 = new LabelControl();
    this.tableLayoutPanel9 = new TableLayoutPanel();
    this.txtWallThickTsam = new TextEdit();
    this.umWallThickTsam = new LabelControl();
    this.txtWarningMessages = new MemoEdit();
    this.grcWarnings = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.labelControl12 = new LabelControl();
    this.tblpAssessmentResults = new TableLayoutPanel();
    this.labelControl19 = new LabelControl();
    this.labelControl20 = new LabelControl();
    this.tableLayoutPanel14 = new TableLayoutPanel();
    this.txtMawpCLevel1 = new TextEdit();
    this.umMawpC1 = new LabelControl();
    this.tableLayoutPanel15 = new TableLayoutPanel();
    this.txtMawpLLevel1 = new TextEdit();
    this.umMawpL1 = new LabelControl();
    this.tblLMAWPCL2 = new TableLayoutPanel();
    this.txtMawpCLevel2 = new TextEdit();
    this.umMawpC2 = new LabelControl();
    this.lbMAWPCL2 = new LabelControl();
    this.lbMAWPLL2 = new LabelControl();
    this.tblLMAWPLL2 = new TableLayoutPanel();
    this.txtMawpLLevel2 = new TextEdit();
    this.umMawpL2 = new LabelControl();
    this.tableLayoutPanel13 = new TableLayoutPanel();
    this.txtWallThickTcam = new TextEdit();
    this.umWallThickTcam = new LabelControl();
    this.labelControl9 = new LabelControl();
    this.grcAssessmentResults = new GroupControl();
    this.grcIntermediateResults = new GroupControl();
    this.tblpIntermediateResults = new TableLayoutPanel();
    this.tableLayoutPanel4 = new TableLayoutPanel();
    this.umTmin = new LabelControl();
    this.txtTmin = new TextEdit();
    this.labelControl1 = new LabelControl();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.umAllowableStrength = new LabelControl();
    this.txtAllowableStrength = new TextEdit();
    this.labelControl4 = new LabelControl();
    this.labelControl5 = new LabelControl();
    this.labelControl6 = new LabelControl();
    this.labelControl7 = new LabelControl();
    this.labelControl8 = new LabelControl();
    this.tableLayoutPanel5 = new TableLayoutPanel();
    this.umMinMeasuredThickness = new LabelControl();
    this.txtMinMeasuredThickness = new TextEdit();
    this.tableLayoutPanel8 = new TableLayoutPanel();
    this.umTrd = new LabelControl();
    this.txtTrd = new TextEdit();
    this.tableLayoutPanel10 = new TableLayoutPanel();
    this.txtRemainingThickRationRT = new TextEdit();
    this.tableLayoutPanel11 = new TableLayoutPanel();
    this.txtParameterQ = new TextEdit();
    this.tableLayoutPanel12 = new TableLayoutPanel();
    this.umThickAveragingLengthL = new LabelControl();
    this.txtThickAveragingLengthL = new TextEdit();
    this.grcAssessmentCriteriaL1 = new GroupControl();
    this.tblpAssessmentCriteriaL1 = new TableLayoutPanel();
    this.labelControl29 = new LabelControl();
    this.tableLayoutPanel20 = new TableLayoutPanel();
    this.txtAverageMThicknessL1 = new TextEdit();
    this.tableLayoutPanel21 = new TableLayoutPanel();
    this.txtMinMThicknessL1 = new TextEdit();
    this.labelControl32 = new LabelControl();
    this.tableLayoutPanel19 = new TableLayoutPanel();
    this.txtMaxAllowWorkPressureL1 = new TextEdit();
    this.labelControl31 = new LabelControl();
    this.grcAssessmentConclusionL1 = new GroupControl();
    this.tableLayoutPanel22 = new TableLayoutPanel();
    this.lbConclusionLevel1 = new LabelControl();
    this.grcAssessmentCriteriaL2 = new GroupControl();
    this.tblpAssessmentCriteriaL2 = new TableLayoutPanel();
    this.labelControl34 = new LabelControl();
    this.tableLayoutPanel25 = new TableLayoutPanel();
    this.txtAverageMThicknessL2 = new TextEdit();
    this.labelControl37 = new LabelControl();
    this.labelControl38 = new LabelControl();
    this.tableLayoutPanel26 = new TableLayoutPanel();
    this.txtMinMThicknessL2 = new TextEdit();
    this.tableLayoutPanel24 = new TableLayoutPanel();
    this.txtMaxAllowWorkPressureL2 = new TextEdit();
    this.grcAssessmentConclusionL2 = new GroupControl();
    this.tableLayoutPanel27 = new TableLayoutPanel();
    this.lbConclusionLevel2 = new LabelControl();
    this.grcInspectionDataSummary = new GroupControl();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.tableLayoutPanel6 = new TableLayoutPanel();
    this.umLm = new LabelControl();
    this.txtLm = new TextEdit();
    this.labelControl11 = new LabelControl();
    this.tableLayoutPanel16 = new TableLayoutPanel();
    this.umLc = new LabelControl();
    this.txtLc = new TextEdit();
    this.labelControl14 = new LabelControl();
    this.labelControl15 = new LabelControl();
    this.labelControl17 = new LabelControl();
    this.tableLayoutPanel17 = new TableLayoutPanel();
    this.umS = new LabelControl();
    this.txtS = new TextEdit();
    this.tableLayoutPanel18 = new TableLayoutPanel();
    this.umC = new LabelControl();
    this.txtC = new TextEdit();
    this.tableLayoutPanel9.SuspendLayout();
    this.txtWallThickTsam.Properties.BeginInit();
    this.txtWarningMessages.Properties.BeginInit();
    this.grcWarnings.BeginInit();
    this.grcWarnings.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.tblpAssessmentResults.SuspendLayout();
    this.tableLayoutPanel14.SuspendLayout();
    this.txtMawpCLevel1.Properties.BeginInit();
    this.tableLayoutPanel15.SuspendLayout();
    this.txtMawpLLevel1.Properties.BeginInit();
    this.tblLMAWPCL2.SuspendLayout();
    this.txtMawpCLevel2.Properties.BeginInit();
    this.tblLMAWPLL2.SuspendLayout();
    this.txtMawpLLevel2.Properties.BeginInit();
    this.tableLayoutPanel13.SuspendLayout();
    this.txtWallThickTcam.Properties.BeginInit();
    this.grcAssessmentResults.BeginInit();
    this.grcAssessmentResults.SuspendLayout();
    this.grcIntermediateResults.BeginInit();
    this.grcIntermediateResults.SuspendLayout();
    this.tblpIntermediateResults.SuspendLayout();
    this.tableLayoutPanel4.SuspendLayout();
    this.txtTmin.Properties.BeginInit();
    this.tableLayoutPanel2.SuspendLayout();
    this.txtAllowableStrength.Properties.BeginInit();
    this.tableLayoutPanel5.SuspendLayout();
    this.txtMinMeasuredThickness.Properties.BeginInit();
    this.tableLayoutPanel8.SuspendLayout();
    this.txtTrd.Properties.BeginInit();
    this.tableLayoutPanel10.SuspendLayout();
    this.txtRemainingThickRationRT.Properties.BeginInit();
    this.tableLayoutPanel11.SuspendLayout();
    this.txtParameterQ.Properties.BeginInit();
    this.tableLayoutPanel12.SuspendLayout();
    this.txtThickAveragingLengthL.Properties.BeginInit();
    this.grcAssessmentCriteriaL1.BeginInit();
    this.grcAssessmentCriteriaL1.SuspendLayout();
    this.tblpAssessmentCriteriaL1.SuspendLayout();
    this.tableLayoutPanel20.SuspendLayout();
    this.txtAverageMThicknessL1.Properties.BeginInit();
    this.tableLayoutPanel21.SuspendLayout();
    this.txtMinMThicknessL1.Properties.BeginInit();
    this.tableLayoutPanel19.SuspendLayout();
    this.txtMaxAllowWorkPressureL1.Properties.BeginInit();
    this.grcAssessmentConclusionL1.BeginInit();
    this.grcAssessmentConclusionL1.SuspendLayout();
    this.tableLayoutPanel22.SuspendLayout();
    this.grcAssessmentCriteriaL2.BeginInit();
    this.grcAssessmentCriteriaL2.SuspendLayout();
    this.tblpAssessmentCriteriaL2.SuspendLayout();
    this.tableLayoutPanel25.SuspendLayout();
    this.txtAverageMThicknessL2.Properties.BeginInit();
    this.tableLayoutPanel26.SuspendLayout();
    this.txtMinMThicknessL2.Properties.BeginInit();
    this.tableLayoutPanel24.SuspendLayout();
    this.txtMaxAllowWorkPressureL2.Properties.BeginInit();
    this.grcAssessmentConclusionL2.BeginInit();
    this.grcAssessmentConclusionL2.SuspendLayout();
    this.tableLayoutPanel27.SuspendLayout();
    this.grcInspectionDataSummary.BeginInit();
    this.grcInspectionDataSummary.SuspendLayout();
    this.tableLayoutPanel1.SuspendLayout();
    this.tableLayoutPanel6.SuspendLayout();
    this.txtLm.Properties.BeginInit();
    this.tableLayoutPanel16.SuspendLayout();
    this.txtLc.Properties.BeginInit();
    this.tableLayoutPanel17.SuspendLayout();
    this.txtS.Properties.BeginInit();
    this.tableLayoutPanel18.SuspendLayout();
    this.txtC.Properties.BeginInit();
    this.SuspendLayout();
    this.labelControl2.Location = new Point(13, 13);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(78, 13);
    this.labelControl2.TabIndex = 10;
    this.labelControl2.Text = "SMYS x E x F";
    this.tableLayoutPanel9.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel9.ColumnCount = 2;
    this.tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel9.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel9.Controls.Add((Control) this.txtWallThickTsam, 0, 0);
    this.tableLayoutPanel9.Controls.Add((Control) this.umWallThickTsam, 1, 0);
    this.tableLayoutPanel9.Location = new Point(296, 11);
    this.tableLayoutPanel9.Margin = new Padding(1);
    this.tableLayoutPanel9.Name = "tableLayoutPanel9";
    this.tableLayoutPanel9.RowCount = 1;
    this.tableLayoutPanel9.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel9.Size = new Size(155, 20);
    this.tableLayoutPanel9.TabIndex = 17;
    this.txtWallThickTsam.Location = new Point(0, 0);
    this.txtWallThickTsam.Margin = new Padding(0);
    this.txtWallThickTsam.Name = "txtWallThickTsam";
    this.txtWallThickTsam.Properties.ReadOnly = true;
    this.txtWallThickTsam.Size = new Size(100, 20);
    this.txtWallThickTsam.TabIndex = 0;
    this.umWallThickTsam.Location = new Point(103, 3);
    this.umWallThickTsam.Name = "umWallThickTsam";
    this.umWallThickTsam.Size = new Size(41, 13);
    this.umWallThickTsam.TabIndex = 1;
    this.umWallThickTsam.Text = "measure";
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(550, 115);
    this.txtWarningMessages.TabIndex = 4;
    this.grcWarnings.Controls.Add((Control) this.tableLayoutPanel3);
    this.grcWarnings.Dock = DockStyle.Top;
    this.grcWarnings.Location = new Point(0, 827);
    this.grcWarnings.Name = "grcWarnings";
    this.grcWarnings.Size = new Size(580, 164);
    this.grcWarnings.TabIndex = 8;
    this.grcWarnings.Text = "Warning Messages";
    this.tableLayoutPanel3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel3.Size = new Size(576, 141);
    this.tableLayoutPanel3.TabIndex = 0;
    this.labelControl12.Location = new Point(13, 13);
    this.labelControl12.Name = "labelControl12";
    this.labelControl12.Size = new Size(279, 13);
    this.labelControl12.TabIndex = 16 /*0x10*/;
    this.labelControl12.Text = "Average measured wall thickness based on CTPlon  (tsam)";
    this.tblpAssessmentResults.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpAssessmentResults.ColumnCount = 2;
    this.tblpAssessmentResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentResults.Controls.Add((Control) this.labelControl19, 0, 2);
    this.tblpAssessmentResults.Controls.Add((Control) this.labelControl20, 0, 3);
    this.tblpAssessmentResults.Controls.Add((Control) this.tableLayoutPanel14, 1, 2);
    this.tblpAssessmentResults.Controls.Add((Control) this.tableLayoutPanel15, 1, 3);
    this.tblpAssessmentResults.Controls.Add((Control) this.tblLMAWPCL2, 1, 4);
    this.tblpAssessmentResults.Controls.Add((Control) this.lbMAWPCL2, 0, 4);
    this.tblpAssessmentResults.Controls.Add((Control) this.lbMAWPLL2, 0, 5);
    this.tblpAssessmentResults.Controls.Add((Control) this.tblLMAWPLL2, 1, 5);
    this.tblpAssessmentResults.Controls.Add((Control) this.tableLayoutPanel9, 1, 0);
    this.tblpAssessmentResults.Controls.Add((Control) this.labelControl12, 0, 0);
    this.tblpAssessmentResults.Controls.Add((Control) this.tableLayoutPanel13, 1, 1);
    this.tblpAssessmentResults.Controls.Add((Control) this.labelControl9, 0, 1);
    this.tblpAssessmentResults.Dock = DockStyle.Fill;
    this.tblpAssessmentResults.Location = new Point(2, 21);
    this.tblpAssessmentResults.Name = "tblpAssessmentResults";
    this.tblpAssessmentResults.Padding = new Padding(10);
    this.tblpAssessmentResults.RowCount = 6;
    this.tblpAssessmentResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentResults.Size = new Size(576, 147);
    this.tblpAssessmentResults.TabIndex = 0;
    this.labelControl19.Location = new Point(13, 57);
    this.labelControl19.Name = "labelControl19";
    this.labelControl19.Size = new Size(201, 13);
    this.labelControl19.TabIndex = 20;
    this.labelControl19.Text = "MAWPC r based on (tsam - FCA) [Level 1]";
    this.labelControl20.Location = new Point(13, 79);
    this.labelControl20.Name = "labelControl20";
    this.labelControl20.Size = new Size(199, 13);
    this.labelControl20.TabIndex = 21;
    this.labelControl20.Text = "MAWPL r based on (tcam - FCA) [Level 1]";
    this.tableLayoutPanel14.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel14.ColumnCount = 2;
    this.tableLayoutPanel14.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel14.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel14.Controls.Add((Control) this.txtMawpCLevel1, 0, 0);
    this.tableLayoutPanel14.Controls.Add((Control) this.umMawpC1, 1, 0);
    this.tableLayoutPanel14.Location = new Point(296, 55);
    this.tableLayoutPanel14.Margin = new Padding(1);
    this.tableLayoutPanel14.Name = "tableLayoutPanel14";
    this.tableLayoutPanel14.RowCount = 1;
    this.tableLayoutPanel14.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel14.Size = new Size(155, 20);
    this.tableLayoutPanel14.TabIndex = 24;
    this.txtMawpCLevel1.Location = new Point(0, 0);
    this.txtMawpCLevel1.Margin = new Padding(0);
    this.txtMawpCLevel1.Name = "txtMawpCLevel1";
    this.txtMawpCLevel1.Properties.ReadOnly = true;
    this.txtMawpCLevel1.Size = new Size(100, 20);
    this.txtMawpCLevel1.TabIndex = 0;
    this.umMawpC1.Location = new Point(103, 3);
    this.umMawpC1.Name = "umMawpC1";
    this.umMawpC1.Size = new Size(41, 13);
    this.umMawpC1.TabIndex = 1;
    this.umMawpC1.Text = "measure";
    this.tableLayoutPanel15.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel15.ColumnCount = 2;
    this.tableLayoutPanel15.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel15.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel15.Controls.Add((Control) this.txtMawpLLevel1, 0, 0);
    this.tableLayoutPanel15.Controls.Add((Control) this.umMawpL1, 1, 0);
    this.tableLayoutPanel15.Location = new Point(296, 77);
    this.tableLayoutPanel15.Margin = new Padding(1);
    this.tableLayoutPanel15.Name = "tableLayoutPanel15";
    this.tableLayoutPanel15.RowCount = 1;
    this.tableLayoutPanel15.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel15.Size = new Size(155, 20);
    this.tableLayoutPanel15.TabIndex = 25;
    this.txtMawpLLevel1.Location = new Point(0, 0);
    this.txtMawpLLevel1.Margin = new Padding(0);
    this.txtMawpLLevel1.Name = "txtMawpLLevel1";
    this.txtMawpLLevel1.Properties.ReadOnly = true;
    this.txtMawpLLevel1.Size = new Size(100, 20);
    this.txtMawpLLevel1.TabIndex = 0;
    this.umMawpL1.Location = new Point(103, 3);
    this.umMawpL1.Name = "umMawpL1";
    this.umMawpL1.Size = new Size(41, 13);
    this.umMawpL1.TabIndex = 1;
    this.umMawpL1.Text = "measure";
    this.tblLMAWPCL2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblLMAWPCL2.ColumnCount = 2;
    this.tblLMAWPCL2.ColumnStyles.Add(new ColumnStyle());
    this.tblLMAWPCL2.ColumnStyles.Add(new ColumnStyle());
    this.tblLMAWPCL2.Controls.Add((Control) this.txtMawpCLevel2, 0, 0);
    this.tblLMAWPCL2.Controls.Add((Control) this.umMawpC2, 1, 0);
    this.tblLMAWPCL2.Location = new Point(296, 99);
    this.tblLMAWPCL2.Margin = new Padding(1);
    this.tblLMAWPCL2.Name = "tblLMAWPCL2";
    this.tblLMAWPCL2.RowCount = 1;
    this.tblLMAWPCL2.RowStyles.Add(new RowStyle());
    this.tblLMAWPCL2.Size = new Size(155, 20);
    this.tblLMAWPCL2.TabIndex = 26;
    this.txtMawpCLevel2.Location = new Point(0, 0);
    this.txtMawpCLevel2.Margin = new Padding(0);
    this.txtMawpCLevel2.Name = "txtMawpCLevel2";
    this.txtMawpCLevel2.Properties.ReadOnly = true;
    this.txtMawpCLevel2.Size = new Size(100, 20);
    this.txtMawpCLevel2.TabIndex = 0;
    this.umMawpC2.Location = new Point(103, 3);
    this.umMawpC2.Name = "umMawpC2";
    this.umMawpC2.Size = new Size(41, 13);
    this.umMawpC2.TabIndex = 1;
    this.umMawpC2.Text = "measure";
    this.lbMAWPCL2.Location = new Point(13, 101);
    this.lbMAWPCL2.Name = "lbMAWPCL2";
    this.lbMAWPCL2.Size = new Size(230, 13);
    this.lbMAWPCL2.TabIndex = 22;
    this.lbMAWPCL2.Text = "MAWPC r based on (tsam - FCA)/RSFa [Level 2]";
    this.lbMAWPLL2.Location = new Point(13, 123);
    this.lbMAWPLL2.Name = "lbMAWPLL2";
    this.lbMAWPLL2.Size = new Size(249, 13);
    this.lbMAWPLL2.TabIndex = 23;
    this.lbMAWPLL2.Text = "MAWPL r based on (tcam - tsl - FCA)/RSFa [Level 2]";
    this.tblLMAWPLL2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblLMAWPLL2.ColumnCount = 2;
    this.tblLMAWPLL2.ColumnStyles.Add(new ColumnStyle());
    this.tblLMAWPLL2.ColumnStyles.Add(new ColumnStyle());
    this.tblLMAWPLL2.Controls.Add((Control) this.txtMawpLLevel2, 0, 0);
    this.tblLMAWPLL2.Controls.Add((Control) this.umMawpL2, 1, 0);
    this.tblLMAWPLL2.Location = new Point(296, 121);
    this.tblLMAWPLL2.Margin = new Padding(1);
    this.tblLMAWPLL2.Name = "tblLMAWPLL2";
    this.tblLMAWPLL2.RowCount = 1;
    this.tblLMAWPLL2.RowStyles.Add(new RowStyle());
    this.tblLMAWPLL2.Size = new Size(155, 20);
    this.tblLMAWPLL2.TabIndex = 27;
    this.txtMawpLLevel2.Location = new Point(0, 0);
    this.txtMawpLLevel2.Margin = new Padding(0);
    this.txtMawpLLevel2.Name = "txtMawpLLevel2";
    this.txtMawpLLevel2.Properties.ReadOnly = true;
    this.txtMawpLLevel2.Size = new Size(100, 20);
    this.txtMawpLLevel2.TabIndex = 0;
    this.umMawpL2.Location = new Point(103, 3);
    this.umMawpL2.Name = "umMawpL2";
    this.umMawpL2.Size = new Size(41, 13);
    this.umMawpL2.TabIndex = 1;
    this.umMawpL2.Text = "measure";
    this.tableLayoutPanel13.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel13.ColumnCount = 2;
    this.tableLayoutPanel13.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel13.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel13.Controls.Add((Control) this.txtWallThickTcam, 0, 0);
    this.tableLayoutPanel13.Controls.Add((Control) this.umWallThickTcam, 1, 0);
    this.tableLayoutPanel13.Location = new Point(296, 33);
    this.tableLayoutPanel13.Margin = new Padding(1);
    this.tableLayoutPanel13.Name = "tableLayoutPanel13";
    this.tableLayoutPanel13.RowCount = 1;
    this.tableLayoutPanel13.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel13.Size = new Size(155, 20);
    this.tableLayoutPanel13.TabIndex = 19;
    this.txtWallThickTcam.Location = new Point(0, 0);
    this.txtWallThickTcam.Margin = new Padding(0);
    this.txtWallThickTcam.Name = "txtWallThickTcam";
    this.txtWallThickTcam.Properties.ReadOnly = true;
    this.txtWallThickTcam.Size = new Size(100, 20);
    this.txtWallThickTcam.TabIndex = 0;
    this.umWallThickTcam.Location = new Point(103, 3);
    this.umWallThickTcam.Name = "umWallThickTcam";
    this.umWallThickTcam.Size = new Size(41, 13);
    this.umWallThickTcam.TabIndex = 1;
    this.umWallThickTcam.Text = "measure";
    this.labelControl9.Location = new Point(13, 35);
    this.labelControl9.Name = "labelControl9";
    this.labelControl9.Size = new Size(276, 13);
    this.labelControl9.TabIndex = 18;
    this.labelControl9.Text = "Average measured wall thickness based on CTPcir  (tcam)";
    this.grcAssessmentResults.Controls.Add((Control) this.tblpAssessmentResults);
    this.grcAssessmentResults.Dock = DockStyle.Top;
    this.grcAssessmentResults.Location = new Point(0, 337);
    this.grcAssessmentResults.Name = "grcAssessmentResults";
    this.grcAssessmentResults.Size = new Size(580, 170);
    this.grcAssessmentResults.TabIndex = 7;
    this.grcAssessmentResults.Text = "Assessment Results";
    this.grcIntermediateResults.Controls.Add((Control) this.tblpIntermediateResults);
    this.grcIntermediateResults.Dock = DockStyle.Top;
    this.grcIntermediateResults.Location = new Point(0, (int) sbyte.MaxValue);
    this.grcIntermediateResults.Name = "grcIntermediateResults";
    this.grcIntermediateResults.Size = new Size(580, 210);
    this.grcIntermediateResults.TabIndex = 6;
    this.grcIntermediateResults.Text = "Intermediate Results";
    this.tblpIntermediateResults.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpIntermediateResults.ColumnCount = 2;
    this.tblpIntermediateResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpIntermediateResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel4, 1, 1);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl1, 0, 1);
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel2, 1, 0);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl2, 0, 0);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl4, 0, 5);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl5, 0, 2);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl6, 0, 3);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl7, 0, 4);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl8, 0, 6);
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel5, 1, 2);
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel8, 1, 3);
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel10, 1, 4);
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel11, 1, 5);
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel12, 1, 6);
    this.tblpIntermediateResults.Dock = DockStyle.Fill;
    this.tblpIntermediateResults.Location = new Point(2, 21);
    this.tblpIntermediateResults.Name = "tblpIntermediateResults";
    this.tblpIntermediateResults.Padding = new Padding(10);
    this.tblpIntermediateResults.RowCount = 7;
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.Size = new Size(576, 187);
    this.tblpIntermediateResults.TabIndex = 0;
    this.tableLayoutPanel4.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel4.ColumnCount = 2;
    this.tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel4.Controls.Add((Control) this.umTmin, 1, 0);
    this.tableLayoutPanel4.Controls.Add((Control) this.txtTmin, 0, 0);
    this.tableLayoutPanel4.Location = new Point(389, 33);
    this.tableLayoutPanel4.Margin = new Padding(1);
    this.tableLayoutPanel4.Name = "tableLayoutPanel4";
    this.tableLayoutPanel4.RowCount = 1;
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel4.Size = new Size(174, 20);
    this.tableLayoutPanel4.TabIndex = 19;
    this.umTmin.Location = new Point(103, 3);
    this.umTmin.Name = "umTmin";
    this.umTmin.Size = new Size(41, 13);
    this.umTmin.TabIndex = 1;
    this.umTmin.Text = "measure";
    this.txtTmin.Location = new Point(0, 0);
    this.txtTmin.Margin = new Padding(0);
    this.txtTmin.Name = "txtTmin";
    this.txtTmin.Properties.ReadOnly = true;
    this.txtTmin.Size = new Size(100, 20);
    this.txtTmin.TabIndex = 0;
    this.labelControl1.Location = new Point(13, 35);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(189, 13);
    this.labelControl1.TabIndex = 12;
    this.labelControl1.Text = "Minimum Required Wall Thickness (tmin)";
    this.tableLayoutPanel2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel2.ColumnCount = 2;
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.Controls.Add((Control) this.umAllowableStrength, 1, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.txtAllowableStrength, 0, 0);
    this.tableLayoutPanel2.Location = new Point(389, 11);
    this.tableLayoutPanel2.Margin = new Padding(1);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.RowCount = 1;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel2.Size = new Size(174, 20);
    this.tableLayoutPanel2.TabIndex = 11;
    this.umAllowableStrength.Location = new Point(103, 3);
    this.umAllowableStrength.Name = "umAllowableStrength";
    this.umAllowableStrength.Size = new Size(41, 13);
    this.umAllowableStrength.TabIndex = 1;
    this.umAllowableStrength.Text = "measure";
    this.txtAllowableStrength.Location = new Point(0, 0);
    this.txtAllowableStrength.Margin = new Padding(0);
    this.txtAllowableStrength.Name = "txtAllowableStrength";
    this.txtAllowableStrength.Properties.ReadOnly = true;
    this.txtAllowableStrength.Size = new Size(100, 20);
    this.txtAllowableStrength.TabIndex = 0;
    this.labelControl4.Location = new Point(13, 123);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(61, 13);
    this.labelControl4.TabIndex = 14;
    this.labelControl4.Text = "Parameter Q";
    this.labelControl5.Location = new Point(13, 57);
    this.labelControl5.Name = "labelControl5";
    this.labelControl5.Size = new Size(139, 13);
    this.labelControl5.TabIndex = 15;
    this.labelControl5.Text = "Minimum Measured Thickness";
    this.labelControl6.Location = new Point(13, 79);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(372, 13);
    this.labelControl6.TabIndex = 16 /*0x10*/;
    this.labelControl6.Text = "Uniform thickness away from the local metal loss location (trd  = tnom - LOSS)";
    this.labelControl7.Location = new Point(13, 101);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(148, 13);
    this.labelControl7.TabIndex = 17;
    this.labelControl7.Text = "Remaining Thickness Ratio (Rt)";
    this.labelControl8.Location = new Point(13, 145);
    this.labelControl8.Name = "labelControl8";
    this.labelControl8.Size = new Size(164, 13);
    this.labelControl8.TabIndex = 18;
    this.labelControl8.Text = "Length for thickness averaging (L)";
    this.tableLayoutPanel5.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel5.ColumnCount = 2;
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel5.Controls.Add((Control) this.umMinMeasuredThickness, 1, 0);
    this.tableLayoutPanel5.Controls.Add((Control) this.txtMinMeasuredThickness, 0, 0);
    this.tableLayoutPanel5.Location = new Point(389, 55);
    this.tableLayoutPanel5.Margin = new Padding(1);
    this.tableLayoutPanel5.Name = "tableLayoutPanel5";
    this.tableLayoutPanel5.RowCount = 1;
    this.tableLayoutPanel5.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel5.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel5.Size = new Size(174, 20);
    this.tableLayoutPanel5.TabIndex = 20;
    this.umMinMeasuredThickness.Location = new Point(103, 3);
    this.umMinMeasuredThickness.Name = "umMinMeasuredThickness";
    this.umMinMeasuredThickness.Size = new Size(41, 13);
    this.umMinMeasuredThickness.TabIndex = 1;
    this.umMinMeasuredThickness.Text = "measure";
    this.txtMinMeasuredThickness.Location = new Point(0, 0);
    this.txtMinMeasuredThickness.Margin = new Padding(0);
    this.txtMinMeasuredThickness.Name = "txtMinMeasuredThickness";
    this.txtMinMeasuredThickness.Properties.ReadOnly = true;
    this.txtMinMeasuredThickness.Size = new Size(100, 20);
    this.txtMinMeasuredThickness.TabIndex = 0;
    this.tableLayoutPanel8.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel8.ColumnCount = 2;
    this.tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel8.Controls.Add((Control) this.umTrd, 1, 0);
    this.tableLayoutPanel8.Controls.Add((Control) this.txtTrd, 0, 0);
    this.tableLayoutPanel8.Location = new Point(389, 77);
    this.tableLayoutPanel8.Margin = new Padding(1);
    this.tableLayoutPanel8.Name = "tableLayoutPanel8";
    this.tableLayoutPanel8.RowCount = 1;
    this.tableLayoutPanel8.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel8.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel8.Size = new Size(174, 20);
    this.tableLayoutPanel8.TabIndex = 22;
    this.umTrd.Location = new Point(103, 3);
    this.umTrd.Name = "umTrd";
    this.umTrd.Size = new Size(41, 13);
    this.umTrd.TabIndex = 1;
    this.umTrd.Text = "measure";
    this.txtTrd.Location = new Point(0, 0);
    this.txtTrd.Margin = new Padding(0);
    this.txtTrd.Name = "txtTrd";
    this.txtTrd.Properties.ReadOnly = true;
    this.txtTrd.Size = new Size(100, 20);
    this.txtTrd.TabIndex = 0;
    this.tableLayoutPanel10.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel10.ColumnCount = 2;
    this.tableLayoutPanel10.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel10.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel10.Controls.Add((Control) this.txtRemainingThickRationRT, 0, 0);
    this.tableLayoutPanel10.Location = new Point(389, 99);
    this.tableLayoutPanel10.Margin = new Padding(1);
    this.tableLayoutPanel10.Name = "tableLayoutPanel10";
    this.tableLayoutPanel10.RowCount = 1;
    this.tableLayoutPanel10.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel10.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel10.Size = new Size(174, 20);
    this.tableLayoutPanel10.TabIndex = 23;
    this.txtRemainingThickRationRT.Location = new Point(0, 0);
    this.txtRemainingThickRationRT.Margin = new Padding(0);
    this.txtRemainingThickRationRT.Name = "txtRemainingThickRationRT";
    this.txtRemainingThickRationRT.Properties.ReadOnly = true;
    this.txtRemainingThickRationRT.Size = new Size(100, 20);
    this.txtRemainingThickRationRT.TabIndex = 0;
    this.tableLayoutPanel11.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel11.ColumnCount = 2;
    this.tableLayoutPanel11.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel11.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel11.Controls.Add((Control) this.txtParameterQ, 0, 0);
    this.tableLayoutPanel11.Location = new Point(389, 121);
    this.tableLayoutPanel11.Margin = new Padding(1);
    this.tableLayoutPanel11.Name = "tableLayoutPanel11";
    this.tableLayoutPanel11.RowCount = 1;
    this.tableLayoutPanel11.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel11.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel11.Size = new Size(174, 20);
    this.tableLayoutPanel11.TabIndex = 24;
    this.txtParameterQ.Location = new Point(0, 0);
    this.txtParameterQ.Margin = new Padding(0);
    this.txtParameterQ.Name = "txtParameterQ";
    this.txtParameterQ.Properties.ReadOnly = true;
    this.txtParameterQ.Size = new Size(100, 20);
    this.txtParameterQ.TabIndex = 0;
    this.tableLayoutPanel12.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel12.ColumnCount = 2;
    this.tableLayoutPanel12.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel12.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel12.Controls.Add((Control) this.umThickAveragingLengthL, 1, 0);
    this.tableLayoutPanel12.Controls.Add((Control) this.txtThickAveragingLengthL, 0, 0);
    this.tableLayoutPanel12.Location = new Point(389, 143);
    this.tableLayoutPanel12.Margin = new Padding(1);
    this.tableLayoutPanel12.Name = "tableLayoutPanel12";
    this.tableLayoutPanel12.RowCount = 1;
    this.tableLayoutPanel12.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel12.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel12.Size = new Size(174, 20);
    this.tableLayoutPanel12.TabIndex = 25;
    this.umThickAveragingLengthL.Location = new Point(103, 3);
    this.umThickAveragingLengthL.Name = "umThickAveragingLengthL";
    this.umThickAveragingLengthL.Size = new Size(41, 13);
    this.umThickAveragingLengthL.TabIndex = 1;
    this.umThickAveragingLengthL.Text = "measure";
    this.txtThickAveragingLengthL.Location = new Point(0, 0);
    this.txtThickAveragingLengthL.Margin = new Padding(0);
    this.txtThickAveragingLengthL.Name = "txtThickAveragingLengthL";
    this.txtThickAveragingLengthL.Properties.ReadOnly = true;
    this.txtThickAveragingLengthL.Size = new Size(100, 20);
    this.txtThickAveragingLengthL.TabIndex = 0;
    this.grcAssessmentCriteriaL1.Controls.Add((Control) this.tblpAssessmentCriteriaL1);
    this.grcAssessmentCriteriaL1.Dock = DockStyle.Top;
    this.grcAssessmentCriteriaL1.Location = new Point(0, 507);
    this.grcAssessmentCriteriaL1.Name = "grcAssessmentCriteriaL1";
    this.grcAssessmentCriteriaL1.Size = new Size(580, 100);
    this.grcAssessmentCriteriaL1.TabIndex = 9;
    this.grcAssessmentCriteriaL1.Text = "Level 1 Assessment Criteria";
    this.tblpAssessmentCriteriaL1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpAssessmentCriteriaL1.ColumnCount = 2;
    this.tblpAssessmentCriteriaL1.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentCriteriaL1.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.labelControl29, 0, 0);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.tableLayoutPanel20, 1, 0);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.tableLayoutPanel21, 1, 1);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.labelControl32, 0, 1);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.tableLayoutPanel19, 1, 2);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.labelControl31, 0, 2);
    this.tblpAssessmentCriteriaL1.Dock = DockStyle.Fill;
    this.tblpAssessmentCriteriaL1.Location = new Point(2, 21);
    this.tblpAssessmentCriteriaL1.Name = "tblpAssessmentCriteriaL1";
    this.tblpAssessmentCriteriaL1.Padding = new Padding(10);
    this.tblpAssessmentCriteriaL1.RowCount = 3;
    this.tblpAssessmentCriteriaL1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL1.Size = new Size(576, 77);
    this.tblpAssessmentCriteriaL1.TabIndex = 1;
    this.labelControl29.Location = new Point(13, 13);
    this.labelControl29.Name = "labelControl29";
    this.labelControl29.Size = new Size(140, 13);
    this.labelControl29.TabIndex = 16 /*0x10*/;
    this.labelControl29.Text = "Average Measured Thickness";
    this.tableLayoutPanel20.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel20.ColumnCount = 2;
    this.tableLayoutPanel20.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel20.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel20.Controls.Add((Control) this.txtAverageMThicknessL1, 0, 0);
    this.tableLayoutPanel20.Location = new Point(196, 11);
    this.tableLayoutPanel20.Margin = new Padding(1);
    this.tableLayoutPanel20.Name = "tableLayoutPanel20";
    this.tableLayoutPanel20.RowCount = 1;
    this.tableLayoutPanel20.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel20.Size = new Size(155, 20);
    this.tableLayoutPanel20.TabIndex = 17;
    this.txtAverageMThicknessL1.Location = new Point(0, 0);
    this.txtAverageMThicknessL1.Margin = new Padding(0);
    this.txtAverageMThicknessL1.Name = "txtAverageMThicknessL1";
    this.txtAverageMThicknessL1.Properties.ReadOnly = true;
    this.txtAverageMThicknessL1.Size = new Size(100, 20);
    this.txtAverageMThicknessL1.TabIndex = 0;
    this.tableLayoutPanel21.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel21.ColumnCount = 2;
    this.tableLayoutPanel21.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel21.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel21.Controls.Add((Control) this.txtMinMThicknessL1, 0, 0);
    this.tableLayoutPanel21.Location = new Point(196, 33);
    this.tableLayoutPanel21.Margin = new Padding(1);
    this.tableLayoutPanel21.Name = "tableLayoutPanel21";
    this.tableLayoutPanel21.RowCount = 1;
    this.tableLayoutPanel21.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel21.Size = new Size(155, 20);
    this.tableLayoutPanel21.TabIndex = 24;
    this.txtMinMThicknessL1.Location = new Point(0, 0);
    this.txtMinMThicknessL1.Margin = new Padding(0);
    this.txtMinMThicknessL1.Name = "txtMinMThicknessL1";
    this.txtMinMThicknessL1.Properties.ReadOnly = true;
    this.txtMinMThicknessL1.Size = new Size(100, 20);
    this.txtMinMThicknessL1.TabIndex = 0;
    this.labelControl32.Location = new Point(13, 35);
    this.labelControl32.Name = "labelControl32";
    this.labelControl32.Size = new Size(139, 13);
    this.labelControl32.TabIndex = 20;
    this.labelControl32.Text = "Minimum Measured Thickness";
    this.tableLayoutPanel19.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel19.ColumnCount = 2;
    this.tableLayoutPanel19.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel19.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel19.Controls.Add((Control) this.txtMaxAllowWorkPressureL1, 0, 0);
    this.tableLayoutPanel19.Location = new Point(196, 55);
    this.tableLayoutPanel19.Margin = new Padding(1);
    this.tableLayoutPanel19.Name = "tableLayoutPanel19";
    this.tableLayoutPanel19.RowCount = 1;
    this.tableLayoutPanel19.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel19.Size = new Size(155, 20);
    this.tableLayoutPanel19.TabIndex = 19;
    this.txtMaxAllowWorkPressureL1.Location = new Point(0, 0);
    this.txtMaxAllowWorkPressureL1.Margin = new Padding(0);
    this.txtMaxAllowWorkPressureL1.Name = "txtMaxAllowWorkPressureL1";
    this.txtMaxAllowWorkPressureL1.Properties.ReadOnly = true;
    this.txtMaxAllowWorkPressureL1.Size = new Size(100, 20);
    this.txtMaxAllowWorkPressureL1.TabIndex = 0;
    this.labelControl31.Location = new Point(13, 57);
    this.labelControl31.Name = "labelControl31";
    this.labelControl31.Size = new Size(179, 13);
    this.labelControl31.TabIndex = 18;
    this.labelControl31.Text = "Maximum Allowable Working Pressure";
    this.grcAssessmentConclusionL1.Controls.Add((Control) this.tableLayoutPanel22);
    this.grcAssessmentConclusionL1.Dock = DockStyle.Top;
    this.grcAssessmentConclusionL1.Location = new Point(0, 607);
    this.grcAssessmentConclusionL1.Name = "grcAssessmentConclusionL1";
    this.grcAssessmentConclusionL1.Size = new Size(580, 60);
    this.grcAssessmentConclusionL1.TabIndex = 10;
    this.grcAssessmentConclusionL1.Text = "Level1 Assessment Conclusion";
    this.tableLayoutPanel22.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel22.ColumnCount = 2;
    this.tableLayoutPanel22.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel22.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel22.Controls.Add((Control) this.lbConclusionLevel1, 0, 0);
    this.tableLayoutPanel22.Dock = DockStyle.Fill;
    this.tableLayoutPanel22.Location = new Point(2, 21);
    this.tableLayoutPanel22.Name = "tableLayoutPanel22";
    this.tableLayoutPanel22.Padding = new Padding(10);
    this.tableLayoutPanel22.RowCount = 1;
    this.tableLayoutPanel22.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel22.Size = new Size(576, 37);
    this.tableLayoutPanel22.TabIndex = 0;
    this.lbConclusionLevel1.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold);
    this.lbConclusionLevel1.Location = new Point(13, 13);
    this.lbConclusionLevel1.Name = "lbConclusionLevel1";
    this.lbConclusionLevel1.Size = new Size(157, 13);
    this.lbConclusionLevel1.TabIndex = 16 /*0x10*/;
    this.lbConclusionLevel1.Text = "The Level 1 Assessment is ..";
    this.grcAssessmentCriteriaL2.Controls.Add((Control) this.tblpAssessmentCriteriaL2);
    this.grcAssessmentCriteriaL2.Dock = DockStyle.Top;
    this.grcAssessmentCriteriaL2.Location = new Point(0, 667);
    this.grcAssessmentCriteriaL2.Name = "grcAssessmentCriteriaL2";
    this.grcAssessmentCriteriaL2.Size = new Size(580, 100);
    this.grcAssessmentCriteriaL2.TabIndex = 11;
    this.grcAssessmentCriteriaL2.Text = "Level 2 Assessment Criteria";
    this.tblpAssessmentCriteriaL2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpAssessmentCriteriaL2.ColumnCount = 2;
    this.tblpAssessmentCriteriaL2.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentCriteriaL2.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.labelControl34, 0, 0);
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.tableLayoutPanel25, 1, 0);
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.labelControl37, 0, 2);
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.labelControl38, 0, 1);
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.tableLayoutPanel26, 1, 1);
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.tableLayoutPanel24, 1, 2);
    this.tblpAssessmentCriteriaL2.Dock = DockStyle.Fill;
    this.tblpAssessmentCriteriaL2.Location = new Point(2, 21);
    this.tblpAssessmentCriteriaL2.Name = "tblpAssessmentCriteriaL2";
    this.tblpAssessmentCriteriaL2.Padding = new Padding(10);
    this.tblpAssessmentCriteriaL2.RowCount = 3;
    this.tblpAssessmentCriteriaL2.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL2.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL2.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL2.Size = new Size(576, 77);
    this.tblpAssessmentCriteriaL2.TabIndex = 1;
    this.labelControl34.Location = new Point(13, 13);
    this.labelControl34.Name = "labelControl34";
    this.labelControl34.Size = new Size(140, 13);
    this.labelControl34.TabIndex = 16 /*0x10*/;
    this.labelControl34.Text = "Average Measured Thickness";
    this.tableLayoutPanel25.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel25.ColumnCount = 2;
    this.tableLayoutPanel25.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel25.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel25.Controls.Add((Control) this.txtAverageMThicknessL2, 0, 0);
    this.tableLayoutPanel25.Location = new Point(196, 11);
    this.tableLayoutPanel25.Margin = new Padding(1);
    this.tableLayoutPanel25.Name = "tableLayoutPanel25";
    this.tableLayoutPanel25.RowCount = 1;
    this.tableLayoutPanel25.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel25.Size = new Size(155, 20);
    this.tableLayoutPanel25.TabIndex = 17;
    this.txtAverageMThicknessL2.Location = new Point(0, 0);
    this.txtAverageMThicknessL2.Margin = new Padding(0);
    this.txtAverageMThicknessL2.Name = "txtAverageMThicknessL2";
    this.txtAverageMThicknessL2.Properties.ReadOnly = true;
    this.txtAverageMThicknessL2.Size = new Size(100, 20);
    this.txtAverageMThicknessL2.TabIndex = 0;
    this.labelControl37.Location = new Point(13, 57);
    this.labelControl37.Name = "labelControl37";
    this.labelControl37.Size = new Size(179, 13);
    this.labelControl37.TabIndex = 18;
    this.labelControl37.Text = "Maximum Allowable Working Pressure";
    this.labelControl38.Location = new Point(13, 35);
    this.labelControl38.Name = "labelControl38";
    this.labelControl38.Size = new Size(139, 13);
    this.labelControl38.TabIndex = 20;
    this.labelControl38.Text = "Minimum Measured Thickness";
    this.tableLayoutPanel26.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel26.ColumnCount = 2;
    this.tableLayoutPanel26.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel26.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel26.Controls.Add((Control) this.txtMinMThicknessL2, 0, 0);
    this.tableLayoutPanel26.Location = new Point(196, 33);
    this.tableLayoutPanel26.Margin = new Padding(1);
    this.tableLayoutPanel26.Name = "tableLayoutPanel26";
    this.tableLayoutPanel26.RowCount = 1;
    this.tableLayoutPanel26.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel26.Size = new Size(155, 20);
    this.tableLayoutPanel26.TabIndex = 24;
    this.txtMinMThicknessL2.Location = new Point(0, 0);
    this.txtMinMThicknessL2.Margin = new Padding(0);
    this.txtMinMThicknessL2.Name = "txtMinMThicknessL2";
    this.txtMinMThicknessL2.Properties.ReadOnly = true;
    this.txtMinMThicknessL2.Size = new Size(100, 20);
    this.txtMinMThicknessL2.TabIndex = 0;
    this.tableLayoutPanel24.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel24.ColumnCount = 2;
    this.tableLayoutPanel24.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel24.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel24.Controls.Add((Control) this.txtMaxAllowWorkPressureL2, 0, 0);
    this.tableLayoutPanel24.Location = new Point(196, 55);
    this.tableLayoutPanel24.Margin = new Padding(1);
    this.tableLayoutPanel24.Name = "tableLayoutPanel24";
    this.tableLayoutPanel24.RowCount = 1;
    this.tableLayoutPanel24.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel24.Size = new Size(155, 20);
    this.tableLayoutPanel24.TabIndex = 19;
    this.txtMaxAllowWorkPressureL2.Location = new Point(0, 0);
    this.txtMaxAllowWorkPressureL2.Margin = new Padding(0);
    this.txtMaxAllowWorkPressureL2.Name = "txtMaxAllowWorkPressureL2";
    this.txtMaxAllowWorkPressureL2.Properties.ReadOnly = true;
    this.txtMaxAllowWorkPressureL2.Size = new Size(100, 20);
    this.txtMaxAllowWorkPressureL2.TabIndex = 0;
    this.grcAssessmentConclusionL2.Controls.Add((Control) this.tableLayoutPanel27);
    this.grcAssessmentConclusionL2.Dock = DockStyle.Top;
    this.grcAssessmentConclusionL2.Location = new Point(0, 767 /*0x02FF*/);
    this.grcAssessmentConclusionL2.Name = "grcAssessmentConclusionL2";
    this.grcAssessmentConclusionL2.Size = new Size(580, 60);
    this.grcAssessmentConclusionL2.TabIndex = 12;
    this.grcAssessmentConclusionL2.Text = "Level2 Assessment Conclusion";
    this.tableLayoutPanel27.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel27.ColumnCount = 2;
    this.tableLayoutPanel27.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel27.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel27.Controls.Add((Control) this.lbConclusionLevel2, 0, 0);
    this.tableLayoutPanel27.Dock = DockStyle.Fill;
    this.tableLayoutPanel27.Location = new Point(2, 21);
    this.tableLayoutPanel27.Name = "tableLayoutPanel27";
    this.tableLayoutPanel27.Padding = new Padding(10);
    this.tableLayoutPanel27.RowCount = 1;
    this.tableLayoutPanel27.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel27.Size = new Size(576, 37);
    this.tableLayoutPanel27.TabIndex = 0;
    this.lbConclusionLevel2.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold);
    this.lbConclusionLevel2.Location = new Point(13, 13);
    this.lbConclusionLevel2.Name = "lbConclusionLevel2";
    this.lbConclusionLevel2.Size = new Size(157, 13);
    this.lbConclusionLevel2.TabIndex = 16 /*0x10*/;
    this.lbConclusionLevel2.Text = "The Level 2 Assessment is ..";
    this.grcInspectionDataSummary.Controls.Add((Control) this.tableLayoutPanel1);
    this.grcInspectionDataSummary.Dock = DockStyle.Top;
    this.grcInspectionDataSummary.Location = new Point(0, 0);
    this.grcInspectionDataSummary.Name = "grcInspectionDataSummary";
    this.grcInspectionDataSummary.Size = new Size(580, (int) sbyte.MaxValue);
    this.grcInspectionDataSummary.TabIndex = 14;
    this.grcInspectionDataSummary.Text = "Inspection Data Summary";
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 2;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel6, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl11, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel16, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl14, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl15, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl17, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel17, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel18, 1, 3);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(2, 21);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 4;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.Size = new Size(576, 104);
    this.tableLayoutPanel1.TabIndex = 0;
    this.tableLayoutPanel6.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel6.ColumnCount = 2;
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel6.Controls.Add((Control) this.umLm, 1, 0);
    this.tableLayoutPanel6.Controls.Add((Control) this.txtLm, 0, 0);
    this.tableLayoutPanel6.Location = new Point(329, 33);
    this.tableLayoutPanel6.Margin = new Padding(1);
    this.tableLayoutPanel6.Name = "tableLayoutPanel6";
    this.tableLayoutPanel6.RowCount = 1;
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel6.Size = new Size(174, 20);
    this.tableLayoutPanel6.TabIndex = 19;
    this.umLm.Location = new Point(103, 3);
    this.umLm.Name = "umLm";
    this.umLm.Size = new Size(41, 13);
    this.umLm.TabIndex = 1;
    this.umLm.Text = "measure";
    this.txtLm.Location = new Point(0, 0);
    this.txtLm.Margin = new Padding(0);
    this.txtLm.Name = "txtLm";
    this.txtLm.Properties.ReadOnly = true;
    this.txtLm.Size = new Size(100, 20);
    this.txtLm.TabIndex = 0;
    this.labelControl11.Location = new Point(13, 35);
    this.labelControl11.Name = "labelControl11";
    this.labelControl11.Size = new Size(312, 13);
    this.labelControl11.TabIndex = 12;
    this.labelControl11.Text = "Spacing of thickness readings in the circumferential direction (Lm)";
    this.tableLayoutPanel16.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel16.ColumnCount = 2;
    this.tableLayoutPanel16.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel16.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel16.Controls.Add((Control) this.umLc, 1, 0);
    this.tableLayoutPanel16.Controls.Add((Control) this.txtLc, 0, 0);
    this.tableLayoutPanel16.Location = new Point(329, 11);
    this.tableLayoutPanel16.Margin = new Padding(1);
    this.tableLayoutPanel16.Name = "tableLayoutPanel16";
    this.tableLayoutPanel16.RowCount = 1;
    this.tableLayoutPanel16.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel16.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel16.Size = new Size(174, 20);
    this.tableLayoutPanel16.TabIndex = 11;
    this.umLc.Location = new Point(103, 3);
    this.umLc.Name = "umLc";
    this.umLc.Size = new Size(41, 13);
    this.umLc.TabIndex = 1;
    this.umLc.Text = "measure";
    this.txtLc.Location = new Point(0, 0);
    this.txtLc.Margin = new Padding(0);
    this.txtLc.Name = "txtLc";
    this.txtLc.Properties.ReadOnly = true;
    this.txtLc.Size = new Size(100, 20);
    this.txtLc.TabIndex = 0;
    this.labelControl14.Location = new Point(13, 13);
    this.labelControl14.Name = "labelControl14";
    this.labelControl14.Size = new Size(293, 13);
    this.labelControl14.TabIndex = 10;
    this.labelControl14.Text = "Spacing of thickness readings in the longitudinal direction (Lc)";
    this.labelControl15.Location = new Point(13, 57);
    this.labelControl15.Name = "labelControl15";
    this.labelControl15.Size = new Size(242, 13);
    this.labelControl15.TabIndex = 13;
    this.labelControl15.Text = "Extent of metal loss in the longitudinal direction (s)";
    this.labelControl17.Location = new Point(13, 79);
    this.labelControl17.Name = "labelControl17";
    this.labelControl17.Size = new Size(258, 13);
    this.labelControl17.TabIndex = 15;
    this.labelControl17.Text = "Extent of metal loss in the circumferential direction (c)";
    this.tableLayoutPanel17.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel17.ColumnCount = 2;
    this.tableLayoutPanel17.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel17.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel17.Controls.Add((Control) this.umS, 1, 0);
    this.tableLayoutPanel17.Controls.Add((Control) this.txtS, 0, 0);
    this.tableLayoutPanel17.Location = new Point(329, 55);
    this.tableLayoutPanel17.Margin = new Padding(1);
    this.tableLayoutPanel17.Name = "tableLayoutPanel17";
    this.tableLayoutPanel17.RowCount = 1;
    this.tableLayoutPanel17.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel17.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel17.Size = new Size(174, 20);
    this.tableLayoutPanel17.TabIndex = 21;
    this.umS.Location = new Point(103, 3);
    this.umS.Name = "umS";
    this.umS.Size = new Size(41, 13);
    this.umS.TabIndex = 1;
    this.umS.Text = "measure";
    this.txtS.Location = new Point(0, 0);
    this.txtS.Margin = new Padding(0);
    this.txtS.Name = "txtS";
    this.txtS.Properties.ReadOnly = true;
    this.txtS.Size = new Size(100, 20);
    this.txtS.TabIndex = 0;
    this.tableLayoutPanel18.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel18.ColumnCount = 2;
    this.tableLayoutPanel18.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel18.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel18.Controls.Add((Control) this.umC, 1, 0);
    this.tableLayoutPanel18.Controls.Add((Control) this.txtC, 0, 0);
    this.tableLayoutPanel18.Location = new Point(329, 77);
    this.tableLayoutPanel18.Margin = new Padding(1);
    this.tableLayoutPanel18.Name = "tableLayoutPanel18";
    this.tableLayoutPanel18.RowCount = 1;
    this.tableLayoutPanel18.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel18.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel18.Size = new Size(174, 20);
    this.tableLayoutPanel18.TabIndex = 20;
    this.umC.Location = new Point(103, 3);
    this.umC.Name = "umC";
    this.umC.Size = new Size(41, 13);
    this.umC.TabIndex = 1;
    this.umC.Text = "measure";
    this.txtC.Location = new Point(0, 0);
    this.txtC.Margin = new Padding(0);
    this.txtC.Name = "txtC";
    this.txtC.Properties.ReadOnly = true;
    this.txtC.Size = new Size(100, 20);
    this.txtC.TabIndex = 0;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.Controls.Add((Control) this.grcWarnings);
    this.Controls.Add((Control) this.grcAssessmentConclusionL2);
    this.Controls.Add((Control) this.grcAssessmentCriteriaL2);
    this.Controls.Add((Control) this.grcAssessmentConclusionL1);
    this.Controls.Add((Control) this.grcAssessmentCriteriaL1);
    this.Controls.Add((Control) this.grcAssessmentResults);
    this.Controls.Add((Control) this.grcIntermediateResults);
    this.Controls.Add((Control) this.grcInspectionDataSummary);
    this.Name = nameof (vwResult);
    this.Size = new Size(580, 991);
    this.Load += new EventHandler(this.vwResult_Load);
    this.tableLayoutPanel9.ResumeLayout(false);
    this.tableLayoutPanel9.PerformLayout();
    this.txtWallThickTsam.Properties.EndInit();
    this.txtWarningMessages.Properties.EndInit();
    this.grcWarnings.EndInit();
    this.grcWarnings.ResumeLayout(false);
    this.tableLayoutPanel3.ResumeLayout(false);
    this.tblpAssessmentResults.ResumeLayout(false);
    this.tblpAssessmentResults.PerformLayout();
    this.tableLayoutPanel14.ResumeLayout(false);
    this.tableLayoutPanel14.PerformLayout();
    this.txtMawpCLevel1.Properties.EndInit();
    this.tableLayoutPanel15.ResumeLayout(false);
    this.tableLayoutPanel15.PerformLayout();
    this.txtMawpLLevel1.Properties.EndInit();
    this.tblLMAWPCL2.ResumeLayout(false);
    this.tblLMAWPCL2.PerformLayout();
    this.txtMawpCLevel2.Properties.EndInit();
    this.tblLMAWPLL2.ResumeLayout(false);
    this.tblLMAWPLL2.PerformLayout();
    this.txtMawpLLevel2.Properties.EndInit();
    this.tableLayoutPanel13.ResumeLayout(false);
    this.tableLayoutPanel13.PerformLayout();
    this.txtWallThickTcam.Properties.EndInit();
    this.grcAssessmentResults.EndInit();
    this.grcAssessmentResults.ResumeLayout(false);
    this.grcIntermediateResults.EndInit();
    this.grcIntermediateResults.ResumeLayout(false);
    this.tblpIntermediateResults.ResumeLayout(false);
    this.tblpIntermediateResults.PerformLayout();
    this.tableLayoutPanel4.ResumeLayout(false);
    this.tableLayoutPanel4.PerformLayout();
    this.txtTmin.Properties.EndInit();
    this.tableLayoutPanel2.ResumeLayout(false);
    this.tableLayoutPanel2.PerformLayout();
    this.txtAllowableStrength.Properties.EndInit();
    this.tableLayoutPanel5.ResumeLayout(false);
    this.tableLayoutPanel5.PerformLayout();
    this.txtMinMeasuredThickness.Properties.EndInit();
    this.tableLayoutPanel8.ResumeLayout(false);
    this.tableLayoutPanel8.PerformLayout();
    this.txtTrd.Properties.EndInit();
    this.tableLayoutPanel10.ResumeLayout(false);
    this.txtRemainingThickRationRT.Properties.EndInit();
    this.tableLayoutPanel11.ResumeLayout(false);
    this.txtParameterQ.Properties.EndInit();
    this.tableLayoutPanel12.ResumeLayout(false);
    this.tableLayoutPanel12.PerformLayout();
    this.txtThickAveragingLengthL.Properties.EndInit();
    this.grcAssessmentCriteriaL1.EndInit();
    this.grcAssessmentCriteriaL1.ResumeLayout(false);
    this.tblpAssessmentCriteriaL1.ResumeLayout(false);
    this.tblpAssessmentCriteriaL1.PerformLayout();
    this.tableLayoutPanel20.ResumeLayout(false);
    this.txtAverageMThicknessL1.Properties.EndInit();
    this.tableLayoutPanel21.ResumeLayout(false);
    this.txtMinMThicknessL1.Properties.EndInit();
    this.tableLayoutPanel19.ResumeLayout(false);
    this.txtMaxAllowWorkPressureL1.Properties.EndInit();
    this.grcAssessmentConclusionL1.EndInit();
    this.grcAssessmentConclusionL1.ResumeLayout(false);
    this.tableLayoutPanel22.ResumeLayout(false);
    this.tableLayoutPanel22.PerformLayout();
    this.grcAssessmentCriteriaL2.EndInit();
    this.grcAssessmentCriteriaL2.ResumeLayout(false);
    this.tblpAssessmentCriteriaL2.ResumeLayout(false);
    this.tblpAssessmentCriteriaL2.PerformLayout();
    this.tableLayoutPanel25.ResumeLayout(false);
    this.txtAverageMThicknessL2.Properties.EndInit();
    this.tableLayoutPanel26.ResumeLayout(false);
    this.txtMinMThicknessL2.Properties.EndInit();
    this.tableLayoutPanel24.ResumeLayout(false);
    this.txtMaxAllowWorkPressureL2.Properties.EndInit();
    this.grcAssessmentConclusionL2.EndInit();
    this.grcAssessmentConclusionL2.ResumeLayout(false);
    this.tableLayoutPanel27.ResumeLayout(false);
    this.tableLayoutPanel27.PerformLayout();
    this.grcInspectionDataSummary.EndInit();
    this.grcInspectionDataSummary.ResumeLayout(false);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.tableLayoutPanel6.ResumeLayout(false);
    this.tableLayoutPanel6.PerformLayout();
    this.txtLm.Properties.EndInit();
    this.tableLayoutPanel16.ResumeLayout(false);
    this.tableLayoutPanel16.PerformLayout();
    this.txtLc.Properties.EndInit();
    this.tableLayoutPanel17.ResumeLayout(false);
    this.tableLayoutPanel17.PerformLayout();
    this.txtS.Properties.EndInit();
    this.tableLayoutPanel18.ResumeLayout(false);
    this.tableLayoutPanel18.PerformLayout();
    this.txtC.Properties.EndInit();
    this.ResumeLayout(false);
  }
}
