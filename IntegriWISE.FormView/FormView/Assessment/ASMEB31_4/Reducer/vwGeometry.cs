// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_4.Reducer.vwGeometry
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.Common;
using IntegriWISE.FormView.Properties;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEB31_4.Reducer;
using IntegriWISE.UserInterface.Component;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_4.Reducer;

public class vwGeometry : XtraUserControl, IGeometryView, IGeometryBaseView, IView
{
  private IContainer components;
  private LabelControl labelControl1;
  private LabelControl labelControl2;
  private LabelControl labelControl4;
  private LabelControl umNominalThickness;
  private TextEdit txtNominalOD;
  private TextEdit txtNominalThickness;
  private TextEdit txtWeldJointEfficiency;
  private LabelControl umNominalOD;
  private TableLayoutPanel tableLayoutPanel1;
  private DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider dxErrorProvider1;
  private LabelControl umOneHalfApexAngle;
  private TextEdit txtOneHalfApexAngle;
  private LabelControl labelControl7;
  private TextEdit txtNominalOD2;
  private LabelControl umNominalOD2;
  private LabelControl labelControl3;
  private PictureEdit pictureGeometry;
  private LabelControl labelControl8;
  private TextEdit txtMechanicalAllowance;
  private LabelControl umMechanicalAllowance;
  private GeometryPresenter _presenter;
  private IRecordView _recordView;
  private IBaseView _baseView;
  private IComponentView _componentView;
  private FormDirtyTracker _dirtyTracker;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    this.labelControl1 = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.labelControl4 = new LabelControl();
    this.umNominalThickness = new LabelControl();
    this.txtNominalOD = new TextEdit();
    this.txtNominalThickness = new TextEdit();
    this.txtWeldJointEfficiency = new TextEdit();
    this.umNominalOD = new LabelControl();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.umMechanicalAllowance = new LabelControl();
    this.txtMechanicalAllowance = new TextEdit();
    this.labelControl8 = new LabelControl();
    this.pictureGeometry = new PictureEdit();
    this.txtNominalOD2 = new TextEdit();
    this.umNominalOD2 = new LabelControl();
    this.labelControl3 = new LabelControl();
    this.labelControl7 = new LabelControl();
    this.txtOneHalfApexAngle = new TextEdit();
    this.umOneHalfApexAngle = new LabelControl();
    this.dxErrorProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(this.components);
    this.txtNominalOD.Properties.BeginInit();
    this.txtNominalThickness.Properties.BeginInit();
    this.txtWeldJointEfficiency.Properties.BeginInit();
    this.tableLayoutPanel1.SuspendLayout();
    this.txtMechanicalAllowance.Properties.BeginInit();
    this.pictureGeometry.Properties.BeginInit();
    this.txtNominalOD2.Properties.BeginInit();
    this.txtOneHalfApexAngle.Properties.BeginInit();
    ((ISupportInitialize) this.dxErrorProvider1).BeginInit();
    this.SuspendLayout();
    this.labelControl1.Location = new Point(13, 13);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(143, 13);
    this.labelControl1.TabIndex = 0;
    this.labelControl1.Text = "Nominal Outside Diameter, Do";
    this.labelControl2.Location = new Point(13, 57);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(117, 13);
    this.labelControl2.TabIndex = 6;
    this.labelControl2.Text = "Nominal Thickness, tnom";
    this.labelControl4.Location = new Point(13, 79);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(112 /*0x70*/, 13);
    this.labelControl4.TabIndex = 9;
    this.labelControl4.Text = "Weld Joint Efficiency, E";
    this.umNominalThickness.Location = new Point(281, 57);
    this.umNominalThickness.Name = "umNominalThickness";
    this.umNominalThickness.Size = new Size(41, 13);
    this.umNominalThickness.TabIndex = 8;
    this.umNominalThickness.Text = "measure";
    this.txtNominalOD.Location = new Point(177, 11);
    this.txtNominalOD.Margin = new Padding(1);
    this.txtNominalOD.Name = "txtNominalOD";
    this.txtNominalOD.Size = new Size(100, 20);
    this.txtNominalOD.TabIndex = 1;
    this.txtNominalThickness.Location = new Point(177, 55);
    this.txtNominalThickness.Margin = new Padding(1);
    this.txtNominalThickness.Name = "txtNominalThickness";
    this.txtNominalThickness.Size = new Size(100, 20);
    this.txtNominalThickness.TabIndex = 7;
    this.txtWeldJointEfficiency.Location = new Point(177, 77);
    this.txtWeldJointEfficiency.Margin = new Padding(1);
    this.txtWeldJointEfficiency.Name = "txtWeldJointEfficiency";
    this.txtWeldJointEfficiency.Size = new Size(100, 20);
    this.txtWeldJointEfficiency.TabIndex = 10;
    this.umNominalOD.Location = new Point(281, 13);
    this.umNominalOD.Name = "umNominalOD";
    this.umNominalOD.Size = new Size(41, 13);
    this.umNominalOD.TabIndex = 2;
    this.umNominalOD.Text = "measure";
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.umMechanicalAllowance, 2, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtMechanicalAllowance, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl8, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.pictureGeometry, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtNominalOD2, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.umNominalOD2, 2, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl3, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.umNominalOD, 2, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtNominalOD, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtNominalThickness, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.umNominalThickness, 2, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl7, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtOneHalfApexAngle, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.umOneHalfApexAngle, 2, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl4, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtWeldJointEfficiency, 1, 3);
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 7;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(335, 320);
    this.tableLayoutPanel1.TabIndex = 0;
    this.umMechanicalAllowance.Location = new Point(281, 101);
    this.umMechanicalAllowance.Name = "umMechanicalAllowance";
    this.umMechanicalAllowance.Size = new Size(41, 13);
    this.umMechanicalAllowance.TabIndex = 13;
    this.umMechanicalAllowance.Text = "measure";
    this.txtMechanicalAllowance.Location = new Point(177, 99);
    this.txtMechanicalAllowance.Margin = new Padding(1);
    this.txtMechanicalAllowance.Name = "txtMechanicalAllowance";
    this.txtMechanicalAllowance.Size = new Size(100, 20);
    this.txtMechanicalAllowance.TabIndex = 12;
    this.labelControl8.Location = new Point(13, 101);
    this.labelControl8.Name = "labelControl8";
    this.labelControl8.Size = new Size(125, 13);
    this.labelControl8.TabIndex = 11;
    this.labelControl8.Text = "Mechanical Allowance, MA";
    this.tableLayoutPanel1.SetColumnSpan((Control) this.pictureGeometry, 3);
    this.pictureGeometry.EditValue = (object) Resources.IW_Reducer;
    this.pictureGeometry.Location = new Point(20, 150);
    this.pictureGeometry.Margin = new Padding(10);
    this.pictureGeometry.Name = "pictureGeometry";
    this.pictureGeometry.Properties.AllowFocused = false;
    this.pictureGeometry.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry.Properties.ReadOnly = true;
    this.pictureGeometry.Properties.ShowMenu = false;
    this.pictureGeometry.Size = new Size(220, 150);
    this.pictureGeometry.TabIndex = 17;
    this.txtNominalOD2.Location = new Point(177, 33);
    this.txtNominalOD2.Margin = new Padding(1);
    this.txtNominalOD2.Name = "txtNominalOD2";
    this.txtNominalOD2.Size = new Size(100, 20);
    this.txtNominalOD2.TabIndex = 4;
    this.umNominalOD2.Location = new Point(281, 35);
    this.umNominalOD2.Name = "umNominalOD2";
    this.umNominalOD2.Size = new Size(41, 13);
    this.umNominalOD2.TabIndex = 5;
    this.umNominalOD2.Text = "measure";
    this.labelControl3.Location = new Point(13, 35);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(158, 13);
    this.labelControl3.TabIndex = 3;
    this.labelControl3.Text = "Nominal Outside Diameter 2, Do2";
    this.labelControl7.Location = new Point(13, 121);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(160 /*0xA0*/, 13);
    this.labelControl7.TabIndex = 14;
    this.labelControl7.Text = "One-half Apex Angle of the Cone";
    this.txtOneHalfApexAngle.Location = new Point(177, 119);
    this.txtOneHalfApexAngle.Margin = new Padding(1);
    this.txtOneHalfApexAngle.Name = "txtOneHalfApexAngle";
    this.txtOneHalfApexAngle.Size = new Size(100, 20);
    this.txtOneHalfApexAngle.TabIndex = 15;
    this.umOneHalfApexAngle.Location = new Point(281, 121);
    this.umOneHalfApexAngle.Name = "umOneHalfApexAngle";
    this.umOneHalfApexAngle.Size = new Size(41, 13);
    this.umOneHalfApexAngle.TabIndex = 16 /*0x10*/;
    this.umOneHalfApexAngle.Text = "measure";
    this.dxErrorProvider1.ContainerControl = (ContainerControl) this;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = nameof (vwGeometry);
    this.Size = new Size(733, 621);
    this.Load += new EventHandler(this.vwGeometry_Load);
    this.txtNominalOD.Properties.EndInit();
    this.txtNominalThickness.Properties.EndInit();
    this.txtWeldJointEfficiency.Properties.EndInit();
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.txtMechanicalAllowance.Properties.EndInit();
    this.pictureGeometry.Properties.EndInit();
    this.txtNominalOD2.Properties.EndInit();
    this.txtOneHalfApexAngle.Properties.EndInit();
    ((ISupportInitialize) this.dxErrorProvider1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public vwGeometry(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._baseView = (IBaseView) recordView;
    this._recordView.GeometryView = (IGeometryBaseView) this;
    this._presenter = new GeometryPresenter(this._recordView, (IGeometryView) this);
  }

  public vwGeometry(IComponentView componentView)
  {
    this.InitializeComponent();
    this._componentView = componentView;
    this._baseView = (IBaseView) componentView;
    this._presenter = new GeometryPresenter(this._componentView, (IGeometryView) this);
  }

  private void vwGeometry_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, this._baseView);
    this._dirtyTracker.IsHandled = true;
    this.txtNominalOD.AllowOnlyN4();
    this.txtNominalOD2.AllowOnlyN4();
    this.txtOneHalfApexAngle.AllowOnlyN4();
    this.txtNominalThickness.AllowOnlyN4();
    this.txtWeldJointEfficiency.AllowOnlyN4();
    this._presenter.LoadGeometry();
    this._dirtyTracker.IsHandled = false;
  }

  public int? GeometryID { get; set; }

  public double? NominalOD
  {
    get => Helpers.ParseNullDouble((object) this.txtNominalOD.Text);
    set => this.txtNominalOD.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? NominalOD2
  {
    get => Helpers.ParseNullDouble((object) this.txtNominalOD2.Text);
    set => this.txtNominalOD2.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? NominalThickness
  {
    get => Helpers.ParseNullDouble((object) this.txtNominalThickness.Text);
    set => this.txtNominalThickness.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? WeldJointEfficiency
  {
    get => Helpers.ParseNullDouble((object) this.txtWeldJointEfficiency.Text);
    set => this.txtWeldJointEfficiency.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? MechanicalAllowance
  {
    get => Helpers.ParseNullDouble((object) this.txtMechanicalAllowance.Text);
    set => this.txtMechanicalAllowance.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? OneHalfApexAngle
  {
    get => Helpers.ParseNullDouble((object) this.txtOneHalfApexAngle.Text);
    set => this.txtOneHalfApexAngle.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMNominalOD
  {
    set => this.umNominalOD.Text = value;
  }

  public string UMNominalOD2
  {
    set => this.umNominalOD2.Text = value;
  }

  public string UMNominalThickness
  {
    set => this.umNominalThickness.Text = value;
  }

  public string UMMechanicalAllowance
  {
    set => this.umMechanicalAllowance.Text = value;
  }

  public string UMOneHalfApexAngle
  {
    set => this.umOneHalfApexAngle.Text = value;
  }

  public string NominalODErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtNominalOD, value);
  }

  public string NominalOD2Err
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtNominalOD2, value);
  }

  public string NominalThicknessErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtNominalThickness, value);
  }

  public string WeldJointEfficiencyErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtWeldJointEfficiency, value);
  }

  public string MechanicalAllowanceErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtMechanicalAllowance, value);
  }

  public string OneHalfApexAngleErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtOneHalfApexAngle, value);
  }

  public string NominalODInfo
  {
    set => this.txtNominalOD.ToolTip = value;
  }

  public string NominalOD2Info
  {
    set => this.txtNominalOD2.ToolTip = value;
  }

  public string NominalThicknessInfo
  {
    set => this.txtNominalThickness.ToolTip = value;
  }

  public string WeldJointEfficiencyInfo
  {
    set => this.txtWeldJointEfficiency.ToolTip = value;
  }

  public string MechanicalAllowanceInfo
  {
    set => this.txtMechanicalAllowance.ToolTip = value;
  }

  public string OneHalfApexAngleInfo
  {
    set => this.txtOneHalfApexAngle.ToolTip = value;
  }

  public bool ValidateGeometry() => this._presenter.Validate();

  public void Save() => this._presenter.Save();

  public void ClearErrors() => this.dxErrorProvider1.ClearErrors();
}
