// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_4.CylindricalSection.Lamination.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEB31_4.CylindricalSection.Lamination;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_4.CylindricalSection.Lamination;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private string _UMAllowableStrength;
  private string _ResultMessages;
  private string _Level1Conclusion;
  private string _LBLevel1Conclusion;
  private bool _Level1Passed;
  private string _LBcLessEqual0_6RootDtc;
  private bool _CLessEqual0_6RootDtc;
  private string _LBsLessEqual0_6RootDtc;
  private bool _SLessEqual0_6RootDtc;
  private string _LBLmsdMoreEqual1_8RootDtc;
  private bool _LmsdMoreEqual1_8RootDtc;
  private string _LBLwMoreEqualMax_2tc_25mm;
  private bool _LwMoreEqualMax_2tc_25mm;
  private string _LBtmmMoreEqual0_1tc;
  private bool _TmmMoreEqual0_1tc;
  private string _LBLhLessEqual0_0_9_Max_s_c;
  private bool _LhLessEqual0_0_9_Max_s_c;
  private double? _MAWP;
  private string _LBMAWP;
  private string _UMMAWP;
  private string _LBtc;
  private string _UMtc;
  private double? _Tc;
  private string _LBAllowableStrength;
  private double? _AllowableStrength;
  private bool _EnableLevel1Conclusion;
  private bool _EnableLevel1Criteria;
  private bool _EnableIntermediateResult;
  private bool _EnableMAWP;
  private IRecordView _recordView;
  private ResultPresenter _presenter;
  private bool _showLevel2;
  private string _LBLevel2Conclusion;
  private string _Level2Conclusion;
  private bool _Level2Passed;
  private double? _MAWPrL2;
  private string _LBMAWPrL2;
  private string _UMMAWPrL2;
  private bool _EnableMAWPr;
  private bool _EnableScreeningCriteriaFigure5_8;
  private string _LongitudinalExtent;
  private string _CircumferentialExtent;
  private bool _EnableLevel2Conclusion;
  private bool _EnableLevel2Criteria;
  private bool _EnableLevel2IntermediateResults;
  private double? _Rt;
  private string _LBRt;
  private double? _LambdaC;
  private string _LBLambdaC;
  private double? _RSF;
  private string _LBRSF;
  private double? _TmmPart5;
  private string _UMTmmPart5;
  private string _LBTmmPart5;
  private bool _LhLessEqual0_0_9_Max_s_cL2;
  private string _LBLhLessEqual0_0_9_Max_s_cL2;
  private bool _TmmMoreEqual0_1tcL2;
  private string _LBtmmMoreEqual0_1tcL2;
  private bool _InHydrogenChargingServiceL2;
  private string _LBInHydrogenChargingServiceL2;
  private bool _RtGreater0_2;
  private bool _TmmMinusFCAGreater2_5;
  private bool _LmsdGreater1_8DtcPower0_5;
  private bool _LambdaCLessThan9;
  private bool _DOverTcGreaterThan20;
  private bool _RSFBetween0_7And1;
  private bool _ElBetween0_7And1;
  private bool _EcBetween0_7And2;
  private bool _ScreeningCriteriaFigure5_6;
  private bool _RSFGreaterRSFa;
  private bool _ScreeningCriteriaFigure5_8;
  private string _LBRtGreater0_2;
  private string _LBTmmMinusFCAGreater2_5;
  private string _LBLmsdGreater1_8DtcPower0_5;
  private string _LBLambdaCLessThan9;
  private string _LBDOverTcGreaterThan20;
  private string _LBRSFBetween0_7And1;
  private string _LBElBetween0_7And1;
  private string _LBEcBetween0_7And2;
  private string _LBScreeningCriteriaFigure5_6;
  private string _LBRSFGreaterRSFa;
  private string _LBScreeningCriteriaFigure5_8;
  private IContainer components;
  private GroupControl grpIntermediateResult;
  private GroupControl grpMAWP;
  private MemoEdit txtWarningMessages;
  private GroupControl groupControl3;
  private TableLayoutPanel tableLayoutPanel3;
  private GroupControl grpLevel1Criteria;
  private GroupControl grpLevel1Conclusion;
  private TableLayoutPanel tblIntermediateResult;
  private TableLayoutPanel tblMAWP;
  private TableLayoutPanel tblLevel1Criteria;
  private TableLayoutPanel tblLevel1Conclusion;
  private GroupControl grpLevel2IntermediateResults;
  private TableLayoutPanel tblLevel2IntermediateResult;
  private GroupControl grpLevel2Conclusion;
  private TableLayoutPanel tblLevel2Conclusion;
  private GroupControl grpLevel2Criteria;
  private TableLayoutPanel tblLevel2Criteria;
  private PanelControl pcBottom;

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.ResultView = (IResultBaseView) this;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  private void vwResult_Load(object sender, EventArgs e)
  {
  }

  private void AddIntermediateResultValue(int rowNum, double? value)
  {
    if (this.tblIntermediateResult.RowCount < rowNum + 1)
      this.tblIntermediateResult.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    textEdit2.AllowOnlyN4();
    this.tblIntermediateResult.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddIntermediateResultLabel(int rowNum, string value)
  {
    if (this.tblIntermediateResult.RowCount < rowNum + 1)
      this.tblIntermediateResult.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblIntermediateResult.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddIntermediateResultUMLabel(int rowNum, string value)
  {
    if (this.tblIntermediateResult.RowCount < rowNum + 1)
      this.tblIntermediateResult.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblIntermediateResult.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  private void AddMAWPValue(int rowNum, double? value)
  {
    if (this.tblMAWP.RowCount < rowNum + 1)
      this.tblMAWP.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    textEdit2.AllowOnlyN4();
    this.tblMAWP.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddMAWPLabel(int rowNum, string value)
  {
    if (this.tblMAWP.RowCount < rowNum + 1)
      this.tblMAWP.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblMAWP.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddMAWPUMLabel(int rowNum, string value)
  {
    if (this.tblMAWP.RowCount < rowNum + 1)
      this.tblMAWP.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblMAWP.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  private void AddLevel1CriteriaLabel(int rowNum, string value)
  {
    if (this.tblLevel1Criteria.RowCount < rowNum + 1)
      this.tblLevel1Criteria.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel1Criteria.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddLevel1CriteriaValue(int rowNum, bool value)
  {
    if (this.tblLevel1Criteria.RowCount < rowNum + 1)
      this.tblLevel1Criteria.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    this.tblLevel1Criteria.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddLevel1ConclusionLabel(int rowNum, string value)
  {
    if (this.tblLevel1Conclusion.RowCount < rowNum + 1)
      this.tblLevel1Conclusion.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel1Conclusion.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddLevel1ConclusionValue(int rowNum, string value)
  {
    if (this.tblLevel1Conclusion.RowCount < rowNum + 1)
      this.tblLevel1Conclusion.RowCount = rowNum + 1;
    MemoEdit memoEdit1 = new MemoEdit();
    memoEdit1.Dock = DockStyle.Fill;
    memoEdit1.Text = Helpers.ParseObjectToString((object) value);
    memoEdit1.Margin = new Padding(1);
    MemoEdit memoEdit2 = memoEdit1;
    memoEdit2.Properties.ReadOnly = true;
    memoEdit2.AutoSizeInLayoutControl = true;
    memoEdit2.Size = new Size(100, 20);
    this.tblLevel1Conclusion.SetColumnSpan((Control) memoEdit2, 3);
    this.tblLevel1Conclusion.Controls.Add((Control) memoEdit2, 0, rowNum + 1);
  }

  private void AddLevel1Passed(int rowNum, bool value)
  {
    if (this.tblLevel1Conclusion.RowCount < rowNum + 1)
      this.tblLevel1Conclusion.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    textEdit2.BackColor = value ? Color.Green : Color.Red;
    this.tblLevel1Conclusion.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddLevel2IntermediateResultValue(int rowNum, double? value)
  {
    if (this.tblLevel2IntermediateResult.RowCount < rowNum + 1)
      this.tblLevel2IntermediateResult.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    textEdit2.AllowOnlyN4();
    this.tblLevel2IntermediateResult.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddLevel2IntermediateResultLabel(int rowNum, string value)
  {
    if (this.tblLevel2IntermediateResult.RowCount < rowNum + 1)
      this.tblLevel2IntermediateResult.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel2IntermediateResult.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddLevel2IntermediateResultUMLabel(int rowNum, string value)
  {
    if (this.tblLevel2IntermediateResult.RowCount < rowNum + 1)
      this.tblLevel2IntermediateResult.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel2IntermediateResult.Controls.Add((Control) labelControl, 2, rowNum + 1);
  }

  private void AddLevel2CriteriaLabel(int rowNum, string value)
  {
    if (this.tblLevel2Criteria.RowCount < rowNum + 1)
      this.tblLevel2Criteria.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel2Criteria.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddLevel2CriteriaValue(int rowNum, bool value)
  {
    if (this.tblLevel2Criteria.RowCount < rowNum + 1)
      this.tblLevel2Criteria.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    this.tblLevel2Criteria.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddConclusionLabel(
    int rowNum,
    string value,
    TableLayoutPanel tablePanel,
    bool hasPassed,
    bool IsBold)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    LabelControl labelControl1 = new LabelControl();
    labelControl1.Text = value;
    labelControl1.Margin = new Padding(1);
    LabelControl labelControl2 = labelControl1;
    if (IsBold)
    {
      labelControl2.Font = new Font(Control.DefaultFont, FontStyle.Bold);
      labelControl2.ForeColor = hasPassed ? Color.Green : Color.Red;
    }
    tablePanel.Controls.Add((Control) labelControl2, 0, rowNum + 1);
  }

  private void AddLevel2ConclusionLabel(int rowNum, string value)
  {
    if (this.tblLevel2Conclusion.RowCount < rowNum + 1)
      this.tblLevel2Conclusion.RowCount = rowNum + 1;
    LabelControl labelControl = new LabelControl();
    labelControl.Text = value;
    labelControl.Margin = new Padding(1);
    this.tblLevel2Conclusion.Controls.Add((Control) labelControl, 0, rowNum + 1);
  }

  private void AddLevel2ConclusionValue(int rowNum, string value)
  {
    if (this.tblLevel2Conclusion.RowCount < rowNum + 1)
      this.tblLevel2Conclusion.RowCount = rowNum + 1;
    MemoEdit memoEdit1 = new MemoEdit();
    memoEdit1.Dock = DockStyle.Fill;
    memoEdit1.Text = Helpers.ParseObjectToString((object) value);
    memoEdit1.Margin = new Padding(1);
    MemoEdit memoEdit2 = memoEdit1;
    memoEdit2.Properties.ReadOnly = true;
    memoEdit2.AutoSizeInLayoutControl = true;
    memoEdit2.Size = new Size(100, 20);
    this.tblLevel2Conclusion.SetColumnSpan((Control) memoEdit2, 3);
    this.tblLevel2Conclusion.Controls.Add((Control) memoEdit2, 0, rowNum + 1);
  }

  public bool Calculate()
  {
    this.tblIntermediateResult.Controls.Clear();
    this.tblMAWP.Controls.Clear();
    this.tblLevel1Conclusion.Controls.Clear();
    this.tblLevel1Criteria.Controls.Clear();
    this.tblLevel2IntermediateResult.Controls.Clear();
    this.tblLevel2Conclusion.Controls.Clear();
    this.tblLevel2Criteria.Controls.Clear();
    return this._presenter.Calculate();
  }

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public bool EnableMAWP
  {
    get => this._EnableMAWP;
    set
    {
      this._EnableMAWP = value;
      this.grpMAWP.Visible = value;
    }
  }

  public bool ShowLevel2
  {
    get => this._showLevel2;
    set
    {
      this._showLevel2 = value;
      this.grpLevel2IntermediateResults.Visible = value;
      this.grpLevel2Conclusion.Visible = value;
      this.grpLevel2Criteria.Visible = value;
    }
  }

  public bool EnableMAWPr
  {
    get => this._EnableMAWPr;
    set
    {
      this._EnableMAWPr = value;
      if (!value || this.grpMAWP.Visible)
        return;
      this.grpMAWP.Visible = value;
    }
  }

  public bool EnableScreeningCriteriaFigure5_8
  {
    get => this._EnableScreeningCriteriaFigure5_8;
    set => this._EnableScreeningCriteriaFigure5_8 = value;
  }

  public bool EnableIntermediateResult
  {
    get => this._EnableIntermediateResult;
    set
    {
      this._EnableIntermediateResult = value;
      this.grpIntermediateResult.Visible = value;
    }
  }

  public bool HydrogenChargingSurface { get; set; }

  public bool EnableLevel1Criteria
  {
    get => this._EnableLevel1Criteria;
    set
    {
      this._EnableLevel1Criteria = value;
      this.grpLevel1Criteria.Visible = value;
    }
  }

  public bool EnableLevel1Conclusion
  {
    get => this._EnableLevel1Conclusion;
    set
    {
      this._EnableLevel1Conclusion = value;
      this.grpLevel1Conclusion.Visible = value;
    }
  }

  public bool EnableLevel2IntermediateResults
  {
    get => this._EnableLevel2IntermediateResults;
    set
    {
      this._EnableLevel2IntermediateResults = value;
      this.grpLevel2IntermediateResults.Visible = value;
    }
  }

  public bool EnableLevel2Criteria
  {
    get => this._EnableLevel2Criteria;
    set
    {
      this._EnableLevel2Criteria = value;
      this.grpLevel2Criteria.Visible = value;
    }
  }

  public bool EnableLevel2Conclusion
  {
    get => this._EnableLevel2Conclusion;
    set
    {
      this._EnableLevel2Conclusion = value;
      this.grpLevel2Conclusion.Visible = value;
    }
  }

  public double? AllowableStrength
  {
    get => this._AllowableStrength;
    set
    {
      this._AllowableStrength = value;
      this.AddIntermediateResultValue(1, value);
    }
  }

  public string UMAllowableStrength
  {
    get => this._UMAllowableStrength;
    set
    {
      this._UMAllowableStrength = value;
      this.AddIntermediateResultUMLabel(1, value);
    }
  }

  public string LBAllowableStrength
  {
    get => this._LBAllowableStrength;
    set
    {
      this._LBAllowableStrength = value;
      this.AddIntermediateResultLabel(1, value);
    }
  }

  public double? tc
  {
    get => this._Tc;
    set
    {
      this._Tc = value;
      this.AddIntermediateResultValue(2, value);
    }
  }

  public string UMtc
  {
    get => this._UMtc;
    set
    {
      this._UMtc = value;
      this.AddIntermediateResultUMLabel(2, value);
    }
  }

  public string LBtc
  {
    get => this._LBtc;
    set
    {
      this._LBtc = value;
      this.AddIntermediateResultLabel(2, value);
    }
  }

  public double? MAWP
  {
    get => this._MAWP;
    set
    {
      this._MAWP = value;
      this.AddMAWPValue(1, value);
    }
  }

  public string UMMAWP
  {
    get => this._UMMAWP;
    set
    {
      this._UMMAWP = value;
      this.AddMAWPUMLabel(1, value);
    }
  }

  public string LBMAWP
  {
    get => this._LBMAWP;
    set
    {
      this._LBMAWP = value;
      this.AddMAWPLabel(1, value);
    }
  }

  public double? MAWPrL2
  {
    get => this._MAWPrL2;
    set
    {
      this._MAWPrL2 = value;
      this.AddMAWPValue(2, value);
    }
  }

  public string UMMAWPrL2
  {
    get => this._UMMAWPrL2;
    set
    {
      this._UMMAWPrL2 = value;
      this.AddMAWPUMLabel(2, value);
    }
  }

  public string LBMAWPrL2
  {
    get => this._LBMAWPrL2;
    set
    {
      this._LBMAWPrL2 = value;
      this.AddMAWPLabel(2, value);
    }
  }

  public bool LhLessEqual0_0_9_Max_s_c
  {
    get => this._LhLessEqual0_0_9_Max_s_c;
    set
    {
      this._LhLessEqual0_0_9_Max_s_c = value;
      this.AddLevel1CriteriaValue(1, value);
    }
  }

  public string LBLhLessEqual0_0_9_Max_s_c
  {
    get => this._LBLhLessEqual0_0_9_Max_s_c;
    set
    {
      this._LBLhLessEqual0_0_9_Max_s_c = value;
      this.AddLevel1CriteriaLabel(1, value);
    }
  }

  public bool tmmMoreEqual0_1tc
  {
    get => this._TmmMoreEqual0_1tc;
    set
    {
      this._TmmMoreEqual0_1tc = value;
      this.AddLevel1CriteriaValue(2, value);
    }
  }

  public string LBtmmMoreEqual0_1tc
  {
    get => this._LBtmmMoreEqual0_1tc;
    set
    {
      this._LBtmmMoreEqual0_1tc = value;
      this.AddLevel1CriteriaLabel(2, value);
    }
  }

  public bool LwMoreEqualMax_2tc_25mm
  {
    get => this._LwMoreEqualMax_2tc_25mm;
    set
    {
      this._LwMoreEqualMax_2tc_25mm = value;
      this.AddLevel1CriteriaValue(3, value);
    }
  }

  public string LBLwMoreEqualMax_2tc_25mm
  {
    get => this._LBLwMoreEqualMax_2tc_25mm;
    set
    {
      this._LBLwMoreEqualMax_2tc_25mm = value;
      this.AddLevel1CriteriaLabel(3, value);
    }
  }

  public bool LmsdMoreEqual1_8RootDtc
  {
    get => this._LmsdMoreEqual1_8RootDtc;
    set
    {
      this._LmsdMoreEqual1_8RootDtc = value;
      this.AddLevel1CriteriaValue(4, value);
    }
  }

  public string LBLmsdMoreEqual1_8RootDtc
  {
    get => this._LBLmsdMoreEqual1_8RootDtc;
    set
    {
      this._LBLmsdMoreEqual1_8RootDtc = value;
      this.AddLevel1CriteriaLabel(4, value);
    }
  }

  public bool InHydrogenCharging { get; set; }

  public bool sLessEqual0_6RootDtc
  {
    get => this._SLessEqual0_6RootDtc;
    set
    {
      this._SLessEqual0_6RootDtc = value;
      this.AddLevel1CriteriaValue(5, value);
    }
  }

  public string LBsLessEqual0_6RootDtc
  {
    get => this._LBsLessEqual0_6RootDtc;
    set
    {
      this._LBsLessEqual0_6RootDtc = value;
      this.AddLevel1CriteriaLabel(5, value);
    }
  }

  public bool cLessEqual0_6RootDtc
  {
    get => this._CLessEqual0_6RootDtc;
    set
    {
      this._CLessEqual0_6RootDtc = value;
      this.AddLevel1CriteriaValue(6, value);
    }
  }

  public string LBcLessEqual0_6RootDtc
  {
    get => this._LBcLessEqual0_6RootDtc;
    set
    {
      this._LBcLessEqual0_6RootDtc = value;
      this.AddLevel1CriteriaLabel(6, value);
    }
  }

  public bool Level1Passed => this._Level1Passed;

  public string Level1Conclusion => this._Level1Conclusion;

  public void SetLevel1Conclusion(string value, bool level1Passed)
  {
    this._Level1Passed = level1Passed;
    this._Level1Conclusion = value;
    this.AddConclusionLabel(1, value, this.tblLevel1Conclusion, this._Level1Passed, true);
  }

  public double? TmmPart5
  {
    get => this._TmmPart5;
    set
    {
      this._TmmPart5 = value;
      this.AddLevel2IntermediateResultValue(1, value);
    }
  }

  public string UMTmmPart5
  {
    get => this._UMTmmPart5;
    set
    {
      this._UMTmmPart5 = value;
      this.AddLevel2IntermediateResultUMLabel(1, value);
    }
  }

  public string LBTmmPart5
  {
    get => this._LBTmmPart5;
    set
    {
      this._LBTmmPart5 = value;
      this.AddLevel2IntermediateResultLabel(1, value);
    }
  }

  public double? Rt
  {
    get => this._Rt;
    set
    {
      this._Rt = value;
      this.AddLevel2IntermediateResultValue(2, value);
    }
  }

  public string LBRt
  {
    get => this._LBRt;
    set
    {
      this._LBRt = value;
      this.AddLevel2IntermediateResultLabel(2, value);
    }
  }

  public double? LambdaC
  {
    get => this._LambdaC;
    set
    {
      this._LambdaC = value;
      this.AddLevel2IntermediateResultValue(3, value);
    }
  }

  public string LBLambdaC
  {
    get => this._LBLambdaC;
    set
    {
      this._LBLambdaC = value;
      this.AddLevel2IntermediateResultLabel(3, value);
    }
  }

  public double? RSF
  {
    get => this._RSF;
    set
    {
      this._RSF = value;
      this.AddLevel2IntermediateResultValue(4, value);
    }
  }

  public string LBRSF
  {
    get => this._LBRSF;
    set
    {
      this._LBRSF = value;
      this.AddLevel2IntermediateResultLabel(4, value);
    }
  }

  public bool LhLessEqual0_0_9_Max_s_cL2
  {
    get => this._LhLessEqual0_0_9_Max_s_cL2;
    set
    {
      this._LhLessEqual0_0_9_Max_s_cL2 = value;
      this.AddLevel2CriteriaValue(1, value);
    }
  }

  public string LBLhLessEqual0_0_9_Max_s_cL2
  {
    get => this._LBLhLessEqual0_0_9_Max_s_cL2;
    set
    {
      this._LBLhLessEqual0_0_9_Max_s_cL2 = value;
      this.AddLevel2CriteriaLabel(1, value);
    }
  }

  public bool tmmMoreEqual0_1tcL2
  {
    get => this._TmmMoreEqual0_1tcL2;
    set
    {
      this._TmmMoreEqual0_1tcL2 = value;
      this.AddLevel2CriteriaValue(2, value);
    }
  }

  public string LBtmmMoreEqual0_1tcL2
  {
    get => this._LBtmmMoreEqual0_1tcL2;
    set
    {
      this._LBtmmMoreEqual0_1tcL2 = value;
      this.AddLevel2CriteriaLabel(2, value);
    }
  }

  public bool InHydrogenChargingServiceL2
  {
    get => this._InHydrogenChargingServiceL2;
    set
    {
      this._InHydrogenChargingServiceL2 = value;
      this.AddLevel2CriteriaValue(3, value);
    }
  }

  public string LBInHydrogenChargingServiceL2
  {
    get => this._LBInHydrogenChargingServiceL2;
    set
    {
      this._LBInHydrogenChargingServiceL2 = value;
      this.AddLevel2CriteriaLabel(3, value);
    }
  }

  public bool RtGreater0_2
  {
    get => this._RtGreater0_2;
    set
    {
      this._RtGreater0_2 = value;
      this.AddLevel2CriteriaValue(4, value);
    }
  }

  public string LBRtGreater0_2
  {
    get => this._LBRtGreater0_2;
    set
    {
      this._LBRtGreater0_2 = value;
      this.AddLevel2CriteriaLabel(4, value);
    }
  }

  public bool TmmMinusFCAGreater2_5
  {
    get => this._TmmMinusFCAGreater2_5;
    set
    {
      this._TmmMinusFCAGreater2_5 = value;
      this.AddLevel2CriteriaValue(5, value);
    }
  }

  public string LBTmmMinusFCAGreater2_5
  {
    get => this._LBTmmMinusFCAGreater2_5;
    set
    {
      this._LBTmmMinusFCAGreater2_5 = value;
      this.AddLevel2CriteriaLabel(5, value);
    }
  }

  public bool LmsdGreater1_8DtcPower0_5
  {
    get => this._LmsdGreater1_8DtcPower0_5;
    set
    {
      this._LmsdGreater1_8DtcPower0_5 = value;
      this.AddLevel2CriteriaValue(6, value);
    }
  }

  public string LBLmsdGreater1_8DtcPower0_5
  {
    get => this._LBLmsdGreater1_8DtcPower0_5;
    set
    {
      this._LBLmsdGreater1_8DtcPower0_5 = value;
      this.AddLevel2CriteriaLabel(6, value);
    }
  }

  public bool LambdaCLessThan9
  {
    get => this._LambdaCLessThan9;
    set
    {
      this._LambdaCLessThan9 = value;
      this.AddLevel2CriteriaValue(7, value);
    }
  }

  public string LBLambdaCLessThan9
  {
    get => this._LBLambdaCLessThan9;
    set
    {
      this._LBLambdaCLessThan9 = value;
      this.AddLevel2CriteriaLabel(7, value);
    }
  }

  public bool DOverTcGreaterThan20
  {
    get => this._DOverTcGreaterThan20;
    set
    {
      this._DOverTcGreaterThan20 = value;
      this.AddLevel2CriteriaValue(8, value);
    }
  }

  public string LBDOverTcGreaterThan20
  {
    get => this._LBDOverTcGreaterThan20;
    set
    {
      this._LBDOverTcGreaterThan20 = value;
      this.AddLevel2CriteriaLabel(8, value);
    }
  }

  public bool RSFBetween0_7And1
  {
    get => this._RSFBetween0_7And1;
    set
    {
      this._RSFBetween0_7And1 = value;
      this.AddLevel2CriteriaValue(9, value);
    }
  }

  public string LBRSFBetween0_7And1
  {
    get => this._LBRSFBetween0_7And1;
    set
    {
      this._LBRSFBetween0_7And1 = value;
      this.AddLevel2CriteriaLabel(9, value);
    }
  }

  public bool ElBetween0_7And1
  {
    get => this._ElBetween0_7And1;
    set
    {
      this._ElBetween0_7And1 = value;
      this.AddLevel2CriteriaValue(10, value);
    }
  }

  public string LBElBetween0_7And1
  {
    get => this._LBElBetween0_7And1;
    set
    {
      this._LBElBetween0_7And1 = value;
      this.AddLevel2CriteriaLabel(10, value);
    }
  }

  public bool EcBetween0_7And2
  {
    get => this._EcBetween0_7And2;
    set
    {
      this._EcBetween0_7And2 = value;
      this.AddLevel2CriteriaValue(11, value);
    }
  }

  public string LBEcBetween0_7And2
  {
    get => this._LBEcBetween0_7And2;
    set
    {
      this._LBEcBetween0_7And2 = value;
      this.AddLevel2CriteriaLabel(11, value);
    }
  }

  public bool ScreeningCriteriaFigure5_6
  {
    get => this._ScreeningCriteriaFigure5_6;
    set
    {
      this._ScreeningCriteriaFigure5_6 = value;
      this.AddLevel2CriteriaValue(12, value);
    }
  }

  public string LBScreeningCriteriaFigure5_6
  {
    get => this._LBScreeningCriteriaFigure5_6;
    set
    {
      this._LBScreeningCriteriaFigure5_6 = value;
      this.AddLevel2CriteriaLabel(12, value);
    }
  }

  public bool RSFGreaterRSFa
  {
    get => this._RSFGreaterRSFa;
    set
    {
      this._RSFGreaterRSFa = value;
      this.AddLevel2CriteriaValue(13, value);
    }
  }

  public string LBRSFGreaterRSFa
  {
    get => this._LBRSFGreaterRSFa;
    set
    {
      this._LBRSFGreaterRSFa = value;
      this.AddLevel2CriteriaLabel(13, value);
    }
  }

  public bool ScreeningCriteriaFigure5_8
  {
    get => this._ScreeningCriteriaFigure5_8;
    set
    {
      this._ScreeningCriteriaFigure5_8 = value;
      this.AddLevel2CriteriaValue(14, value);
    }
  }

  public string LBScreeningCriteriaFigure5_8
  {
    get => this._LBScreeningCriteriaFigure5_8;
    set
    {
      this._LBScreeningCriteriaFigure5_8 = value;
      this.AddLevel2CriteriaLabel(14, value);
    }
  }

  public bool Level2Passed => this._Level2Passed;

  public string LongitudinalExtent => this._LongitudinalExtent;

  public string CircumferentialExtent => this._CircumferentialExtent;

  public string Level2Conclusion => this._Level2Conclusion;

  public void SetLevel2Conclusion(
    string longExtent,
    string circExtent,
    string finalConclusion,
    bool level2Passed)
  {
    this._Level2Passed = level2Passed;
    this._LongitudinalExtent = longExtent;
    this._CircumferentialExtent = circExtent;
    this._Level2Conclusion = finalConclusion;
    this.AddConclusionLabel(1, longExtent, this.tblLevel2Conclusion, level2Passed, false);
    this.AddConclusionLabel(2, circExtent, this.tblLevel2Conclusion, level2Passed, false);
    this.AddConclusionLabel(3, finalConclusion, this.tblLevel2Conclusion, level2Passed, true);
  }

  public string ResultMessages
  {
    get => this._ResultMessages;
    set
    {
      this._ResultMessages = value;
      this.txtWarningMessages.Text = value;
    }
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public string CodeEdition => "";

  public string Title => "Assessment to API 579 Part 13: Laminations";

  public string Introduction
  {
    get
    {
      return "Laminations are defined as being a plane of non-fusion in the interior of a steel plate that results from the steel manufacturing process. Laminations can reduce the strength of the plate when bending stresses, compressive stresses, or through thickness stresses are present. Laminations can also trap hydrogen in components that are in wet hydrogen sulphide service, which may result in HIC or blisters. \r\n\r\nThe API 579 Level 1 and Level 2 Lamination assessment procedures are a screening criterion for laminations based on: the lamination size, orientation relative to the surface, and spacing of the lamination to weld joints, structural discontinuities, and other laminations.";
    }
  }

  public string CommentsAndAssumptions
  {
    get
    {
      return "The Lamination procedures (Part 13) assume that: \r\n1. The original design criteria were in accordance with a recognised standard or code.\r\n2. The assessment procedures only apply to components that are not operating in the creep range and cyclic service. \r\n3. The material is considered to have sufficient material toughness and it is not subject to embrittlement during \r\n    operation due to temperature or the process environment. \r\n4. The component is a Type A Component (Part 4, paragraph 4.2.5) subject to internal pressure. (i.e. supplemental \r\n    loads are assumed negligible).\t\r\n5. There is no indication of through-thickness cracking and/or cracking in the direction towards any of the surface.\r\n6. Physical bulging is not present. If there is evidence of a surface bulge, then the laminations shall be evaluated \r\n    as a blister (Part 7).\r\n7. The lamination is located parallel to the plate surface. If it has a through-wall component, then the through-wall \r\n    components shall be evaluated as a crack-like flaw (Part 9).\r\n8. If there are two or more laminations that are closely spaced at different depths in the wall thickness of the component, \r\n    then they have to be evaluated as HIC damage (Part 7).";
    }
  }

  public string References
  {
    get
    {
      return "API 579 \"Fitness-for-Service\", Second Edition, The American Society of Mechanical Engineers. Part 13: Laminations.";
    }
  }

  public string Limitations => string.Empty;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.grpIntermediateResult = new GroupControl();
    this.tblIntermediateResult = new TableLayoutPanel();
    this.grpMAWP = new GroupControl();
    this.tblMAWP = new TableLayoutPanel();
    this.txtWarningMessages = new MemoEdit();
    this.groupControl3 = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.grpLevel1Criteria = new GroupControl();
    this.tblLevel1Criteria = new TableLayoutPanel();
    this.grpLevel1Conclusion = new GroupControl();
    this.tblLevel1Conclusion = new TableLayoutPanel();
    this.grpLevel2IntermediateResults = new GroupControl();
    this.tblLevel2IntermediateResult = new TableLayoutPanel();
    this.grpLevel2Conclusion = new GroupControl();
    this.tblLevel2Conclusion = new TableLayoutPanel();
    this.grpLevel2Criteria = new GroupControl();
    this.tblLevel2Criteria = new TableLayoutPanel();
    this.pcBottom = new PanelControl();
    this.grpIntermediateResult.BeginInit();
    this.grpIntermediateResult.SuspendLayout();
    this.grpMAWP.BeginInit();
    this.grpMAWP.SuspendLayout();
    this.txtWarningMessages.Properties.BeginInit();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.grpLevel1Criteria.BeginInit();
    this.grpLevel1Criteria.SuspendLayout();
    this.grpLevel1Conclusion.BeginInit();
    this.grpLevel1Conclusion.SuspendLayout();
    this.grpLevel2IntermediateResults.BeginInit();
    this.grpLevel2IntermediateResults.SuspendLayout();
    this.grpLevel2Conclusion.BeginInit();
    this.grpLevel2Conclusion.SuspendLayout();
    this.grpLevel2Criteria.BeginInit();
    this.grpLevel2Criteria.SuspendLayout();
    this.pcBottom.BeginInit();
    this.SuspendLayout();
    this.grpIntermediateResult.AutoSize = true;
    this.grpIntermediateResult.Controls.Add((Control) this.tblIntermediateResult);
    this.grpIntermediateResult.Dock = DockStyle.Top;
    this.grpIntermediateResult.Location = new Point(0, 0);
    this.grpIntermediateResult.Name = "grpIntermediateResult";
    this.grpIntermediateResult.Size = new Size(250, 43);
    this.grpIntermediateResult.TabIndex = 2;
    this.grpIntermediateResult.Text = "Intermediate Assessment Result";
    this.tblIntermediateResult.AutoSize = true;
    this.tblIntermediateResult.ColumnCount = 3;
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.Dock = DockStyle.Fill;
    this.tblIntermediateResult.Location = new Point(2, 21);
    this.tblIntermediateResult.Name = "tblIntermediateResult";
    this.tblIntermediateResult.Padding = new Padding(10);
    this.tblIntermediateResult.RowCount = 1;
    this.tblIntermediateResult.RowStyles.Add(new RowStyle());
    this.tblIntermediateResult.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblIntermediateResult.Size = new Size(246, 20);
    this.tblIntermediateResult.TabIndex = 0;
    this.grpMAWP.AutoSize = true;
    this.grpMAWP.Controls.Add((Control) this.tblMAWP);
    this.grpMAWP.Dock = DockStyle.Top;
    this.grpMAWP.Location = new Point(0, 43);
    this.grpMAWP.Name = "grpMAWP";
    this.grpMAWP.Size = new Size(250, 43);
    this.grpMAWP.TabIndex = 3;
    this.grpMAWP.Text = "Maximum Allowable Working Pressure";
    this.tblMAWP.AutoSize = true;
    this.tblMAWP.ColumnCount = 3;
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.Dock = DockStyle.Fill;
    this.tblMAWP.Location = new Point(2, 21);
    this.tblMAWP.Name = "tblMAWP";
    this.tblMAWP.Padding = new Padding(10);
    this.tblMAWP.RowCount = 1;
    this.tblMAWP.RowStyles.Add(new RowStyle());
    this.tblMAWP.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblMAWP.Size = new Size(246, 20);
    this.tblMAWP.TabIndex = 1;
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(220, 70);
    this.txtWarningMessages.TabIndex = 4;
    this.groupControl3.AutoSize = true;
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel3);
    this.groupControl3.Dock = DockStyle.Top;
    this.groupControl3.Location = new Point(0, 301);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(250, 119);
    this.groupControl3.TabIndex = 5;
    this.groupControl3.Text = "Warning Messages";
    this.tableLayoutPanel3.AutoSize = true;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 145f));
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 145f));
    this.tableLayoutPanel3.Size = new Size(246, 96 /*0x60*/);
    this.tableLayoutPanel3.TabIndex = 0;
    this.grpLevel1Criteria.AutoSize = true;
    this.grpLevel1Criteria.Controls.Add((Control) this.tblLevel1Criteria);
    this.grpLevel1Criteria.Dock = DockStyle.Top;
    this.grpLevel1Criteria.Location = new Point(0, 86);
    this.grpLevel1Criteria.Name = "grpLevel1Criteria";
    this.grpLevel1Criteria.Size = new Size(250, 43);
    this.grpLevel1Criteria.TabIndex = 6;
    this.grpLevel1Criteria.Text = "Level 1 Assessment Criteria";
    this.tblLevel1Criteria.AutoSize = true;
    this.tblLevel1Criteria.ColumnCount = 3;
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.Dock = DockStyle.Fill;
    this.tblLevel1Criteria.Location = new Point(2, 21);
    this.tblLevel1Criteria.Name = "tblLevel1Criteria";
    this.tblLevel1Criteria.Padding = new Padding(10);
    this.tblLevel1Criteria.RowCount = 1;
    this.tblLevel1Criteria.RowStyles.Add(new RowStyle());
    this.tblLevel1Criteria.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblLevel1Criteria.Size = new Size(246, 20);
    this.tblLevel1Criteria.TabIndex = 1;
    this.grpLevel1Conclusion.AutoSize = true;
    this.grpLevel1Conclusion.Controls.Add((Control) this.tblLevel1Conclusion);
    this.grpLevel1Conclusion.Dock = DockStyle.Top;
    this.grpLevel1Conclusion.Location = new Point(0, 129);
    this.grpLevel1Conclusion.Name = "grpLevel1Conclusion";
    this.grpLevel1Conclusion.Size = new Size(250, 43);
    this.grpLevel1Conclusion.TabIndex = 7;
    this.grpLevel1Conclusion.Text = "Level 1 Assessment Conclusion";
    this.tblLevel1Conclusion.AutoSize = true;
    this.tblLevel1Conclusion.ColumnCount = 3;
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.Dock = DockStyle.Fill;
    this.tblLevel1Conclusion.Location = new Point(2, 21);
    this.tblLevel1Conclusion.Name = "tblLevel1Conclusion";
    this.tblLevel1Conclusion.Padding = new Padding(10);
    this.tblLevel1Conclusion.RowCount = 1;
    this.tblLevel1Conclusion.RowStyles.Add(new RowStyle());
    this.tblLevel1Conclusion.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblLevel1Conclusion.Size = new Size(246, 20);
    this.tblLevel1Conclusion.TabIndex = 1;
    this.grpLevel2IntermediateResults.AutoSize = true;
    this.grpLevel2IntermediateResults.Controls.Add((Control) this.tblLevel2IntermediateResult);
    this.grpLevel2IntermediateResults.Dock = DockStyle.Top;
    this.grpLevel2IntermediateResults.Location = new Point(0, 172);
    this.grpLevel2IntermediateResults.Name = "grpLevel2IntermediateResults";
    this.grpLevel2IntermediateResults.Size = new Size(250, 43);
    this.grpLevel2IntermediateResults.TabIndex = 8;
    this.grpLevel2IntermediateResults.Text = "Level 2 Intermediate Assessment Results";
    this.tblLevel2IntermediateResult.AutoSize = true;
    this.tblLevel2IntermediateResult.ColumnCount = 3;
    this.tblLevel2IntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2IntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2IntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2IntermediateResult.Dock = DockStyle.Fill;
    this.tblLevel2IntermediateResult.Location = new Point(2, 21);
    this.tblLevel2IntermediateResult.Name = "tblLevel2IntermediateResult";
    this.tblLevel2IntermediateResult.Padding = new Padding(10);
    this.tblLevel2IntermediateResult.RowCount = 1;
    this.tblLevel2IntermediateResult.RowStyles.Add(new RowStyle());
    this.tblLevel2IntermediateResult.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblLevel2IntermediateResult.Size = new Size(246, 20);
    this.tblLevel2IntermediateResult.TabIndex = 0;
    this.grpLevel2Conclusion.AutoSize = true;
    this.grpLevel2Conclusion.Controls.Add((Control) this.tblLevel2Conclusion);
    this.grpLevel2Conclusion.Dock = DockStyle.Top;
    this.grpLevel2Conclusion.Location = new Point(0, 258);
    this.grpLevel2Conclusion.Name = "grpLevel2Conclusion";
    this.grpLevel2Conclusion.Size = new Size(250, 43);
    this.grpLevel2Conclusion.TabIndex = 10;
    this.grpLevel2Conclusion.Text = "Level 2 Assessment Conclusion";
    this.tblLevel2Conclusion.AutoSize = true;
    this.tblLevel2Conclusion.ColumnCount = 3;
    this.tblLevel2Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Conclusion.Dock = DockStyle.Fill;
    this.tblLevel2Conclusion.Location = new Point(2, 21);
    this.tblLevel2Conclusion.Name = "tblLevel2Conclusion";
    this.tblLevel2Conclusion.Padding = new Padding(10);
    this.tblLevel2Conclusion.RowCount = 1;
    this.tblLevel2Conclusion.RowStyles.Add(new RowStyle());
    this.tblLevel2Conclusion.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblLevel2Conclusion.Size = new Size(246, 20);
    this.tblLevel2Conclusion.TabIndex = 1;
    this.grpLevel2Criteria.AutoSize = true;
    this.grpLevel2Criteria.Controls.Add((Control) this.tblLevel2Criteria);
    this.grpLevel2Criteria.Dock = DockStyle.Top;
    this.grpLevel2Criteria.Location = new Point(0, 215);
    this.grpLevel2Criteria.Name = "grpLevel2Criteria";
    this.grpLevel2Criteria.Size = new Size(250, 43);
    this.grpLevel2Criteria.TabIndex = 9;
    this.grpLevel2Criteria.Text = "Level 2 Assessment Criteria";
    this.tblLevel2Criteria.AutoSize = true;
    this.tblLevel2Criteria.ColumnCount = 3;
    this.tblLevel2Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Criteria.Dock = DockStyle.Fill;
    this.tblLevel2Criteria.Location = new Point(2, 21);
    this.tblLevel2Criteria.Name = "tblLevel2Criteria";
    this.tblLevel2Criteria.Padding = new Padding(10);
    this.tblLevel2Criteria.RowCount = 1;
    this.tblLevel2Criteria.RowStyles.Add(new RowStyle());
    this.tblLevel2Criteria.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblLevel2Criteria.Size = new Size(246, 20);
    this.tblLevel2Criteria.TabIndex = 1;
    this.pcBottom.Dock = DockStyle.Fill;
    this.pcBottom.Location = new Point(0, 420);
    this.pcBottom.Name = "pcBottom";
    this.pcBottom.Size = new Size(250, 14);
    this.pcBottom.TabIndex = 34;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.Controls.Add((Control) this.pcBottom);
    this.Controls.Add((Control) this.groupControl3);
    this.Controls.Add((Control) this.grpLevel2Conclusion);
    this.Controls.Add((Control) this.grpLevel2Criteria);
    this.Controls.Add((Control) this.grpLevel2IntermediateResults);
    this.Controls.Add((Control) this.grpLevel1Conclusion);
    this.Controls.Add((Control) this.grpLevel1Criteria);
    this.Controls.Add((Control) this.grpMAWP);
    this.Controls.Add((Control) this.grpIntermediateResult);
    this.Name = nameof (vwResult);
    this.Size = new Size(250, 434);
    this.Load += new EventHandler(this.vwResult_Load);
    this.grpIntermediateResult.EndInit();
    this.grpIntermediateResult.ResumeLayout(false);
    this.grpIntermediateResult.PerformLayout();
    this.grpMAWP.EndInit();
    this.grpMAWP.ResumeLayout(false);
    this.grpMAWP.PerformLayout();
    this.txtWarningMessages.Properties.EndInit();
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.groupControl3.PerformLayout();
    this.tableLayoutPanel3.ResumeLayout(false);
    this.grpLevel1Criteria.EndInit();
    this.grpLevel1Criteria.ResumeLayout(false);
    this.grpLevel1Criteria.PerformLayout();
    this.grpLevel1Conclusion.EndInit();
    this.grpLevel1Conclusion.ResumeLayout(false);
    this.grpLevel1Conclusion.PerformLayout();
    this.grpLevel2IntermediateResults.EndInit();
    this.grpLevel2IntermediateResults.ResumeLayout(false);
    this.grpLevel2IntermediateResults.PerformLayout();
    this.grpLevel2Conclusion.EndInit();
    this.grpLevel2Conclusion.ResumeLayout(false);
    this.grpLevel2Conclusion.PerformLayout();
    this.grpLevel2Criteria.EndInit();
    this.grpLevel2Criteria.ResumeLayout(false);
    this.grpLevel2Criteria.PerformLayout();
    this.pcBottom.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
