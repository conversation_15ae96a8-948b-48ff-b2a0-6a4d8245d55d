// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_4.Elbow.vwGeometry
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.Common;
using IntegriWISE.FormView.Properties;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEB31_4.Elbow;
using IntegriWISE.UserInterface.Component;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_4.Elbow;

public class vwGeometry : XtraUserControl, IGeometryView, IGeometryBaseView, IView
{
  private string _UMBendAngle;
  private GeometryPresenter _presenter;
  private IRecordView _recordView;
  private IBaseView _baseView;
  private IComponentView _componentView;
  private FormDirtyTracker _dirtyTracker;
  private IContainer components;
  private LabelControl labelControl1;
  private LabelControl labelControl2;
  private LabelControl labelControl4;
  private LabelControl labelControl6;
  private LabelControl umNominalThickness;
  private TextEdit txtNominalOD;
  private TextEdit txtNominalThickness;
  private TextEdit txtWeldJointEfficiency;
  private TextEdit txtMechanicalAllowance;
  private LabelControl umNominalOD;
  private LabelControl umMechanicalAllowance;
  private TableLayoutPanel tableLayoutPanel1;
  private DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider dxErrorProvider1;
  private LabelControl umBendAngle;
  private LabelControl umBendRadius;
  private TextEdit txtBendAngle;
  private TextEdit txtBendRadius;
  private LabelControl labelControl8;
  private LabelControl labelControl7;
  private PictureEdit pictureGeometry;

  public vwGeometry(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._baseView = (IBaseView) recordView;
    this._recordView.GeometryView = (IGeometryBaseView) this;
    this._presenter = new GeometryPresenter(this._recordView, (IGeometryView) this);
  }

  public vwGeometry(IComponentView componentView)
  {
    this.InitializeComponent();
    this._componentView = componentView;
    this._baseView = (IBaseView) componentView;
    this._presenter = new GeometryPresenter(this._componentView, (IGeometryView) this);
  }

  private void vwGeometry_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, this._baseView);
    this._dirtyTracker.IsHandled = true;
    this.txtNominalOD.AllowOnlyN4();
    this.txtNominalThickness.AllowOnlyN4();
    this.txtMechanicalAllowance.AllowOnlyN4();
    this.txtWeldJointEfficiency.AllowOnlyN4();
    this.txtBendAngle.AllowOnlyN4();
    this.txtBendRadius.AllowOnlyN4();
    this._presenter.LoadGeometry();
    this._dirtyTracker.IsHandled = false;
  }

  public int? GeometryID { get; set; }

  public double? NominalOD
  {
    get => Helpers.ParseNullDouble((object) this.txtNominalOD.Text);
    set => this.txtNominalOD.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? NominalThickness
  {
    get => Helpers.ParseNullDouble((object) this.txtNominalThickness.Text);
    set => this.txtNominalThickness.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? MechanicalAllowance
  {
    get => Helpers.ParseNullDouble((object) this.txtMechanicalAllowance.Text);
    set => this.txtMechanicalAllowance.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? WeldJointEfficiency
  {
    get => Helpers.ParseNullDouble((object) this.txtWeldJointEfficiency.Text);
    set => this.txtWeldJointEfficiency.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? BendRadius
  {
    get => Helpers.ParseNullDouble((object) this.txtBendRadius.Text);
    set => this.txtBendRadius.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? BendAngle
  {
    get => Helpers.ParseNullDouble((object) this.txtBendAngle.Text);
    set => this.txtBendAngle.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMNominalOD
  {
    set => this.umNominalOD.Text = value;
  }

  public string UMNominalThickness
  {
    set => this.umNominalThickness.Text = value;
  }

  public string UMMechanicalAllowance
  {
    set => this.umMechanicalAllowance.Text = value;
  }

  public string UMBendRadius
  {
    set => this.umBendRadius.Text = value;
  }

  public string UMBendAngle
  {
    get => this._UMBendAngle;
    set
    {
      this._UMBendAngle = value;
      this.umBendAngle.Text = value;
    }
  }

  public string NominalODErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtNominalOD, value);
  }

  public string NominalThicknessErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtNominalThickness, value);
  }

  public string MechanicalAllowanceErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtMechanicalAllowance, value);
  }

  public string WeldJointEfficiencyErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtWeldJointEfficiency, value);
  }

  public string BendRadiusErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtBendRadius, value);
  }

  public string BendAngleErr
  {
    set => this.dxErrorProvider1.SetError((Control) this.txtBendAngle, value);
  }

  public string NominalODInfo
  {
    set => this.txtNominalOD.ToolTip = value;
  }

  public string NominalThicknessInfo
  {
    set => this.txtNominalThickness.ToolTip = value;
  }

  public string MechanicalAllowanceInfo
  {
    set => this.txtMechanicalAllowance.ToolTip = value;
  }

  public string WeldJointEfficiencyInfo
  {
    set => this.txtWeldJointEfficiency.ToolTip = value;
  }

  public string BendRadiusInfo
  {
    set => this.txtBendRadius.ToolTip = value;
  }

  public string BendAngleInfo
  {
    set => this.txtBendAngle.ToolTip = value;
  }

  public bool ValidateGeometry() => this._presenter.Validate();

  public void Save() => this._presenter.Save();

  public void ClearErrors() => this.dxErrorProvider1.ClearErrors();

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    this.labelControl1 = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.labelControl4 = new LabelControl();
    this.labelControl6 = new LabelControl();
    this.umNominalThickness = new LabelControl();
    this.txtNominalOD = new TextEdit();
    this.txtNominalThickness = new TextEdit();
    this.txtWeldJointEfficiency = new TextEdit();
    this.txtMechanicalAllowance = new TextEdit();
    this.umNominalOD = new LabelControl();
    this.umMechanicalAllowance = new LabelControl();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.pictureGeometry = new PictureEdit();
    this.umBendAngle = new LabelControl();
    this.umBendRadius = new LabelControl();
    this.txtBendAngle = new TextEdit();
    this.txtBendRadius = new TextEdit();
    this.labelControl8 = new LabelControl();
    this.labelControl7 = new LabelControl();
    this.dxErrorProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider(this.components);
    this.txtNominalOD.Properties.BeginInit();
    this.txtNominalThickness.Properties.BeginInit();
    this.txtWeldJointEfficiency.Properties.BeginInit();
    this.txtMechanicalAllowance.Properties.BeginInit();
    this.tableLayoutPanel1.SuspendLayout();
    this.pictureGeometry.Properties.BeginInit();
    this.txtBendAngle.Properties.BeginInit();
    this.txtBendRadius.Properties.BeginInit();
    ((ISupportInitialize) this.dxErrorProvider1).BeginInit();
    this.SuspendLayout();
    this.labelControl1.Location = new Point(13, 13);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(143, 13);
    this.labelControl1.TabIndex = 0;
    this.labelControl1.Text = "Nominal Outside Diameter, Do";
    this.labelControl2.Location = new Point(13, 35);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(117, 13);
    this.labelControl2.TabIndex = 3;
    this.labelControl2.Text = "Nominal Thickness, tnom";
    this.labelControl4.Location = new Point(13, 57);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(112 /*0x70*/, 13);
    this.labelControl4.TabIndex = 6;
    this.labelControl4.Text = "Weld Joint Efficiency, E";
    this.labelControl6.Location = new Point(13, 79);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(125, 13);
    this.labelControl6.TabIndex = 8;
    this.labelControl6.Text = "Mechanical Allowance, MA";
    this.umNominalThickness.Location = new Point(264, 35);
    this.umNominalThickness.Name = "umNominalThickness";
    this.umNominalThickness.Size = new Size(41, 13);
    this.umNominalThickness.TabIndex = 5;
    this.umNominalThickness.Text = "measure";
    this.txtNominalOD.Location = new Point(160 /*0xA0*/, 11);
    this.txtNominalOD.Margin = new Padding(1);
    this.txtNominalOD.Name = "txtNominalOD";
    this.txtNominalOD.Size = new Size(100, 20);
    this.txtNominalOD.TabIndex = 1;
    this.txtNominalThickness.Location = new Point(160 /*0xA0*/, 33);
    this.txtNominalThickness.Margin = new Padding(1);
    this.txtNominalThickness.Name = "txtNominalThickness";
    this.txtNominalThickness.Size = new Size(100, 20);
    this.txtNominalThickness.TabIndex = 4;
    this.txtWeldJointEfficiency.Location = new Point(160 /*0xA0*/, 55);
    this.txtWeldJointEfficiency.Margin = new Padding(1);
    this.txtWeldJointEfficiency.Name = "txtWeldJointEfficiency";
    this.txtWeldJointEfficiency.Size = new Size(100, 20);
    this.txtWeldJointEfficiency.TabIndex = 7;
    this.txtMechanicalAllowance.Location = new Point(160 /*0xA0*/, 77);
    this.txtMechanicalAllowance.Margin = new Padding(1);
    this.txtMechanicalAllowance.Name = "txtMechanicalAllowance";
    this.txtMechanicalAllowance.Size = new Size(100, 20);
    this.txtMechanicalAllowance.TabIndex = 9;
    this.umNominalOD.Location = new Point(264, 13);
    this.umNominalOD.Name = "umNominalOD";
    this.umNominalOD.Size = new Size(41, 13);
    this.umNominalOD.TabIndex = 2;
    this.umNominalOD.Text = "measure";
    this.umMechanicalAllowance.Location = new Point(264, 79);
    this.umMechanicalAllowance.Name = "umMechanicalAllowance";
    this.umMechanicalAllowance.Size = new Size(41, 13);
    this.umMechanicalAllowance.TabIndex = 10;
    this.umMechanicalAllowance.Text = "measure";
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 3;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.pictureGeometry, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.umBendAngle, 2, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.umBendRadius, 2, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtBendAngle, 1, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtBendRadius, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl8, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl7, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.umMechanicalAllowance, 2, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.umNominalThickness, 2, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.umNominalOD, 2, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl4, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtMechanicalAllowance, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl6, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtNominalOD, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtNominalThickness, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtWeldJointEfficiency, 1, 2);
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 7;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(318, 303);
    this.tableLayoutPanel1.TabIndex = 0;
    this.tableLayoutPanel1.SetColumnSpan((Control) this.pictureGeometry, 3);
    this.pictureGeometry.EditValue = (object) Resources.IW_Elbow;
    this.pictureGeometry.Location = new Point(20, 152);
    this.pictureGeometry.Margin = new Padding(10);
    this.pictureGeometry.Name = "pictureGeometry";
    this.pictureGeometry.Properties.AllowFocused = false;
    this.pictureGeometry.Properties.AllowScrollViaMouseDrag = false;
    this.pictureGeometry.Properties.Appearance.BackColor = Color.Transparent;
    this.pictureGeometry.Properties.Appearance.Options.UseBackColor = true;
    this.pictureGeometry.Properties.BorderStyle = BorderStyles.NoBorder;
    this.pictureGeometry.Properties.ReadOnly = true;
    this.pictureGeometry.Properties.ShowMenu = false;
    this.pictureGeometry.Size = new Size(220, 131);
    this.pictureGeometry.TabIndex = 17;
    this.umBendAngle.Location = new Point(264, 123);
    this.umBendAngle.Name = "umBendAngle";
    this.umBendAngle.Size = new Size(41, 13);
    this.umBendAngle.TabIndex = 16 /*0x10*/;
    this.umBendAngle.Text = "measure";
    this.umBendRadius.Location = new Point(264, 101);
    this.umBendRadius.Name = "umBendRadius";
    this.umBendRadius.Size = new Size(41, 13);
    this.umBendRadius.TabIndex = 13;
    this.umBendRadius.Text = "measure";
    this.txtBendAngle.Location = new Point(160 /*0xA0*/, 121);
    this.txtBendAngle.Margin = new Padding(1);
    this.txtBendAngle.Name = "txtBendAngle";
    this.txtBendAngle.Size = new Size(100, 20);
    this.txtBendAngle.TabIndex = 15;
    this.txtBendRadius.Location = new Point(160 /*0xA0*/, 99);
    this.txtBendRadius.Margin = new Padding(1);
    this.txtBendRadius.Name = "txtBendRadius";
    this.txtBendRadius.Size = new Size(100, 20);
    this.txtBendRadius.TabIndex = 12;
    this.labelControl8.Location = new Point(13, 123);
    this.labelControl8.Name = "labelControl8";
    this.labelControl8.Size = new Size(54, 13);
    this.labelControl8.TabIndex = 14;
    this.labelControl8.Text = "Bend Angle";
    this.labelControl7.Location = new Point(13, 101);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(79, 13);
    this.labelControl7.TabIndex = 11;
    this.labelControl7.Text = "Bend Radius, Rb";
    this.dxErrorProvider1.ContainerControl = (ContainerControl) this;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoSize = true;
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = nameof (vwGeometry);
    this.Size = new Size(555, 561);
    this.Load += new EventHandler(this.vwGeometry_Load);
    this.txtNominalOD.Properties.EndInit();
    this.txtNominalThickness.Properties.EndInit();
    this.txtWeldJointEfficiency.Properties.EndInit();
    this.txtMechanicalAllowance.Properties.EndInit();
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.pictureGeometry.Properties.EndInit();
    this.txtBendAngle.Properties.EndInit();
    this.txtBendRadius.Properties.EndInit();
    ((ISupportInitialize) this.dxErrorProvider1).EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
