// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_4.Elbow.LocalMetalLossGrooveLike.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.Common.Tools;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEB31_4.Elbow.LocalMetalLossGrooveLike;
using IntegriWISE.UserInterface.Record;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_4.Elbow.LocalMetalLossGrooveLike;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private IRecordView _recordView;
  private ResultPresenter _presenter;
  private string _Intermediate_tmm_Label;
  private string _Intermediate_tmm_Value;
  private string _Intermediate_tmm_UM;
  private string _Intermediate_trd_Label;
  private string _Intermediate_trd_Value;
  private string _Intermediate_trd_UM;
  private string _Intermediate_tc_Label;
  private string _Intermediate_tc_Value;
  private string _Intermediate_tc_UM;
  private string _MAWP_Label;
  private string _MAWP_Value;
  private string _MAWP_UM;
  private string _MAWPr_Label;
  private string _MAWPr_Value;
  private string _MAWPr_UM;
  private string _Results_lambda_Label;
  private string _Results_lambda_Value;
  private string _Results_lambdaC_Label;
  private string _Results_lambdaC_Value;
  public string _Results_RSF_Label;
  public string _Results_RSF_Value;
  public string _Results_TSF_Label;
  public string _Results_TSF_Value;
  public string _Criteria_RtGreaterOrEqual0_20_Label;
  public string _Criteria_RtGreaterOrEqual0_20_Value;
  public string _Criteria_tmmMinusFCAGreaterOrEqual2_5_Label;
  public string _Criteria_tmmMinusFCAGreaterOrEqual2_5_Value;
  public string _Criteria_LmsdGreaterOrEqual1_8Dtc_Label;
  public string _Criteria_LmsdGreaterOrEqual1_8Dtc_Value;
  public string _Criteria_grGreaterOrEqual1MinusRttc_Label;
  public string _Criteria_grGreaterOrEqual1MinusRttc_Value;
  public string _Criteria_lambdaCLessThanEqual9_Label;
  public string _Criteria_lambdaCLessThanEqual9_Value;
  public string _Criteria_DDivTcGreaterThanEqual20_Label;
  public string _Criteria_DDivTcGreaterThanEqual20_Value;
  public string _Criteria_RSFBetween07And1_Label;
  public string _Criteria_RSFBetween07And1_Value;
  public string _Criteria_ElBetween07And1_Label;
  public string _Criteria_ElBetween07And1_Value;
  public string _Criteria_EcBetween07And1_Label;
  public string _Criteria_EcBetween07And1_Value;
  public string _Criteria_ScreeningCriteriaFigure5_6_Label;
  public string _Criteria_ScreeningCriteriaFigure5_6_Value;
  public string _Criteria_RSFGreaterEqualRSFa_Label;
  public string _Criteria_RSFGreaterEqualRSFa_Value;
  public string _Criteria_ScreeningCriteriaFigure5_8_Label;
  public string _Criteria_ScreeningCriteriaFigure5_8_Value;
  private string _conclusion_Level1;
  private string _Conclusion_ConditionLongitudinalExtentLevel1;
  private string _Conclusion_ConditionCircumferentialExtentLevel1;
  private IContainer components;
  private GroupControl grpIntermediateLevel1Result;
  private TableLayoutPanel tblIntermediateLevel1Result;
  private GroupControl grpLevel1AssessmentResult;
  private TableLayoutPanel tblLevel1Result;
  private GroupControl grpLevel1Criteria;
  private TableLayoutPanel tblLevel1Criteria;
  private GroupControl grpLevel1Conclusion;
  private TableLayoutPanel tblLevel1Conclusion;
  private GroupControl groupControl3;
  private TableLayoutPanel tableLayoutPanel3;
  private MemoEdit txtWarningMessages;
  private GroupControl grpSummary;
  private TableLayoutPanel tblSummary;
  private GroupControl grpMAWP;
  private TableLayoutPanel tblMAWP;

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.ResultView = (IResultBaseView) this;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  private void vwResult_Load(object sender, EventArgs e)
  {
  }

  public void AddResultLabel(string tableName, int rowNum, string value)
  {
    TableLayoutPanel tableLayoutPanel = ((IEnumerable<Control>) this.Controls.Find(tableName, true)).FirstOrDefault<Control>() as TableLayoutPanel;
    if (tableLayoutPanel.RowCount < rowNum + 1)
      tableLayoutPanel.RowCount = rowNum + 1;
    LabelControl labelControl1 = new LabelControl();
    labelControl1.Text = value;
    labelControl1.Padding = new Padding(0, 1, 0, 0);
    LabelControl labelControl2 = labelControl1;
    tableLayoutPanel.Controls.Add((Control) labelControl2, 0, rowNum + 1);
  }

  public void AddResultValue(string tableName, int rowNum, string value)
  {
    TableLayoutPanel tableLayoutPanel = ((IEnumerable<Control>) this.Controls.Find(tableName, true)).FirstOrDefault<Control>() as TableLayoutPanel;
    if (tableLayoutPanel.RowCount < rowNum + 1)
      tableLayoutPanel.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = value;
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    tableLayoutPanel.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  public void AddResultUM(string tableName, int rowNum, string value)
  {
    TableLayoutPanel tableLayoutPanel = ((IEnumerable<Control>) this.Controls.Find(tableName, true)).FirstOrDefault<Control>() as TableLayoutPanel;
    if (tableLayoutPanel.RowCount < rowNum + 1)
      tableLayoutPanel.RowCount = rowNum + 1;
    LabelControl labelControl1 = new LabelControl();
    labelControl1.Text = value;
    labelControl1.Padding = new Padding(0, 1, 0, 0);
    LabelControl labelControl2 = labelControl1;
    tableLayoutPanel.Controls.Add((Control) labelControl2, 2, rowNum + 1);
  }

  public void AddConclusionValue(
    string tableName,
    int rowNum,
    string value,
    bool result,
    bool ShowColour = false)
  {
    TableLayoutPanel tableLayoutPanel = ((IEnumerable<Control>) this.Controls.Find(tableName, true)).FirstOrDefault<Control>() as TableLayoutPanel;
    if (tableLayoutPanel.RowCount < rowNum + 1)
      tableLayoutPanel.RowCount = rowNum + 1;
    MemoEdit memoEdit1 = new MemoEdit();
    memoEdit1.Dock = DockStyle.Fill;
    memoEdit1.Text = value.ParseObjectToString();
    memoEdit1.Margin = new Padding(1);
    MemoEdit memoEdit2 = memoEdit1;
    memoEdit2.Properties.ReadOnly = true;
    memoEdit2.Properties.ScrollBars = ScrollBars.None;
    memoEdit2.AutoSizeInLayoutControl = true;
    memoEdit2.Size = new Size(tableLayoutPanel.Width, 20);
    if (ShowColour)
    {
      memoEdit2.BackColor = result ? Color.Green : Color.Red;
      memoEdit2.ForeColor = Color.White;
    }
    tableLayoutPanel.SetColumnSpan((Control) memoEdit2, 3);
    tableLayoutPanel.Controls.Add((Control) memoEdit2, 0, rowNum + 1);
  }

  public bool Calculate()
  {
    this.tblSummary.Controls.Clear();
    this.tblIntermediateLevel1Result.Controls.Clear();
    this.tblMAWP.Controls.Clear();
    this.tblLevel1Conclusion.Controls.Clear();
    this.tblLevel1Criteria.Controls.Clear();
    this.tblLevel1Result.Controls.Clear();
    return this._presenter.Calculate();
  }

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public string ResultMessages
  {
    get => this.txtWarningMessages.Text;
    set => this.txtWarningMessages.Text = value;
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public string CodeEdition => this._presenter.CodeEdition;

  public bool MAWPVisible
  {
    get => this.grpMAWP.Visible;
    set => this.grpMAWP.Visible = value;
  }

  public string Title => $"Assessment to API 579 Part 5. Local Metal Loss. {"To Level 1"}";

  public string Introduction
  {
    get
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append("The section 5 assessment procedure is aimed at examining pressure equipment subject to local");
      stringBuilder.Append("metal loss resulting from corrosion or erosion. The assessment methodology is based on establishing");
      stringBuilder.Append("a remaining strength factor (RSF) for the affected area, for comparison with RSF acceptance criteria.");
      stringBuilder.Append("The types of flaws characterised as local metal loss for the section 5 assessment");
      stringBuilder.Append("procedure are locally thinned areas (LTA's) and groove-like flaws.");
      return stringBuilder.ToString();
    }
  }

  public string CommentsAndAssumptions
  {
    get
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.AppendLine("The local metal loss procedure in Part 5 assumes that:");
      stringBuilder.AppendLine("1. The original design criteria were in accordance with a recognised code or standard.");
      stringBuilder.AppendLine("2. The component is not operating in the creep range.");
      stringBuilder.AppendLine("3. The component is not in cyclic service.");
      stringBuilder.AppendLine("4. The material is considered to have sufficient material toughness.");
      stringBuilder.AppendLine("5. Level 1 covers Type A Components (Part 4, paragraph 4.2.5) subject to internal pressure. (i.e. supplemental loads are assumed negligible).");
      stringBuilder.AppendLine("6. Level 2 covers type A or B Components (see Part 4, paragraph 4.2.5) subject to internal pressure, external pressure, supplemental loads (see Annex A, paragraph A.2.7), or any combination of loading.");
      stringBuilder.AppendLine("If the component is a cylindrical shell, conical shell, or elbow, the circumferential extent of the flaw is evaluated using the procedure in paragraph 5.4.2.2.i) ");
      stringBuilder.AppendLine("It is considered that supplemental loads are not present or are not significant. If supplemental loads are significant, then for Level 2 Assessment, the circumferential extent of the region of local metal loss shall be evaluated using the procedures in paragraph 5.4.3.4.    ");
      return stringBuilder.ToString();
    }
  }

  public string Limitations => new StringBuilder().ToString();

  public string References
  {
    get
    {
      StringBuilder stringBuilder = new StringBuilder();
      stringBuilder.Append("1. API 579 \"Fitness-for-Service\", Second Edition, The American Society of Mechanical Engineers.");
      stringBuilder.AppendLine("Part 5: Assessment of Local Metal Loss");
      return stringBuilder.ToString();
    }
  }

  public string Summary_S_Label
  {
    set => this.AddResultLabel("tblSummary", 0, value);
  }

  public double Summary_S_Value
  {
    set => this.AddResultValue("tblSummary", 0, value.DimensionFromIW().ToString("n4"));
  }

  public string Summary_S_UM
  {
    set => this.AddResultUM("tblSummary", 0, value);
  }

  public string Summary_C_Label
  {
    set => this.AddResultLabel("tblSummary", 1, value);
  }

  public double Summary_C_Value
  {
    set => this.AddResultValue("tblSummary", 1, value.DimensionFromIW().ToString("n4"));
  }

  public string Summary_C_UM
  {
    set => this.AddResultUM("tblSummary", 1, value);
  }

  public string Intermediate_tmm_Label
  {
    get => this._Intermediate_tmm_Label;
    set
    {
      this.AddResultLabel("tblIntermediateLevel1Result", 0, value);
      this._Intermediate_tmm_Label = value;
    }
  }

  public string Intermediate_tmm_Value
  {
    get => this._Intermediate_tmm_Value;
    set
    {
      this.AddResultValue("tblIntermediateLevel1Result", 0, value);
      this._Intermediate_tmm_Value = value;
    }
  }

  public string Intermediate_tmm_UM
  {
    get => this._Intermediate_tmm_UM;
    set
    {
      this.AddResultUM("tblIntermediateLevel1Result", 0, value);
      this._Intermediate_tmm_UM = value;
    }
  }

  public string Intermediate_trd_Label
  {
    get => this._Intermediate_trd_Label;
    set
    {
      this.AddResultLabel("tblIntermediateLevel1Result", 1, value);
      this._Intermediate_trd_Label = value;
    }
  }

  public string Intermediate_trd_Value
  {
    get => this._Intermediate_trd_Value;
    set
    {
      this.AddResultValue("tblIntermediateLevel1Result", 1, value);
      this._Intermediate_trd_Value = value;
    }
  }

  public string Intermediate_trd_UM
  {
    get => this._Intermediate_trd_UM;
    set
    {
      this.AddResultUM("tblIntermediateLevel1Result", 1, value);
      this._Intermediate_trd_UM = value;
    }
  }

  public string Intermediate_tc_Label
  {
    get => this._Intermediate_tc_Label;
    set
    {
      this.AddResultLabel("tblIntermediateLevel1Result", 2, value);
      this._Intermediate_tc_Label = value;
    }
  }

  public string Intermediate_tc_Value
  {
    get => this._Intermediate_tc_Value;
    set
    {
      this.AddResultValue("tblIntermediateLevel1Result", 2, value);
      this._Intermediate_tc_Value = value;
    }
  }

  public string Intermediate_tc_UM
  {
    get => this._Intermediate_tc_UM;
    set
    {
      this.AddResultUM("tblIntermediateLevel1Result", 2, value);
      this._Intermediate_tc_UM = value;
    }
  }

  public string MAWP_Label
  {
    get => this._MAWP_Label;
    set
    {
      this.AddResultLabel("tblMAWP", 0, value);
      this._MAWP_Label = value;
    }
  }

  public string MAWP_Value
  {
    get => this._MAWP_Value;
    set
    {
      this.AddResultValue("tblMAWP", 0, value);
      this._MAWP_Value = value;
    }
  }

  public string MAWP_UM
  {
    get => this._MAWP_UM;
    set
    {
      this.AddResultUM("tblMAWP", 0, value);
      this._MAWP_UM = value;
    }
  }

  public string MAWPr_Label
  {
    get => this._MAWPr_Label;
    set
    {
      this.AddResultLabel("tblMAWP", 1, value);
      this._MAWPr_Label = value;
    }
  }

  public string MAWPr_Value
  {
    get => this._MAWPr_Value;
    set
    {
      this.AddResultValue("tblMAWP", 1, value);
      this._MAWPr_Value = value;
    }
  }

  public string MAWPr_UM
  {
    get => this._MAWPr_UM;
    set
    {
      this.AddResultUM("tblMAWP", 1, value);
      this._MAWPr_UM = value;
    }
  }

  public string Results_lambda_Label
  {
    get => this._Results_lambda_Label;
    set
    {
      this.AddResultLabel("tblLevel1Result", 0, value);
      this._Results_lambda_Label = value;
    }
  }

  public string Results_lambda_Value
  {
    get => this._Results_lambda_Value;
    set
    {
      this.AddResultValue("tblLevel1Result", 0, value);
      this._Results_lambda_Value = value;
    }
  }

  public string Results_lambdaC_Label
  {
    get => this._Results_lambdaC_Label;
    set
    {
      this.AddResultLabel("tblLevel1Result", 1, value);
      this._Results_lambdaC_Label = value;
    }
  }

  public string Results_lambdaC_Value
  {
    get => this._Results_lambdaC_Value;
    set
    {
      this.AddResultValue("tblLevel1Result", 1, value);
      this._Results_lambdaC_Value = value;
    }
  }

  public string Results_RSF_Label
  {
    get => this._Results_RSF_Label;
    set
    {
      this.AddResultLabel("tblLevel1Result", 2, value);
      this._Results_RSF_Label = value;
    }
  }

  public string Results_RSF_Value
  {
    get => this._Results_RSF_Value;
    set
    {
      this.AddResultValue("tblLevel1Result", 2, value);
      this._Results_RSF_Value = value;
    }
  }

  public string Results_TSF_Label
  {
    get => this._Results_TSF_Label;
    set
    {
      this.AddResultLabel("tblLevel1Result", 3, value);
      this._Results_TSF_Label = value;
    }
  }

  public string Results_TSF_Value
  {
    get => this._Results_TSF_Value;
    set
    {
      this.AddResultValue("tblLevel1Result", 3, value);
      this._Results_TSF_Value = value;
    }
  }

  public string Criteria_RtGreaterOrEqual0_20_Label
  {
    get => this._Criteria_RtGreaterOrEqual0_20_Label;
    set
    {
      this.AddResultLabel("tblLevel1Criteria", 0, value);
      this._Criteria_RtGreaterOrEqual0_20_Label = value;
    }
  }

  public string Criteria_RtGreaterOrEqual0_20_Value
  {
    get => this._Criteria_RtGreaterOrEqual0_20_Value;
    set
    {
      this.AddResultValue("tblLevel1Criteria", 0, value);
      this._Criteria_RtGreaterOrEqual0_20_Value = value;
    }
  }

  public string Criteria_tmmMinusFCAGreaterOrEqual2_5_Label
  {
    get => this._Criteria_tmmMinusFCAGreaterOrEqual2_5_Label;
    set
    {
      this.AddResultLabel("tblLevel1Criteria", 1, value);
      this._Criteria_tmmMinusFCAGreaterOrEqual2_5_Label = value;
    }
  }

  public string Criteria_tmmMinusFCAGreaterOrEqual2_5_Value
  {
    get => this._Criteria_tmmMinusFCAGreaterOrEqual2_5_Value;
    set
    {
      this.AddResultValue("tblLevel1Criteria", 1, value);
      this._Criteria_tmmMinusFCAGreaterOrEqual2_5_Value = value;
    }
  }

  public string Criteria_LmsdGreaterOrEqual1_8Dtc_Label
  {
    get => this._Criteria_LmsdGreaterOrEqual1_8Dtc_Label;
    set
    {
      this.AddResultLabel("tblLevel1Criteria", 2, value);
      this._Criteria_LmsdGreaterOrEqual1_8Dtc_Label = value;
    }
  }

  public string Criteria_LmsdGreaterOrEqual1_8Dtc_Value
  {
    get => this._Criteria_LmsdGreaterOrEqual1_8Dtc_Value;
    set
    {
      this.AddResultValue("tblLevel1Criteria", 2, value);
      this._Criteria_LmsdGreaterOrEqual1_8Dtc_Value = value;
    }
  }

  public string Criteria_grGreaterOrEqual1MinusRttc_Label
  {
    get => this._Criteria_grGreaterOrEqual1MinusRttc_Label;
    set
    {
      this.AddResultLabel("tblLevel1Criteria", 3, value);
      this._Criteria_grGreaterOrEqual1MinusRttc_Label = value;
    }
  }

  public string Criteria_grGreaterOrEqual1MinusRttc_Value
  {
    get => this._Criteria_grGreaterOrEqual1MinusRttc_Value;
    set
    {
      this.AddResultValue("tblLevel1Criteria", 3, value);
      this._Criteria_grGreaterOrEqual1MinusRttc_Value = value;
    }
  }

  public string Criteria_lambdaCLessThanEqual9_Label
  {
    get => this._Criteria_lambdaCLessThanEqual9_Label;
    set
    {
      this.AddResultLabel("tblLevel1Criteria", 4, value);
      this._Criteria_lambdaCLessThanEqual9_Label = value;
    }
  }

  public string Criteria_lambdaCLessThanEqual9_Value
  {
    get => this._Criteria_lambdaCLessThanEqual9_Value;
    set
    {
      this.AddResultValue("tblLevel1Criteria", 4, value);
      this._Criteria_lambdaCLessThanEqual9_Value = value;
    }
  }

  public string Criteria_DDivTcGreaterThanEqual20_Label
  {
    get => this._Criteria_DDivTcGreaterThanEqual20_Label;
    set
    {
      this.AddResultLabel("tblLevel1Criteria", 5, value);
      this._Criteria_DDivTcGreaterThanEqual20_Label = value;
    }
  }

  public string Criteria_DDivTcGreaterThanEqual20_Value
  {
    get => this._Criteria_DDivTcGreaterThanEqual20_Value;
    set
    {
      this.AddResultValue("tblLevel1Criteria", 5, value);
      this._Criteria_DDivTcGreaterThanEqual20_Value = value;
    }
  }

  public string Criteria_RSFBetween07And1_Label
  {
    get => this._Criteria_RSFBetween07And1_Label;
    set
    {
      this.AddResultLabel("tblLevel1Criteria", 6, value);
      this._Criteria_RSFBetween07And1_Label = value;
    }
  }

  public string Criteria_RSFBetween07And1_Value
  {
    get => this._Criteria_RSFBetween07And1_Value;
    set
    {
      this.AddResultValue("tblLevel1Criteria", 6, value);
      this._Criteria_RSFBetween07And1_Value = value;
    }
  }

  public string Criteria_ElBetween07And1_Label
  {
    get => this._Criteria_ElBetween07And1_Label;
    set
    {
      this.AddResultLabel("tblLevel1Criteria", 7, value);
      this._Criteria_ElBetween07And1_Label = value;
    }
  }

  public string Criteria_ElBetween07And1_Value
  {
    get => this._Criteria_ElBetween07And1_Value;
    set
    {
      this.AddResultValue("tblLevel1Criteria", 7, value);
      this._Criteria_ElBetween07And1_Value = value;
    }
  }

  public string Criteria_EcBetween07And1_Label
  {
    get => this._Criteria_EcBetween07And1_Label;
    set
    {
      this.AddResultLabel("tblLevel1Criteria", 8, value);
      this._Criteria_EcBetween07And1_Label = value;
    }
  }

  public string Criteria_EcBetween07And1_Value
  {
    get => this._Criteria_EcBetween07And1_Value;
    set
    {
      this.AddResultValue("tblLevel1Criteria", 8, value);
      this._Criteria_EcBetween07And1_Value = value;
    }
  }

  public string Criteria_ScreeningCriteriaFigure5_6_Label
  {
    get => this._Criteria_ScreeningCriteriaFigure5_6_Label;
    set
    {
      this.AddResultLabel("tblLevel1Criteria", 9, value);
      this._Criteria_ScreeningCriteriaFigure5_6_Label = value;
    }
  }

  public string Criteria_ScreeningCriteriaFigure5_6_Value
  {
    get => this._Criteria_ScreeningCriteriaFigure5_6_Value;
    set
    {
      this.AddResultValue("tblLevel1Criteria", 9, value);
      this._Criteria_ScreeningCriteriaFigure5_6_Value = value;
    }
  }

  public string Criteria_RSFGreaterEqualRSFa_Label
  {
    get => this._Criteria_RSFGreaterEqualRSFa_Label;
    set
    {
      this.AddResultLabel("tblLevel1Criteria", 10, value);
      this._Criteria_RSFGreaterEqualRSFa_Label = value;
    }
  }

  public string Criteria_RSFGreaterEqualRSFa_Value
  {
    get => this._Criteria_RSFGreaterEqualRSFa_Value;
    set
    {
      this.AddResultValue("tblLevel1Criteria", 10, value);
      this._Criteria_RSFGreaterEqualRSFa_Value = value;
    }
  }

  public string Criteria_ScreeningCriteriaFigure5_8_Label
  {
    get => this._Criteria_ScreeningCriteriaFigure5_8_Label;
    set
    {
      this.AddResultLabel("tblLevel1Criteria", 11, value);
      this._Criteria_ScreeningCriteriaFigure5_8_Label = value;
    }
  }

  public string Criteria_ScreeningCriteriaFigure5_8_Value
  {
    get => this._Criteria_ScreeningCriteriaFigure5_8_Value;
    set
    {
      this.AddResultValue("tblLevel1Criteria", 11, value);
      this._Criteria_ScreeningCriteriaFigure5_8_Value = value;
    }
  }

  public bool Level1Passed { get; set; }

  public string Conclusion_ConditionLongitudinalExtentLevel1
  {
    get => this._Conclusion_ConditionLongitudinalExtentLevel1;
    set
    {
      this.AddConclusionValue("tblLevel1Conclusion", 0, value, false, false);
      this._Conclusion_ConditionLongitudinalExtentLevel1 = value;
    }
  }

  public string Conclusion_ConditionCircumferentialExtentLevel1
  {
    get => this._Conclusion_ConditionCircumferentialExtentLevel1;
    set
    {
      this.AddConclusionValue("tblLevel1Conclusion", 1, value, false, false);
      this._Conclusion_ConditionCircumferentialExtentLevel1 = value;
    }
  }

  public string Conclusion_Level1
  {
    get => this._conclusion_Level1;
    set
    {
      this.AddConclusionValue("tblLevel1Conclusion", 2, value, this.Level1Passed, true);
      this._conclusion_Level1 = value;
    }
  }

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.grpIntermediateLevel1Result = new GroupControl();
    this.tblIntermediateLevel1Result = new TableLayoutPanel();
    this.grpLevel1AssessmentResult = new GroupControl();
    this.tblLevel1Result = new TableLayoutPanel();
    this.grpLevel1Criteria = new GroupControl();
    this.tblLevel1Criteria = new TableLayoutPanel();
    this.grpLevel1Conclusion = new GroupControl();
    this.tblLevel1Conclusion = new TableLayoutPanel();
    this.groupControl3 = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.txtWarningMessages = new MemoEdit();
    this.grpSummary = new GroupControl();
    this.tblSummary = new TableLayoutPanel();
    this.grpMAWP = new GroupControl();
    this.tblMAWP = new TableLayoutPanel();
    this.grpIntermediateLevel1Result.BeginInit();
    this.grpIntermediateLevel1Result.SuspendLayout();
    this.grpLevel1AssessmentResult.BeginInit();
    this.grpLevel1AssessmentResult.SuspendLayout();
    this.grpLevel1Criteria.BeginInit();
    this.grpLevel1Criteria.SuspendLayout();
    this.grpLevel1Conclusion.BeginInit();
    this.grpLevel1Conclusion.SuspendLayout();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.txtWarningMessages.Properties.BeginInit();
    this.grpSummary.BeginInit();
    this.grpSummary.SuspendLayout();
    this.grpMAWP.BeginInit();
    this.grpMAWP.SuspendLayout();
    this.SuspendLayout();
    this.grpIntermediateLevel1Result.AutoSize = true;
    this.grpIntermediateLevel1Result.Controls.Add((Control) this.tblIntermediateLevel1Result);
    this.grpIntermediateLevel1Result.Dock = DockStyle.Top;
    this.grpIntermediateLevel1Result.Location = new Point(0, 43);
    this.grpIntermediateLevel1Result.Name = "grpIntermediateLevel1Result";
    this.grpIntermediateLevel1Result.Size = new Size(686, 43);
    this.grpIntermediateLevel1Result.TabIndex = 4;
    this.grpIntermediateLevel1Result.Text = "Intermediate Result";
    this.tblIntermediateLevel1Result.AutoSize = true;
    this.tblIntermediateLevel1Result.ColumnCount = 3;
    this.tblIntermediateLevel1Result.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateLevel1Result.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateLevel1Result.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateLevel1Result.Dock = DockStyle.Fill;
    this.tblIntermediateLevel1Result.Location = new Point(2, 21);
    this.tblIntermediateLevel1Result.Name = "tblIntermediateLevel1Result";
    this.tblIntermediateLevel1Result.Padding = new Padding(10);
    this.tblIntermediateLevel1Result.RowCount = 1;
    this.tblIntermediateLevel1Result.RowStyles.Add(new RowStyle());
    this.tblIntermediateLevel1Result.Size = new Size(682, 20);
    this.tblIntermediateLevel1Result.TabIndex = 0;
    this.grpLevel1AssessmentResult.AutoSize = true;
    this.grpLevel1AssessmentResult.Controls.Add((Control) this.tblLevel1Result);
    this.grpLevel1AssessmentResult.Dock = DockStyle.Top;
    this.grpLevel1AssessmentResult.Location = new Point(0, 129);
    this.grpLevel1AssessmentResult.Name = "grpLevel1AssessmentResult";
    this.grpLevel1AssessmentResult.Size = new Size(686, 43);
    this.grpLevel1AssessmentResult.TabIndex = 1;
    this.grpLevel1AssessmentResult.Text = "Assessment Result";
    this.tblLevel1Result.AutoSize = true;
    this.tblLevel1Result.ColumnCount = 3;
    this.tblLevel1Result.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Result.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Result.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Result.Dock = DockStyle.Fill;
    this.tblLevel1Result.Location = new Point(2, 21);
    this.tblLevel1Result.Name = "tblLevel1Result";
    this.tblLevel1Result.Padding = new Padding(10);
    this.tblLevel1Result.RowCount = 1;
    this.tblLevel1Result.RowStyles.Add(new RowStyle());
    this.tblLevel1Result.Size = new Size(682, 20);
    this.tblLevel1Result.TabIndex = 0;
    this.grpLevel1Criteria.AutoSize = true;
    this.grpLevel1Criteria.Controls.Add((Control) this.tblLevel1Criteria);
    this.grpLevel1Criteria.Dock = DockStyle.Top;
    this.grpLevel1Criteria.Location = new Point(0, 172);
    this.grpLevel1Criteria.Name = "grpLevel1Criteria";
    this.grpLevel1Criteria.Size = new Size(686, 43);
    this.grpLevel1Criteria.TabIndex = 2;
    this.grpLevel1Criteria.Text = "Assessment Criteria";
    this.tblLevel1Criteria.AutoSize = true;
    this.tblLevel1Criteria.ColumnCount = 3;
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.Dock = DockStyle.Fill;
    this.tblLevel1Criteria.Location = new Point(2, 21);
    this.tblLevel1Criteria.Name = "tblLevel1Criteria";
    this.tblLevel1Criteria.Padding = new Padding(10);
    this.tblLevel1Criteria.RowCount = 1;
    this.tblLevel1Criteria.RowStyles.Add(new RowStyle());
    this.tblLevel1Criteria.Size = new Size(682, 20);
    this.tblLevel1Criteria.TabIndex = 1;
    this.grpLevel1Conclusion.AutoSize = true;
    this.grpLevel1Conclusion.Controls.Add((Control) this.tblLevel1Conclusion);
    this.grpLevel1Conclusion.Dock = DockStyle.Top;
    this.grpLevel1Conclusion.Location = new Point(0, 215);
    this.grpLevel1Conclusion.Name = "grpLevel1Conclusion";
    this.grpLevel1Conclusion.Size = new Size(686, 43);
    this.grpLevel1Conclusion.TabIndex = 3;
    this.grpLevel1Conclusion.Text = "Assessment Conclusion";
    this.tblLevel1Conclusion.AutoSize = true;
    this.tblLevel1Conclusion.ColumnCount = 3;
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.Dock = DockStyle.Fill;
    this.tblLevel1Conclusion.Location = new Point(2, 21);
    this.tblLevel1Conclusion.Name = "tblLevel1Conclusion";
    this.tblLevel1Conclusion.Padding = new Padding(10);
    this.tblLevel1Conclusion.RowCount = 1;
    this.tblLevel1Conclusion.RowStyles.Add(new RowStyle());
    this.tblLevel1Conclusion.Size = new Size(682, 20);
    this.tblLevel1Conclusion.TabIndex = 1;
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel3);
    this.groupControl3.Dock = DockStyle.Fill;
    this.groupControl3.Location = new Point(0, 258);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(686, 352);
    this.groupControl3.TabIndex = 5;
    this.groupControl3.Text = "Warning Messages";
    this.tableLayoutPanel3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 280f));
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 280f));
    this.tableLayoutPanel3.Size = new Size(682, 329);
    this.tableLayoutPanel3.TabIndex = 0;
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.MinimumSize = new Size(10, 10);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(656, 303);
    this.txtWarningMessages.TabIndex = 4;
    this.grpSummary.AutoSize = true;
    this.grpSummary.Controls.Add((Control) this.tblSummary);
    this.grpSummary.Dock = DockStyle.Top;
    this.grpSummary.Location = new Point(0, 0);
    this.grpSummary.Name = "grpSummary";
    this.grpSummary.Size = new Size(686, 43);
    this.grpSummary.TabIndex = 0;
    this.grpSummary.Text = "Inspection Data Summary";
    this.tblSummary.AutoSize = true;
    this.tblSummary.ColumnCount = 3;
    this.tblSummary.ColumnStyles.Add(new ColumnStyle());
    this.tblSummary.ColumnStyles.Add(new ColumnStyle());
    this.tblSummary.ColumnStyles.Add(new ColumnStyle());
    this.tblSummary.Dock = DockStyle.Fill;
    this.tblSummary.Location = new Point(2, 21);
    this.tblSummary.Name = "tblSummary";
    this.tblSummary.Padding = new Padding(10);
    this.tblSummary.RowCount = 1;
    this.tblSummary.RowStyles.Add(new RowStyle());
    this.tblSummary.Size = new Size(682, 20);
    this.tblSummary.TabIndex = 0;
    this.grpMAWP.AutoSize = true;
    this.grpMAWP.Controls.Add((Control) this.tblMAWP);
    this.grpMAWP.Dock = DockStyle.Top;
    this.grpMAWP.Location = new Point(0, 86);
    this.grpMAWP.Name = "grpMAWP";
    this.grpMAWP.Size = new Size(686, 43);
    this.grpMAWP.TabIndex = 6;
    this.grpMAWP.Text = "Maximum Allowable Working Pressure";
    this.tblMAWP.AutoSize = true;
    this.tblMAWP.ColumnCount = 3;
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.Dock = DockStyle.Fill;
    this.tblMAWP.Location = new Point(2, 21);
    this.tblMAWP.Name = "tblMAWP";
    this.tblMAWP.Padding = new Padding(10);
    this.tblMAWP.RowCount = 1;
    this.tblMAWP.RowStyles.Add(new RowStyle());
    this.tblMAWP.Size = new Size(682, 20);
    this.tblMAWP.TabIndex = 0;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.Controls.Add((Control) this.groupControl3);
    this.Controls.Add((Control) this.grpLevel1Conclusion);
    this.Controls.Add((Control) this.grpLevel1Criteria);
    this.Controls.Add((Control) this.grpLevel1AssessmentResult);
    this.Controls.Add((Control) this.grpMAWP);
    this.Controls.Add((Control) this.grpIntermediateLevel1Result);
    this.Controls.Add((Control) this.grpSummary);
    this.Name = nameof (vwResult);
    this.Size = new Size(686, 610);
    this.Load += new EventHandler(this.vwResult_Load);
    this.grpIntermediateLevel1Result.EndInit();
    this.grpIntermediateLevel1Result.ResumeLayout(false);
    this.grpIntermediateLevel1Result.PerformLayout();
    this.grpLevel1AssessmentResult.EndInit();
    this.grpLevel1AssessmentResult.ResumeLayout(false);
    this.grpLevel1AssessmentResult.PerformLayout();
    this.grpLevel1Criteria.EndInit();
    this.grpLevel1Criteria.ResumeLayout(false);
    this.grpLevel1Criteria.PerformLayout();
    this.grpLevel1Conclusion.EndInit();
    this.grpLevel1Conclusion.ResumeLayout(false);
    this.grpLevel1Conclusion.PerformLayout();
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.tableLayoutPanel3.ResumeLayout(false);
    this.txtWarningMessages.Properties.EndInit();
    this.grpSummary.EndInit();
    this.grpSummary.ResumeLayout(false);
    this.grpSummary.PerformLayout();
    this.grpMAWP.EndInit();
    this.grpMAWP.ResumeLayout(false);
    this.grpMAWP.PerformLayout();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
