// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEB31_4.vwMaterial
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEB31_4;
using IntegriWISE.UserInterface.Material;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEB31_4;

public class vwMaterial : RecordTabControl, IMaterialView, IMaterialBaseView, IView
{
  private IContainer components;
  private TableLayoutPanel tableLayoutPanel1;
  private LabelControl labelControl13;
  private TextEdit txtMaterialSpecNo;
  private TextEdit txtDesignCode;
  private LabelControl labelControl4;
  private LabelControl labelControl1;
  private LabelControl labelControl7;
  private LabelControl labelControl11;
  private TableLayoutPanel tableLayoutPanel2;
  private LabelControl umAllowableStrength;
  private TextEdit txtAllowableStress;
  private TableLayoutPanel tableLayoutPanel3;
  private TextEdit txtYieldStrength;
  private LabelControl umYieldStrength;
  private TextEdit txtCodeEdition;
  private TextEdit txtMaterialGrade;
  private LabelControl labelControl6;
  private GroupControl groupControl1;
  private LabelControl labelControl15;
  private LabelControl labelControl14;
  private CheckEdit chkUserDefined;
  private TableLayoutPanel tableLayoutPanel4;
  private LabelControl umYieldStrengthNew;
  private TextEdit txtYieldStrengthNew;
  private TableLayoutPanel tableLayoutPanel5;
  private LabelControl umTensileStrengthNew;
  private TextEdit txtTensileStrengthNew;
  private LabelControl labelControl2;
  private TableLayoutPanel tableLayoutPanel6;
  private LabelControl umTensileStrength;
  private TextEdit txtTensileStrength;
  private IRecordView _recordView;
  private MaterialPresenter _presenter;
  private FormDirtyTracker _dirtyTracker;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.txtMaterialGrade = new TextEdit();
    this.labelControl13 = new LabelControl();
    this.txtMaterialSpecNo = new TextEdit();
    this.txtDesignCode = new TextEdit();
    this.labelControl4 = new LabelControl();
    this.labelControl1 = new LabelControl();
    this.labelControl7 = new LabelControl();
    this.labelControl11 = new LabelControl();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.umAllowableStrength = new LabelControl();
    this.txtAllowableStress = new TextEdit();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.txtYieldStrength = new TextEdit();
    this.umYieldStrength = new LabelControl();
    this.txtCodeEdition = new TextEdit();
    this.labelControl6 = new LabelControl();
    this.labelControl2 = new LabelControl();
    this.tableLayoutPanel6 = new TableLayoutPanel();
    this.umTensileStrength = new LabelControl();
    this.txtTensileStrength = new TextEdit();
    this.groupControl1 = new GroupControl();
    this.labelControl15 = new LabelControl();
    this.labelControl14 = new LabelControl();
    this.chkUserDefined = new CheckEdit();
    this.tableLayoutPanel4 = new TableLayoutPanel();
    this.umYieldStrengthNew = new LabelControl();
    this.txtYieldStrengthNew = new TextEdit();
    this.tableLayoutPanel5 = new TableLayoutPanel();
    this.umTensileStrengthNew = new LabelControl();
    this.txtTensileStrengthNew = new TextEdit();
    this.tableLayoutPanel1.SuspendLayout();
    this.txtMaterialGrade.Properties.BeginInit();
    this.txtMaterialSpecNo.Properties.BeginInit();
    this.txtDesignCode.Properties.BeginInit();
    this.tableLayoutPanel2.SuspendLayout();
    this.txtAllowableStress.Properties.BeginInit();
    this.tableLayoutPanel3.SuspendLayout();
    this.txtYieldStrength.Properties.BeginInit();
    this.txtCodeEdition.Properties.BeginInit();
    this.tableLayoutPanel6.SuspendLayout();
    this.txtTensileStrength.Properties.BeginInit();
    this.groupControl1.BeginInit();
    this.groupControl1.SuspendLayout();
    this.chkUserDefined.Properties.BeginInit();
    this.tableLayoutPanel4.SuspendLayout();
    this.txtYieldStrengthNew.Properties.BeginInit();
    this.tableLayoutPanel5.SuspendLayout();
    this.txtTensileStrengthNew.Properties.BeginInit();
    this.SuspendLayout();
    this.tableLayoutPanel1.AutoSize = true;
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 2;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 180f));
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.txtMaterialGrade, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl13, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtMaterialSpecNo, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtDesignCode, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl4, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl1, 0, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl7, 0, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl11, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel2, 1, 6);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel3, 1, 4);
    this.tableLayoutPanel1.Controls.Add((Control) this.txtCodeEdition, 1, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl6, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.labelControl2, 0, 5);
    this.tableLayoutPanel1.Controls.Add((Control) this.tableLayoutPanel6, 1, 5);
    this.tableLayoutPanel1.Dock = DockStyle.Top;
    this.tableLayoutPanel1.Location = new Point(0, 0);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 7;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.Size = new Size(598, 172);
    this.tableLayoutPanel1.TabIndex = 0;
    this.txtMaterialGrade.Location = new Point(191, 55);
    this.txtMaterialGrade.Margin = new Padding(1);
    this.txtMaterialGrade.Name = "txtMaterialGrade";
    this.txtMaterialGrade.Properties.ReadOnly = true;
    this.txtMaterialGrade.Size = new Size(250, 20);
    this.txtMaterialGrade.TabIndex = 5;
    this.txtMaterialGrade.TabStop = false;
    this.labelControl13.Location = new Point(13, 13);
    this.labelControl13.Name = "labelControl13";
    this.labelControl13.Size = new Size(60, 13);
    this.labelControl13.TabIndex = 0;
    this.labelControl13.Text = "Design Code";
    this.txtMaterialSpecNo.Location = new Point(191, 33);
    this.txtMaterialSpecNo.Margin = new Padding(1);
    this.txtMaterialSpecNo.Name = "txtMaterialSpecNo";
    this.txtMaterialSpecNo.Properties.ReadOnly = true;
    this.txtMaterialSpecNo.Size = new Size(250, 20);
    this.txtMaterialSpecNo.TabIndex = 3;
    this.txtMaterialSpecNo.TabStop = false;
    this.txtDesignCode.EditValue = (object) "ASME B31.4";
    this.txtDesignCode.Location = new Point(191, 11);
    this.txtDesignCode.Margin = new Padding(1);
    this.txtDesignCode.Name = "txtDesignCode";
    this.txtDesignCode.Properties.ReadOnly = true;
    this.txtDesignCode.Size = new Size(250, 20);
    this.txtDesignCode.TabIndex = 1;
    this.txtDesignCode.TabStop = false;
    this.labelControl4.Location = new Point(13, 35);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(117, 13);
    this.labelControl4.TabIndex = 2;
    this.labelControl4.Text = "Material Specification No";
    this.labelControl1.Location = new Point(13, 143);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(78, 13);
    this.labelControl1.TabIndex = 12;
    this.labelControl1.Text = "Allowable Stress";
    this.labelControl7.Location = new Point(13, 101);
    this.labelControl7.Name = "labelControl7";
    this.labelControl7.Size = new Size(90, 13);
    this.labelControl7.TabIndex = 8;
    this.labelControl7.Text = "Min. Yield Strength";
    this.labelControl11.Location = new Point(13, 79);
    this.labelControl11.Name = "labelControl11";
    this.labelControl11.Size = new Size(60, 13);
    this.labelControl11.TabIndex = 6;
    this.labelControl11.Text = "Code Edition";
    this.tableLayoutPanel2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel2.ColumnCount = 2;
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.Controls.Add((Control) this.umAllowableStrength, 1, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.txtAllowableStress, 0, 0);
    this.tableLayoutPanel2.Location = new Point(191, 141);
    this.tableLayoutPanel2.Margin = new Padding(1);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.RowCount = 1;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.Size = new Size(196, 20);
    this.tableLayoutPanel2.TabIndex = 13;
    this.umAllowableStrength.Location = new Point(103, 3);
    this.umAllowableStrength.Name = "umAllowableStrength";
    this.umAllowableStrength.Size = new Size(41, 13);
    this.umAllowableStrength.TabIndex = 1;
    this.umAllowableStrength.Text = "measure";
    this.txtAllowableStress.Location = new Point(0, 0);
    this.txtAllowableStress.Margin = new Padding(0);
    this.txtAllowableStress.Name = "txtAllowableStress";
    this.txtAllowableStress.Properties.ReadOnly = true;
    this.txtAllowableStress.Size = new Size(100, 20);
    this.txtAllowableStress.TabIndex = 0;
    this.txtAllowableStress.TabStop = false;
    this.tableLayoutPanel3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel3.ColumnCount = 2;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtYieldStrength, 0, 0);
    this.tableLayoutPanel3.Controls.Add((Control) this.umYieldStrength, 1, 0);
    this.tableLayoutPanel3.Location = new Point(191, 99);
    this.tableLayoutPanel3.Margin = new Padding(1);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel3.Size = new Size(196, 18);
    this.tableLayoutPanel3.TabIndex = 9;
    this.txtYieldStrength.Location = new Point(0, 0);
    this.txtYieldStrength.Margin = new Padding(0);
    this.txtYieldStrength.Name = "txtYieldStrength";
    this.txtYieldStrength.Properties.ReadOnly = true;
    this.txtYieldStrength.Size = new Size(100, 20);
    this.txtYieldStrength.TabIndex = 0;
    this.txtYieldStrength.TabStop = false;
    this.umYieldStrength.Location = new Point(103, 3);
    this.umYieldStrength.Name = "umYieldStrength";
    this.umYieldStrength.Size = new Size(41, 13);
    this.umYieldStrength.TabIndex = 1;
    this.umYieldStrength.Text = "measure";
    this.txtCodeEdition.Location = new Point(191, 77);
    this.txtCodeEdition.Margin = new Padding(1);
    this.txtCodeEdition.Name = "txtCodeEdition";
    this.txtCodeEdition.Properties.ReadOnly = true;
    this.txtCodeEdition.Size = new Size(100, 20);
    this.txtCodeEdition.TabIndex = 7;
    this.txtCodeEdition.TabStop = false;
    this.labelControl6.Location = new Point(13, 57);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(70, 13);
    this.labelControl6.TabIndex = 4;
    this.labelControl6.Text = "Material Grade";
    this.labelControl2.Location = new Point(13, 121);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(101, 13);
    this.labelControl2.TabIndex = 10;
    this.labelControl2.Text = "Min. Tensile Strength";
    this.tableLayoutPanel6.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel6.ColumnCount = 2;
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel6.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel6.Controls.Add((Control) this.umTensileStrength, 1, 0);
    this.tableLayoutPanel6.Controls.Add((Control) this.txtTensileStrength, 0, 0);
    this.tableLayoutPanel6.Location = new Point(191, 119);
    this.tableLayoutPanel6.Margin = new Padding(1);
    this.tableLayoutPanel6.Name = "tableLayoutPanel6";
    this.tableLayoutPanel6.RowCount = 1;
    this.tableLayoutPanel6.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel6.Size = new Size(178, 20);
    this.tableLayoutPanel6.TabIndex = 11;
    this.umTensileStrength.Location = new Point(103, 3);
    this.umTensileStrength.Name = "umTensileStrength";
    this.umTensileStrength.Size = new Size(41, 13);
    this.umTensileStrength.TabIndex = 1;
    this.umTensileStrength.Text = "measure";
    this.txtTensileStrength.Location = new Point(0, 0);
    this.txtTensileStrength.Margin = new Padding(0);
    this.txtTensileStrength.Name = "txtTensileStrength";
    this.txtTensileStrength.Properties.ReadOnly = true;
    this.txtTensileStrength.Size = new Size(100, 20);
    this.txtTensileStrength.TabIndex = 0;
    this.txtTensileStrength.TabStop = false;
    this.groupControl1.Controls.Add((Control) this.labelControl15);
    this.groupControl1.Controls.Add((Control) this.labelControl14);
    this.groupControl1.Controls.Add((Control) this.chkUserDefined);
    this.groupControl1.Controls.Add((Control) this.tableLayoutPanel4);
    this.groupControl1.Controls.Add((Control) this.tableLayoutPanel5);
    this.groupControl1.Location = new Point(13, 178);
    this.groupControl1.Name = "groupControl1";
    this.groupControl1.Size = new Size(500, 100);
    this.groupControl1.TabIndex = 1;
    this.groupControl1.Text = "User-defined Strength Properties";
    this.labelControl15.Location = new Point(6, 67);
    this.labelControl15.Name = "labelControl15";
    this.labelControl15.Size = new Size(258, 13);
    this.labelControl15.TabIndex = 3;
    this.labelControl15.Text = "Ultimate Tensile Strength at Assessment Temperature";
    this.labelControl14.Location = new Point(6, 46);
    this.labelControl14.Name = "labelControl14";
    this.labelControl14.Size = new Size(205, 13);
    this.labelControl14.TabIndex = 1;
    this.labelControl14.Text = "Yield Strength at Assessment Temperature";
    this.chkUserDefined.Location = new Point(4, 23);
    this.chkUserDefined.Margin = new Padding(1);
    this.chkUserDefined.Name = "chkUserDefined";
    this.chkUserDefined.Properties.Caption = "User-defined?";
    this.chkUserDefined.Size = new Size(330, 19);
    this.chkUserDefined.TabIndex = 0;
    this.chkUserDefined.CheckedChanged += new EventHandler(this.chkUserDefined_CheckedChanged);
    this.tableLayoutPanel4.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel4.ColumnCount = 2;
    this.tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel4.Controls.Add((Control) this.umYieldStrengthNew, 1, 0);
    this.tableLayoutPanel4.Controls.Add((Control) this.txtYieldStrengthNew, 0, 0);
    this.tableLayoutPanel4.Location = new Point(281, 43);
    this.tableLayoutPanel4.Margin = new Padding(1);
    this.tableLayoutPanel4.Name = "tableLayoutPanel4";
    this.tableLayoutPanel4.RowCount = 2;
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel4.Size = new Size(196, 20);
    this.tableLayoutPanel4.TabIndex = 2;
    this.umYieldStrengthNew.Location = new Point(103, 3);
    this.umYieldStrengthNew.Name = "umYieldStrengthNew";
    this.umYieldStrengthNew.Size = new Size(41, 13);
    this.umYieldStrengthNew.TabIndex = 1;
    this.umYieldStrengthNew.Text = "measure";
    this.txtYieldStrengthNew.Location = new Point(0, 0);
    this.txtYieldStrengthNew.Margin = new Padding(0);
    this.txtYieldStrengthNew.Name = "txtYieldStrengthNew";
    this.txtYieldStrengthNew.Properties.AllowNullInput = DefaultBoolean.True;
    this.txtYieldStrengthNew.Size = new Size(100, 20);
    this.txtYieldStrengthNew.TabIndex = 0;
    this.tableLayoutPanel5.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel5.ColumnCount = 2;
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel5.Controls.Add((Control) this.umTensileStrengthNew, 1, 0);
    this.tableLayoutPanel5.Controls.Add((Control) this.txtTensileStrengthNew, 0, 0);
    this.tableLayoutPanel5.Location = new Point(281, 64 /*0x40*/);
    this.tableLayoutPanel5.Margin = new Padding(1);
    this.tableLayoutPanel5.Name = "tableLayoutPanel5";
    this.tableLayoutPanel5.RowCount = 1;
    this.tableLayoutPanel5.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel5.Size = new Size(196, 20);
    this.tableLayoutPanel5.TabIndex = 4;
    this.umTensileStrengthNew.Location = new Point(103, 3);
    this.umTensileStrengthNew.Name = "umTensileStrengthNew";
    this.umTensileStrengthNew.Size = new Size(41, 13);
    this.umTensileStrengthNew.TabIndex = 1;
    this.umTensileStrengthNew.Text = "measure";
    this.txtTensileStrengthNew.Location = new Point(0, 0);
    this.txtTensileStrengthNew.Margin = new Padding(0);
    this.txtTensileStrengthNew.Name = "txtTensileStrengthNew";
    this.txtTensileStrengthNew.Size = new Size(100, 20);
    this.txtTensileStrengthNew.TabIndex = 0;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.AutoSize = true;
    this.Controls.Add((Control) this.groupControl1);
    this.Controls.Add((Control) this.tableLayoutPanel1);
    this.Name = nameof (vwMaterial);
    this.Size = new Size(598, 281);
    this.Load += new EventHandler(this.vwMaterial_Load);
    this.Leave += new EventHandler(this.vwMaterial_Leave);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.txtMaterialGrade.Properties.EndInit();
    this.txtMaterialSpecNo.Properties.EndInit();
    this.txtDesignCode.Properties.EndInit();
    this.tableLayoutPanel2.ResumeLayout(false);
    this.tableLayoutPanel2.PerformLayout();
    this.txtAllowableStress.Properties.EndInit();
    this.tableLayoutPanel3.ResumeLayout(false);
    this.tableLayoutPanel3.PerformLayout();
    this.txtYieldStrength.Properties.EndInit();
    this.txtCodeEdition.Properties.EndInit();
    this.tableLayoutPanel6.ResumeLayout(false);
    this.tableLayoutPanel6.PerformLayout();
    this.txtTensileStrength.Properties.EndInit();
    this.groupControl1.EndInit();
    this.groupControl1.ResumeLayout(false);
    this.groupControl1.PerformLayout();
    this.chkUserDefined.Properties.EndInit();
    this.tableLayoutPanel4.ResumeLayout(false);
    this.tableLayoutPanel4.PerformLayout();
    this.txtYieldStrengthNew.Properties.EndInit();
    this.tableLayoutPanel5.ResumeLayout(false);
    this.tableLayoutPanel5.PerformLayout();
    this.txtTensileStrengthNew.Properties.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public vwMaterial(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._presenter = new MaterialPresenter(this._recordView, (IMaterialView) this);
  }

  private void vwMaterial_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView);
    this._dirtyTracker.IsHandled = true;
    this.txtYieldStrength.AllowOnlyN4();
    this.txtTensileStrength.AllowOnlyN4();
    this.txtAllowableStress.AllowOnlyN4();
    this.txtYieldStrengthNew.AllowOnlyN4();
    this.txtTensileStrengthNew.AllowOnlyN4();
    this._presenter.InitMaterialView();
    this._dirtyTracker.IsHandled = false;
  }

  private void chkUserDefined_CheckedChanged(object sender, EventArgs e)
  {
    this._presenter.UserDefined();
  }

  public string MaterialSpecNo
  {
    get => this.txtMaterialSpecNo.Text;
    set => this.txtMaterialSpecNo.Text = value;
  }

  public string MaterialGrade
  {
    get => this.txtMaterialGrade.Text;
    set => this.txtMaterialGrade.Text = value;
  }

  public string MaterialXMLString
  {
    get => this._presenter.NewMaterialXMLString();
    set
    {
      if (value == null)
        this.UserDefined = false;
      this._presenter.PopulateMaterial(value);
    }
  }

  public bool UserDefined
  {
    get => this.chkUserDefined.Checked;
    set
    {
      this.chkUserDefined.EditValue = (object) value;
      this.txtYieldStrengthNew.Enabled = value;
      this.txtTensileStrengthNew.Enabled = value;
    }
  }

  public string CodeEdition
  {
    get => this.txtCodeEdition.Text;
    set => this.txtCodeEdition.Text = value;
  }

  public double? YieldStrength
  {
    set => this.txtYieldStrength.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? TensileStrength
  {
    set => this.txtTensileStrength.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? AllowableStrength
  {
    set => this.txtAllowableStress.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? TensileStrengthNew
  {
    get => Helpers.ParseNullDouble((object) this.txtTensileStrengthNew.Text);
    set => this.txtTensileStrengthNew.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? YieldStrengthNew
  {
    get => Helpers.ParseNullDouble((object) this.txtYieldStrengthNew.Text);
    set => this.txtYieldStrengthNew.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMYieldStrength
  {
    set => this.umYieldStrength.Text = value;
  }

  public string UMTensileStrength
  {
    set => this.umTensileStrength.Text = value;
  }

  public string UMAllowableStrength
  {
    set => this.umAllowableStrength.Text = value;
  }

  public string UMYieldStrengthNew
  {
    set => this.umYieldStrengthNew.Text = value;
  }

  public string UMTensileStrengthNew
  {
    set => this.umTensileStrengthNew.Text = value;
  }

  public void Save()
  {
  }

  private void chkUserDefined_EditValueChanging(object sender, ChangingEventArgs e)
  {
    if (this._recordView.Material.MaterialXML != null || !(bool) e.NewValue)
      return;
    int num = (int) XtraMessageBox.Show("Please select a Material from General View before overriding its properties");
    e.Cancel = true;
  }

  private void vwMaterial_Leave(object sender, EventArgs e)
  {
    if (!this._recordView.AssessmentID.HasValue)
      return;
    int? assessmentId = this._recordView.AssessmentID;
    if ((assessmentId.GetValueOrDefault() != 0 ? 1 : (!assessmentId.HasValue ? 1 : 0)) == 0 || this._recordView.Material.MaterialXML == null)
      return;
    this._recordView.SerialiseMaterial();
  }
}
