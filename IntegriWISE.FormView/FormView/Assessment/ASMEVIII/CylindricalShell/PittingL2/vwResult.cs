// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEVIII.CylindricalShell.PittingL2.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Mask;
using IntegriWISE.Common;
using IntegriWISE.DataTransferObjects.AssessmentData;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEVIII.CylindricalShell.PittingL2;
using IntegriWISE.UserInterface.Record;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.Layout;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEVIII.CylindricalShell.PittingL2;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private IContainer components;
  private GroupControl grpMAWP;
  private TableLayoutPanel tblMAWP;
  private GroupControl grpIntermediateResult;
  private TableLayoutPanel tblIntermediateResult;
  private GroupControl grpLevel1Criteria;
  private TableLayoutPanel tblCriteria;
  private GroupControl grpLevel1Conclusion;
  private TableLayoutPanel tblConclusion;
  private GroupControl groupControl3;
  private TableLayoutPanel tableLayoutPanel3;
  private MemoEdit txtWarningMessages;
  private string _ResultMessages;
  private string _LBMAWPr;
  private double? _MAWPr;
  private string _LBMAWP;
  private double? _MAWP;
  private bool _EnableIntermediateResult;
  private bool _EnableLevel1Conclusion;
  private bool _EnableLevel1Criteria;
  private bool _EnableMAWP;
  private string _LBAllowableStrength;
  private double? _AllowableStrength;
  private bool _level2Passed;
  private string _level2Conclusion;
  private IRecordView _recordView;
  private ResultPresenter _presenter;
  private List<PittingCoupleRanked> _InsideCouples;
  private List<PittingCoupleRanked> _OutsideCouples;
  private int _level2RowIndex;
  private int _level2OutsideRowIndex;
  private string _LBRSFpit;
  private double _RSFpit;
  private string _LBRSFPitMoreEqualRSFa;
  private bool _rSFPitMoreEqualRSFa;
  private string _LongitudinalExtentType2;
  private string _CircumferentialExtentType2;
  private string _LBRSFcombMoreEqualRSFa;
  private bool _RSFcombMoreEqualRSFa;
  private string _LongitudinalExtentType3And4;
  private string _CircumferentialExtentType3And4;
  private string _LBRSFltaMoreEqualRSFa;
  private bool _RSFltaMoreEqualRSFa;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.grpMAWP = new GroupControl();
    this.tblMAWP = new TableLayoutPanel();
    this.grpIntermediateResult = new GroupControl();
    this.tblIntermediateResult = new TableLayoutPanel();
    this.grpLevel1Criteria = new GroupControl();
    this.tblCriteria = new TableLayoutPanel();
    this.grpLevel1Conclusion = new GroupControl();
    this.tblConclusion = new TableLayoutPanel();
    this.groupControl3 = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.txtWarningMessages = new MemoEdit();
    this.grpMAWP.BeginInit();
    this.grpMAWP.SuspendLayout();
    this.grpIntermediateResult.BeginInit();
    this.grpIntermediateResult.SuspendLayout();
    this.grpLevel1Criteria.BeginInit();
    this.grpLevel1Criteria.SuspendLayout();
    this.grpLevel1Conclusion.BeginInit();
    this.grpLevel1Conclusion.SuspendLayout();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.txtWarningMessages.Properties.BeginInit();
    this.SuspendLayout();
    this.grpMAWP.AutoSize = true;
    this.grpMAWP.Controls.Add((Control) this.tblMAWP);
    this.grpMAWP.Dock = DockStyle.Top;
    this.grpMAWP.Location = new Point(0, 0);
    this.grpMAWP.Name = "grpMAWP";
    this.grpMAWP.Size = new Size(587, 43);
    this.grpMAWP.TabIndex = 16 /*0x10*/;
    this.grpMAWP.Text = "Maximum Allowable Working Pressure";
    this.tblMAWP.AutoSize = true;
    this.tblMAWP.ColumnCount = 3;
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWP.Dock = DockStyle.Fill;
    this.tblMAWP.Location = new Point(2, 21);
    this.tblMAWP.Name = "tblMAWP";
    this.tblMAWP.Padding = new Padding(10);
    this.tblMAWP.RowCount = 1;
    this.tblMAWP.RowStyles.Add(new RowStyle());
    this.tblMAWP.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblMAWP.Size = new Size(583, 20);
    this.tblMAWP.TabIndex = 1;
    this.grpIntermediateResult.AutoSize = true;
    this.grpIntermediateResult.Controls.Add((Control) this.tblIntermediateResult);
    this.grpIntermediateResult.Dock = DockStyle.Top;
    this.grpIntermediateResult.Location = new Point(0, 43);
    this.grpIntermediateResult.Name = "grpIntermediateResult";
    this.grpIntermediateResult.Size = new Size(587, 43);
    this.grpIntermediateResult.TabIndex = 17;
    this.grpIntermediateResult.Text = "Intermediate Assessment Result";
    this.tblIntermediateResult.AutoSize = true;
    this.tblIntermediateResult.ColumnCount = 3;
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.Dock = DockStyle.Fill;
    this.tblIntermediateResult.Location = new Point(2, 21);
    this.tblIntermediateResult.Name = "tblIntermediateResult";
    this.tblIntermediateResult.Padding = new Padding(10);
    this.tblIntermediateResult.RowCount = 1;
    this.tblIntermediateResult.RowStyles.Add(new RowStyle());
    this.tblIntermediateResult.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblIntermediateResult.Size = new Size(583, 20);
    this.tblIntermediateResult.TabIndex = 0;
    this.grpLevel1Criteria.AutoSize = true;
    this.grpLevel1Criteria.Controls.Add((Control) this.tblCriteria);
    this.grpLevel1Criteria.Dock = DockStyle.Top;
    this.grpLevel1Criteria.Location = new Point(0, 86);
    this.grpLevel1Criteria.Name = "grpLevel1Criteria";
    this.grpLevel1Criteria.Size = new Size(587, 43);
    this.grpLevel1Criteria.TabIndex = 18;
    this.grpLevel1Criteria.Text = "Assessment Criteria";
    this.tblCriteria.AutoSize = true;
    this.tblCriteria.ColumnCount = 3;
    this.tblCriteria.ColumnStyles.Add(new ColumnStyle());
    this.tblCriteria.ColumnStyles.Add(new ColumnStyle());
    this.tblCriteria.ColumnStyles.Add(new ColumnStyle());
    this.tblCriteria.Dock = DockStyle.Fill;
    this.tblCriteria.Location = new Point(2, 21);
    this.tblCriteria.Name = "tblCriteria";
    this.tblCriteria.Padding = new Padding(10);
    this.tblCriteria.RowCount = 1;
    this.tblCriteria.RowStyles.Add(new RowStyle());
    this.tblCriteria.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblCriteria.Size = new Size(583, 20);
    this.tblCriteria.TabIndex = 1;
    this.grpLevel1Conclusion.AutoSize = true;
    this.grpLevel1Conclusion.Controls.Add((Control) this.tblConclusion);
    this.grpLevel1Conclusion.Dock = DockStyle.Top;
    this.grpLevel1Conclusion.Location = new Point(0, 129);
    this.grpLevel1Conclusion.Name = "grpLevel1Conclusion";
    this.grpLevel1Conclusion.Size = new Size(587, 43);
    this.grpLevel1Conclusion.TabIndex = 19;
    this.grpLevel1Conclusion.Text = "Assessment Conclusion";
    this.tblConclusion.AutoSize = true;
    this.tblConclusion.ColumnCount = 3;
    this.tblConclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblConclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblConclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblConclusion.Dock = DockStyle.Fill;
    this.tblConclusion.Location = new Point(2, 21);
    this.tblConclusion.Name = "tblConclusion";
    this.tblConclusion.Padding = new Padding(10);
    this.tblConclusion.RowCount = 1;
    this.tblConclusion.RowStyles.Add(new RowStyle());
    this.tblConclusion.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblConclusion.Size = new Size(583, 20);
    this.tblConclusion.TabIndex = 1;
    this.groupControl3.AutoSize = true;
    this.groupControl3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel3);
    this.groupControl3.Dock = DockStyle.Fill;
    this.groupControl3.Location = new Point(0, 172);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(587, 351);
    this.groupControl3.TabIndex = 29;
    this.groupControl3.Text = "Warning Messages";
    this.tableLayoutPanel3.AutoSize = true;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 59f));
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 59f));
    this.tableLayoutPanel3.Size = new Size(583, 328);
    this.tableLayoutPanel3.TabIndex = 0;
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.MinimumSize = new Size(10, 10);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(557, 302);
    this.txtWarningMessages.TabIndex = 4;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.AutoSize = true;
    this.Controls.Add((Control) this.groupControl3);
    this.Controls.Add((Control) this.grpLevel1Conclusion);
    this.Controls.Add((Control) this.grpLevel1Criteria);
    this.Controls.Add((Control) this.grpIntermediateResult);
    this.Controls.Add((Control) this.grpMAWP);
    this.Name = nameof (vwResult);
    this.Size = new Size(587, 523);
    this.Load += new EventHandler(this.vwResult_Load);
    this.grpMAWP.EndInit();
    this.grpMAWP.ResumeLayout(false);
    this.grpMAWP.PerformLayout();
    this.grpIntermediateResult.EndInit();
    this.grpIntermediateResult.ResumeLayout(false);
    this.grpIntermediateResult.PerformLayout();
    this.grpLevel1Criteria.EndInit();
    this.grpLevel1Criteria.ResumeLayout(false);
    this.grpLevel1Criteria.PerformLayout();
    this.grpLevel1Conclusion.EndInit();
    this.grpLevel1Conclusion.ResumeLayout(false);
    this.grpLevel1Conclusion.PerformLayout();
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.groupControl3.PerformLayout();
    this.tableLayoutPanel3.ResumeLayout(false);
    this.txtWarningMessages.Properties.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.ResultView = (IResultBaseView) this;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  private void vwResult_Load(object sender, EventArgs e)
  {
  }

  private bool AddToTableOrUpdateExisting(TableLayoutPanel tlp, string value, string name)
  {
    foreach (Control control in (ArrangedElementCollection) tlp.Controls)
    {
      if (control is TextEdit textEdit && string.Compare(textEdit.Name, name) == 0)
      {
        textEdit.Text = value;
        textEdit.Properties.Mask.MaskType = value == "N/A" ? MaskType.None : MaskType.RegEx;
        return true;
      }
    }
    return false;
  }

  private void AddIntermediateResultValue(int rowNum, double? value, TableLayoutPanel tablePanel)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = value.HasValue ? Helpers.ParseObjectToString((object) value) : "N/A";
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    if (value.HasValue)
      textEdit2.AllowOnlyN4();
    tablePanel.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddIntermediateResultValue(int rowNum, string value, TableLayoutPanel tablePanel)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    tablePanel.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddIntermediateResultLabel(int rowNum, string value, TableLayoutPanel tablePanel)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    LabelControl labelControl1 = new LabelControl();
    labelControl1.Text = value;
    labelControl1.Margin = new Padding(1);
    LabelControl labelControl2 = labelControl1;
    tablePanel.Controls.Add((Control) labelControl2, 0, rowNum + 1);
  }

  private void AddIntermediateResultUMLabel(int rowNum, string value, TableLayoutPanel tablePanel)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    LabelControl labelControl1 = new LabelControl();
    labelControl1.Text = value;
    labelControl1.Margin = new Padding(1);
    LabelControl labelControl2 = labelControl1;
    tablePanel.Controls.Add((Control) labelControl2, 2, rowNum + 1);
  }

  private void AddCriteriaLabel(int rowNum, string value, TableLayoutPanel tablePanel)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    LabelControl labelControl1 = new LabelControl();
    labelControl1.Text = value;
    labelControl1.Margin = new Padding(1);
    LabelControl labelControl2 = labelControl1;
    tablePanel.Controls.Add((Control) labelControl2, 0, rowNum + 1);
  }

  private void AddCriteriaValue(int rowNum, bool? value, TableLayoutPanel tablePanel)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = value.HasValue ? Helpers.ParseObjectToString((object) value) : "N/A";
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    tablePanel.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddConclusionLabel(
    int rowNum,
    string value,
    TableLayoutPanel tablePanel,
    bool hasPassed,
    bool IsBold)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    LabelControl labelControl1 = new LabelControl();
    labelControl1.Text = value;
    labelControl1.Margin = new Padding(1);
    labelControl1.AutoSizeMode = LabelAutoSizeMode.Vertical;
    labelControl1.Width = tablePanel.Width - 35;
    LabelControl labelControl2 = labelControl1;
    if (IsBold)
    {
      labelControl2.Font = new Font(Control.DefaultFont, FontStyle.Bold);
      labelControl2.ForeColor = hasPassed ? Color.Green : Color.Red;
    }
    tablePanel.Controls.Add((Control) labelControl2, 0, rowNum + 1);
  }

  private void AddPassed(bool value, TableLayoutPanel tablePanel)
  {
    tablePanel.BackColor = value ? Color.Green : Color.Red;
  }

  public bool Calculate()
  {
    this.tblIntermediateResult.Controls.Clear();
    this.tblConclusion.Controls.Clear();
    this.tblCriteria.Controls.Clear();
    this.tblMAWP.Controls.Clear();
    this.tblIntermediateResult.Visible = false;
    this.tblConclusion.Visible = false;
    this.tblCriteria.Visible = false;
    this.tblMAWP.Visible = false;
    bool flag = this._presenter.Calculate();
    this.tblIntermediateResult.Visible = true;
    this.tblConclusion.Visible = true;
    this.tblCriteria.Visible = true;
    this.tblMAWP.Visible = true;
    return flag;
  }

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public bool EnableMAWPr { get; set; }

  public bool EnableMAWP
  {
    get => this._EnableMAWP;
    set
    {
      this.grpMAWP.Visible = value;
      if (this._EnableMAWP == value)
        return;
      this._EnableMAWP = value;
    }
  }

  public bool EnableIntermediateResult
  {
    get => this._EnableIntermediateResult;
    set
    {
      this._EnableIntermediateResult = value;
      this.grpIntermediateResult.Visible = value;
    }
  }

  public bool EnableLevel1Criteria
  {
    get => this._EnableLevel1Criteria;
    set
    {
      this._EnableLevel1Criteria = value;
      this.grpLevel1Criteria.Visible = value;
    }
  }

  public bool EnableLevel1Conclusion
  {
    get => this._EnableLevel1Conclusion;
    set
    {
      this._EnableLevel1Conclusion = value;
      this.grpLevel1Conclusion.Visible = value;
    }
  }

  public double? AllowableStrength
  {
    get => this._AllowableStrength;
    set
    {
      this.AddIntermediateResultValue(1, Helpers.ParseObjectToString((object) value), this.tblMAWP);
      this._AllowableStrength = value;
    }
  }

  public string UMAllowableStrength
  {
    set => this.AddIntermediateResultUMLabel(1, value, this.tblMAWP);
  }

  public string LBAllowableStrength
  {
    get => this._LBAllowableStrength;
    set
    {
      this.AddIntermediateResultLabel(1, value, this.tblMAWP);
      this._LBAllowableStrength = value;
    }
  }

  public double? MAWP
  {
    get => this._MAWP;
    set
    {
      this.AddIntermediateResultValue(2, value, this.tblMAWP);
      this._MAWP = value;
    }
  }

  public string UMMAWP
  {
    set => this.AddIntermediateResultUMLabel(2, value, this.tblMAWP);
  }

  public string LBMAWP
  {
    get => this._LBMAWP;
    set
    {
      this._LBMAWP = value;
      this.AddIntermediateResultLabel(2, value, this.tblMAWP);
    }
  }

  public double? MAWPr
  {
    get => this._MAWPr;
    set
    {
      this._MAWPr = value;
      this.AddIntermediateResultValue(3, value, this.tblMAWP);
    }
  }

  public string UMMAWPr
  {
    set => this.AddIntermediateResultUMLabel(3, value, this.tblMAWP);
  }

  public string LBMAWPr
  {
    get => this._LBMAWPr;
    set
    {
      this._LBMAWPr = value;
      this.AddIntermediateResultLabel(3, value, this.tblMAWP);
    }
  }

  public List<PittingCoupleRanked> InsideCouples
  {
    get => this._InsideCouples;
    set
    {
      this._InsideCouples = value;
      foreach (PittingCoupleRanked pittingCoupleRanked in value)
      {
        ++this._level2RowIndex;
        this.AddIntermediateResultLabel(this._level2RowIndex, $"Remaining Strength factor for inside pit couple {this._level2RowIndex} - RSF", this.tblIntermediateResult);
        this.AddIntermediateResultValue(this._level2RowIndex, new double?(pittingCoupleRanked.RSF), this.tblIntermediateResult);
      }
    }
  }

  public List<PittingCoupleRanked> OutsideCouples
  {
    get => this._OutsideCouples;
    set
    {
      this._OutsideCouples = value;
      foreach (PittingCoupleRanked pittingCoupleRanked in value)
      {
        ++this._level2OutsideRowIndex;
        ++this._level2RowIndex;
        this.AddIntermediateResultLabel(this._level2RowIndex, $"Remaining Strength factor for outside pit couple {this._level2OutsideRowIndex} - RSF", this.tblIntermediateResult);
        this.AddIntermediateResultValue(this._level2RowIndex, new double?(pittingCoupleRanked.RSF), this.tblIntermediateResult);
      }
    }
  }

  public string LBRSFpit
  {
    get => this._LBRSFpit;
    set
    {
      this._LBRSFpit = value;
      ++this._level2RowIndex;
      this.AddIntermediateResultLabel(this._level2RowIndex, value, this.tblIntermediateResult);
    }
  }

  public double RSFpit
  {
    get => this._RSFpit;
    set
    {
      this._RSFpit = value;
      this.AddIntermediateResultValue(this._level2RowIndex, new double?(value), this.tblIntermediateResult);
    }
  }

  public string LBRSFPitMoreEqualRSFa
  {
    get => this._LBRSFPitMoreEqualRSFa;
    set
    {
      this._LBRSFPitMoreEqualRSFa = value;
      this.AddCriteriaLabel(1, value, this.tblCriteria);
    }
  }

  public bool RSFPitMoreEqualRSFa
  {
    get => this._rSFPitMoreEqualRSFa;
    set
    {
      this._rSFPitMoreEqualRSFa = value;
      this.AddCriteriaValue(1, new bool?(value), this.tblCriteria);
    }
  }

  public string LongitudinalExtentType2
  {
    get => this._LongitudinalExtentType2;
    set
    {
      this._LongitudinalExtentType2 = value;
      this.AddConclusionLabel(1, value, this.tblConclusion, true, false);
    }
  }

  public string CircumferentialExtentType2
  {
    get => this._CircumferentialExtentType2;
    set
    {
      this._CircumferentialExtentType2 = value;
      this.AddConclusionLabel(1, value, this.tblConclusion, true, false);
    }
  }

  public string LBRSFcombMoreEqualRSFa
  {
    get => this._LBRSFcombMoreEqualRSFa;
    set
    {
      this._LBRSFcombMoreEqualRSFa = value;
      this.AddCriteriaLabel(1, value, this.tblCriteria);
    }
  }

  public bool RSFcombMoreEqualRSFa
  {
    get => this._RSFcombMoreEqualRSFa;
    set
    {
      this._RSFcombMoreEqualRSFa = value;
      this.AddCriteriaValue(1, new bool?(value), this.tblCriteria);
    }
  }

  public string LongitudinalExtentType3And4
  {
    get => this._LongitudinalExtentType3And4;
    set
    {
      this._LongitudinalExtentType3And4 = value;
      this.AddConclusionLabel(1, value, this.tblConclusion, true, false);
    }
  }

  public string CircumferentialExtentType3And4
  {
    get => this._CircumferentialExtentType3And4;
    set
    {
      this._CircumferentialExtentType3And4 = value;
      this.AddConclusionLabel(2, value, this.tblConclusion, true, false);
    }
  }

  public string LBRSFltaMoreEqualRSFa
  {
    get => this._LBRSFltaMoreEqualRSFa;
    set
    {
      this._LBRSFltaMoreEqualRSFa = value;
      this.AddCriteriaLabel(1, value, this.tblCriteria);
    }
  }

  public bool RSFltaMoreEqualRSFa
  {
    get => this._RSFltaMoreEqualRSFa;
    set
    {
      this._RSFltaMoreEqualRSFa = value;
      this.AddCriteriaValue(1, new bool?(value), this.tblCriteria);
    }
  }

  public bool Level2Passed => this._level2Passed;

  public string Level2Conclusion => this._level2Conclusion;

  public void SetLevel2Conclusion(string value, bool level2Passed)
  {
    this._level2Passed = level2Passed;
    this._level2Conclusion = value;
    this.AddConclusionLabel(1, value, this.tblConclusion, this._level2Passed, true);
  }

  public string ResultMessages
  {
    get => this._ResultMessages;
    set
    {
      this._ResultMessages = value;
      this.txtWarningMessages.Text = value;
    }
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public string CodeEdition => this._presenter.CodeEdition;

  public string Title => "Assessment to API 579 Part 6. Pitting Corrosion.";

  public string Introduction
  {
    get
    {
      return "Pitting is defined as localised regions of metal loss that can be characterised by a pit diameter on the order of the plate thickness or less, and a pit depth that is less than the plate thickness. \r\nIn a Level 1 Assessment standard pit charts (damage surface) and the maximum pit depth in the area being evaluated are used to estimate the Remaining Strength Factor, RSF. In a Level 2 Assessment a representative sample of pit-couples is used to define the area of pitting damage.";
    }
  }

  public string CommentsAndAssumptions
  {
    get
    {
      return "The pitting assessment procedure in Part 6 assumes that: \r\n\r\n1. The original design criteria were in accordance with a recognised code or standard \r\n2. The component is not operating in the creep regime \r\n3. The material is considered to have sufficient material toughness. If the user is uncertain about toughness, a \r\n    Part 3 (Brittle Fracture) assessment should be performed. If the component is subject to embrittlement during \r\n    operation due to temperature and/or the process environment, a Level 3 assessment should be performed \r\n4. The component is not in cyclic service \r\n5. The component under evaluation does not contain crack-like flaws. If crack-like flaws are present, the \r\n    assessment procedures in Part 9 (Crack-like flaws) shall be utilised. \r\n6. Level 1 assessment covers Type A components (see Part 4, paragraph 4.2.5) subject to internal pressure \r\n    (i.e. supplemental loads are assumed to be negligible).\r\n7. Level 2 assessment covers Type A or B components (see Part 4, paragraph 4.2.5) subject to internal \r\n    pressure, external pressure, supplemental loads, or any combination of them. \r\n8. The pitting damage is composed of many pits; individual pits or isolated pairs should be evaluated using \r\n    the assessment procedure in Part 5.  \r\n\r\nLevel 1 Assessment shall be limited to components with one-sided widespread pitting damage, on equipment designed to a recognised code or standard. Additional requirements for Level 1 Assessment are:\r\n  1) The pitting damage is arrested.    \r\n  2) The pitting damage is located on only one surface (ID or OD) of the component.\r\n\r\nLevel 2 Assessment requirements are: \r\n  1) The pitting damage is located on either one surface or both surfaces. \r\n  2) The pitting damage is characterized as widespread pitting, localized region of pitting, LTA located in a region \r\n      of pitting damage, or pitting that is confined within a LTA.\r\n  3) A representative sample of pit-couples should be used in the assessment. \r\n  4) It is assumed that supplemental loads are not presented or negligible, otherwise the assessment procedure in \r\n      paragraph ******* API 579 should be used to determine the acceptability of the longitudinal stress direction \r\n      in a cylindrical/conical shell or pipe with pitting damage.";
    }
  }

  public string References
  {
    get
    {
      return "API 579 \"Fitness-for-Service\", Second Edition, The American Society of Mechanical Engineers. Part 6: Assessment of Pitting Corrosion.";
    }
  }

  public string Limitations => string.Empty;
}
