// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.ASMEVIII.CylindricalShell.GeneralMetalLossThicknessReading.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.ASMEVIII.CylindricalShell.GeneralMetalLossThicknessReading;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.ASMEVIII.CylindricalShell.GeneralMetalLossThicknessReading;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private IRecordView _recordView;
  private ResultPresenter _presenter;
  private bool _level2;
  private bool _level1Passed;
  private bool _level2Passed;
  private IContainer components;
  private TextEdit txtMAWPrRSFaL2;
  private TableLayoutPanel tableLayoutPanel27;
  private LabelControl lbConclusionLevel2;
  private LabelControl umCov;
  private GroupControl grcAssessmentCriteriaL1;
  private TableLayoutPanel tblpAssessmentCriteriaL1;
  private TableLayoutPanel tableLayoutPanel19;
  private TextEdit txtMaxAllowWorkPressureL1;
  private LabelControl labelControl29;
  private TableLayoutPanel tableLayoutPanel20;
  private TextEdit txtAverageMThicknessL1;
  private LabelControl labelControl31;
  private LabelControl labelControl32;
  private TableLayoutPanel tableLayoutPanel21;
  private TextEdit txtMinMThicknessL1;
  private LabelControl umMAWPrRSFaL2;
  private TextEdit txtCov;
  private LabelControl labelControl1;
  private TableLayoutPanel tableLayoutPanel4;
  private LabelControl umMinReqThickTmin;
  private TextEdit txtMinReqThickTmin;
  private LabelControl umMinMeasuredThicknessTmm;
  private TableLayoutPanel tableLayoutPanel2;
  private LabelControl umAllowableStrength;
  private TextEdit txtAllowableStrength;
  private TextEdit txtMinMeasuredThicknessTmm;
  private TableLayoutPanel tableLayoutPanel24;
  private TextEdit txtMaxAllowWorkPressureL2;
  private TextEdit txtAverageMThicknessL2;
  private TextEdit txtMinMThicknessL2;
  private GroupControl grcAssessmentConclusionL2;
  private TableLayoutPanel tblpAssessmentCriteriaL2;
  private LabelControl labelControl34;
  private TableLayoutPanel tableLayoutPanel25;
  private LabelControl labelControl37;
  private LabelControl labelControl38;
  private TableLayoutPanel tableLayoutPanel26;
  private GroupControl grcAssessmentCriteriaL2;
  private LabelControl lbConclusionLevel1;
  private TableLayoutPanel tableLayoutPanel22;
  private GroupControl grcAssessmentConclusionL1;
  private LabelControl labelControl2;
  private LabelControl labelControl5;
  private MemoEdit txtWarningMessages;
  private TableLayoutPanel tableLayoutPanel3;
  private GroupControl groupControl3;
  private LabelControl labelControl4;
  private LabelControl labelControl6;
  private LabelControl lbTlim;
  private TableLayoutPanel tableLayoutPanel5;
  private TableLayoutPanel tableLayoutPanel8;
  private TableLayoutPanel tblpIntermediateResults;
  private TableLayoutPanel tblMAWPrRSFaL2;
  private GroupControl grcIntermediateResults;
  private TableLayoutPanel tableLayoutPanel7;
  private LabelControl umAverageMeasuredReadingsTam;
  private TextEdit txtAverageMeasuredReadingsTam;
  private TableLayoutPanel grpTlimValue;
  private LabelControl umLimitingThicknessTlim;
  private TextEdit txtLimitingThicknessTlim;
  private LabelControl labelControl3;
  private TableLayoutPanel tableLayoutPanel11;
  private TextEdit txtMAWPr;
  private LabelControl umMAWPr;
  private LabelControl lbMAWPrRSFaL2;

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  public bool Calculate() => this._presenter.Calculate();

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public bool Level2
  {
    get => this._level2;
    set
    {
      this._level2 = value;
      if (!value)
        this.HideLevel2Results();
      else
        this.ShowLevel2Results();
    }
  }

  public double? AllowableStrength
  {
    get => Helpers.ParseNullDouble((object) this.txtAllowableStrength.Text);
    set => this.txtAllowableStrength.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? TMin
  {
    get => Helpers.ParseNullDouble((object) this.txtMinReqThickTmin.Text);
    set => this.txtMinReqThickTmin.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Tmm
  {
    get => Helpers.ParseNullDouble((object) this.txtMinMeasuredThicknessTmm.Text);
    set => this.txtMinMeasuredThicknessTmm.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Tam
  {
    get => Helpers.ParseNullDouble((object) this.txtAverageMeasuredReadingsTam.Text);
    set => this.txtAverageMeasuredReadingsTam.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Cov
  {
    get => Helpers.ParseNullDouble((object) this.txtCov.Text);
    set => this.txtCov.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? Tlim
  {
    get => Helpers.ParseNullDouble((object) this.txtLimitingThicknessTlim.Text);
    set => this.txtLimitingThicknessTlim.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? MAWPrTamMinusFCA
  {
    get => Helpers.ParseNullDouble((object) this.txtMAWPr.Text);
    set => this.txtMAWPr.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? MAWPrTamMinusFCAOverRSFa
  {
    get => Helpers.ParseNullDouble((object) this.txtMAWPrRSFaL2.Text);
    set => this.txtMAWPrRSFaL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool AverageMeasuredThicknessL1
  {
    get => Helpers.ParseObjectToBool((object) this.txtAverageMThicknessL1.Text);
    set => this.txtAverageMThicknessL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool MawpL1
  {
    get => Helpers.ParseObjectToBool((object) this.txtMaxAllowWorkPressureL1.Text);
    set => this.txtMaxAllowWorkPressureL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool MinimumMeasuredThicknessL1
  {
    get => Helpers.ParseObjectToBool((object) this.txtMinMThicknessL1.Text);
    set => this.txtMinMThicknessL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool Level1Passed
  {
    get => this._level1Passed;
    set
    {
      this._level1Passed = value;
      if (value)
      {
        this.HideLevel2Results();
        this.lbConclusionLevel1.ForeColor = Color.Green;
      }
      else
        this.lbConclusionLevel1.ForeColor = Color.DarkRed;
    }
  }

  public string Level1AssessmentConclusion
  {
    get => this.lbConclusionLevel1.Text;
    set => this.lbConclusionLevel1.Text = value;
  }

  public bool AverageMeasuredThicknessL2
  {
    get => Helpers.ParseObjectToBool((object) this.txtAverageMThicknessL2.Text);
    set => this.txtAverageMThicknessL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool MawpL2
  {
    get => Helpers.ParseObjectToBool((object) this.txtMaxAllowWorkPressureL2.Text);
    set => this.txtMaxAllowWorkPressureL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool MinimumMeasuredThicknessL2
  {
    get => Helpers.ParseObjectToBool((object) this.txtMinMThicknessL2.Text);
    set => this.txtMinMThicknessL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool Level2Passed
  {
    get => this._level2Passed;
    set
    {
      this._level2Passed = value;
      if (value)
        this.lbConclusionLevel2.ForeColor = Color.DarkGreen;
      else
        this.lbConclusionLevel2.ForeColor = Color.DarkRed;
    }
  }

  public string Level2AssessmentConclusion
  {
    get => this.lbConclusionLevel2.Text;
    set => this.lbConclusionLevel2.Text = value;
  }

  public string UMAllowableStrength
  {
    set => this.umAllowableStrength.Text = value;
  }

  public string UMMinReqThickTmin
  {
    set => this.umMinReqThickTmin.Text = value;
  }

  public string UMMinMeasuredThicknessTmm
  {
    set => this.umMinMeasuredThicknessTmm.Text = value;
  }

  public string UMLimitingThicknessTlim
  {
    set => this.umLimitingThicknessTlim.Text = value;
  }

  public string UMAverageMeasuredReadingsTam
  {
    set => this.umAverageMeasuredReadingsTam.Text = value;
  }

  public string UMMAWPr
  {
    set => this.umMAWPr.Text = value;
  }

  public string UMMAWPrRSFa
  {
    set => this.umMAWPrRSFaL2.Text = value;
  }

  public string ResultMessages
  {
    get => this.txtWarningMessages.Text;
    set => this.txtWarningMessages.Text = value;
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public string CodeEdition => this._presenter.CodeEdition;

  public string Title
  {
    get => "Assessment to API 579 Part 4: General Metal Loss. Point Thickness Readings";
  }

  public string Introduction
  {
    get
    {
      return "This module assesses general metal loss based on point thickness readings, according to the API 579 Part 4 procedure. The coefficient of variation (COV) of thickness readings is calculated, and if the COV is less than 10% then the average of all thickness readings is assessed against the construction code minimum required thickness, corrected for the required future corrosion allowance (FCA). If the COV is greater than 10%, the procedures using critical thickness profiles in API 579 Part 4 or 5 should be used instead.";
    }
  }

  public string CommentsAndAssumptions
  {
    get
    {
      return "The API 579 metal loss procedures in Part 4 assume that: \n 1. The original design criteria were in accordance with a recognised code or standard. \n2. The component is not operating in the creep range.\n3. The metal loss is relatively smooth (e.g. no local stress concentrations).\n4. The component is not in cyclic service. \n5. Level 1 covers Type A Component (Part 4, paragraph 4.2.5) subject to internal pressure. (i.e. supplemental loads are assumed negligible).\n6. Level 2 covers type A or B Components (see Part 4, paragraph 4.2.5) subject to internal pressure, external pressure, supplemental loads (see Annex A, paragraph A.2.7), or any combination of loading. \n\nAPI 579 recommends a minimum of 15 thickness readings should be used unless the level of NDE utilized can be used to confirm that the metal loss is general.";
    }
  }

  public string References
  {
    get
    {
      return "API 579 'Fitness-for-Service', Second Edition, The American Society of Mechanical Engineers. Part 4: Assessment of General Metal Loss.";
    }
  }

  public string Limitations => string.Empty;

  public void ShowTlim(bool show)
  {
    if (show)
      this.tblpIntermediateResults.RowStyles[5] = new RowStyle(SizeType.Absolute, 22f);
    else
      this.tblpIntermediateResults.RowStyles[5] = new RowStyle(SizeType.Absolute, 0.0f);
  }

  private void HideLevel2Results()
  {
    this.lbMAWPrRSFaL2.Visible = false;
    this.umMAWPrRSFaL2.Visible = false;
    this.txtMAWPrRSFaL2.Visible = false;
    this.tblMAWPrRSFaL2.Visible = false;
    this.grcAssessmentCriteriaL2.Visible = false;
    this.grcAssessmentConclusionL2.Visible = false;
  }

  private void ShowLevel2Results()
  {
    this.lbMAWPrRSFaL2.Visible = true;
    this.umMAWPrRSFaL2.Visible = true;
    this.txtMAWPrRSFaL2.Visible = true;
    this.tblMAWPrRSFaL2.Visible = true;
    this.grcAssessmentCriteriaL2.Visible = true;
    this.grcAssessmentConclusionL2.Visible = true;
    this.grcAssessmentCriteriaL2.Visible = true;
    this.grcAssessmentConclusionL2.Visible = true;
  }

  private void vwResult_Load(object sender, EventArgs e)
  {
    this.txtAllowableStrength.ShowOnlyN4();
    this.txtMinReqThickTmin.ShowOnlyN4();
    this.txtMinMeasuredThicknessTmm.ShowOnlyN4();
    this.txtCov.ShowOnlyN4();
    this.txtLimitingThicknessTlim.ShowOnlyN4();
    this.txtAverageMeasuredReadingsTam.ShowOnlyN4();
    this.txtMAWPrRSFaL2.ShowOnlyN4();
    this.txtMAWPr.ShowOnlyN4();
    this.txtAllowableStrength.ShowOnlyN4();
    this.txtAllowableStrength.ShowOnlyN4();
    this.txtAllowableStrength.ShowOnlyN4();
  }

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.txtMAWPrRSFaL2 = new TextEdit();
    this.tableLayoutPanel27 = new TableLayoutPanel();
    this.lbConclusionLevel2 = new LabelControl();
    this.umCov = new LabelControl();
    this.grcAssessmentCriteriaL1 = new GroupControl();
    this.tblpAssessmentCriteriaL1 = new TableLayoutPanel();
    this.labelControl29 = new LabelControl();
    this.tableLayoutPanel20 = new TableLayoutPanel();
    this.txtAverageMThicknessL1 = new TextEdit();
    this.tableLayoutPanel21 = new TableLayoutPanel();
    this.txtMinMThicknessL1 = new TextEdit();
    this.labelControl32 = new LabelControl();
    this.tableLayoutPanel19 = new TableLayoutPanel();
    this.txtMaxAllowWorkPressureL1 = new TextEdit();
    this.labelControl31 = new LabelControl();
    this.umMAWPrRSFaL2 = new LabelControl();
    this.txtCov = new TextEdit();
    this.labelControl1 = new LabelControl();
    this.tableLayoutPanel4 = new TableLayoutPanel();
    this.umMinReqThickTmin = new LabelControl();
    this.txtMinReqThickTmin = new TextEdit();
    this.umMinMeasuredThicknessTmm = new LabelControl();
    this.tableLayoutPanel2 = new TableLayoutPanel();
    this.umAllowableStrength = new LabelControl();
    this.txtAllowableStrength = new TextEdit();
    this.txtMinMeasuredThicknessTmm = new TextEdit();
    this.tableLayoutPanel24 = new TableLayoutPanel();
    this.txtMaxAllowWorkPressureL2 = new TextEdit();
    this.txtAverageMThicknessL2 = new TextEdit();
    this.txtMinMThicknessL2 = new TextEdit();
    this.grcAssessmentConclusionL2 = new GroupControl();
    this.tblpAssessmentCriteriaL2 = new TableLayoutPanel();
    this.labelControl34 = new LabelControl();
    this.tableLayoutPanel25 = new TableLayoutPanel();
    this.labelControl37 = new LabelControl();
    this.labelControl38 = new LabelControl();
    this.tableLayoutPanel26 = new TableLayoutPanel();
    this.grcAssessmentCriteriaL2 = new GroupControl();
    this.lbConclusionLevel1 = new LabelControl();
    this.tableLayoutPanel22 = new TableLayoutPanel();
    this.grcAssessmentConclusionL1 = new GroupControl();
    this.labelControl2 = new LabelControl();
    this.labelControl5 = new LabelControl();
    this.txtWarningMessages = new MemoEdit();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.groupControl3 = new GroupControl();
    this.labelControl4 = new LabelControl();
    this.labelControl6 = new LabelControl();
    this.lbTlim = new LabelControl();
    this.tableLayoutPanel5 = new TableLayoutPanel();
    this.tableLayoutPanel8 = new TableLayoutPanel();
    this.tblpIntermediateResults = new TableLayoutPanel();
    this.tableLayoutPanel11 = new TableLayoutPanel();
    this.txtMAWPr = new TextEdit();
    this.umMAWPr = new LabelControl();
    this.labelControl3 = new LabelControl();
    this.grpTlimValue = new TableLayoutPanel();
    this.umLimitingThicknessTlim = new LabelControl();
    this.txtLimitingThicknessTlim = new TextEdit();
    this.tableLayoutPanel7 = new TableLayoutPanel();
    this.umAverageMeasuredReadingsTam = new LabelControl();
    this.txtAverageMeasuredReadingsTam = new TextEdit();
    this.lbMAWPrRSFaL2 = new LabelControl();
    this.tblMAWPrRSFaL2 = new TableLayoutPanel();
    this.grcIntermediateResults = new GroupControl();
    this.txtMAWPrRSFaL2.Properties.BeginInit();
    this.tableLayoutPanel27.SuspendLayout();
    this.grcAssessmentCriteriaL1.BeginInit();
    this.grcAssessmentCriteriaL1.SuspendLayout();
    this.tblpAssessmentCriteriaL1.SuspendLayout();
    this.tableLayoutPanel20.SuspendLayout();
    this.txtAverageMThicknessL1.Properties.BeginInit();
    this.tableLayoutPanel21.SuspendLayout();
    this.txtMinMThicknessL1.Properties.BeginInit();
    this.tableLayoutPanel19.SuspendLayout();
    this.txtMaxAllowWorkPressureL1.Properties.BeginInit();
    this.txtCov.Properties.BeginInit();
    this.tableLayoutPanel4.SuspendLayout();
    this.txtMinReqThickTmin.Properties.BeginInit();
    this.tableLayoutPanel2.SuspendLayout();
    this.txtAllowableStrength.Properties.BeginInit();
    this.txtMinMeasuredThicknessTmm.Properties.BeginInit();
    this.tableLayoutPanel24.SuspendLayout();
    this.txtMaxAllowWorkPressureL2.Properties.BeginInit();
    this.txtAverageMThicknessL2.Properties.BeginInit();
    this.txtMinMThicknessL2.Properties.BeginInit();
    this.grcAssessmentConclusionL2.BeginInit();
    this.grcAssessmentConclusionL2.SuspendLayout();
    this.tblpAssessmentCriteriaL2.SuspendLayout();
    this.tableLayoutPanel25.SuspendLayout();
    this.tableLayoutPanel26.SuspendLayout();
    this.grcAssessmentCriteriaL2.BeginInit();
    this.grcAssessmentCriteriaL2.SuspendLayout();
    this.tableLayoutPanel22.SuspendLayout();
    this.grcAssessmentConclusionL1.BeginInit();
    this.grcAssessmentConclusionL1.SuspendLayout();
    this.txtWarningMessages.Properties.BeginInit();
    this.tableLayoutPanel3.SuspendLayout();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.tableLayoutPanel5.SuspendLayout();
    this.tableLayoutPanel8.SuspendLayout();
    this.tblpIntermediateResults.SuspendLayout();
    this.tableLayoutPanel11.SuspendLayout();
    this.txtMAWPr.Properties.BeginInit();
    this.grpTlimValue.SuspendLayout();
    this.txtLimitingThicknessTlim.Properties.BeginInit();
    this.tableLayoutPanel7.SuspendLayout();
    this.txtAverageMeasuredReadingsTam.Properties.BeginInit();
    this.tblMAWPrRSFaL2.SuspendLayout();
    this.grcIntermediateResults.BeginInit();
    this.grcIntermediateResults.SuspendLayout();
    this.SuspendLayout();
    this.txtMAWPrRSFaL2.Location = new Point(0, 0);
    this.txtMAWPrRSFaL2.Margin = new Padding(0);
    this.txtMAWPrRSFaL2.Name = "txtMAWPrRSFaL2";
    this.txtMAWPrRSFaL2.Properties.ReadOnly = true;
    this.txtMAWPrRSFaL2.Size = new Size(100, 20);
    this.txtMAWPrRSFaL2.TabIndex = 0;
    this.tableLayoutPanel27.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel27.ColumnCount = 2;
    this.tableLayoutPanel27.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel27.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel27.Controls.Add((Control) this.lbConclusionLevel2, 0, 0);
    this.tableLayoutPanel27.Dock = DockStyle.Fill;
    this.tableLayoutPanel27.Location = new Point(2, 21);
    this.tableLayoutPanel27.Name = "tableLayoutPanel27";
    this.tableLayoutPanel27.Padding = new Padding(10);
    this.tableLayoutPanel27.RowCount = 1;
    this.tableLayoutPanel27.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel27.Size = new Size(576, 37);
    this.tableLayoutPanel27.TabIndex = 0;
    this.lbConclusionLevel2.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold);
    this.lbConclusionLevel2.Location = new Point(13, 13);
    this.lbConclusionLevel2.Name = "lbConclusionLevel2";
    this.lbConclusionLevel2.Size = new Size(157, 13);
    this.lbConclusionLevel2.TabIndex = 16 /*0x10*/;
    this.lbConclusionLevel2.Text = "The Level 2 Assessment is ..";
    this.umCov.Location = new Point(103, 3);
    this.umCov.Name = "umCov";
    this.umCov.Size = new Size(11, 13);
    this.umCov.TabIndex = 1;
    this.umCov.Text = "%";
    this.grcAssessmentCriteriaL1.Controls.Add((Control) this.tblpAssessmentCriteriaL1);
    this.grcAssessmentCriteriaL1.Dock = DockStyle.Top;
    this.grcAssessmentCriteriaL1.Location = new Point(0, 219);
    this.grcAssessmentCriteriaL1.Name = "grcAssessmentCriteriaL1";
    this.grcAssessmentCriteriaL1.Size = new Size(580, 101);
    this.grcAssessmentCriteriaL1.TabIndex = 16 /*0x10*/;
    this.grcAssessmentCriteriaL1.Text = "Level 1 Assessment Criteria";
    this.tblpAssessmentCriteriaL1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpAssessmentCriteriaL1.ColumnCount = 2;
    this.tblpAssessmentCriteriaL1.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentCriteriaL1.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.labelControl29, 0, 0);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.tableLayoutPanel20, 1, 0);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.tableLayoutPanel21, 1, 1);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.labelControl32, 0, 1);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.tableLayoutPanel19, 1, 2);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.labelControl31, 0, 2);
    this.tblpAssessmentCriteriaL1.Dock = DockStyle.Fill;
    this.tblpAssessmentCriteriaL1.Location = new Point(2, 21);
    this.tblpAssessmentCriteriaL1.Name = "tblpAssessmentCriteriaL1";
    this.tblpAssessmentCriteriaL1.Padding = new Padding(10);
    this.tblpAssessmentCriteriaL1.RowCount = 3;
    this.tblpAssessmentCriteriaL1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL1.Size = new Size(576, 78);
    this.tblpAssessmentCriteriaL1.TabIndex = 0;
    this.labelControl29.Location = new Point(13, 13);
    this.labelControl29.Name = "labelControl29";
    this.labelControl29.Size = new Size(140, 13);
    this.labelControl29.TabIndex = 16 /*0x10*/;
    this.labelControl29.Text = "Average Measured Thickness";
    this.tableLayoutPanel20.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel20.ColumnCount = 2;
    this.tableLayoutPanel20.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel20.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel20.Controls.Add((Control) this.txtAverageMThicknessL1, 0, 0);
    this.tableLayoutPanel20.Location = new Point(196, 11);
    this.tableLayoutPanel20.Margin = new Padding(1);
    this.tableLayoutPanel20.Name = "tableLayoutPanel20";
    this.tableLayoutPanel20.RowCount = 1;
    this.tableLayoutPanel20.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel20.Size = new Size(155, 20);
    this.tableLayoutPanel20.TabIndex = 17;
    this.txtAverageMThicknessL1.Location = new Point(0, 0);
    this.txtAverageMThicknessL1.Margin = new Padding(0);
    this.txtAverageMThicknessL1.Name = "txtAverageMThicknessL1";
    this.txtAverageMThicknessL1.Properties.ReadOnly = true;
    this.txtAverageMThicknessL1.Size = new Size(100, 20);
    this.txtAverageMThicknessL1.TabIndex = 0;
    this.tableLayoutPanel21.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel21.ColumnCount = 2;
    this.tableLayoutPanel21.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel21.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel21.Controls.Add((Control) this.txtMinMThicknessL1, 0, 0);
    this.tableLayoutPanel21.Location = new Point(196, 33);
    this.tableLayoutPanel21.Margin = new Padding(1);
    this.tableLayoutPanel21.Name = "tableLayoutPanel21";
    this.tableLayoutPanel21.RowCount = 1;
    this.tableLayoutPanel21.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel21.Size = new Size(155, 20);
    this.tableLayoutPanel21.TabIndex = 24;
    this.txtMinMThicknessL1.Location = new Point(0, 0);
    this.txtMinMThicknessL1.Margin = new Padding(0);
    this.txtMinMThicknessL1.Name = "txtMinMThicknessL1";
    this.txtMinMThicknessL1.Properties.ReadOnly = true;
    this.txtMinMThicknessL1.Size = new Size(100, 20);
    this.txtMinMThicknessL1.TabIndex = 0;
    this.labelControl32.Location = new Point(13, 35);
    this.labelControl32.Name = "labelControl32";
    this.labelControl32.Size = new Size(139, 13);
    this.labelControl32.TabIndex = 20;
    this.labelControl32.Text = "Minimum Measured Thickness";
    this.tableLayoutPanel19.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel19.ColumnCount = 2;
    this.tableLayoutPanel19.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel19.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel19.Controls.Add((Control) this.txtMaxAllowWorkPressureL1, 0, 0);
    this.tableLayoutPanel19.Location = new Point(196, 55);
    this.tableLayoutPanel19.Margin = new Padding(1);
    this.tableLayoutPanel19.Name = "tableLayoutPanel19";
    this.tableLayoutPanel19.RowCount = 1;
    this.tableLayoutPanel19.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel19.Size = new Size(155, 20);
    this.tableLayoutPanel19.TabIndex = 19;
    this.txtMaxAllowWorkPressureL1.Location = new Point(0, 0);
    this.txtMaxAllowWorkPressureL1.Margin = new Padding(0);
    this.txtMaxAllowWorkPressureL1.Name = "txtMaxAllowWorkPressureL1";
    this.txtMaxAllowWorkPressureL1.Properties.ReadOnly = true;
    this.txtMaxAllowWorkPressureL1.Size = new Size(100, 20);
    this.txtMaxAllowWorkPressureL1.TabIndex = 0;
    this.labelControl31.Location = new Point(13, 57);
    this.labelControl31.Name = "labelControl31";
    this.labelControl31.Size = new Size(179, 13);
    this.labelControl31.TabIndex = 18;
    this.labelControl31.Text = "Maximum Allowable Working Pressure";
    this.umMAWPrRSFaL2.Location = new Point(103, 3);
    this.umMAWPrRSFaL2.Name = "umMAWPrRSFaL2";
    this.umMAWPrRSFaL2.Size = new Size(41, 13);
    this.umMAWPrRSFaL2.TabIndex = 1;
    this.umMAWPrRSFaL2.Text = "measure";
    this.txtCov.Location = new Point(0, 0);
    this.txtCov.Margin = new Padding(0);
    this.txtCov.Name = "txtCov";
    this.txtCov.Properties.ReadOnly = true;
    this.txtCov.Size = new Size(100, 20);
    this.txtCov.TabIndex = 0;
    this.labelControl1.Location = new Point(13, 35);
    this.labelControl1.Name = "labelControl1";
    this.labelControl1.Size = new Size(192 /*0xC0*/, 13);
    this.labelControl1.TabIndex = 12;
    this.labelControl1.Text = "Minimum Required Wall Thickness  (tmin)";
    this.tableLayoutPanel4.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel4.ColumnCount = 2;
    this.tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel4.Controls.Add((Control) this.umMinReqThickTmin, 1, 0);
    this.tableLayoutPanel4.Controls.Add((Control) this.txtMinReqThickTmin, 0, 0);
    this.tableLayoutPanel4.Location = new Point(209, 33);
    this.tableLayoutPanel4.Margin = new Padding(1);
    this.tableLayoutPanel4.Name = "tableLayoutPanel4";
    this.tableLayoutPanel4.RowCount = 1;
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel4.Size = new Size(174, 20);
    this.tableLayoutPanel4.TabIndex = 19;
    this.umMinReqThickTmin.Location = new Point(103, 3);
    this.umMinReqThickTmin.Name = "umMinReqThickTmin";
    this.umMinReqThickTmin.Size = new Size(41, 13);
    this.umMinReqThickTmin.TabIndex = 1;
    this.umMinReqThickTmin.Text = "measure";
    this.txtMinReqThickTmin.Location = new Point(0, 0);
    this.txtMinReqThickTmin.Margin = new Padding(0);
    this.txtMinReqThickTmin.Name = "txtMinReqThickTmin";
    this.txtMinReqThickTmin.Properties.ReadOnly = true;
    this.txtMinReqThickTmin.Size = new Size(100, 20);
    this.txtMinReqThickTmin.TabIndex = 0;
    this.umMinMeasuredThicknessTmm.Location = new Point(103, 3);
    this.umMinMeasuredThicknessTmm.Name = "umMinMeasuredThicknessTmm";
    this.umMinMeasuredThicknessTmm.Size = new Size(41, 13);
    this.umMinMeasuredThicknessTmm.TabIndex = 1;
    this.umMinMeasuredThicknessTmm.Text = "measure";
    this.tableLayoutPanel2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel2.ColumnCount = 2;
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel2.Controls.Add((Control) this.umAllowableStrength, 1, 0);
    this.tableLayoutPanel2.Controls.Add((Control) this.txtAllowableStrength, 0, 0);
    this.tableLayoutPanel2.Location = new Point(209, 11);
    this.tableLayoutPanel2.Margin = new Padding(1);
    this.tableLayoutPanel2.Name = "tableLayoutPanel2";
    this.tableLayoutPanel2.RowCount = 1;
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel2.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel2.Size = new Size(174, 20);
    this.tableLayoutPanel2.TabIndex = 11;
    this.umAllowableStrength.Location = new Point(103, 3);
    this.umAllowableStrength.Name = "umAllowableStrength";
    this.umAllowableStrength.Size = new Size(41, 13);
    this.umAllowableStrength.TabIndex = 1;
    this.umAllowableStrength.Text = "measure";
    this.txtAllowableStrength.Location = new Point(0, 0);
    this.txtAllowableStrength.Margin = new Padding(0);
    this.txtAllowableStrength.Name = "txtAllowableStrength";
    this.txtAllowableStrength.Properties.ReadOnly = true;
    this.txtAllowableStrength.Size = new Size(100, 20);
    this.txtAllowableStrength.TabIndex = 0;
    this.txtMinMeasuredThicknessTmm.Location = new Point(0, 0);
    this.txtMinMeasuredThicknessTmm.Margin = new Padding(0);
    this.txtMinMeasuredThicknessTmm.Name = "txtMinMeasuredThicknessTmm";
    this.txtMinMeasuredThicknessTmm.Properties.ReadOnly = true;
    this.txtMinMeasuredThicknessTmm.Size = new Size(100, 20);
    this.txtMinMeasuredThicknessTmm.TabIndex = 0;
    this.tableLayoutPanel24.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel24.ColumnCount = 2;
    this.tableLayoutPanel24.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel24.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel24.Controls.Add((Control) this.txtMaxAllowWorkPressureL2, 0, 0);
    this.tableLayoutPanel24.Location = new Point(196, 55);
    this.tableLayoutPanel24.Margin = new Padding(1);
    this.tableLayoutPanel24.Name = "tableLayoutPanel24";
    this.tableLayoutPanel24.RowCount = 1;
    this.tableLayoutPanel24.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel24.Size = new Size(155, 20);
    this.tableLayoutPanel24.TabIndex = 19;
    this.txtMaxAllowWorkPressureL2.Location = new Point(0, 0);
    this.txtMaxAllowWorkPressureL2.Margin = new Padding(0);
    this.txtMaxAllowWorkPressureL2.Name = "txtMaxAllowWorkPressureL2";
    this.txtMaxAllowWorkPressureL2.Properties.ReadOnly = true;
    this.txtMaxAllowWorkPressureL2.Size = new Size(100, 20);
    this.txtMaxAllowWorkPressureL2.TabIndex = 0;
    this.txtAverageMThicknessL2.Location = new Point(0, 0);
    this.txtAverageMThicknessL2.Margin = new Padding(0);
    this.txtAverageMThicknessL2.Name = "txtAverageMThicknessL2";
    this.txtAverageMThicknessL2.Properties.ReadOnly = true;
    this.txtAverageMThicknessL2.Size = new Size(100, 20);
    this.txtAverageMThicknessL2.TabIndex = 0;
    this.txtMinMThicknessL2.Location = new Point(0, 0);
    this.txtMinMThicknessL2.Margin = new Padding(0);
    this.txtMinMThicknessL2.Name = "txtMinMThicknessL2";
    this.txtMinMThicknessL2.Properties.ReadOnly = true;
    this.txtMinMThicknessL2.Size = new Size(100, 20);
    this.txtMinMThicknessL2.TabIndex = 0;
    this.grcAssessmentConclusionL2.Controls.Add((Control) this.tableLayoutPanel27);
    this.grcAssessmentConclusionL2.Dock = DockStyle.Top;
    this.grcAssessmentConclusionL2.Location = new Point(0, 481);
    this.grcAssessmentConclusionL2.Name = "grcAssessmentConclusionL2";
    this.grcAssessmentConclusionL2.Size = new Size(580, 60);
    this.grcAssessmentConclusionL2.TabIndex = 19;
    this.grcAssessmentConclusionL2.Text = "Level2 Assessment Conclusion";
    this.tblpAssessmentCriteriaL2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpAssessmentCriteriaL2.ColumnCount = 2;
    this.tblpAssessmentCriteriaL2.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentCriteriaL2.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.labelControl34, 0, 0);
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.tableLayoutPanel25, 1, 0);
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.labelControl37, 0, 2);
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.labelControl38, 0, 1);
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.tableLayoutPanel26, 1, 1);
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.tableLayoutPanel24, 1, 2);
    this.tblpAssessmentCriteriaL2.Dock = DockStyle.Fill;
    this.tblpAssessmentCriteriaL2.Location = new Point(2, 21);
    this.tblpAssessmentCriteriaL2.Name = "tblpAssessmentCriteriaL2";
    this.tblpAssessmentCriteriaL2.Padding = new Padding(10);
    this.tblpAssessmentCriteriaL2.RowCount = 3;
    this.tblpAssessmentCriteriaL2.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL2.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL2.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL2.Size = new Size(576, 78);
    this.tblpAssessmentCriteriaL2.TabIndex = 0;
    this.labelControl34.Location = new Point(13, 13);
    this.labelControl34.Name = "labelControl34";
    this.labelControl34.Size = new Size(140, 13);
    this.labelControl34.TabIndex = 16 /*0x10*/;
    this.labelControl34.Text = "Average Measured Thickness";
    this.tableLayoutPanel25.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel25.ColumnCount = 2;
    this.tableLayoutPanel25.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel25.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel25.Controls.Add((Control) this.txtAverageMThicknessL2, 0, 0);
    this.tableLayoutPanel25.Location = new Point(196, 11);
    this.tableLayoutPanel25.Margin = new Padding(1);
    this.tableLayoutPanel25.Name = "tableLayoutPanel25";
    this.tableLayoutPanel25.RowCount = 1;
    this.tableLayoutPanel25.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel25.Size = new Size(155, 20);
    this.tableLayoutPanel25.TabIndex = 17;
    this.labelControl37.Location = new Point(13, 57);
    this.labelControl37.Name = "labelControl37";
    this.labelControl37.Size = new Size(179, 13);
    this.labelControl37.TabIndex = 18;
    this.labelControl37.Text = "Maximum Allowable Working Pressure";
    this.labelControl38.Location = new Point(13, 35);
    this.labelControl38.Name = "labelControl38";
    this.labelControl38.Size = new Size(139, 13);
    this.labelControl38.TabIndex = 20;
    this.labelControl38.Text = "Minimum Measured Thickness";
    this.tableLayoutPanel26.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel26.ColumnCount = 2;
    this.tableLayoutPanel26.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel26.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel26.Controls.Add((Control) this.txtMinMThicknessL2, 0, 0);
    this.tableLayoutPanel26.Location = new Point(196, 33);
    this.tableLayoutPanel26.Margin = new Padding(1);
    this.tableLayoutPanel26.Name = "tableLayoutPanel26";
    this.tableLayoutPanel26.RowCount = 1;
    this.tableLayoutPanel26.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel26.Size = new Size(155, 20);
    this.tableLayoutPanel26.TabIndex = 24;
    this.grcAssessmentCriteriaL2.Controls.Add((Control) this.tblpAssessmentCriteriaL2);
    this.grcAssessmentCriteriaL2.Dock = DockStyle.Top;
    this.grcAssessmentCriteriaL2.Location = new Point(0, 380);
    this.grcAssessmentCriteriaL2.Name = "grcAssessmentCriteriaL2";
    this.grcAssessmentCriteriaL2.Size = new Size(580, 101);
    this.grcAssessmentCriteriaL2.TabIndex = 18;
    this.grcAssessmentCriteriaL2.Text = "Level 2 Assessment Criteria";
    this.lbConclusionLevel1.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold);
    this.lbConclusionLevel1.Location = new Point(13, 13);
    this.lbConclusionLevel1.Name = "lbConclusionLevel1";
    this.lbConclusionLevel1.Size = new Size(157, 13);
    this.lbConclusionLevel1.TabIndex = 16 /*0x10*/;
    this.lbConclusionLevel1.Text = "The Level 1 Assessment is ..";
    this.tableLayoutPanel22.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel22.ColumnCount = 2;
    this.tableLayoutPanel22.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel22.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel22.Controls.Add((Control) this.lbConclusionLevel1, 0, 0);
    this.tableLayoutPanel22.Dock = DockStyle.Fill;
    this.tableLayoutPanel22.Location = new Point(2, 21);
    this.tableLayoutPanel22.Name = "tableLayoutPanel22";
    this.tableLayoutPanel22.Padding = new Padding(10);
    this.tableLayoutPanel22.RowCount = 1;
    this.tableLayoutPanel22.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel22.Size = new Size(576, 37);
    this.tableLayoutPanel22.TabIndex = 0;
    this.grcAssessmentConclusionL1.Controls.Add((Control) this.tableLayoutPanel22);
    this.grcAssessmentConclusionL1.Dock = DockStyle.Top;
    this.grcAssessmentConclusionL1.Location = new Point(0, 320);
    this.grcAssessmentConclusionL1.Name = "grcAssessmentConclusionL1";
    this.grcAssessmentConclusionL1.Size = new Size(580, 60);
    this.grcAssessmentConclusionL1.TabIndex = 17;
    this.grcAssessmentConclusionL1.Text = "Level1 Assessment Conclusion";
    this.labelControl2.Location = new Point(13, 13);
    this.labelControl2.Name = "labelControl2";
    this.labelControl2.Size = new Size(78, 13);
    this.labelControl2.TabIndex = 10;
    this.labelControl2.Text = "Allowable Stress";
    this.labelControl5.Location = new Point(13, 57);
    this.labelControl5.Name = "labelControl5";
    this.labelControl5.Size = new Size(139, 13);
    this.labelControl5.TabIndex = 15;
    this.labelControl5.Text = "Minimum Measured Thickness";
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(550, 115);
    this.txtWarningMessages.TabIndex = 4;
    this.tableLayoutPanel3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel3.Size = new Size(576, 141);
    this.tableLayoutPanel3.TabIndex = 0;
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel3);
    this.groupControl3.Dock = DockStyle.Top;
    this.groupControl3.Location = new Point(0, 541);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(580, 164);
    this.groupControl3.TabIndex = 15;
    this.groupControl3.Text = "Warning Messages";
    this.labelControl4.Location = new Point(13, 79);
    this.labelControl4.Name = "labelControl4";
    this.labelControl4.Size = new Size(167, 13);
    this.labelControl4.TabIndex = 14;
    this.labelControl4.Text = "Average Measured Readings (tam)";
    this.labelControl6.Location = new Point(13, 101);
    this.labelControl6.Name = "labelControl6";
    this.labelControl6.Size = new Size(19, 13);
    this.labelControl6.TabIndex = 16 /*0x10*/;
    this.labelControl6.Text = "Cov";
    this.lbTlim.Location = new Point(13, 123);
    this.lbTlim.Name = "lbTlim";
    this.lbTlim.Size = new Size(113, 13);
    this.lbTlim.TabIndex = 17;
    this.lbTlim.Text = "Limiting Thickness (Tlim)";
    this.tableLayoutPanel5.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel5.ColumnCount = 2;
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel5.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel5.Controls.Add((Control) this.umMinMeasuredThicknessTmm, 1, 0);
    this.tableLayoutPanel5.Controls.Add((Control) this.txtMinMeasuredThicknessTmm, 0, 0);
    this.tableLayoutPanel5.Location = new Point(209, 55);
    this.tableLayoutPanel5.Margin = new Padding(1);
    this.tableLayoutPanel5.Name = "tableLayoutPanel5";
    this.tableLayoutPanel5.RowCount = 1;
    this.tableLayoutPanel5.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel5.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel5.Size = new Size(174, 20);
    this.tableLayoutPanel5.TabIndex = 20;
    this.tableLayoutPanel8.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel8.ColumnCount = 2;
    this.tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel8.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel8.Controls.Add((Control) this.umCov, 1, 0);
    this.tableLayoutPanel8.Controls.Add((Control) this.txtCov, 0, 0);
    this.tableLayoutPanel8.Location = new Point(209, 99);
    this.tableLayoutPanel8.Margin = new Padding(1);
    this.tableLayoutPanel8.Name = "tableLayoutPanel8";
    this.tableLayoutPanel8.RowCount = 1;
    this.tableLayoutPanel8.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel8.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel8.Size = new Size(174, 20);
    this.tableLayoutPanel8.TabIndex = 22;
    this.tblpIntermediateResults.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpIntermediateResults.ColumnCount = 2;
    this.tblpIntermediateResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpIntermediateResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel11, 1, 6);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl3, 0, 6);
    this.tblpIntermediateResults.Controls.Add((Control) this.grpTlimValue, 1, 5);
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel7, 1, 3);
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel4, 1, 1);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl1, 0, 1);
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel2, 1, 0);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl2, 0, 0);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl6, 0, 4);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbTlim, 0, 5);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbMAWPrRSFaL2, 0, 7);
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel8, 1, 4);
    this.tblpIntermediateResults.Controls.Add((Control) this.tblMAWPrRSFaL2, 1, 7);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl5, 0, 2);
    this.tblpIntermediateResults.Controls.Add((Control) this.tableLayoutPanel5, 1, 2);
    this.tblpIntermediateResults.Controls.Add((Control) this.labelControl4, 0, 3);
    this.tblpIntermediateResults.Dock = DockStyle.Fill;
    this.tblpIntermediateResults.Location = new Point(2, 21);
    this.tblpIntermediateResults.Name = "tblpIntermediateResults";
    this.tblpIntermediateResults.Padding = new Padding(10);
    this.tblpIntermediateResults.RowCount = 8;
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.Size = new Size(576, 196);
    this.tblpIntermediateResults.TabIndex = 0;
    this.tableLayoutPanel11.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel11.ColumnCount = 2;
    this.tableLayoutPanel11.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel11.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel11.Controls.Add((Control) this.txtMAWPr, 0, 0);
    this.tableLayoutPanel11.Controls.Add((Control) this.umMAWPr, 1, 0);
    this.tableLayoutPanel11.Location = new Point(209, 143);
    this.tableLayoutPanel11.Margin = new Padding(1);
    this.tableLayoutPanel11.Name = "tableLayoutPanel11";
    this.tableLayoutPanel11.RowCount = 1;
    this.tableLayoutPanel11.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel11.Size = new Size(155, 20);
    this.tableLayoutPanel11.TabIndex = 29;
    this.txtMAWPr.Location = new Point(0, 0);
    this.txtMAWPr.Margin = new Padding(0);
    this.txtMAWPr.Name = "txtMAWPr";
    this.txtMAWPr.Properties.ReadOnly = true;
    this.txtMAWPr.Size = new Size(100, 20);
    this.txtMAWPr.TabIndex = 0;
    this.umMAWPr.Location = new Point(103, 3);
    this.umMAWPr.Name = "umMAWPr";
    this.umMAWPr.Size = new Size(41, 13);
    this.umMAWPr.TabIndex = 1;
    this.umMAWPr.Text = "measure";
    this.labelControl3.Location = new Point(13, 145);
    this.labelControl3.Name = "labelControl3";
    this.labelControl3.Size = new Size(143, 13);
    this.labelControl3.TabIndex = 28;
    this.labelControl3.Text = "MAWPr based on (Tam - FCA)";
    this.grpTlimValue.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.grpTlimValue.ColumnCount = 2;
    this.grpTlimValue.ColumnStyles.Add(new ColumnStyle());
    this.grpTlimValue.ColumnStyles.Add(new ColumnStyle());
    this.grpTlimValue.Controls.Add((Control) this.umLimitingThicknessTlim, 1, 0);
    this.grpTlimValue.Controls.Add((Control) this.txtLimitingThicknessTlim, 0, 0);
    this.grpTlimValue.Location = new Point(209, 121);
    this.grpTlimValue.Margin = new Padding(1);
    this.grpTlimValue.Name = "grpTlimValue";
    this.grpTlimValue.RowCount = 1;
    this.grpTlimValue.RowStyles.Add(new RowStyle());
    this.grpTlimValue.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.grpTlimValue.Size = new Size(174, 20);
    this.grpTlimValue.TabIndex = 27;
    this.umLimitingThicknessTlim.Location = new Point(103, 3);
    this.umLimitingThicknessTlim.Name = "umLimitingThicknessTlim";
    this.umLimitingThicknessTlim.Size = new Size(41, 13);
    this.umLimitingThicknessTlim.TabIndex = 1;
    this.umLimitingThicknessTlim.Text = "measure";
    this.txtLimitingThicknessTlim.Location = new Point(0, 0);
    this.txtLimitingThicknessTlim.Margin = new Padding(0);
    this.txtLimitingThicknessTlim.Name = "txtLimitingThicknessTlim";
    this.txtLimitingThicknessTlim.Properties.ReadOnly = true;
    this.txtLimitingThicknessTlim.Size = new Size(100, 20);
    this.txtLimitingThicknessTlim.TabIndex = 0;
    this.tableLayoutPanel7.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel7.ColumnCount = 2;
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel7.Controls.Add((Control) this.umAverageMeasuredReadingsTam, 1, 0);
    this.tableLayoutPanel7.Controls.Add((Control) this.txtAverageMeasuredReadingsTam, 0, 0);
    this.tableLayoutPanel7.Location = new Point(209, 77);
    this.tableLayoutPanel7.Margin = new Padding(1);
    this.tableLayoutPanel7.Name = "tableLayoutPanel7";
    this.tableLayoutPanel7.RowCount = 1;
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel7.Size = new Size(174, 20);
    this.tableLayoutPanel7.TabIndex = 26;
    this.umAverageMeasuredReadingsTam.Location = new Point(103, 3);
    this.umAverageMeasuredReadingsTam.Name = "umAverageMeasuredReadingsTam";
    this.umAverageMeasuredReadingsTam.Size = new Size(41, 13);
    this.umAverageMeasuredReadingsTam.TabIndex = 1;
    this.umAverageMeasuredReadingsTam.Text = "measure";
    this.txtAverageMeasuredReadingsTam.Location = new Point(0, 0);
    this.txtAverageMeasuredReadingsTam.Margin = new Padding(0);
    this.txtAverageMeasuredReadingsTam.Name = "txtAverageMeasuredReadingsTam";
    this.txtAverageMeasuredReadingsTam.Properties.ReadOnly = true;
    this.txtAverageMeasuredReadingsTam.Size = new Size(100, 20);
    this.txtAverageMeasuredReadingsTam.TabIndex = 0;
    this.lbMAWPrRSFaL2.Location = new Point(13, 167);
    this.lbMAWPrRSFaL2.Name = "lbMAWPrRSFaL2";
    this.lbMAWPrRSFaL2.Size = new Size(172, 13);
    this.lbMAWPrRSFaL2.TabIndex = 18;
    this.lbMAWPrRSFaL2.Text = "MAWPr based on (Tam - FCA)/RSFa";
    this.tblMAWPrRSFaL2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblMAWPrRSFaL2.ColumnCount = 2;
    this.tblMAWPrRSFaL2.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWPrRSFaL2.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWPrRSFaL2.Controls.Add((Control) this.umMAWPrRSFaL2, 1, 0);
    this.tblMAWPrRSFaL2.Controls.Add((Control) this.txtMAWPrRSFaL2, 0, 0);
    this.tblMAWPrRSFaL2.Location = new Point(209, 165);
    this.tblMAWPrRSFaL2.Margin = new Padding(1);
    this.tblMAWPrRSFaL2.Name = "tblMAWPrRSFaL2";
    this.tblMAWPrRSFaL2.RowCount = 1;
    this.tblMAWPrRSFaL2.RowStyles.Add(new RowStyle());
    this.tblMAWPrRSFaL2.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tblMAWPrRSFaL2.Size = new Size(174, 20);
    this.tblMAWPrRSFaL2.TabIndex = 25;
    this.grcIntermediateResults.Controls.Add((Control) this.tblpIntermediateResults);
    this.grcIntermediateResults.Dock = DockStyle.Top;
    this.grcIntermediateResults.Location = new Point(0, 0);
    this.grcIntermediateResults.Name = "grcIntermediateResults";
    this.grcIntermediateResults.Size = new Size(580, 219);
    this.grcIntermediateResults.TabIndex = 13;
    this.grcIntermediateResults.Text = "Intermediate Results";
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.Controls.Add((Control) this.groupControl3);
    this.Controls.Add((Control) this.grcAssessmentConclusionL2);
    this.Controls.Add((Control) this.grcAssessmentCriteriaL2);
    this.Controls.Add((Control) this.grcAssessmentConclusionL1);
    this.Controls.Add((Control) this.grcAssessmentCriteriaL1);
    this.Controls.Add((Control) this.grcIntermediateResults);
    this.Name = nameof (vwResult);
    this.Size = new Size(580, 705);
    this.Load += new EventHandler(this.vwResult_Load);
    this.txtMAWPrRSFaL2.Properties.EndInit();
    this.tableLayoutPanel27.ResumeLayout(false);
    this.tableLayoutPanel27.PerformLayout();
    this.grcAssessmentCriteriaL1.EndInit();
    this.grcAssessmentCriteriaL1.ResumeLayout(false);
    this.tblpAssessmentCriteriaL1.ResumeLayout(false);
    this.tblpAssessmentCriteriaL1.PerformLayout();
    this.tableLayoutPanel20.ResumeLayout(false);
    this.txtAverageMThicknessL1.Properties.EndInit();
    this.tableLayoutPanel21.ResumeLayout(false);
    this.txtMinMThicknessL1.Properties.EndInit();
    this.tableLayoutPanel19.ResumeLayout(false);
    this.txtMaxAllowWorkPressureL1.Properties.EndInit();
    this.txtCov.Properties.EndInit();
    this.tableLayoutPanel4.ResumeLayout(false);
    this.tableLayoutPanel4.PerformLayout();
    this.txtMinReqThickTmin.Properties.EndInit();
    this.tableLayoutPanel2.ResumeLayout(false);
    this.tableLayoutPanel2.PerformLayout();
    this.txtAllowableStrength.Properties.EndInit();
    this.txtMinMeasuredThicknessTmm.Properties.EndInit();
    this.tableLayoutPanel24.ResumeLayout(false);
    this.txtMaxAllowWorkPressureL2.Properties.EndInit();
    this.txtAverageMThicknessL2.Properties.EndInit();
    this.txtMinMThicknessL2.Properties.EndInit();
    this.grcAssessmentConclusionL2.EndInit();
    this.grcAssessmentConclusionL2.ResumeLayout(false);
    this.tblpAssessmentCriteriaL2.ResumeLayout(false);
    this.tblpAssessmentCriteriaL2.PerformLayout();
    this.tableLayoutPanel25.ResumeLayout(false);
    this.tableLayoutPanel26.ResumeLayout(false);
    this.grcAssessmentCriteriaL2.EndInit();
    this.grcAssessmentCriteriaL2.ResumeLayout(false);
    this.tableLayoutPanel22.ResumeLayout(false);
    this.tableLayoutPanel22.PerformLayout();
    this.grcAssessmentConclusionL1.EndInit();
    this.grcAssessmentConclusionL1.ResumeLayout(false);
    this.txtWarningMessages.Properties.EndInit();
    this.tableLayoutPanel3.ResumeLayout(false);
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.tableLayoutPanel5.ResumeLayout(false);
    this.tableLayoutPanel5.PerformLayout();
    this.tableLayoutPanel8.ResumeLayout(false);
    this.tableLayoutPanel8.PerformLayout();
    this.tblpIntermediateResults.ResumeLayout(false);
    this.tblpIntermediateResults.PerformLayout();
    this.tableLayoutPanel11.ResumeLayout(false);
    this.tableLayoutPanel11.PerformLayout();
    this.txtMAWPr.Properties.EndInit();
    this.grpTlimValue.ResumeLayout(false);
    this.grpTlimValue.PerformLayout();
    this.txtLimitingThicknessTlim.Properties.EndInit();
    this.tableLayoutPanel7.ResumeLayout(false);
    this.tableLayoutPanel7.PerformLayout();
    this.txtAverageMeasuredReadingsTam.Properties.EndInit();
    this.tblMAWPrRSFaL2.ResumeLayout(false);
    this.tblMAWPrRSFaL2.PerformLayout();
    this.grcIntermediateResults.EndInit();
    this.grcIntermediateResults.ResumeLayout(false);
    this.ResumeLayout(false);
  }
}
