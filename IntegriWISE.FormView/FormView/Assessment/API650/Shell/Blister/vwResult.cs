// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.API650.Shell.Blister.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.API650.Shell.Blister;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.API650.Shell.Blister;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private string _LBcLessEquLRootDtc;
  private bool _TmmMoreEqual0_5tc;
  private string _LBtmmMoreEqual0_5tc;
  private bool _TmmMoreEqual0_5tcl2;
  private string _LBtmmMoreEqual0_5tcl2;
  private bool _BpLessEqual0_1min_s_c;
  private string _LBBpLessEqual0_1min_s_c;
  private bool _LwMoreMax_2tc_25;
  private string _LBLwMoreMax_2tc_25;
  private bool _LmsdMoreEqual1_8RootDtc;
  private string _LBLmsdMoreEqual1_8RootDtc;
  private bool _Level1Passed;
  private string _LBLevel1Conclusion;
  private bool _CLessEquLRootDtc;
  private string _LBsLessEqualRootInDtc;
  private bool _SLessEqualRootInDtc;
  private string _LBBdMax_s_cLessEqual50mm;
  private bool _BdMax_s_cLessEqual50mm;
  private string _LBBd;
  private string _UMBd;
  private double? _Bd;
  private string _LBtc;
  private string _UMtc;
  private double? _Tc;
  private bool _LwMoreEqualMax_2tc_25mml2;
  private string _LBLwMoreEqualMax_2tc_25mml2;
  private bool _LwMoreEqualMax_2tc_25mml1;
  private string _LBLwMoreEqualMax_2tc_25mml1;
  private bool _RtGreater0_2;
  private string _LBRtGreater0_2;
  private bool _EnableLevel1Conclusion;
  private bool _EnableLevel1Criteria;
  private bool _EnableIntermediateResult;
  private string _Level1Conclusion;
  private string _ResultMessages;
  private bool _showLevel2;
  private IRecordView _recordView;
  private ResultPresenter _presenter;
  private string _DamageType;
  private string _LBDamageType;
  private double _dHIC;
  private string _LBdHIC;
  private string _UMdHIC;
  private double _RSF;
  private string _LBRSF;
  private double _RT;
  private string _LBRT;
  private double _LambdaC;
  private string _LBLambdaC;
  private bool? _RSFGreaterRSFa;
  private string _LBRSFGreaterRSFa;
  private bool? _LambdaCLessThan9;
  private string _LBLambdaCLessThan9;
  private bool? _DOverTcGreaterThan20;
  private string _LBDOverTcGreaterThan20;
  private bool? _RSFBetween0_7And1;
  private string _LBRSFBetween0_7And1;
  private bool? _ElBetween0_7And1;
  private string _LBElBetween0_7And1;
  private bool? _EcBetween0_7And2;
  private string _LBEcBetween0_7And2;
  private bool? _ScreeningCriteriaFigure5_8;
  private string _LBScreeningCriteriaFigure5_8;
  private double? _tsf;
  private string _LBtsf;
  private bool _Level2Passed;
  private string _LongitudinalExtent;
  private string _CircumferentialExtent;
  private string _Level2Conclusion;
  private bool _EnableMFH;
  private double _MFH;
  private string _UMMFH;
  private string _LBMFH;
  private bool _EnableMFHr;
  private double _MFHr;
  private string _UMMFHr;
  private string _LBMFHr;
  private IContainer components;
  private GroupControl grpMFH;
  private TableLayoutPanel tblMFH;
  private GroupControl grpIntermediateResult;
  private TableLayoutPanel tblIntermediateResult;
  private GroupControl grpLevel1Criteria;
  private TableLayoutPanel tblLevel1Criteria;
  private GroupControl grpLevel1Conclusion;
  private TableLayoutPanel tblLevel1Conclusion;
  private GroupControl grpLevel2Conclusion;
  private TableLayoutPanel tblLevel2Conclusion;
  private GroupControl grpLevel2Criteria;
  private TableLayoutPanel tblLevel2Criteria;
  private GroupControl grpIntermediateLevel2Result;
  private TableLayoutPanel tblIntermediateLevel2Result;
  private GroupControl groupControl3;
  private TableLayoutPanel tableLayoutPanel3;
  private MemoEdit txtWarningMessages;
  private PanelControl panelControl1;

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._recordView.ResultView = (IResultBaseView) this;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  private void vwResult_Load(object sender, EventArgs e)
  {
  }

  private void AddIntermediateResultValue(int rowNum, double? value, TableLayoutPanel tablePanel)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = value.HasValue ? Helpers.ParseObjectToString((object) value) : "N/A";
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    if (value.HasValue)
      textEdit2.AllowOnlyN4();
    tablePanel.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddIntermediateResultValue(int rowNum, string value, TableLayoutPanel tablePanel)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = Helpers.ParseObjectToString((object) value);
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    tablePanel.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddIntermediateResultLabel(int rowNum, string value, TableLayoutPanel tablePanel)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    LabelControl labelControl1 = new LabelControl();
    labelControl1.Text = value;
    labelControl1.Padding = new Padding(0, 1, 0, 0);
    LabelControl labelControl2 = labelControl1;
    tablePanel.Controls.Add((Control) labelControl2, 0, rowNum + 1);
  }

  private void AddIntermediateResultUMLabel(int rowNum, string value, TableLayoutPanel tablePanel)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    LabelControl labelControl1 = new LabelControl();
    labelControl1.Text = value;
    labelControl1.Padding = new Padding(0, 1, 0, 0);
    LabelControl labelControl2 = labelControl1;
    tablePanel.Controls.Add((Control) labelControl2, 2, rowNum + 1);
  }

  private void AddCriteriaLabel(int rowNum, string value, TableLayoutPanel tablePanel)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    LabelControl labelControl1 = new LabelControl();
    labelControl1.Text = value;
    labelControl1.Padding = new Padding(0, 1, 0, 0);
    LabelControl labelControl2 = labelControl1;
    tablePanel.Controls.Add((Control) labelControl2, 0, rowNum + 1);
  }

  private void AddCriteriaValue(int rowNum, bool? value, TableLayoutPanel tablePanel)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    TextEdit textEdit1 = new TextEdit();
    textEdit1.Text = value.HasValue ? Helpers.ParseObjectToString((object) value) : "N/A";
    textEdit1.Margin = new Padding(1);
    TextEdit textEdit2 = textEdit1;
    textEdit2.Properties.ReadOnly = true;
    tablePanel.Controls.Add((Control) textEdit2, 1, rowNum + 1);
  }

  private void AddConclusionLabel(
    int rowNum,
    string value,
    TableLayoutPanel tablePanel,
    bool hasPassed,
    bool IsBold)
  {
    if (tablePanel.RowCount < rowNum + 1)
      tablePanel.RowCount = rowNum + 1;
    LabelControl labelControl1 = new LabelControl();
    labelControl1.Text = value;
    labelControl1.Padding = new Padding(0, 1, 0, 0);
    LabelControl labelControl2 = labelControl1;
    if (IsBold)
    {
      labelControl2.Font = new Font(Control.DefaultFont, FontStyle.Bold);
      labelControl2.ForeColor = hasPassed ? Color.Green : Color.Red;
    }
    tablePanel.Controls.Add((Control) labelControl2, 0, rowNum + 1);
  }

  private void AddPassed(bool value, TableLayoutPanel tablePanel)
  {
    tablePanel.BackColor = value ? Color.Green : Color.Red;
  }

  public bool Calculate()
  {
    this.tblIntermediateResult.Controls.Clear();
    this.tblLevel1Conclusion.Controls.Clear();
    this.tblLevel1Criteria.Controls.Clear();
    this.tblMFH.Controls.Clear();
    this.tblIntermediateLevel2Result.Controls.Clear();
    this.tblLevel2Criteria.Controls.Clear();
    this.tblLevel2Conclusion.Controls.Clear();
    this.tblIntermediateResult.Visible = false;
    this.tblLevel1Conclusion.Visible = false;
    this.tblLevel1Criteria.Visible = false;
    this.tblMFH.Visible = false;
    this.tblIntermediateLevel2Result.Visible = false;
    this.tblLevel2Criteria.Visible = false;
    this.tblLevel2Conclusion.Visible = false;
    bool flag = this._presenter.Calculate();
    this.tblIntermediateResult.Visible = true;
    this.tblLevel1Conclusion.Visible = true;
    this.tblLevel1Criteria.Visible = true;
    this.tblMFH.Visible = true;
    this.tblIntermediateLevel2Result.Visible = true;
    this.tblLevel2Criteria.Visible = true;
    this.tblLevel2Conclusion.Visible = true;
    return flag;
  }

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public bool ShowLevel2
  {
    get => this._showLevel2;
    set
    {
      this._showLevel2 = value;
      this.grpLevel2Conclusion.Visible = value;
      this.grpLevel2Criteria.Visible = value;
    }
  }

  public bool ShowLevel2Intermediate
  {
    get => this.grpIntermediateLevel2Result.Visible;
    set => this.grpIntermediateLevel2Result.Visible = value;
  }

  public bool EnableIntermediateResult
  {
    get => this._EnableIntermediateResult;
    set
    {
      this._EnableIntermediateResult = value;
      this.grpIntermediateResult.Visible = value;
    }
  }

  public bool EnableLevel1Criteria
  {
    get => this._EnableLevel1Criteria;
    set
    {
      this._EnableLevel1Criteria = value;
      this.grpLevel1Criteria.Visible = value;
    }
  }

  public bool EnableLevel1Conclusion
  {
    get => this._EnableLevel1Conclusion;
    set
    {
      this._EnableLevel1Conclusion = value;
      this.grpLevel1Conclusion.Visible = value;
    }
  }

  public double? tc
  {
    get => this._Tc;
    set
    {
      this._Tc = value;
      this.AddIntermediateResultValue(1, value, this.tblIntermediateResult);
    }
  }

  public string UMtc
  {
    get => this._UMtc;
    set
    {
      this._UMtc = value;
      this.AddIntermediateResultUMLabel(1, value, this.tblIntermediateResult);
    }
  }

  public string LBtc
  {
    get => this._LBtc;
    set
    {
      this._LBtc = value;
      this.AddIntermediateResultLabel(1, value, this.tblIntermediateResult);
    }
  }

  public double? Bd
  {
    get => this._Bd;
    set
    {
      this._Bd = value;
      this.AddIntermediateResultValue(2, value, this.tblIntermediateResult);
    }
  }

  public string UMBd
  {
    get => this._UMBd;
    set
    {
      this._UMBd = value;
      this.AddIntermediateResultUMLabel(2, value, this.tblIntermediateResult);
    }
  }

  public string LBBd
  {
    get => this._LBBd;
    set
    {
      this._LBBd = value;
      this.AddIntermediateResultLabel(2, value, this.tblIntermediateResult);
    }
  }

  public bool BdMax_s_cLessEqual50mm
  {
    get => this._BdMax_s_cLessEqual50mm;
    set
    {
      this._BdMax_s_cLessEqual50mm = value;
      this.AddCriteriaValue(1, new bool?(value), this.tblLevel1Criteria);
    }
  }

  public string LBBdMax_s_cLessEqual50mm
  {
    get => this._LBBdMax_s_cLessEqual50mm;
    set
    {
      this._LBBdMax_s_cLessEqual50mm = value;
      this.AddCriteriaLabel(1, value, this.tblLevel1Criteria);
    }
  }

  public bool sLessEqualRootInDtc
  {
    get => this._SLessEqualRootInDtc;
    set
    {
      this._SLessEqualRootInDtc = value;
      this.AddCriteriaValue(2, new bool?(value), this.tblLevel1Criteria);
    }
  }

  public string LBsLessEqualRootInDtc
  {
    get => this._LBsLessEqualRootInDtc;
    set
    {
      this._LBsLessEqualRootInDtc = value;
      this.AddCriteriaLabel(2, value, this.tblLevel1Criteria);
    }
  }

  public bool cLessEquLRootDtc
  {
    get => this._CLessEquLRootDtc;
    set
    {
      this._CLessEquLRootDtc = value;
      this.AddCriteriaValue(3, new bool?(value), this.tblLevel1Criteria);
    }
  }

  public string LBcLessEquLRootDtc
  {
    get => this._LBcLessEquLRootDtc;
    set
    {
      this._LBcLessEquLRootDtc = value;
      this.AddCriteriaLabel(3, value, this.tblLevel1Criteria);
    }
  }

  public bool tmmMoreEqual0_5tc
  {
    get => this._TmmMoreEqual0_5tc;
    set
    {
      this._TmmMoreEqual0_5tc = value;
      this.AddCriteriaValue(4, new bool?(value), this.tblLevel1Criteria);
    }
  }

  public string LBtmmMoreEqual0_5tc
  {
    get => this._LBtmmMoreEqual0_5tc;
    set
    {
      this._LBtmmMoreEqual0_5tc = value;
      this.AddCriteriaLabel(4, value, this.tblLevel1Criteria);
    }
  }

  public bool BpLessEqual0_1min_s_c
  {
    get => this._BpLessEqual0_1min_s_c;
    set
    {
      this._BpLessEqual0_1min_s_c = value;
      this.AddCriteriaValue(5, new bool?(value), this.tblLevel1Criteria);
    }
  }

  public string LBBpLessEqual0_1min_s_c
  {
    get => this._LBBpLessEqual0_1min_s_c;
    set
    {
      this._LBBpLessEqual0_1min_s_c = value;
      this.AddCriteriaLabel(5, value, this.tblLevel1Criteria);
    }
  }

  public bool LwMoreMax_2tc_25
  {
    get => this._LwMoreMax_2tc_25;
    set
    {
      this._LwMoreMax_2tc_25 = value;
      this.AddCriteriaValue(6, new bool?(value), this.tblLevel1Criteria);
    }
  }

  public string LBLwMoreMax_2tc_25
  {
    get => this._LBLwMoreMax_2tc_25;
    set
    {
      this._LBLwMoreMax_2tc_25 = value;
      this.AddCriteriaLabel(6, value, this.tblLevel1Criteria);
    }
  }

  public bool LmsdMoreEqual1_8RootDtc
  {
    get => this._LmsdMoreEqual1_8RootDtc;
    set
    {
      this._LmsdMoreEqual1_8RootDtc = value;
      this.AddCriteriaValue(7, new bool?(value), this.tblLevel1Criteria);
    }
  }

  public string LBLmsdMoreEqual1_8RootDtc
  {
    get => this._LBLmsdMoreEqual1_8RootDtc;
    set
    {
      this._LBLmsdMoreEqual1_8RootDtc = value;
      this.AddCriteriaLabel(7, value, this.tblLevel1Criteria);
    }
  }

  public bool Level1Passed => this._Level1Passed;

  public string Level1Conclusion => this._Level1Conclusion;

  public void SetLevel1Conclusion(string value, bool level1Passed)
  {
    this._Level1Passed = level1Passed;
    this._Level1Conclusion = value;
    this.AddConclusionLabel(1, value, this.tblLevel1Conclusion, this._Level1Passed, true);
  }

  public string ResultMessages
  {
    get => this._ResultMessages;
    set
    {
      this._ResultMessages = value;
      this.txtWarningMessages.Text = value;
    }
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public bool CrownCrackPart5 { get; set; }

  public string DamageType
  {
    get => this._DamageType;
    set
    {
      this._DamageType = value;
      this.AddIntermediateResultValue(1, value, this.tblIntermediateLevel2Result);
    }
  }

  public string LBDamageType
  {
    get => this._LBDamageType;
    set
    {
      this._LBDamageType = value;
      this.AddIntermediateResultLabel(1, value, this.tblIntermediateLevel2Result);
    }
  }

  public double dHIC
  {
    get => this._dHIC;
    set
    {
      this._dHIC = value;
      this.AddIntermediateResultValue(2, new double?(value), this.tblIntermediateLevel2Result);
    }
  }

  public string LBdHIC
  {
    get => this._LBdHIC;
    set
    {
      this._LBdHIC = value;
      this.AddIntermediateResultLabel(2, value, this.tblIntermediateLevel2Result);
    }
  }

  public string UMdHIC
  {
    get => this._UMdHIC;
    set
    {
      this._UMdHIC = value;
      this.AddIntermediateResultUMLabel(2, value, this.tblIntermediateLevel2Result);
    }
  }

  public double RSF
  {
    get => this._RSF;
    set
    {
      this._RSF = value;
      this.AddIntermediateResultValue(3, new double?(value), this.tblIntermediateLevel2Result);
    }
  }

  public string LBRSF
  {
    get => this._LBRSF;
    set
    {
      this._LBRSF = value;
      this.AddIntermediateResultLabel(3, value, this.tblIntermediateLevel2Result);
    }
  }

  public double RT
  {
    get => this._RT;
    set
    {
      this._RT = value;
      this.AddIntermediateResultValue(2, new double?(value), this.tblIntermediateLevel2Result);
    }
  }

  public string LBRT
  {
    get => this._LBRT;
    set
    {
      this._LBRT = value;
      this.AddIntermediateResultLabel(2, value, this.tblIntermediateLevel2Result);
    }
  }

  public double LambdaC
  {
    get => this._LambdaC;
    set
    {
      this._LambdaC = value;
      this.AddIntermediateResultValue(4, new double?(value), this.tblIntermediateLevel2Result);
    }
  }

  public string LBLambdaC
  {
    get => this._LBLambdaC;
    set
    {
      this._LBLambdaC = value;
      this.AddIntermediateResultLabel(4, value, this.tblIntermediateLevel2Result);
    }
  }

  public bool? RSFGreaterRSFa
  {
    get => this._RSFGreaterRSFa;
    set
    {
      this._RSFGreaterRSFa = value;
      this.AddCriteriaValue(3, value, this.tblLevel2Criteria);
    }
  }

  public string LBRSFGreaterRSFa
  {
    get => this._LBRSFGreaterRSFa;
    set
    {
      this._LBRSFGreaterRSFa = value;
      this.AddCriteriaLabel(3, value, this.tblLevel2Criteria);
    }
  }

  public bool? LambdaCLessThan9
  {
    get => this._LambdaCLessThan9;
    set
    {
      this._LambdaCLessThan9 = value;
      this.AddCriteriaValue(4, value, this.tblLevel2Criteria);
    }
  }

  public string LBLambdaCLessThan9
  {
    get => this._LBLambdaCLessThan9;
    set
    {
      this._LBLambdaCLessThan9 = value;
      this.AddCriteriaLabel(4, value, this.tblLevel2Criteria);
    }
  }

  public bool? DOverTcGreaterThan20
  {
    get => this._DOverTcGreaterThan20;
    set
    {
      this._DOverTcGreaterThan20 = value;
      this.AddCriteriaValue(5, value, this.tblLevel2Criteria);
    }
  }

  public string LBDOverTcGreaterThan20
  {
    get => this._LBDOverTcGreaterThan20;
    set
    {
      this._LBDOverTcGreaterThan20 = value;
      this.AddCriteriaLabel(5, value, this.tblLevel2Criteria);
    }
  }

  public bool? RSFBetween0_7And1
  {
    get => this._RSFBetween0_7And1;
    set
    {
      this._RSFBetween0_7And1 = value;
      this.AddCriteriaValue(6, value, this.tblLevel2Criteria);
    }
  }

  public string LBRSFBetween0_7And1
  {
    get => this._LBRSFBetween0_7And1;
    set
    {
      this._LBRSFBetween0_7And1 = value;
      this.AddCriteriaLabel(6, value, this.tblLevel2Criteria);
    }
  }

  public bool? ElBetween0_7And1
  {
    get => this._ElBetween0_7And1;
    set
    {
      this._ElBetween0_7And1 = value;
      this.AddCriteriaValue(7, value, this.tblLevel2Criteria);
    }
  }

  public string LBElBetween0_7And1
  {
    get => this._LBElBetween0_7And1;
    set
    {
      this._LBElBetween0_7And1 = value;
      this.AddCriteriaLabel(7, value, this.tblLevel2Criteria);
    }
  }

  public bool? EcBetween0_7And2
  {
    get => this._EcBetween0_7And2;
    set
    {
      this._EcBetween0_7And2 = value;
      this.AddCriteriaValue(8, value, this.tblLevel2Criteria);
    }
  }

  public string LBEcBetween0_7And2
  {
    get => this._LBEcBetween0_7And2;
    set
    {
      this._LBEcBetween0_7And2 = value;
      this.AddCriteriaLabel(8, value, this.tblLevel2Criteria);
    }
  }

  public bool? ScreeningCriteriaFigure5_8
  {
    get => this._ScreeningCriteriaFigure5_8;
    set
    {
      this._ScreeningCriteriaFigure5_8 = value;
      this.AddCriteriaValue(9, value, this.tblLevel2Criteria);
    }
  }

  public string LBScreeningCriteriaFigure5_8
  {
    get => this._LBScreeningCriteriaFigure5_8;
    set
    {
      this._LBScreeningCriteriaFigure5_8 = value;
      this.AddCriteriaLabel(9, value, this.tblLevel2Criteria);
    }
  }

  public bool LmsdMoreEqual1_8RootDtcl2
  {
    get => this._LmsdMoreEqual1_8RootDtc;
    set
    {
      this._LmsdMoreEqual1_8RootDtc = value;
      this.AddCriteriaValue(10, new bool?(value), this.tblLevel2Criteria);
    }
  }

  public string LBLmsdMoreEqual1_8RootDtcl2
  {
    get => this._LBLmsdMoreEqual1_8RootDtc;
    set
    {
      this._LBLmsdMoreEqual1_8RootDtc = value;
      this.AddCriteriaLabel(10, value, this.tblLevel2Criteria);
    }
  }

  public double? TSF
  {
    get => this._tsf;
    set
    {
      this._tsf = value;
      this.AddIntermediateResultValue(5, value, this.tblIntermediateLevel2Result);
    }
  }

  public string LBTSF
  {
    get => this._LBtsf;
    set
    {
      this._LBtsf = value;
      this.AddIntermediateResultLabel(5, value, this.tblIntermediateLevel2Result);
    }
  }

  public bool LwMoreEqualMax_2tc_25mml2
  {
    get => this._LwMoreEqualMax_2tc_25mml2;
    set
    {
      this._LwMoreEqualMax_2tc_25mml2 = value;
      this.AddCriteriaValue(11, new bool?(value), this.tblLevel2Criteria);
    }
  }

  public string LBLwMoreEqualMax_2tc_25mml2
  {
    get => this._LBLwMoreEqualMax_2tc_25mml2;
    set
    {
      this._LBLwMoreEqualMax_2tc_25mml2 = value;
      this.AddCriteriaLabel(11, value, this.tblLevel2Criteria);
    }
  }

  public bool LwMoreEqualMax_2tc_25mml1
  {
    get => this._LwMoreEqualMax_2tc_25mml2;
    set
    {
      this._LwMoreEqualMax_2tc_25mml2 = value;
      this.AddCriteriaValue(11, new bool?(value), this.tblLevel2Criteria);
    }
  }

  public string LBLwMoreEqualMax_2tc_25mml1
  {
    get => this._LBLwMoreEqualMax_2tc_25mml2;
    set
    {
      this._LBLwMoreEqualMax_2tc_25mml2 = value;
      this.AddCriteriaLabel(11, value, this.tblLevel2Criteria);
    }
  }

  public bool RtGreater0_2
  {
    get => this._RtGreater0_2;
    set
    {
      this._RtGreater0_2 = value;
      this.AddCriteriaValue(13, new bool?(value), this.tblLevel2Criteria);
    }
  }

  public string LBRtGreater0_2
  {
    get => this._LBRtGreater0_2;
    set
    {
      this._LBRtGreater0_2 = value;
      this.AddCriteriaLabel(13, value, this.tblLevel2Criteria);
    }
  }

  public bool tmmMoreEqual0_5tcl2
  {
    get => this._TmmMoreEqual0_5tcl2;
    set
    {
      this._TmmMoreEqual0_5tcl2 = value;
      this.AddCriteriaValue(12, new bool?(value), this.tblLevel2Criteria);
    }
  }

  public string LBtmmMoreEqual0_5tcl2
  {
    get => this._LBtmmMoreEqual0_5tcl2;
    set
    {
      this._LBtmmMoreEqual0_5tcl2 = value;
      this.AddCriteriaLabel(12, value, this.tblLevel2Criteria);
    }
  }

  public bool Level2Passed => this._Level2Passed;

  public string LongitudinalExtent => this._LongitudinalExtent;

  public string CircumferentialExtent => this._CircumferentialExtent;

  public string Level2Conclusion => this._Level2Conclusion;

  public void SetLevel2Conclusion(
    string longExtent,
    string circExtent,
    string finalConclusion,
    bool level2Passed)
  {
    this._Level2Passed = level2Passed;
    this._LongitudinalExtent = longExtent;
    this._CircumferentialExtent = circExtent;
    this._Level2Conclusion = finalConclusion;
    if (!string.IsNullOrWhiteSpace(longExtent))
      this.AddConclusionLabel(1, longExtent, this.tblLevel2Conclusion, level2Passed, false);
    if (!string.IsNullOrWhiteSpace(circExtent))
      this.AddConclusionLabel(2, circExtent, this.tblLevel2Conclusion, level2Passed, false);
    this.AddConclusionLabel(3, finalConclusion, this.tblLevel2Conclusion, level2Passed, true);
  }

  public bool EnableMFH
  {
    get => this._EnableMFH;
    set
    {
      this._EnableMFH = value;
      this.grpMFH.Visible = value;
    }
  }

  public double MFH
  {
    get => this._MFH;
    set
    {
      this._MFH = value;
      this.AddIntermediateResultValue(1, new double?(value), this.tblMFH);
    }
  }

  public string UMMFH
  {
    get => this._UMMFH;
    set
    {
      this._UMMFH = value;
      this.AddIntermediateResultUMLabel(1, value, this.tblMFH);
    }
  }

  public string LBMFH
  {
    get => this._LBMFH;
    set
    {
      this._LBMFH = value;
      this.AddIntermediateResultLabel(1, value, this.tblMFH);
    }
  }

  public bool EnableMFHr
  {
    get => this._EnableMFH;
    set => this._EnableMFH = value;
  }

  public double MFHr
  {
    get => this._MFHr;
    set
    {
      this._MFHr = value;
      this.AddIntermediateResultValue(2, new double?(value), this.tblMFH);
    }
  }

  public string UMMFHr
  {
    get => this._UMMFHr;
    set
    {
      this._UMMFHr = value;
      this.AddIntermediateResultUMLabel(2, value, this.tblMFH);
    }
  }

  public string LBMFHr
  {
    get => this._LBMFHr;
    set
    {
      this._LBMFHr = value;
      this.AddIntermediateResultLabel(2, value, this.tblMFH);
    }
  }

  public string CodeEdition => "";

  public string Title => "Assessment to API 579 Part 7. Hydrogen Blisters";

  public string Introduction
  {
    get
    {
      return "Hydrogen blistering is caused by hydrogen accumulation at imperfections in the steel, such as laminations or inclusions. Atomic hydrogen generated by the environment or process fluids (eg. wet H2S or hydrofluoric acid) combines at imperfections to form hydrogen molecules that are too large to diffuse through the steel. Hydrogen blistering is characterised by physical bulging of the surface(s) of equipment. Sometimes cracks can extend from the periphery of a blister and can propagate in a through-wall direction, particularly if the blister is located near a weld.\r\n\r\nThe assessment for Blistering is conducted to the Level 1 and Level 2 procedures in API 579 'Fitness-For-Service' Section 7. \r\n";
    }
  }

  public string CommentsAndAssumptions
  {
    get
    {
      return "The procedure for assessment of blisters in API 579 Part 7 assumes that: \r\n1. The original design criteria were in accordance with a recognised code or standard. \r\n2. The operating temperature is less than 204.4°C (400°F) for carbon steel or low alloy steels, or is below \r\n    the applicable design curve in API 941, whichever is greater. Blisters associated with high temperature \r\n    hydrogen attack are specifically excluded from this assessment. \r\n3. The material is considered to be ductile and is not subject to embrittlement during operation due to \r\n    temperature or the process environment. \r\n4. The component is not in cyclic service. \r\n5. There is physical bulging, which is discovered by visual or UT examination. If physical bulging is not \r\n    present, the defect should be evaluated as a lamination. \r\n6. Level 1 covers Type A Components (Part 4, paragraph 4.2.5) subject to internal pressure. (i.e. supplemental \r\n    loads are assumed to be negligible).\r\n7. Level 2 covers Type A or B components (Part 4, paragraph 4.2.5) subject to internal pressure, external \r\n    pressure, supplemental loads or any combination of them.\r\n8. The assessment procedures in this section are not applicable to HIC or SOHIC damage. \r\n9. There are no periphery cracks directed towards the inside or outside surface of the component as shown in \r\n    API 579 figure 7.5 .\r\n";
    }
  }

  public string References
  {
    get
    {
      return "API 579 'Fitness-for-Service', Second Edition, The American Society of Mechanical Engineers. Part 7: Hydrogen Blisters and Hydrogen Damage Associated with HIC and SOHIC.";
    }
  }

  public string Limitations => string.Empty;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.grpMFH = new GroupControl();
    this.tblMFH = new TableLayoutPanel();
    this.grpIntermediateResult = new GroupControl();
    this.tblIntermediateResult = new TableLayoutPanel();
    this.grpLevel1Criteria = new GroupControl();
    this.tblLevel1Criteria = new TableLayoutPanel();
    this.grpLevel1Conclusion = new GroupControl();
    this.tblLevel1Conclusion = new TableLayoutPanel();
    this.grpLevel2Conclusion = new GroupControl();
    this.tblLevel2Conclusion = new TableLayoutPanel();
    this.grpLevel2Criteria = new GroupControl();
    this.tblLevel2Criteria = new TableLayoutPanel();
    this.grpIntermediateLevel2Result = new GroupControl();
    this.tblIntermediateLevel2Result = new TableLayoutPanel();
    this.groupControl3 = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.txtWarningMessages = new MemoEdit();
    this.panelControl1 = new PanelControl();
    this.grpMFH.BeginInit();
    this.grpMFH.SuspendLayout();
    this.grpIntermediateResult.BeginInit();
    this.grpIntermediateResult.SuspendLayout();
    this.grpLevel1Criteria.BeginInit();
    this.grpLevel1Criteria.SuspendLayout();
    this.grpLevel1Conclusion.BeginInit();
    this.grpLevel1Conclusion.SuspendLayout();
    this.grpLevel2Conclusion.BeginInit();
    this.grpLevel2Conclusion.SuspendLayout();
    this.grpLevel2Criteria.BeginInit();
    this.grpLevel2Criteria.SuspendLayout();
    this.grpIntermediateLevel2Result.BeginInit();
    this.grpIntermediateLevel2Result.SuspendLayout();
    this.groupControl3.BeginInit();
    this.groupControl3.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.txtWarningMessages.Properties.BeginInit();
    this.panelControl1.BeginInit();
    this.SuspendLayout();
    this.grpMFH.AutoSize = true;
    this.grpMFH.Controls.Add((Control) this.tblMFH);
    this.grpMFH.Dock = DockStyle.Top;
    this.grpMFH.Location = new Point(0, 0);
    this.grpMFH.Name = "grpMFH";
    this.grpMFH.Size = new Size(587, 43);
    this.grpMFH.TabIndex = 16 /*0x10*/;
    this.grpMFH.Text = "Maximum Fill Height";
    this.tblMFH.AutoSize = true;
    this.tblMFH.ColumnCount = 3;
    this.tblMFH.ColumnStyles.Add(new ColumnStyle());
    this.tblMFH.ColumnStyles.Add(new ColumnStyle());
    this.tblMFH.ColumnStyles.Add(new ColumnStyle());
    this.tblMFH.Dock = DockStyle.Fill;
    this.tblMFH.Location = new Point(2, 21);
    this.tblMFH.Name = "tblMFH";
    this.tblMFH.Padding = new Padding(10);
    this.tblMFH.RowCount = 1;
    this.tblMFH.RowStyles.Add(new RowStyle());
    this.tblMFH.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblMFH.Size = new Size(583, 20);
    this.tblMFH.TabIndex = 1;
    this.grpIntermediateResult.AutoSize = true;
    this.grpIntermediateResult.Controls.Add((Control) this.tblIntermediateResult);
    this.grpIntermediateResult.Dock = DockStyle.Top;
    this.grpIntermediateResult.Location = new Point(0, 43);
    this.grpIntermediateResult.Name = "grpIntermediateResult";
    this.grpIntermediateResult.Size = new Size(587, 43);
    this.grpIntermediateResult.TabIndex = 17;
    this.grpIntermediateResult.Text = "Level 1 Intermediate Assessment Result";
    this.tblIntermediateResult.AutoSize = true;
    this.tblIntermediateResult.ColumnCount = 3;
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateResult.Dock = DockStyle.Fill;
    this.tblIntermediateResult.Location = new Point(2, 21);
    this.tblIntermediateResult.Name = "tblIntermediateResult";
    this.tblIntermediateResult.Padding = new Padding(10);
    this.tblIntermediateResult.RowCount = 1;
    this.tblIntermediateResult.RowStyles.Add(new RowStyle());
    this.tblIntermediateResult.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblIntermediateResult.Size = new Size(583, 20);
    this.tblIntermediateResult.TabIndex = 0;
    this.grpLevel1Criteria.AutoSize = true;
    this.grpLevel1Criteria.Controls.Add((Control) this.tblLevel1Criteria);
    this.grpLevel1Criteria.Dock = DockStyle.Top;
    this.grpLevel1Criteria.Location = new Point(0, 86);
    this.grpLevel1Criteria.Name = "grpLevel1Criteria";
    this.grpLevel1Criteria.Size = new Size(587, 43);
    this.grpLevel1Criteria.TabIndex = 18;
    this.grpLevel1Criteria.Text = "Level 1 Assessment Criteria";
    this.tblLevel1Criteria.AutoSize = true;
    this.tblLevel1Criteria.ColumnCount = 3;
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Criteria.Dock = DockStyle.Fill;
    this.tblLevel1Criteria.Location = new Point(2, 21);
    this.tblLevel1Criteria.Name = "tblLevel1Criteria";
    this.tblLevel1Criteria.Padding = new Padding(10);
    this.tblLevel1Criteria.RowCount = 1;
    this.tblLevel1Criteria.RowStyles.Add(new RowStyle());
    this.tblLevel1Criteria.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblLevel1Criteria.Size = new Size(583, 20);
    this.tblLevel1Criteria.TabIndex = 1;
    this.grpLevel1Conclusion.AutoSize = true;
    this.grpLevel1Conclusion.Controls.Add((Control) this.tblLevel1Conclusion);
    this.grpLevel1Conclusion.Dock = DockStyle.Top;
    this.grpLevel1Conclusion.Location = new Point(0, 129);
    this.grpLevel1Conclusion.Name = "grpLevel1Conclusion";
    this.grpLevel1Conclusion.Size = new Size(587, 43);
    this.grpLevel1Conclusion.TabIndex = 19;
    this.grpLevel1Conclusion.Text = "Level 1 Assessment Conclusion";
    this.tblLevel1Conclusion.AutoSize = true;
    this.tblLevel1Conclusion.ColumnCount = 3;
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel1Conclusion.Dock = DockStyle.Fill;
    this.tblLevel1Conclusion.Location = new Point(2, 21);
    this.tblLevel1Conclusion.Name = "tblLevel1Conclusion";
    this.tblLevel1Conclusion.Padding = new Padding(10);
    this.tblLevel1Conclusion.RowCount = 1;
    this.tblLevel1Conclusion.RowStyles.Add(new RowStyle());
    this.tblLevel1Conclusion.RowStyles.Add(new RowStyle(SizeType.Absolute, 1f));
    this.tblLevel1Conclusion.Size = new Size(583, 20);
    this.tblLevel1Conclusion.TabIndex = 1;
    this.grpLevel2Conclusion.AutoSize = true;
    this.grpLevel2Conclusion.Controls.Add((Control) this.tblLevel2Conclusion);
    this.grpLevel2Conclusion.Dock = DockStyle.Top;
    this.grpLevel2Conclusion.Location = new Point(0, 258);
    this.grpLevel2Conclusion.Name = "grpLevel2Conclusion";
    this.grpLevel2Conclusion.Size = new Size(587, 43);
    this.grpLevel2Conclusion.TabIndex = 27;
    this.grpLevel2Conclusion.Text = "Level 2 Assessment Conclusion";
    this.tblLevel2Conclusion.AutoSize = true;
    this.tblLevel2Conclusion.ColumnCount = 3;
    this.tblLevel2Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Conclusion.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Conclusion.Dock = DockStyle.Fill;
    this.tblLevel2Conclusion.Location = new Point(2, 21);
    this.tblLevel2Conclusion.Name = "tblLevel2Conclusion";
    this.tblLevel2Conclusion.Padding = new Padding(10);
    this.tblLevel2Conclusion.RowCount = 1;
    this.tblLevel2Conclusion.RowStyles.Add(new RowStyle());
    this.tblLevel2Conclusion.Size = new Size(583, 20);
    this.tblLevel2Conclusion.TabIndex = 1;
    this.grpLevel2Criteria.AutoSize = true;
    this.grpLevel2Criteria.Controls.Add((Control) this.tblLevel2Criteria);
    this.grpLevel2Criteria.Dock = DockStyle.Top;
    this.grpLevel2Criteria.Location = new Point(0, 215);
    this.grpLevel2Criteria.Name = "grpLevel2Criteria";
    this.grpLevel2Criteria.Size = new Size(587, 43);
    this.grpLevel2Criteria.TabIndex = 26;
    this.grpLevel2Criteria.Text = "Level 2 Assessment Criteria";
    this.tblLevel2Criteria.AutoSize = true;
    this.tblLevel2Criteria.ColumnCount = 3;
    this.tblLevel2Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Criteria.ColumnStyles.Add(new ColumnStyle());
    this.tblLevel2Criteria.Dock = DockStyle.Fill;
    this.tblLevel2Criteria.Location = new Point(2, 21);
    this.tblLevel2Criteria.Name = "tblLevel2Criteria";
    this.tblLevel2Criteria.Padding = new Padding(10);
    this.tblLevel2Criteria.RowCount = 1;
    this.tblLevel2Criteria.RowStyles.Add(new RowStyle());
    this.tblLevel2Criteria.Size = new Size(583, 20);
    this.tblLevel2Criteria.TabIndex = 1;
    this.grpIntermediateLevel2Result.AutoSize = true;
    this.grpIntermediateLevel2Result.Controls.Add((Control) this.tblIntermediateLevel2Result);
    this.grpIntermediateLevel2Result.Dock = DockStyle.Top;
    this.grpIntermediateLevel2Result.Location = new Point(0, 172);
    this.grpIntermediateLevel2Result.Name = "grpIntermediateLevel2Result";
    this.grpIntermediateLevel2Result.Size = new Size(587, 43);
    this.grpIntermediateLevel2Result.TabIndex = 25;
    this.grpIntermediateLevel2Result.Text = "Level 2 Intermediate Assessment Result";
    this.tblIntermediateLevel2Result.AutoSize = true;
    this.tblIntermediateLevel2Result.ColumnCount = 3;
    this.tblIntermediateLevel2Result.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateLevel2Result.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateLevel2Result.ColumnStyles.Add(new ColumnStyle());
    this.tblIntermediateLevel2Result.Dock = DockStyle.Fill;
    this.tblIntermediateLevel2Result.Location = new Point(2, 21);
    this.tblIntermediateLevel2Result.Name = "tblIntermediateLevel2Result";
    this.tblIntermediateLevel2Result.Padding = new Padding(10);
    this.tblIntermediateLevel2Result.RowCount = 1;
    this.tblIntermediateLevel2Result.RowStyles.Add(new RowStyle());
    this.tblIntermediateLevel2Result.Size = new Size(583, 20);
    this.tblIntermediateLevel2Result.TabIndex = 0;
    this.groupControl3.AutoSize = true;
    this.groupControl3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.groupControl3.Controls.Add((Control) this.tableLayoutPanel3);
    this.groupControl3.Dock = DockStyle.Top;
    this.groupControl3.Location = new Point(0, 301);
    this.groupControl3.Name = "groupControl3";
    this.groupControl3.Size = new Size(587, 222);
    this.groupControl3.TabIndex = 29;
    this.groupControl3.Text = "Warning Messages";
    this.tableLayoutPanel3.AutoSize = true;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 179f));
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Absolute, 179f));
    this.tableLayoutPanel3.Size = new Size(583, 199);
    this.tableLayoutPanel3.TabIndex = 0;
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.MinimumSize = new Size(10, 10);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(557, 173);
    this.txtWarningMessages.TabIndex = 4;
    this.panelControl1.Dock = DockStyle.Fill;
    this.panelControl1.Location = new Point(0, 523);
    this.panelControl1.Name = "panelControl1";
    this.panelControl1.Size = new Size(587, 6);
    this.panelControl1.TabIndex = 30;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.Controls.Add((Control) this.panelControl1);
    this.Controls.Add((Control) this.groupControl3);
    this.Controls.Add((Control) this.grpLevel2Conclusion);
    this.Controls.Add((Control) this.grpLevel2Criteria);
    this.Controls.Add((Control) this.grpIntermediateLevel2Result);
    this.Controls.Add((Control) this.grpLevel1Conclusion);
    this.Controls.Add((Control) this.grpLevel1Criteria);
    this.Controls.Add((Control) this.grpIntermediateResult);
    this.Controls.Add((Control) this.grpMFH);
    this.Name = nameof (vwResult);
    this.Size = new Size(587, 529);
    this.Load += new EventHandler(this.vwResult_Load);
    this.grpMFH.EndInit();
    this.grpMFH.ResumeLayout(false);
    this.grpMFH.PerformLayout();
    this.grpIntermediateResult.EndInit();
    this.grpIntermediateResult.ResumeLayout(false);
    this.grpIntermediateResult.PerformLayout();
    this.grpLevel1Criteria.EndInit();
    this.grpLevel1Criteria.ResumeLayout(false);
    this.grpLevel1Criteria.PerformLayout();
    this.grpLevel1Conclusion.EndInit();
    this.grpLevel1Conclusion.ResumeLayout(false);
    this.grpLevel1Conclusion.PerformLayout();
    this.grpLevel2Conclusion.EndInit();
    this.grpLevel2Conclusion.ResumeLayout(false);
    this.grpLevel2Conclusion.PerformLayout();
    this.grpLevel2Criteria.EndInit();
    this.grpLevel2Criteria.ResumeLayout(false);
    this.grpLevel2Criteria.PerformLayout();
    this.grpIntermediateLevel2Result.EndInit();
    this.grpIntermediateLevel2Result.ResumeLayout(false);
    this.grpIntermediateLevel2Result.PerformLayout();
    this.groupControl3.EndInit();
    this.groupControl3.ResumeLayout(false);
    this.groupControl3.PerformLayout();
    this.tableLayoutPanel3.ResumeLayout(false);
    this.txtWarningMessages.Properties.EndInit();
    this.panelControl1.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }
}
