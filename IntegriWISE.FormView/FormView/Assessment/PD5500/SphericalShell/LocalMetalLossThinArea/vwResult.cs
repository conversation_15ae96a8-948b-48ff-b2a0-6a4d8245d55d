// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Assessment.PD5500.SphericalShell.LocalMetalLossThinArea.vwResult
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.Common;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment.PD5500.SphericalShell.LocalMetalLossThinArea;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView.Assessment.PD5500.SphericalShell.LocalMetalLossThinArea;

public class vwResult : XtraUserControl, IResultView, IResultBaseView, IView
{
  private IContainer components;
  private LabelControl lbAllowableStress;
  private TextEdit txtLambda;
  private MemoEdit txtWarningMessages;
  private GroupControl grcWarnings;
  private TableLayoutPanel tableLayoutPanel3;
  private LabelControl lbLambda;
  private TableLayoutPanel tblpAssessmentResults;
  private GroupControl grcAssessmentResults;
  private GroupControl grcIntermediateResults;
  private TableLayoutPanel tblpIntermediateResults;
  private TableLayoutPanel tblpTmm;
  private LabelControl umTmm;
  private TextEdit txtTmm;
  private TableLayoutPanel tblpAllowableStress;
  private LabelControl umAllowableStress;
  private TextEdit txtAllowableStress;
  private LabelControl lbTmm;
  private LabelControl lbTrd;
  private LabelControl lbTc;
  private TableLayoutPanel tblpTrd;
  private LabelControl umTrd;
  private TextEdit txtTrd;
  private TableLayoutPanel tblpTc;
  private LabelControl umTc;
  private TextEdit txtTc;
  private LabelControl lbRSF;
  private TextEdit txtRSF;
  private GroupControl grcAssessmentCriteriaL1;
  private GroupControl grcAssessmentConclusionL1;
  private TableLayoutPanel tableLayoutPanel22;
  private LabelControl lbConclusionLevel1;
  private GroupControl grcAssessmentCriteriaL2;
  private GroupControl grcAssessmentConclusionL2;
  private TableLayoutPanel tableLayoutPanel27;
  private LabelControl lbConditionLongitudinalExtentL2;
  private TableLayoutPanel tblpAssessmentCriteriaL1;
  private LabelControl lbScreeningCriteriaFigure5_6;
  private TextEdit txtScreeningCriteriaFigure5_6;
  private TextEdit txtRSFGreaterRSFa;
  private LabelControl lbRSFGreaterRSFa;
  private TableLayoutPanel tblpAssessmentCriteriaL2;
  private LabelControl lbmin_RSFiGreaterThanOrEqualToRSFa;
  private TableLayoutPanel tableLayoutPanel25;
  private TextEdit txtmin_RSFiGreaterThanOrEqualToRSFa;
  private GroupControl grcInspectionDataSummary;
  private TableLayoutPanel tableLayoutPanel1;
  private TableLayoutPanel tblpLm;
  private LabelControl umLm;
  private TextEdit txtLm;
  private LabelControl lbLm;
  private LabelControl umLc;
  private TextEdit txtLc;
  private LabelControl lbLc;
  private LabelControl lbS;
  private LabelControl lbC;
  private TableLayoutPanel tblpS;
  private LabelControl umS;
  private TextEdit txtS;
  private TableLayoutPanel tblpC;
  private LabelControl umC;
  private TextEdit txtC;
  private LabelControl lbConditionCircumferentialExtentL1;
  private LabelControl lbConditionLongitudinalExtentL1;
  private LabelControl lbConclusionLevel2;
  private LabelControl lbConditionCircumferentialExtentL2;
  private GroupControl grcAssessmentCriteria;
  private TableLayoutPanel tableLayoutPanel4;
  private LabelControl lbRtGreater0_2;
  private TextEdit txtRtGreater0_2;
  private TextEdit txtTmmMinusFCAGreater2_5;
  private LabelControl lbTmmMinusFCAGreater2_5;
  private TextEdit txtLmsdGreater1_8DtcPower0_5;
  private LabelControl lbLmsdGreater1_8DtcPower0_5;
  private GroupControl grcAssessmentResultsL2;
  private TableLayoutPanel tableLayoutPanel7;
  private TextEdit txtminRSFi;
  private LabelControl lbminRSFi;
  private GroupControl grcMAWP;
  private TableLayoutPanel tbpMawp;
  private TableLayoutPanel tableLayoutPanel11;
  private LabelControl umMAWPValue;
  private TextEdit txtMAWPValue;
  private LabelControl lbMAWPValue;
  private LabelControl lbMAWPrL1;
  private LabelControl lbMAWPrL2;
  private TableLayoutPanel tblMAWPrL1;
  private LabelControl umMAWPrL1;
  private TextEdit txtMAWPrL1;
  private TableLayoutPanel tblMAWPrL2;
  private LabelControl umMAWPrL2;
  private TextEdit txtMAWPrL2;
  private TableLayoutPanel tblpLc;
  private IRecordView _recordView;
  private ResultPresenter _presenter;
  private bool _level2;
  private bool _level1Passed;
  private bool _level2Passed;
  private int _iConditionLongitudinalExtentLevel1;
  private int _iConditionLongitudinalExtentLevel2;
  private bool _ConditionsCircumferentialExtent;
  private bool _mawp;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.lbAllowableStress = new LabelControl();
    this.txtLambda = new TextEdit();
    this.txtWarningMessages = new MemoEdit();
    this.grcWarnings = new GroupControl();
    this.tableLayoutPanel3 = new TableLayoutPanel();
    this.lbLambda = new LabelControl();
    this.tblpAssessmentResults = new TableLayoutPanel();
    this.lbRSF = new LabelControl();
    this.txtRSF = new TextEdit();
    this.grcAssessmentResults = new GroupControl();
    this.grcIntermediateResults = new GroupControl();
    this.tblpIntermediateResults = new TableLayoutPanel();
    this.tblpAllowableStress = new TableLayoutPanel();
    this.umAllowableStress = new LabelControl();
    this.txtAllowableStress = new TextEdit();
    this.lbTmm = new LabelControl();
    this.lbTrd = new LabelControl();
    this.lbTc = new LabelControl();
    this.tblpTmm = new TableLayoutPanel();
    this.umTmm = new LabelControl();
    this.txtTmm = new TextEdit();
    this.tblpTrd = new TableLayoutPanel();
    this.umTrd = new LabelControl();
    this.txtTrd = new TextEdit();
    this.tblpTc = new TableLayoutPanel();
    this.umTc = new LabelControl();
    this.txtTc = new TextEdit();
    this.grcAssessmentCriteriaL1 = new GroupControl();
    this.tblpAssessmentCriteriaL1 = new TableLayoutPanel();
    this.lbScreeningCriteriaFigure5_6 = new LabelControl();
    this.lbRSFGreaterRSFa = new LabelControl();
    this.txtScreeningCriteriaFigure5_6 = new TextEdit();
    this.txtRSFGreaterRSFa = new TextEdit();
    this.grcAssessmentConclusionL1 = new GroupControl();
    this.tableLayoutPanel22 = new TableLayoutPanel();
    this.lbConditionCircumferentialExtentL1 = new LabelControl();
    this.lbConclusionLevel1 = new LabelControl();
    this.lbConditionLongitudinalExtentL1 = new LabelControl();
    this.grcAssessmentCriteriaL2 = new GroupControl();
    this.tblpAssessmentCriteriaL2 = new TableLayoutPanel();
    this.lbmin_RSFiGreaterThanOrEqualToRSFa = new LabelControl();
    this.tableLayoutPanel25 = new TableLayoutPanel();
    this.txtmin_RSFiGreaterThanOrEqualToRSFa = new TextEdit();
    this.grcAssessmentConclusionL2 = new GroupControl();
    this.tableLayoutPanel27 = new TableLayoutPanel();
    this.lbConclusionLevel2 = new LabelControl();
    this.lbConditionLongitudinalExtentL2 = new LabelControl();
    this.lbConditionCircumferentialExtentL2 = new LabelControl();
    this.grcInspectionDataSummary = new GroupControl();
    this.tableLayoutPanel1 = new TableLayoutPanel();
    this.tblpLm = new TableLayoutPanel();
    this.umLm = new LabelControl();
    this.txtLm = new TextEdit();
    this.lbLm = new LabelControl();
    this.tblpLc = new TableLayoutPanel();
    this.umLc = new LabelControl();
    this.txtLc = new TextEdit();
    this.lbLc = new LabelControl();
    this.lbS = new LabelControl();
    this.lbC = new LabelControl();
    this.tblpS = new TableLayoutPanel();
    this.umS = new LabelControl();
    this.txtS = new TextEdit();
    this.tblpC = new TableLayoutPanel();
    this.umC = new LabelControl();
    this.txtC = new TextEdit();
    this.grcAssessmentCriteria = new GroupControl();
    this.tableLayoutPanel4 = new TableLayoutPanel();
    this.lbRtGreater0_2 = new LabelControl();
    this.lbTmmMinusFCAGreater2_5 = new LabelControl();
    this.lbLmsdGreater1_8DtcPower0_5 = new LabelControl();
    this.txtLmsdGreater1_8DtcPower0_5 = new TextEdit();
    this.txtTmmMinusFCAGreater2_5 = new TextEdit();
    this.txtRtGreater0_2 = new TextEdit();
    this.grcAssessmentResultsL2 = new GroupControl();
    this.tableLayoutPanel7 = new TableLayoutPanel();
    this.txtminRSFi = new TextEdit();
    this.lbminRSFi = new LabelControl();
    this.grcMAWP = new GroupControl();
    this.tbpMawp = new TableLayoutPanel();
    this.tableLayoutPanel11 = new TableLayoutPanel();
    this.umMAWPValue = new LabelControl();
    this.txtMAWPValue = new TextEdit();
    this.lbMAWPValue = new LabelControl();
    this.lbMAWPrL1 = new LabelControl();
    this.lbMAWPrL2 = new LabelControl();
    this.tblMAWPrL1 = new TableLayoutPanel();
    this.umMAWPrL1 = new LabelControl();
    this.txtMAWPrL1 = new TextEdit();
    this.tblMAWPrL2 = new TableLayoutPanel();
    this.umMAWPrL2 = new LabelControl();
    this.txtMAWPrL2 = new TextEdit();
    this.txtLambda.Properties.BeginInit();
    this.txtWarningMessages.Properties.BeginInit();
    this.grcWarnings.BeginInit();
    this.grcWarnings.SuspendLayout();
    this.tableLayoutPanel3.SuspendLayout();
    this.tblpAssessmentResults.SuspendLayout();
    this.txtRSF.Properties.BeginInit();
    this.grcAssessmentResults.BeginInit();
    this.grcAssessmentResults.SuspendLayout();
    this.grcIntermediateResults.BeginInit();
    this.grcIntermediateResults.SuspendLayout();
    this.tblpIntermediateResults.SuspendLayout();
    this.tblpAllowableStress.SuspendLayout();
    this.txtAllowableStress.Properties.BeginInit();
    this.tblpTmm.SuspendLayout();
    this.txtTmm.Properties.BeginInit();
    this.tblpTrd.SuspendLayout();
    this.txtTrd.Properties.BeginInit();
    this.tblpTc.SuspendLayout();
    this.txtTc.Properties.BeginInit();
    this.grcAssessmentCriteriaL1.BeginInit();
    this.grcAssessmentCriteriaL1.SuspendLayout();
    this.tblpAssessmentCriteriaL1.SuspendLayout();
    this.txtScreeningCriteriaFigure5_6.Properties.BeginInit();
    this.txtRSFGreaterRSFa.Properties.BeginInit();
    this.grcAssessmentConclusionL1.BeginInit();
    this.grcAssessmentConclusionL1.SuspendLayout();
    this.tableLayoutPanel22.SuspendLayout();
    this.grcAssessmentCriteriaL2.BeginInit();
    this.grcAssessmentCriteriaL2.SuspendLayout();
    this.tblpAssessmentCriteriaL2.SuspendLayout();
    this.tableLayoutPanel25.SuspendLayout();
    this.txtmin_RSFiGreaterThanOrEqualToRSFa.Properties.BeginInit();
    this.grcAssessmentConclusionL2.BeginInit();
    this.grcAssessmentConclusionL2.SuspendLayout();
    this.tableLayoutPanel27.SuspendLayout();
    this.grcInspectionDataSummary.BeginInit();
    this.grcInspectionDataSummary.SuspendLayout();
    this.tableLayoutPanel1.SuspendLayout();
    this.tblpLm.SuspendLayout();
    this.txtLm.Properties.BeginInit();
    this.tblpLc.SuspendLayout();
    this.txtLc.Properties.BeginInit();
    this.tblpS.SuspendLayout();
    this.txtS.Properties.BeginInit();
    this.tblpC.SuspendLayout();
    this.txtC.Properties.BeginInit();
    this.grcAssessmentCriteria.BeginInit();
    this.grcAssessmentCriteria.SuspendLayout();
    this.tableLayoutPanel4.SuspendLayout();
    this.txtLmsdGreater1_8DtcPower0_5.Properties.BeginInit();
    this.txtTmmMinusFCAGreater2_5.Properties.BeginInit();
    this.txtRtGreater0_2.Properties.BeginInit();
    this.grcAssessmentResultsL2.BeginInit();
    this.grcAssessmentResultsL2.SuspendLayout();
    this.tableLayoutPanel7.SuspendLayout();
    this.txtminRSFi.Properties.BeginInit();
    this.grcMAWP.BeginInit();
    this.grcMAWP.SuspendLayout();
    this.tbpMawp.SuspendLayout();
    this.tableLayoutPanel11.SuspendLayout();
    this.txtMAWPValue.Properties.BeginInit();
    this.tblMAWPrL1.SuspendLayout();
    this.txtMAWPrL1.Properties.BeginInit();
    this.tblMAWPrL2.SuspendLayout();
    this.txtMAWPrL2.Properties.BeginInit();
    this.SuspendLayout();
    this.lbAllowableStress.Location = new Point(13, 13);
    this.lbAllowableStress.Name = "lbAllowableStress";
    this.lbAllowableStress.Size = new Size(75, 13);
    this.lbAllowableStress.TabIndex = 10;
    this.lbAllowableStress.Text = "AllowableStress";
    this.txtLambda.Location = new Point(53, 10);
    this.txtLambda.Margin = new Padding(0);
    this.txtLambda.Name = "txtLambda";
    this.txtLambda.Properties.ReadOnly = true;
    this.txtLambda.Size = new Size(100, 20);
    this.txtLambda.TabIndex = 0;
    this.txtWarningMessages.Dock = DockStyle.Fill;
    this.txtWarningMessages.Location = new Point(13, 13);
    this.txtWarningMessages.Name = "txtWarningMessages";
    this.txtWarningMessages.Properties.ReadOnly = true;
    this.txtWarningMessages.Size = new Size(550, 144 /*0x90*/);
    this.txtWarningMessages.TabIndex = 4;
    this.grcWarnings.Controls.Add((Control) this.tableLayoutPanel3);
    this.grcWarnings.Dock = DockStyle.Fill;
    this.grcWarnings.Location = new Point(0, 957);
    this.grcWarnings.Name = "grcWarnings";
    this.grcWarnings.Size = new Size(580, 193);
    this.grcWarnings.TabIndex = 8;
    this.grcWarnings.Text = "Warning Messages";
    this.tableLayoutPanel3.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel3.ColumnCount = 1;
    this.tableLayoutPanel3.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel3.Controls.Add((Control) this.txtWarningMessages, 0, 0);
    this.tableLayoutPanel3.Dock = DockStyle.Fill;
    this.tableLayoutPanel3.Location = new Point(2, 21);
    this.tableLayoutPanel3.Name = "tableLayoutPanel3";
    this.tableLayoutPanel3.Padding = new Padding(10);
    this.tableLayoutPanel3.RowCount = 1;
    this.tableLayoutPanel3.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel3.Size = new Size(576, 170);
    this.tableLayoutPanel3.TabIndex = 0;
    this.lbLambda.Location = new Point(13, 13);
    this.lbLambda.Name = "lbLambda";
    this.lbLambda.Size = new Size(37, 13);
    this.lbLambda.TabIndex = 16 /*0x10*/;
    this.lbLambda.Text = "Lambda";
    this.tblpAssessmentResults.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpAssessmentResults.ColumnCount = 2;
    this.tblpAssessmentResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentResults.Controls.Add((Control) this.lbRSF, 0, 1);
    this.tblpAssessmentResults.Controls.Add((Control) this.lbLambda, 0, 0);
    this.tblpAssessmentResults.Controls.Add((Control) this.txtRSF, 1, 1);
    this.tblpAssessmentResults.Controls.Add((Control) this.txtLambda, 1, 0);
    this.tblpAssessmentResults.Dock = DockStyle.Fill;
    this.tblpAssessmentResults.Location = new Point(2, 21);
    this.tblpAssessmentResults.Name = "tblpAssessmentResults";
    this.tblpAssessmentResults.Padding = new Padding(10);
    this.tblpAssessmentResults.RowCount = 2;
    this.tblpAssessmentResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentResults.Size = new Size(576, 57);
    this.tblpAssessmentResults.TabIndex = 0;
    this.lbRSF.Location = new Point(13, 35);
    this.lbRSF.Name = "lbRSF";
    this.lbRSF.Size = new Size(19, 13);
    this.lbRSF.TabIndex = 20;
    this.lbRSF.Text = "RSF";
    this.txtRSF.Location = new Point(53, 32 /*0x20*/);
    this.txtRSF.Margin = new Padding(0);
    this.txtRSF.Name = "txtRSF";
    this.txtRSF.Properties.ReadOnly = true;
    this.txtRSF.Size = new Size(100, 20);
    this.txtRSF.TabIndex = 0;
    this.grcAssessmentResults.Controls.Add((Control) this.tblpAssessmentResults);
    this.grcAssessmentResults.Dock = DockStyle.Top;
    this.grcAssessmentResults.Location = new Point(0, 360);
    this.grcAssessmentResults.Name = "grcAssessmentResults";
    this.grcAssessmentResults.Size = new Size(580, 80 /*0x50*/);
    this.grcAssessmentResults.TabIndex = 7;
    this.grcAssessmentResults.Text = "Assessment Results";
    this.grcIntermediateResults.Controls.Add((Control) this.tblpIntermediateResults);
    this.grcIntermediateResults.Dock = DockStyle.Top;
    this.grcIntermediateResults.Location = new Point(0, (int) sbyte.MaxValue);
    this.grcIntermediateResults.Name = "grcIntermediateResults";
    this.grcIntermediateResults.Size = new Size(580, (int) sbyte.MaxValue);
    this.grcIntermediateResults.TabIndex = 6;
    this.grcIntermediateResults.Text = "Intermediate Results";
    this.tblpIntermediateResults.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpIntermediateResults.ColumnCount = 2;
    this.tblpIntermediateResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpIntermediateResults.ColumnStyles.Add(new ColumnStyle());
    this.tblpIntermediateResults.Controls.Add((Control) this.tblpAllowableStress, 1, 0);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbAllowableStress, 0, 0);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbTmm, 0, 1);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbTrd, 0, 2);
    this.tblpIntermediateResults.Controls.Add((Control) this.lbTc, 0, 3);
    this.tblpIntermediateResults.Controls.Add((Control) this.tblpTmm, 1, 1);
    this.tblpIntermediateResults.Controls.Add((Control) this.tblpTrd, 1, 2);
    this.tblpIntermediateResults.Controls.Add((Control) this.tblpTc, 1, 3);
    this.tblpIntermediateResults.Dock = DockStyle.Fill;
    this.tblpIntermediateResults.Location = new Point(2, 21);
    this.tblpIntermediateResults.Name = "tblpIntermediateResults";
    this.tblpIntermediateResults.Padding = new Padding(10);
    this.tblpIntermediateResults.RowCount = 4;
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpIntermediateResults.Size = new Size(576, 104);
    this.tblpIntermediateResults.TabIndex = 0;
    this.tblpAllowableStress.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpAllowableStress.ColumnCount = 2;
    this.tblpAllowableStress.ColumnStyles.Add(new ColumnStyle());
    this.tblpAllowableStress.ColumnStyles.Add(new ColumnStyle());
    this.tblpAllowableStress.Controls.Add((Control) this.umAllowableStress, 1, 0);
    this.tblpAllowableStress.Controls.Add((Control) this.txtAllowableStress, 0, 0);
    this.tblpAllowableStress.Location = new Point(92, 11);
    this.tblpAllowableStress.Margin = new Padding(1);
    this.tblpAllowableStress.Name = "tblpAllowableStress";
    this.tblpAllowableStress.RowCount = 1;
    this.tblpAllowableStress.RowStyles.Add(new RowStyle());
    this.tblpAllowableStress.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tblpAllowableStress.Size = new Size(174, 20);
    this.tblpAllowableStress.TabIndex = 11;
    this.umAllowableStress.Location = new Point(103, 3);
    this.umAllowableStress.Name = "umAllowableStress";
    this.umAllowableStress.Size = new Size(41, 13);
    this.umAllowableStress.TabIndex = 1;
    this.umAllowableStress.Text = "measure";
    this.txtAllowableStress.Location = new Point(0, 0);
    this.txtAllowableStress.Margin = new Padding(0);
    this.txtAllowableStress.Name = "txtAllowableStress";
    this.txtAllowableStress.Properties.ReadOnly = true;
    this.txtAllowableStress.Size = new Size(100, 20);
    this.txtAllowableStress.TabIndex = 0;
    this.lbTmm.Location = new Point(13, 35);
    this.lbTmm.Name = "lbTmm";
    this.lbTmm.Size = new Size(22, 13);
    this.lbTmm.TabIndex = 15;
    this.lbTmm.Text = "Tmm";
    this.lbTrd.Location = new Point(13, 57);
    this.lbTrd.Name = "lbTrd";
    this.lbTrd.Size = new Size(16 /*0x10*/, 13);
    this.lbTrd.TabIndex = 16 /*0x10*/;
    this.lbTrd.Text = "Trd";
    this.lbTc.Location = new Point(13, 79);
    this.lbTc.Name = "lbTc";
    this.lbTc.Size = new Size(11, 13);
    this.lbTc.TabIndex = 18;
    this.lbTc.Text = "Tc";
    this.tblpTmm.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpTmm.ColumnCount = 2;
    this.tblpTmm.ColumnStyles.Add(new ColumnStyle());
    this.tblpTmm.ColumnStyles.Add(new ColumnStyle());
    this.tblpTmm.Controls.Add((Control) this.umTmm, 1, 0);
    this.tblpTmm.Controls.Add((Control) this.txtTmm, 0, 0);
    this.tblpTmm.Location = new Point(92, 33);
    this.tblpTmm.Margin = new Padding(1);
    this.tblpTmm.Name = "tblpTmm";
    this.tblpTmm.RowCount = 1;
    this.tblpTmm.RowStyles.Add(new RowStyle());
    this.tblpTmm.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tblpTmm.Size = new Size(174, 20);
    this.tblpTmm.TabIndex = 20;
    this.umTmm.Location = new Point(103, 3);
    this.umTmm.Name = "umTmm";
    this.umTmm.Size = new Size(41, 13);
    this.umTmm.TabIndex = 1;
    this.umTmm.Text = "measure";
    this.txtTmm.Location = new Point(0, 0);
    this.txtTmm.Margin = new Padding(0);
    this.txtTmm.Name = "txtTmm";
    this.txtTmm.Properties.ReadOnly = true;
    this.txtTmm.Size = new Size(100, 20);
    this.txtTmm.TabIndex = 0;
    this.tblpTrd.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpTrd.ColumnCount = 2;
    this.tblpTrd.ColumnStyles.Add(new ColumnStyle());
    this.tblpTrd.ColumnStyles.Add(new ColumnStyle());
    this.tblpTrd.Controls.Add((Control) this.umTrd, 1, 0);
    this.tblpTrd.Controls.Add((Control) this.txtTrd, 0, 0);
    this.tblpTrd.Location = new Point(92, 55);
    this.tblpTrd.Margin = new Padding(1);
    this.tblpTrd.Name = "tblpTrd";
    this.tblpTrd.RowCount = 1;
    this.tblpTrd.RowStyles.Add(new RowStyle());
    this.tblpTrd.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tblpTrd.Size = new Size(174, 20);
    this.tblpTrd.TabIndex = 22;
    this.umTrd.Location = new Point(103, 3);
    this.umTrd.Name = "umTrd";
    this.umTrd.Size = new Size(41, 13);
    this.umTrd.TabIndex = 1;
    this.umTrd.Text = "measure";
    this.txtTrd.Location = new Point(0, 0);
    this.txtTrd.Margin = new Padding(0);
    this.txtTrd.Name = "txtTrd";
    this.txtTrd.Properties.ReadOnly = true;
    this.txtTrd.Size = new Size(100, 20);
    this.txtTrd.TabIndex = 0;
    this.tblpTc.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpTc.ColumnCount = 2;
    this.tblpTc.ColumnStyles.Add(new ColumnStyle());
    this.tblpTc.ColumnStyles.Add(new ColumnStyle());
    this.tblpTc.Controls.Add((Control) this.umTc, 1, 0);
    this.tblpTc.Controls.Add((Control) this.txtTc, 0, 0);
    this.tblpTc.Location = new Point(92, 77);
    this.tblpTc.Margin = new Padding(1);
    this.tblpTc.Name = "tblpTc";
    this.tblpTc.RowCount = 1;
    this.tblpTc.RowStyles.Add(new RowStyle());
    this.tblpTc.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tblpTc.Size = new Size(174, 20);
    this.tblpTc.TabIndex = 25;
    this.umTc.Location = new Point(103, 3);
    this.umTc.Name = "umTc";
    this.umTc.Size = new Size(41, 13);
    this.umTc.TabIndex = 1;
    this.umTc.Text = "measure";
    this.txtTc.Location = new Point(0, 0);
    this.txtTc.Margin = new Padding(0);
    this.txtTc.Name = "txtTc";
    this.txtTc.Properties.ReadOnly = true;
    this.txtTc.Size = new Size(100, 20);
    this.txtTc.TabIndex = 0;
    this.grcAssessmentCriteriaL1.Controls.Add((Control) this.tblpAssessmentCriteriaL1);
    this.grcAssessmentCriteriaL1.Dock = DockStyle.Top;
    this.grcAssessmentCriteriaL1.Location = new Point(0, 541);
    this.grcAssessmentCriteriaL1.Name = "grcAssessmentCriteriaL1";
    this.grcAssessmentCriteriaL1.Size = new Size(580, 81);
    this.grcAssessmentCriteriaL1.TabIndex = 9;
    this.grcAssessmentCriteriaL1.Text = "Level 1 Assessment Criteria";
    this.tblpAssessmentCriteriaL1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpAssessmentCriteriaL1.ColumnCount = 2;
    this.tblpAssessmentCriteriaL1.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentCriteriaL1.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.lbScreeningCriteriaFigure5_6, 0, 0);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.lbRSFGreaterRSFa, 0, 1);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.txtScreeningCriteriaFigure5_6, 1, 0);
    this.tblpAssessmentCriteriaL1.Controls.Add((Control) this.txtRSFGreaterRSFa, 1, 1);
    this.tblpAssessmentCriteriaL1.Dock = DockStyle.Fill;
    this.tblpAssessmentCriteriaL1.Location = new Point(2, 21);
    this.tblpAssessmentCriteriaL1.Name = "tblpAssessmentCriteriaL1";
    this.tblpAssessmentCriteriaL1.Padding = new Padding(10);
    this.tblpAssessmentCriteriaL1.RowCount = 2;
    this.tblpAssessmentCriteriaL1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL1.Size = new Size(576, 58);
    this.tblpAssessmentCriteriaL1.TabIndex = 1;
    this.lbScreeningCriteriaFigure5_6.Location = new Point(13, 13);
    this.lbScreeningCriteriaFigure5_6.Name = "lbScreeningCriteriaFigure5_6";
    this.lbScreeningCriteriaFigure5_6.Size = new Size(130, 13);
    this.lbScreeningCriteriaFigure5_6.TabIndex = 16 /*0x10*/;
    this.lbScreeningCriteriaFigure5_6.Text = "ScreeningCriteriaFigure5_6";
    this.lbRSFGreaterRSFa.Location = new Point(13, 35);
    this.lbRSFGreaterRSFa.Name = "lbRSFGreaterRSFa";
    this.lbRSFGreaterRSFa.Size = new Size(81, 13);
    this.lbRSFGreaterRSFa.TabIndex = 20;
    this.lbRSFGreaterRSFa.Text = "RSFGreaterRSFa";
    this.txtScreeningCriteriaFigure5_6.Location = new Point(146, 10);
    this.txtScreeningCriteriaFigure5_6.Margin = new Padding(0);
    this.txtScreeningCriteriaFigure5_6.Name = "txtScreeningCriteriaFigure5_6";
    this.txtScreeningCriteriaFigure5_6.Properties.ReadOnly = true;
    this.txtScreeningCriteriaFigure5_6.Size = new Size(100, 20);
    this.txtScreeningCriteriaFigure5_6.TabIndex = 0;
    this.txtRSFGreaterRSFa.Location = new Point(146, 32 /*0x20*/);
    this.txtRSFGreaterRSFa.Margin = new Padding(0);
    this.txtRSFGreaterRSFa.Name = "txtRSFGreaterRSFa";
    this.txtRSFGreaterRSFa.Properties.ReadOnly = true;
    this.txtRSFGreaterRSFa.Size = new Size(100, 20);
    this.txtRSFGreaterRSFa.TabIndex = 0;
    this.grcAssessmentConclusionL1.Controls.Add((Control) this.tableLayoutPanel22);
    this.grcAssessmentConclusionL1.Dock = DockStyle.Top;
    this.grcAssessmentConclusionL1.Location = new Point(0, 622);
    this.grcAssessmentConclusionL1.Name = "grcAssessmentConclusionL1";
    this.grcAssessmentConclusionL1.Size = new Size(580, 103);
    this.grcAssessmentConclusionL1.TabIndex = 10;
    this.grcAssessmentConclusionL1.Text = "Level1 Assessment Conclusion";
    this.tableLayoutPanel22.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel22.ColumnCount = 2;
    this.tableLayoutPanel22.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel22.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel22.Controls.Add((Control) this.lbConditionCircumferentialExtentL1, 0, 1);
    this.tableLayoutPanel22.Controls.Add((Control) this.lbConclusionLevel1, 0, 2);
    this.tableLayoutPanel22.Controls.Add((Control) this.lbConditionLongitudinalExtentL1, 0, 0);
    this.tableLayoutPanel22.Dock = DockStyle.Fill;
    this.tableLayoutPanel22.Location = new Point(2, 21);
    this.tableLayoutPanel22.Name = "tableLayoutPanel22";
    this.tableLayoutPanel22.Padding = new Padding(10);
    this.tableLayoutPanel22.RowCount = 3;
    this.tableLayoutPanel22.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel22.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel22.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel22.Size = new Size(576, 80 /*0x50*/);
    this.tableLayoutPanel22.TabIndex = 0;
    this.lbConditionCircumferentialExtentL1.Location = new Point(13, 33);
    this.lbConditionCircumferentialExtentL1.Name = "lbConditionCircumferentialExtentL1";
    this.lbConditionCircumferentialExtentL1.Size = new Size(160 /*0xA0*/, 13);
    this.lbConditionCircumferentialExtentL1.TabIndex = 17;
    this.lbConditionCircumferentialExtentL1.Text = "ConditionCircumferentialExtentL1";
    this.lbConclusionLevel1.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold);
    this.lbConclusionLevel1.Location = new Point(13, 53);
    this.lbConclusionLevel1.Name = "lbConclusionLevel1";
    this.lbConclusionLevel1.Size = new Size(157, 13);
    this.lbConclusionLevel1.TabIndex = 16 /*0x10*/;
    this.lbConclusionLevel1.Text = "The Level 1 Assessment is ..";
    this.lbConditionLongitudinalExtentL1.Location = new Point(13, 13);
    this.lbConditionLongitudinalExtentL1.Name = "lbConditionLongitudinalExtentL1";
    this.lbConditionLongitudinalExtentL1.Size = new Size(145, 13);
    this.lbConditionLongitudinalExtentL1.TabIndex = 18;
    this.lbConditionLongitudinalExtentL1.Text = "ConditionLongitudinalExtentL1";
    this.grcAssessmentCriteriaL2.Controls.Add((Control) this.tblpAssessmentCriteriaL2);
    this.grcAssessmentCriteriaL2.Dock = DockStyle.Top;
    this.grcAssessmentCriteriaL2.Location = new Point(0, 791);
    this.grcAssessmentCriteriaL2.Name = "grcAssessmentCriteriaL2";
    this.grcAssessmentCriteriaL2.Size = new Size(580, 63 /*0x3F*/);
    this.grcAssessmentCriteriaL2.TabIndex = 11;
    this.grcAssessmentCriteriaL2.Text = "Level 2 Assessment Criteria";
    this.tblpAssessmentCriteriaL2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpAssessmentCriteriaL2.ColumnCount = 2;
    this.tblpAssessmentCriteriaL2.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentCriteriaL2.ColumnStyles.Add(new ColumnStyle());
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.lbmin_RSFiGreaterThanOrEqualToRSFa, 0, 0);
    this.tblpAssessmentCriteriaL2.Controls.Add((Control) this.tableLayoutPanel25, 1, 0);
    this.tblpAssessmentCriteriaL2.Dock = DockStyle.Fill;
    this.tblpAssessmentCriteriaL2.Location = new Point(2, 21);
    this.tblpAssessmentCriteriaL2.Name = "tblpAssessmentCriteriaL2";
    this.tblpAssessmentCriteriaL2.Padding = new Padding(10);
    this.tblpAssessmentCriteriaL2.RowCount = 1;
    this.tblpAssessmentCriteriaL2.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tblpAssessmentCriteriaL2.Size = new Size(576, 40);
    this.tblpAssessmentCriteriaL2.TabIndex = 1;
    this.lbmin_RSFiGreaterThanOrEqualToRSFa.Location = new Point(13, 13);
    this.lbmin_RSFiGreaterThanOrEqualToRSFa.Name = "lbmin_RSFiGreaterThanOrEqualToRSFa";
    this.lbmin_RSFiGreaterThanOrEqualToRSFa.Size = new Size(179, 13);
    this.lbmin_RSFiGreaterThanOrEqualToRSFa.TabIndex = 16 /*0x10*/;
    this.lbmin_RSFiGreaterThanOrEqualToRSFa.Text = "min_RSFiGreaterThanOrEqualToRSFa";
    this.tableLayoutPanel25.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel25.ColumnCount = 2;
    this.tableLayoutPanel25.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel25.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel25.Controls.Add((Control) this.txtmin_RSFiGreaterThanOrEqualToRSFa, 1, 0);
    this.tableLayoutPanel25.Location = new Point(196, 11);
    this.tableLayoutPanel25.Margin = new Padding(1);
    this.tableLayoutPanel25.Name = "tableLayoutPanel25";
    this.tableLayoutPanel25.RowCount = 1;
    this.tableLayoutPanel25.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel25.Size = new Size(155, 20);
    this.tableLayoutPanel25.TabIndex = 17;
    this.txtmin_RSFiGreaterThanOrEqualToRSFa.Location = new Point(0, 0);
    this.txtmin_RSFiGreaterThanOrEqualToRSFa.Margin = new Padding(0);
    this.txtmin_RSFiGreaterThanOrEqualToRSFa.Name = "txtmin_RSFiGreaterThanOrEqualToRSFa";
    this.txtmin_RSFiGreaterThanOrEqualToRSFa.Properties.ReadOnly = true;
    this.txtmin_RSFiGreaterThanOrEqualToRSFa.Size = new Size(100, 20);
    this.txtmin_RSFiGreaterThanOrEqualToRSFa.TabIndex = 0;
    this.grcAssessmentConclusionL2.Controls.Add((Control) this.tableLayoutPanel27);
    this.grcAssessmentConclusionL2.Dock = DockStyle.Top;
    this.grcAssessmentConclusionL2.Location = new Point(0, 854);
    this.grcAssessmentConclusionL2.Name = "grcAssessmentConclusionL2";
    this.grcAssessmentConclusionL2.Size = new Size(580, 103);
    this.grcAssessmentConclusionL2.TabIndex = 12;
    this.grcAssessmentConclusionL2.Text = "Level2 Assessment Conclusion";
    this.tableLayoutPanel27.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel27.ColumnCount = 2;
    this.tableLayoutPanel27.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel27.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel27.Controls.Add((Control) this.lbConclusionLevel2, 0, 2);
    this.tableLayoutPanel27.Controls.Add((Control) this.lbConditionLongitudinalExtentL2, 0, 0);
    this.tableLayoutPanel27.Controls.Add((Control) this.lbConditionCircumferentialExtentL2, 0, 1);
    this.tableLayoutPanel27.Dock = DockStyle.Fill;
    this.tableLayoutPanel27.Location = new Point(2, 21);
    this.tableLayoutPanel27.Name = "tableLayoutPanel27";
    this.tableLayoutPanel27.Padding = new Padding(10);
    this.tableLayoutPanel27.RowCount = 3;
    this.tableLayoutPanel27.RowStyles.Add(new RowStyle(SizeType.Percent, 100f));
    this.tableLayoutPanel27.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel27.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel27.Size = new Size(576, 80 /*0x50*/);
    this.tableLayoutPanel27.TabIndex = 0;
    this.lbConclusionLevel2.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Bold);
    this.lbConclusionLevel2.Location = new Point(13, 53);
    this.lbConclusionLevel2.Name = "lbConclusionLevel2";
    this.lbConclusionLevel2.Size = new Size(157, 13);
    this.lbConclusionLevel2.TabIndex = 18;
    this.lbConclusionLevel2.Text = "The Level 2 Assessment is ..";
    this.lbConditionLongitudinalExtentL2.Location = new Point(13, 13);
    this.lbConditionLongitudinalExtentL2.Name = "lbConditionLongitudinalExtentL2";
    this.lbConditionLongitudinalExtentL2.Size = new Size(145, 13);
    this.lbConditionLongitudinalExtentL2.TabIndex = 16 /*0x10*/;
    this.lbConditionLongitudinalExtentL2.Text = "ConditionLongitudinalExtentL2";
    this.lbConditionCircumferentialExtentL2.Location = new Point(13, 33);
    this.lbConditionCircumferentialExtentL2.Name = "lbConditionCircumferentialExtentL2";
    this.lbConditionCircumferentialExtentL2.Size = new Size(160 /*0xA0*/, 13);
    this.lbConditionCircumferentialExtentL2.TabIndex = 17;
    this.lbConditionCircumferentialExtentL2.Text = "ConditionCircumferentialExtentL2";
    this.grcInspectionDataSummary.Controls.Add((Control) this.tableLayoutPanel1);
    this.grcInspectionDataSummary.Dock = DockStyle.Top;
    this.grcInspectionDataSummary.Location = new Point(0, 0);
    this.grcInspectionDataSummary.Name = "grcInspectionDataSummary";
    this.grcInspectionDataSummary.Size = new Size(580, (int) sbyte.MaxValue);
    this.grcInspectionDataSummary.TabIndex = 15;
    this.grcInspectionDataSummary.Text = "Inspection Data Summary";
    this.tableLayoutPanel1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel1.ColumnCount = 2;
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel1.Controls.Add((Control) this.tblpLm, 1, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbLm, 0, 1);
    this.tableLayoutPanel1.Controls.Add((Control) this.tblpLc, 1, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbLc, 0, 0);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbS, 0, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.lbC, 0, 3);
    this.tableLayoutPanel1.Controls.Add((Control) this.tblpS, 1, 2);
    this.tableLayoutPanel1.Controls.Add((Control) this.tblpC, 1, 3);
    this.tableLayoutPanel1.Dock = DockStyle.Fill;
    this.tableLayoutPanel1.Location = new Point(2, 21);
    this.tableLayoutPanel1.Name = "tableLayoutPanel1";
    this.tableLayoutPanel1.Padding = new Padding(10);
    this.tableLayoutPanel1.RowCount = 4;
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel1.Size = new Size(576, 104);
    this.tableLayoutPanel1.TabIndex = 0;
    this.tblpLm.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpLm.ColumnCount = 2;
    this.tblpLm.ColumnStyles.Add(new ColumnStyle());
    this.tblpLm.ColumnStyles.Add(new ColumnStyle());
    this.tblpLm.Controls.Add((Control) this.umLm, 1, 0);
    this.tblpLm.Controls.Add((Control) this.txtLm, 0, 0);
    this.tblpLm.Location = new Point(30, 33);
    this.tblpLm.Margin = new Padding(1);
    this.tblpLm.Name = "tblpLm";
    this.tblpLm.RowCount = 1;
    this.tblpLm.RowStyles.Add(new RowStyle());
    this.tblpLm.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tblpLm.Size = new Size(174, 20);
    this.tblpLm.TabIndex = 19;
    this.umLm.Location = new Point(103, 3);
    this.umLm.Name = "umLm";
    this.umLm.Size = new Size(41, 13);
    this.umLm.TabIndex = 1;
    this.umLm.Text = "measure";
    this.txtLm.Location = new Point(0, 0);
    this.txtLm.Margin = new Padding(0);
    this.txtLm.Name = "txtLm";
    this.txtLm.Properties.ReadOnly = true;
    this.txtLm.Size = new Size(100, 20);
    this.txtLm.TabIndex = 0;
    this.lbLm.Location = new Point(13, 35);
    this.lbLm.Name = "lbLm";
    this.lbLm.Size = new Size(13, 13);
    this.lbLm.TabIndex = 12;
    this.lbLm.Text = "Lm";
    this.tblpLc.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpLc.ColumnCount = 2;
    this.tblpLc.ColumnStyles.Add(new ColumnStyle());
    this.tblpLc.ColumnStyles.Add(new ColumnStyle());
    this.tblpLc.Controls.Add((Control) this.umLc, 1, 0);
    this.tblpLc.Controls.Add((Control) this.txtLc, 0, 0);
    this.tblpLc.Location = new Point(30, 11);
    this.tblpLc.Margin = new Padding(1);
    this.tblpLc.Name = "tblpLc";
    this.tblpLc.RowCount = 1;
    this.tblpLc.RowStyles.Add(new RowStyle());
    this.tblpLc.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tblpLc.Size = new Size(174, 20);
    this.tblpLc.TabIndex = 11;
    this.umLc.Location = new Point(103, 3);
    this.umLc.Name = "umLc";
    this.umLc.Size = new Size(41, 13);
    this.umLc.TabIndex = 1;
    this.umLc.Text = "measure";
    this.txtLc.Location = new Point(0, 0);
    this.txtLc.Margin = new Padding(0);
    this.txtLc.Name = "txtLc";
    this.txtLc.Properties.ReadOnly = true;
    this.txtLc.Size = new Size(100, 20);
    this.txtLc.TabIndex = 0;
    this.lbLc.Location = new Point(13, 13);
    this.lbLc.Name = "lbLc";
    this.lbLc.Size = new Size(10, 13);
    this.lbLc.TabIndex = 10;
    this.lbLc.Text = "Lc";
    this.lbS.Location = new Point(13, 57);
    this.lbS.Name = "lbS";
    this.lbS.Size = new Size(6, 13);
    this.lbS.TabIndex = 13;
    this.lbS.Text = "S";
    this.lbC.Location = new Point(13, 79);
    this.lbC.Name = "lbC";
    this.lbC.Size = new Size(7, 13);
    this.lbC.TabIndex = 15;
    this.lbC.Text = "C";
    this.tblpS.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpS.ColumnCount = 2;
    this.tblpS.ColumnStyles.Add(new ColumnStyle());
    this.tblpS.ColumnStyles.Add(new ColumnStyle());
    this.tblpS.Controls.Add((Control) this.umS, 1, 0);
    this.tblpS.Controls.Add((Control) this.txtS, 0, 0);
    this.tblpS.Location = new Point(30, 55);
    this.tblpS.Margin = new Padding(1);
    this.tblpS.Name = "tblpS";
    this.tblpS.RowCount = 1;
    this.tblpS.RowStyles.Add(new RowStyle());
    this.tblpS.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tblpS.Size = new Size(174, 20);
    this.tblpS.TabIndex = 21;
    this.umS.Location = new Point(103, 3);
    this.umS.Name = "umS";
    this.umS.Size = new Size(41, 13);
    this.umS.TabIndex = 1;
    this.umS.Text = "measure";
    this.txtS.Location = new Point(0, 0);
    this.txtS.Margin = new Padding(0);
    this.txtS.Name = "txtS";
    this.txtS.Properties.ReadOnly = true;
    this.txtS.Size = new Size(100, 20);
    this.txtS.TabIndex = 0;
    this.tblpC.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblpC.ColumnCount = 2;
    this.tblpC.ColumnStyles.Add(new ColumnStyle());
    this.tblpC.ColumnStyles.Add(new ColumnStyle());
    this.tblpC.Controls.Add((Control) this.umC, 1, 0);
    this.tblpC.Controls.Add((Control) this.txtC, 0, 0);
    this.tblpC.Location = new Point(30, 77);
    this.tblpC.Margin = new Padding(1);
    this.tblpC.Name = "tblpC";
    this.tblpC.RowCount = 1;
    this.tblpC.RowStyles.Add(new RowStyle());
    this.tblpC.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tblpC.Size = new Size(174, 20);
    this.tblpC.TabIndex = 20;
    this.umC.Location = new Point(103, 3);
    this.umC.Name = "umC";
    this.umC.Size = new Size(41, 13);
    this.umC.TabIndex = 1;
    this.umC.Text = "measure";
    this.txtC.Location = new Point(0, 0);
    this.txtC.Margin = new Padding(0);
    this.txtC.Name = "txtC";
    this.txtC.Properties.ReadOnly = true;
    this.txtC.Size = new Size(100, 20);
    this.txtC.TabIndex = 0;
    this.grcAssessmentCriteria.Controls.Add((Control) this.tableLayoutPanel4);
    this.grcAssessmentCriteria.Dock = DockStyle.Top;
    this.grcAssessmentCriteria.Location = new Point(0, 440);
    this.grcAssessmentCriteria.Name = "grcAssessmentCriteria";
    this.grcAssessmentCriteria.Size = new Size(580, 101);
    this.grcAssessmentCriteria.TabIndex = 16 /*0x10*/;
    this.grcAssessmentCriteria.Text = "Assessment Criteria";
    this.tableLayoutPanel4.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel4.ColumnCount = 2;
    this.tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel4.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel4.Controls.Add((Control) this.lbRtGreater0_2, 0, 0);
    this.tableLayoutPanel4.Controls.Add((Control) this.lbTmmMinusFCAGreater2_5, 0, 1);
    this.tableLayoutPanel4.Controls.Add((Control) this.lbLmsdGreater1_8DtcPower0_5, 0, 2);
    this.tableLayoutPanel4.Controls.Add((Control) this.txtLmsdGreater1_8DtcPower0_5, 1, 2);
    this.tableLayoutPanel4.Controls.Add((Control) this.txtTmmMinusFCAGreater2_5, 1, 1);
    this.tableLayoutPanel4.Controls.Add((Control) this.txtRtGreater0_2, 1, 0);
    this.tableLayoutPanel4.Dock = DockStyle.Fill;
    this.tableLayoutPanel4.Location = new Point(2, 21);
    this.tableLayoutPanel4.Name = "tableLayoutPanel4";
    this.tableLayoutPanel4.Padding = new Padding(10);
    this.tableLayoutPanel4.RowCount = 3;
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel4.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel4.Size = new Size(576, 78);
    this.tableLayoutPanel4.TabIndex = 1;
    this.lbRtGreater0_2.Location = new Point(13, 13);
    this.lbRtGreater0_2.Name = "lbRtGreater0_2";
    this.lbRtGreater0_2.Size = new Size(66, 13);
    this.lbRtGreater0_2.TabIndex = 16 /*0x10*/;
    this.lbRtGreater0_2.Text = "RtGreater0_2";
    this.lbTmmMinusFCAGreater2_5.Location = new Point(13, 35);
    this.lbTmmMinusFCAGreater2_5.Name = "lbTmmMinusFCAGreater2_5";
    this.lbTmmMinusFCAGreater2_5.Size = new Size(124, 13);
    this.lbTmmMinusFCAGreater2_5.TabIndex = 20;
    this.lbTmmMinusFCAGreater2_5.Text = "TmmMinusFCAGreater2_5";
    this.lbLmsdGreater1_8DtcPower0_5.Location = new Point(13, 57);
    this.lbLmsdGreater1_8DtcPower0_5.Name = "lbLmsdGreater1_8DtcPower0_5";
    this.lbLmsdGreater1_8DtcPower0_5.Size = new Size(143, 13);
    this.lbLmsdGreater1_8DtcPower0_5.TabIndex = 18;
    this.lbLmsdGreater1_8DtcPower0_5.Text = "LmsdGreater1_8DtcPower0_5";
    this.txtLmsdGreater1_8DtcPower0_5.Location = new Point(159, 54);
    this.txtLmsdGreater1_8DtcPower0_5.Margin = new Padding(0);
    this.txtLmsdGreater1_8DtcPower0_5.Name = "txtLmsdGreater1_8DtcPower0_5";
    this.txtLmsdGreater1_8DtcPower0_5.Properties.ReadOnly = true;
    this.txtLmsdGreater1_8DtcPower0_5.Size = new Size(100, 20);
    this.txtLmsdGreater1_8DtcPower0_5.TabIndex = 0;
    this.txtTmmMinusFCAGreater2_5.Location = new Point(159, 32 /*0x20*/);
    this.txtTmmMinusFCAGreater2_5.Margin = new Padding(0);
    this.txtTmmMinusFCAGreater2_5.Name = "txtTmmMinusFCAGreater2_5";
    this.txtTmmMinusFCAGreater2_5.Properties.ReadOnly = true;
    this.txtTmmMinusFCAGreater2_5.Size = new Size(100, 20);
    this.txtTmmMinusFCAGreater2_5.TabIndex = 0;
    this.txtRtGreater0_2.Location = new Point(159, 10);
    this.txtRtGreater0_2.Margin = new Padding(0);
    this.txtRtGreater0_2.Name = "txtRtGreater0_2";
    this.txtRtGreater0_2.Properties.ReadOnly = true;
    this.txtRtGreater0_2.Size = new Size(100, 20);
    this.txtRtGreater0_2.TabIndex = 0;
    this.grcAssessmentResultsL2.Controls.Add((Control) this.tableLayoutPanel7);
    this.grcAssessmentResultsL2.Dock = DockStyle.Top;
    this.grcAssessmentResultsL2.Location = new Point(0, 725);
    this.grcAssessmentResultsL2.Name = "grcAssessmentResultsL2";
    this.grcAssessmentResultsL2.Size = new Size(580, 66);
    this.grcAssessmentResultsL2.TabIndex = 17;
    this.grcAssessmentResultsL2.Text = "Level 2 Assessment Results";
    this.tableLayoutPanel7.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel7.ColumnCount = 2;
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel7.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel7.Controls.Add((Control) this.txtminRSFi, 1, 0);
    this.tableLayoutPanel7.Controls.Add((Control) this.lbminRSFi, 0, 0);
    this.tableLayoutPanel7.Dock = DockStyle.Fill;
    this.tableLayoutPanel7.Location = new Point(2, 21);
    this.tableLayoutPanel7.Name = "tableLayoutPanel7";
    this.tableLayoutPanel7.Padding = new Padding(10);
    this.tableLayoutPanel7.RowCount = 1;
    this.tableLayoutPanel7.RowStyles.Add(new RowStyle(SizeType.Absolute, 23f));
    this.tableLayoutPanel7.Size = new Size(576, 43);
    this.tableLayoutPanel7.TabIndex = 0;
    this.txtminRSFi.Location = new Point(53, 10);
    this.txtminRSFi.Margin = new Padding(0);
    this.txtminRSFi.Name = "txtminRSFi";
    this.txtminRSFi.Properties.ReadOnly = true;
    this.txtminRSFi.Size = new Size(100, 20);
    this.txtminRSFi.TabIndex = 0;
    this.lbminRSFi.Location = new Point(13, 13);
    this.lbminRSFi.Name = "lbminRSFi";
    this.lbminRSFi.Size = new Size(37, 13);
    this.lbminRSFi.TabIndex = 27;
    this.lbminRSFi.Text = "minRSFi";
    this.grcMAWP.Controls.Add((Control) this.tbpMawp);
    this.grcMAWP.Dock = DockStyle.Top;
    this.grcMAWP.Location = new Point(0, 254);
    this.grcMAWP.Name = "grcMAWP";
    this.grcMAWP.Size = new Size(580, 106);
    this.grcMAWP.TabIndex = 18;
    this.grcMAWP.Text = "Maximum Allowable Working Pressure";
    this.tbpMawp.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tbpMawp.ColumnCount = 2;
    this.tbpMawp.ColumnStyles.Add(new ColumnStyle());
    this.tbpMawp.ColumnStyles.Add(new ColumnStyle());
    this.tbpMawp.Controls.Add((Control) this.tableLayoutPanel11, 1, 0);
    this.tbpMawp.Controls.Add((Control) this.lbMAWPValue, 0, 0);
    this.tbpMawp.Controls.Add((Control) this.lbMAWPrL1, 0, 1);
    this.tbpMawp.Controls.Add((Control) this.lbMAWPrL2, 0, 2);
    this.tbpMawp.Controls.Add((Control) this.tblMAWPrL1, 1, 1);
    this.tbpMawp.Controls.Add((Control) this.tblMAWPrL2, 1, 2);
    this.tbpMawp.Dock = DockStyle.Fill;
    this.tbpMawp.Location = new Point(2, 21);
    this.tbpMawp.Name = "tbpMawp";
    this.tbpMawp.Padding = new Padding(10);
    this.tbpMawp.RowCount = 3;
    this.tbpMawp.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tbpMawp.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tbpMawp.RowStyles.Add(new RowStyle(SizeType.Absolute, 22f));
    this.tbpMawp.Size = new Size(576, 83);
    this.tbpMawp.TabIndex = 0;
    this.tableLayoutPanel11.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tableLayoutPanel11.ColumnCount = 2;
    this.tableLayoutPanel11.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel11.ColumnStyles.Add(new ColumnStyle());
    this.tableLayoutPanel11.Controls.Add((Control) this.umMAWPValue, 1, 0);
    this.tableLayoutPanel11.Controls.Add((Control) this.txtMAWPValue, 0, 0);
    this.tableLayoutPanel11.Location = new Point(74, 11);
    this.tableLayoutPanel11.Margin = new Padding(1);
    this.tableLayoutPanel11.Name = "tableLayoutPanel11";
    this.tableLayoutPanel11.RowCount = 1;
    this.tableLayoutPanel11.RowStyles.Add(new RowStyle());
    this.tableLayoutPanel11.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tableLayoutPanel11.Size = new Size(174, 20);
    this.tableLayoutPanel11.TabIndex = 11;
    this.umMAWPValue.Location = new Point(103, 3);
    this.umMAWPValue.Name = "umMAWPValue";
    this.umMAWPValue.Size = new Size(41, 13);
    this.umMAWPValue.TabIndex = 1;
    this.umMAWPValue.Text = "measure";
    this.txtMAWPValue.Location = new Point(0, 0);
    this.txtMAWPValue.Margin = new Padding(0);
    this.txtMAWPValue.Name = "txtMAWPValue";
    this.txtMAWPValue.Properties.ReadOnly = true;
    this.txtMAWPValue.Size = new Size(100, 20);
    this.txtMAWPValue.TabIndex = 0;
    this.lbMAWPValue.Location = new Point(13, 13);
    this.lbMAWPValue.Name = "lbMAWPValue";
    this.lbMAWPValue.Size = new Size(57, 13);
    this.lbMAWPValue.TabIndex = 10;
    this.lbMAWPValue.Text = "MAWPValue";
    this.lbMAWPrL1.Location = new Point(13, 35);
    this.lbMAWPrL1.Name = "lbMAWPrL1";
    this.lbMAWPrL1.Size = new Size(46, 13);
    this.lbMAWPrL1.TabIndex = 15;
    this.lbMAWPrL1.Text = "MAWPrL1";
    this.lbMAWPrL2.Location = new Point(13, 57);
    this.lbMAWPrL2.Name = "lbMAWPrL2";
    this.lbMAWPrL2.Size = new Size(46, 13);
    this.lbMAWPrL2.TabIndex = 16 /*0x10*/;
    this.lbMAWPrL2.Text = "MAWPrL2";
    this.tblMAWPrL1.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblMAWPrL1.ColumnCount = 2;
    this.tblMAWPrL1.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWPrL1.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWPrL1.Controls.Add((Control) this.umMAWPrL1, 1, 0);
    this.tblMAWPrL1.Controls.Add((Control) this.txtMAWPrL1, 0, 0);
    this.tblMAWPrL1.Location = new Point(74, 33);
    this.tblMAWPrL1.Margin = new Padding(1);
    this.tblMAWPrL1.Name = "tblMAWPrL1";
    this.tblMAWPrL1.RowCount = 1;
    this.tblMAWPrL1.RowStyles.Add(new RowStyle());
    this.tblMAWPrL1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tblMAWPrL1.Size = new Size(174, 20);
    this.tblMAWPrL1.TabIndex = 20;
    this.umMAWPrL1.Location = new Point(103, 3);
    this.umMAWPrL1.Name = "umMAWPrL1";
    this.umMAWPrL1.Size = new Size(41, 13);
    this.umMAWPrL1.TabIndex = 1;
    this.umMAWPrL1.Text = "measure";
    this.txtMAWPrL1.Location = new Point(0, 0);
    this.txtMAWPrL1.Margin = new Padding(0);
    this.txtMAWPrL1.Name = "txtMAWPrL1";
    this.txtMAWPrL1.Properties.ReadOnly = true;
    this.txtMAWPrL1.Size = new Size(100, 20);
    this.txtMAWPrL1.TabIndex = 0;
    this.tblMAWPrL2.AutoSizeMode = AutoSizeMode.GrowAndShrink;
    this.tblMAWPrL2.ColumnCount = 2;
    this.tblMAWPrL2.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWPrL2.ColumnStyles.Add(new ColumnStyle());
    this.tblMAWPrL2.Controls.Add((Control) this.umMAWPrL2, 1, 0);
    this.tblMAWPrL2.Controls.Add((Control) this.txtMAWPrL2, 0, 0);
    this.tblMAWPrL2.Location = new Point(74, 55);
    this.tblMAWPrL2.Margin = new Padding(1);
    this.tblMAWPrL2.Name = "tblMAWPrL2";
    this.tblMAWPrL2.RowCount = 1;
    this.tblMAWPrL2.RowStyles.Add(new RowStyle());
    this.tblMAWPrL2.RowStyles.Add(new RowStyle(SizeType.Absolute, 20f));
    this.tblMAWPrL2.Size = new Size(174, 20);
    this.tblMAWPrL2.TabIndex = 22;
    this.umMAWPrL2.Location = new Point(103, 3);
    this.umMAWPrL2.Name = "umMAWPrL2";
    this.umMAWPrL2.Size = new Size(41, 13);
    this.umMAWPrL2.TabIndex = 1;
    this.umMAWPrL2.Text = "measure";
    this.txtMAWPrL2.Location = new Point(0, 0);
    this.txtMAWPrL2.Margin = new Padding(0);
    this.txtMAWPrL2.Name = "txtMAWPrL2";
    this.txtMAWPrL2.Properties.ReadOnly = true;
    this.txtMAWPrL2.Size = new Size(100, 20);
    this.txtMAWPrL2.TabIndex = 0;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.Controls.Add((Control) this.grcWarnings);
    this.Controls.Add((Control) this.grcAssessmentConclusionL2);
    this.Controls.Add((Control) this.grcAssessmentCriteriaL2);
    this.Controls.Add((Control) this.grcAssessmentResultsL2);
    this.Controls.Add((Control) this.grcAssessmentConclusionL1);
    this.Controls.Add((Control) this.grcAssessmentCriteriaL1);
    this.Controls.Add((Control) this.grcAssessmentCriteria);
    this.Controls.Add((Control) this.grcAssessmentResults);
    this.Controls.Add((Control) this.grcMAWP);
    this.Controls.Add((Control) this.grcIntermediateResults);
    this.Controls.Add((Control) this.grcInspectionDataSummary);
    this.Name = nameof (vwResult);
    this.Size = new Size(580, 1150);
    this.Load += new EventHandler(this.vwResult_Load);
    this.txtLambda.Properties.EndInit();
    this.txtWarningMessages.Properties.EndInit();
    this.grcWarnings.EndInit();
    this.grcWarnings.ResumeLayout(false);
    this.tableLayoutPanel3.ResumeLayout(false);
    this.tblpAssessmentResults.ResumeLayout(false);
    this.tblpAssessmentResults.PerformLayout();
    this.txtRSF.Properties.EndInit();
    this.grcAssessmentResults.EndInit();
    this.grcAssessmentResults.ResumeLayout(false);
    this.grcIntermediateResults.EndInit();
    this.grcIntermediateResults.ResumeLayout(false);
    this.tblpIntermediateResults.ResumeLayout(false);
    this.tblpIntermediateResults.PerformLayout();
    this.tblpAllowableStress.ResumeLayout(false);
    this.tblpAllowableStress.PerformLayout();
    this.txtAllowableStress.Properties.EndInit();
    this.tblpTmm.ResumeLayout(false);
    this.tblpTmm.PerformLayout();
    this.txtTmm.Properties.EndInit();
    this.tblpTrd.ResumeLayout(false);
    this.tblpTrd.PerformLayout();
    this.txtTrd.Properties.EndInit();
    this.tblpTc.ResumeLayout(false);
    this.tblpTc.PerformLayout();
    this.txtTc.Properties.EndInit();
    this.grcAssessmentCriteriaL1.EndInit();
    this.grcAssessmentCriteriaL1.ResumeLayout(false);
    this.tblpAssessmentCriteriaL1.ResumeLayout(false);
    this.tblpAssessmentCriteriaL1.PerformLayout();
    this.txtScreeningCriteriaFigure5_6.Properties.EndInit();
    this.txtRSFGreaterRSFa.Properties.EndInit();
    this.grcAssessmentConclusionL1.EndInit();
    this.grcAssessmentConclusionL1.ResumeLayout(false);
    this.tableLayoutPanel22.ResumeLayout(false);
    this.tableLayoutPanel22.PerformLayout();
    this.grcAssessmentCriteriaL2.EndInit();
    this.grcAssessmentCriteriaL2.ResumeLayout(false);
    this.tblpAssessmentCriteriaL2.ResumeLayout(false);
    this.tblpAssessmentCriteriaL2.PerformLayout();
    this.tableLayoutPanel25.ResumeLayout(false);
    this.txtmin_RSFiGreaterThanOrEqualToRSFa.Properties.EndInit();
    this.grcAssessmentConclusionL2.EndInit();
    this.grcAssessmentConclusionL2.ResumeLayout(false);
    this.tableLayoutPanel27.ResumeLayout(false);
    this.tableLayoutPanel27.PerformLayout();
    this.grcInspectionDataSummary.EndInit();
    this.grcInspectionDataSummary.ResumeLayout(false);
    this.tableLayoutPanel1.ResumeLayout(false);
    this.tableLayoutPanel1.PerformLayout();
    this.tblpLm.ResumeLayout(false);
    this.tblpLm.PerformLayout();
    this.txtLm.Properties.EndInit();
    this.tblpLc.ResumeLayout(false);
    this.tblpLc.PerformLayout();
    this.txtLc.Properties.EndInit();
    this.tblpS.ResumeLayout(false);
    this.tblpS.PerformLayout();
    this.txtS.Properties.EndInit();
    this.tblpC.ResumeLayout(false);
    this.tblpC.PerformLayout();
    this.txtC.Properties.EndInit();
    this.grcAssessmentCriteria.EndInit();
    this.grcAssessmentCriteria.ResumeLayout(false);
    this.tableLayoutPanel4.ResumeLayout(false);
    this.tableLayoutPanel4.PerformLayout();
    this.txtLmsdGreater1_8DtcPower0_5.Properties.EndInit();
    this.txtTmmMinusFCAGreater2_5.Properties.EndInit();
    this.txtRtGreater0_2.Properties.EndInit();
    this.grcAssessmentResultsL2.EndInit();
    this.grcAssessmentResultsL2.ResumeLayout(false);
    this.tableLayoutPanel7.ResumeLayout(false);
    this.tableLayoutPanel7.PerformLayout();
    this.txtminRSFi.Properties.EndInit();
    this.grcMAWP.EndInit();
    this.grcMAWP.ResumeLayout(false);
    this.tbpMawp.ResumeLayout(false);
    this.tbpMawp.PerformLayout();
    this.tableLayoutPanel11.ResumeLayout(false);
    this.tableLayoutPanel11.PerformLayout();
    this.txtMAWPValue.Properties.EndInit();
    this.tblMAWPrL1.ResumeLayout(false);
    this.tblMAWPrL1.PerformLayout();
    this.txtMAWPrL1.Properties.EndInit();
    this.tblMAWPrL2.ResumeLayout(false);
    this.tblMAWPrL2.PerformLayout();
    this.txtMAWPrL2.Properties.EndInit();
    this.ResumeLayout(false);
  }

  public vwResult(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._presenter = new ResultPresenter(recordView, (IResultView) this);
  }

  public bool Calculate() => this._presenter.Calculate();

  public bool ExportToExcel() => this._presenter.ExportToExcel();

  public bool Level2
  {
    get => this._level2;
    set
    {
      this._level2 = value;
      if (!value)
        this.HideLevel2Results();
      else
        this.ShowLevel2Results();
    }
  }

  public bool MAWP
  {
    get => this._mawp;
    set
    {
      this._mawp = value;
      if (!value)
        this.HideMAWPResults();
      else
        this.ShowMAWPResults();
    }
  }

  public double? LongitudinalThicknessReadingSpacingLc
  {
    get => Helpers.ParseNullDouble((object) this.txtLc.Text);
    set => this.txtLc.Text = Helpers.ParseObjectToString((object) value);
  }

  public string LongitudinalThicknessReadingSpacingLcLabel
  {
    get => this.lbLc.Text;
    set => this.lbLc.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMLc
  {
    get => this.umLc.Text;
    set => this.umLc.Text = value;
  }

  public double? CircumferentialThicknessReadingSpacingLm
  {
    get => Helpers.ParseNullDouble((object) this.txtLm.Text);
    set => this.txtLm.Text = Helpers.ParseObjectToString((object) value);
  }

  public string CircumferentialThicknessReadingSpacingLmLabel
  {
    get => this.lbLm.Text;
    set => this.lbLm.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMLm
  {
    get => this.umLm.Text;
    set => this.umLm.Text = value;
  }

  public double? LongitudinalMetalLossExtentS
  {
    get => Helpers.ParseNullDouble((object) this.txtS.Text);
    set => this.txtS.Text = Helpers.ParseObjectToString((object) value);
  }

  public string LongitudinalMetalLossExtentSLabel
  {
    get => this.lbS.Text;
    set => this.lbS.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMS
  {
    get => this.umS.Text;
    set => this.umS.Text = value;
  }

  public double? CircumferentiallMetalLossExtentC
  {
    get => Helpers.ParseNullDouble((object) this.txtC.Text);
    set => this.txtC.Text = Helpers.ParseObjectToString((object) value);
  }

  public string CircumferentiallMetalLossExtentCLabel
  {
    get => this.lbC.Text;
    set => this.lbC.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMC
  {
    get => this.umC.Text;
    set => this.umC.Text = value;
  }

  public bool AllowableStressVisible
  {
    get => this.lbAllowableStress.Visible;
    set
    {
      this.lbAllowableStress.Visible = value;
      this.tblpAllowableStress.Visible = value;
      this.txtAllowableStress.Visible = value;
      this.umAllowableStress.Visible = value;
      if (value)
        this.tblpIntermediateResults.RowStyles[0] = new RowStyle(SizeType.Absolute, 22f);
      else
        this.tblpIntermediateResults.RowStyles[0] = new RowStyle(SizeType.Absolute, 0.0f);
    }
  }

  public double? AllowableStress
  {
    get => Helpers.ParseNullDouble((object) this.txtAllowableStress.Text);
    set => this.txtAllowableStress.Text = Helpers.ParseObjectToString((object) value);
  }

  public string AllowableStressLabel
  {
    get => this.lbAllowableStress.Text;
    set => this.lbAllowableStress.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMAllowableStress
  {
    get => this.umAllowableStress.Text;
    set => this.umAllowableStress.Text = value;
  }

  public double? Tmm
  {
    get => Helpers.ParseNullDouble((object) this.txtTmm.Text);
    set => this.txtTmm.Text = Helpers.ParseObjectToString((object) value);
  }

  public string TmmLabel
  {
    get => this.lbTmm.Text;
    set => this.lbTmm.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMTmm
  {
    get => this.umTmm.Text;
    set => this.umTmm.Text = value;
  }

  public double? Trd
  {
    get => Helpers.ParseNullDouble((object) this.txtTrd.Text);
    set => this.txtTrd.Text = Helpers.ParseObjectToString((object) value);
  }

  public string TrdLabel
  {
    get => this.lbTrd.Text;
    set => this.lbTrd.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMTrd
  {
    get => this.umTrd.Text;
    set => this.umTrd.Text = value;
  }

  public double? Tc
  {
    get => Helpers.ParseNullDouble((object) this.txtTc.Text);
    set => this.txtTc.Text = Helpers.ParseObjectToString((object) value);
  }

  public string TcLabel
  {
    get => this.lbTc.Text;
    set => this.lbTc.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMTc
  {
    get => this.umTc.Text;
    set => this.umTc.Text = value;
  }

  public double? Lambda
  {
    get => Helpers.ParseNullDouble((object) this.txtLambda.Text);
    set => this.txtLambda.Text = Helpers.ParseObjectToString((object) value);
  }

  public string LambdaLabel
  {
    get => this.lbLambda.Text;
    set => this.lbLambda.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? RSF
  {
    get => Helpers.ParseNullDouble((object) this.txtRSF.Text);
    set => this.txtRSF.Text = Helpers.ParseObjectToString((object) value);
  }

  public string RSFLabel
  {
    get => this.lbRSF.Text;
    set => this.lbRSF.Text = Helpers.ParseObjectToString((object) value);
  }

  public double? minRSFi
  {
    get => Helpers.ParseNullDouble((object) this.txtminRSFi.Text);
    set => this.txtminRSFi.Text = Helpers.ParseObjectToString((object) value);
  }

  public string minRSFiLabel
  {
    get => this.lbminRSFi.Text;
    set => this.lbminRSFi.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool RtGreater0_2
  {
    get => Helpers.ParseObjectToBool((object) this.txtRtGreater0_2.Text);
    set => this.txtRtGreater0_2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string RtGreater0_2Label
  {
    get => this.lbRtGreater0_2.Text;
    set => this.lbRtGreater0_2.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool TmmMinusFCAGreater2_5
  {
    get => Helpers.ParseObjectToBool((object) this.txtTmmMinusFCAGreater2_5.Text);
    set => this.txtTmmMinusFCAGreater2_5.Text = Helpers.ParseObjectToString((object) value);
  }

  public string TmmMinusFCAGreater2_5Label
  {
    get => this.lbTmmMinusFCAGreater2_5.Text;
    set => this.lbTmmMinusFCAGreater2_5.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool LmsdGreater1_8DtcPower0_5
  {
    get => Helpers.ParseObjectToBool((object) this.txtLmsdGreater1_8DtcPower0_5.Text);
    set => this.txtLmsdGreater1_8DtcPower0_5.Text = Helpers.ParseObjectToString((object) value);
  }

  public string LmsdGreater1_8DtcPower0_5Label
  {
    get => this.lbLmsdGreater1_8DtcPower0_5.Text;
    set => this.lbLmsdGreater1_8DtcPower0_5.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool ScreeningCriteriaFigure5_6
  {
    get => Helpers.ParseObjectToBool((object) this.txtScreeningCriteriaFigure5_6.Text);
    set => this.txtScreeningCriteriaFigure5_6.Text = Helpers.ParseObjectToString((object) value);
  }

  public string ScreeningCriteriaFigure5_6Label
  {
    get => this.lbScreeningCriteriaFigure5_6.Text;
    set => this.lbScreeningCriteriaFigure5_6.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool RSFGreaterRSFa
  {
    get => Helpers.ParseObjectToBool((object) this.txtRSFGreaterRSFa.Text);
    set => this.txtRSFGreaterRSFa.Text = Helpers.ParseObjectToString((object) value);
  }

  public string RSFGreaterRSFaLabel
  {
    get => this.lbRSFGreaterRSFa.Text;
    set => this.lbRSFGreaterRSFa.Text = Helpers.ParseObjectToString((object) value);
  }

  public bool Level1Passed
  {
    get => this._level1Passed;
    set
    {
      this._level1Passed = value;
      if (value)
      {
        this.HideLevel2Results();
        this.lbConclusionLevel1.ForeColor = Color.Green;
      }
      else
        this.lbConclusionLevel1.ForeColor = Color.DarkRed;
    }
  }

  public string ConditionLongitudinalExtentL1
  {
    get => this.lbConditionLongitudinalExtentL1.Text;
    set => this.lbConditionLongitudinalExtentL1.Text = value;
  }

  public string Level1AssessmentConclusion
  {
    get => this.lbConclusionLevel1.Text;
    set => this.lbConclusionLevel1.Text = value;
  }

  public bool min_RSFiGreaterThanOrEqualToRSFa
  {
    get => Helpers.ParseObjectToBool((object) this.txtmin_RSFiGreaterThanOrEqualToRSFa.Text);
    set
    {
      this.txtmin_RSFiGreaterThanOrEqualToRSFa.Text = Helpers.ParseObjectToString((object) value);
    }
  }

  public string min_RSFiGreaterThanOrEqualToRSFaLabel
  {
    get => this.lbmin_RSFiGreaterThanOrEqualToRSFa.Text;
    set
    {
      this.lbmin_RSFiGreaterThanOrEqualToRSFa.Text = Helpers.ParseObjectToString((object) value);
    }
  }

  public bool Level2Passed
  {
    get => this._level2Passed;
    set
    {
      this._level2Passed = value;
      if (value)
        this.lbConclusionLevel2.ForeColor = Color.DarkGreen;
      else
        this.lbConclusionLevel2.ForeColor = Color.DarkRed;
    }
  }

  public string ConditionLongitudinalExtentL2
  {
    get => this.lbConditionLongitudinalExtentL2.Text;
    set => this.lbConditionLongitudinalExtentL2.Text = value;
  }

  public string Level2AssessmentConclusion
  {
    get => this.lbConclusionLevel2.Text;
    set => this.lbConclusionLevel2.Text = value;
  }

  public double? MAWPValue
  {
    get => Helpers.ParseNullDouble((object) this.txtMAWPValue.Text);
    set => this.txtMAWPValue.Text = Helpers.ParseObjectToString((object) value);
  }

  public string MAWPValueLabel
  {
    get => this.lbMAWPValue.Text;
    set => this.lbMAWPValue.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMMawpValue
  {
    get => this.umMAWPValue.Text;
    set => this.umMAWPValue.Text = value;
  }

  public bool MAWPrL1Visible
  {
    get => this.lbMAWPrL1.Visible;
    set
    {
      this.lbMAWPrL1.Visible = value;
      this.tblMAWPrL1.Visible = value;
      this.txtMAWPrL1.Visible = value;
      this.umMAWPrL1.Visible = value;
    }
  }

  public double? MAWPrL1
  {
    get => Helpers.ParseNullDouble((object) this.txtMAWPrL1.Text);
    set => this.txtMAWPrL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string MAWPrL1Label
  {
    get => this.lbMAWPrL1.Text;
    set => this.lbMAWPrL1.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMMawprL1
  {
    get => this.umMAWPrL1.Text;
    set => this.umMAWPrL1.Text = value;
  }

  public bool MAWPrL2Visible
  {
    get => this.lbMAWPrL2.Visible;
    set
    {
      this.lbMAWPrL2.Visible = value;
      this.tblMAWPrL2.Visible = value;
      this.txtMAWPrL2.Visible = value;
      this.umMAWPrL2.Visible = value;
    }
  }

  public double? MAWPrL2
  {
    get => Helpers.ParseNullDouble((object) this.txtMAWPrL2.Text);
    set => this.txtMAWPrL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string MAWPrL2Label
  {
    get => this.lbMAWPrL2.Text;
    set => this.lbMAWPrL2.Text = Helpers.ParseObjectToString((object) value);
  }

  public string UMMawprL2
  {
    get => this.umMAWPrL2.Text;
    set => this.umMAWPrL2.Text = value;
  }

  public string ResultMessages
  {
    get => this.txtWarningMessages.Text;
    set => this.txtWarningMessages.Text = value;
  }

  public void ShowMessage(string message)
  {
    int num = (int) XtraMessageBox.Show(message);
  }

  public string CodeEdition => this._presenter.CodeEdition;

  public string Title => "Assessment to API 579 Part 5. Local Metal Loss.";

  public string Introduction
  {
    get
    {
      return "The section 5 assessment procedure is aimed at examining pressure equipment subject to local metal loss resulting from corrosion or erosion. The assessment methodology is based on establishing a remaining strength factor (RSF) for the affected area, for comparison with RSF acceptance criteria. The types of flaws characterised as local metal loss for the section 5 assessment procedure are locally thinned areas (LTA's) and groove-like flaws.";
    }
  }

  public string CommentsAndAssumptions
  {
    get
    {
      return "The local metal loss procedure in Part 5 assumes that: \n1. The original design criteria were in accordance with a recognised code or standard. \n2. The component is not operating in the creep range.\n3. The component is not in cyclic service. \n4. The material is considered to have sufficient material toughness.\n5. Level 1 covers Type A Components (Part 4, paragraph 4.2.5) subject to internal pressure. (i.e. supplemental loads are assumed negligible).\n6. Level 2 covers type A or B Components (see Part 4, paragraph 4.2.5) subject to internal pressure, external pressure, supplemental loads (see Annex A, paragraph A.2.7), or any combination of loading.\n\nIf the component is a cylindrical shell, conical shell, or elbow, the circumferential extent of the flaw is evaluated using the procedure in paragraph 5.4.2.2.i) \n\nIt is considered that supplemental loads are not present or are not significant. If supplemental loads are significant, then for Level 2 Assessment, the circumferential extent of the region of local metal loss shall be evaluated using the procedures in paragraph 5.4.3.4.";
    }
  }

  public string References
  {
    get
    {
      return "API 579 'Fitness-for-Service', Second Edition, The American Society of Mechanical Engineers. Part 5: Assessment of Local Metal Loss.";
    }
  }

  public string Limitations => string.Empty;

  private void HideLevel2Results()
  {
    this.grcAssessmentResultsL2.Visible = false;
    this.grcAssessmentCriteriaL2.Visible = false;
    this.grcAssessmentConclusionL2.Visible = false;
  }

  private void ShowLevel2Results()
  {
    this.grcAssessmentResultsL2.Visible = true;
    this.grcAssessmentCriteriaL2.Visible = true;
    this.grcAssessmentConclusionL2.Visible = true;
  }

  private void HideMAWPResults() => this.grcMAWP.Visible = false;

  private void ShowMAWPResults() => this.grcMAWP.Visible = true;

  private void vwResult_Load(object sender, EventArgs e)
  {
    this.txtLc.ShowOnlyN4();
    this.txtLm.ShowOnlyN4();
    this.txtS.ShowOnlyN4();
    this.txtC.ShowOnlyN4();
    this.txtAllowableStress.ShowOnlyN4();
    this.txtTmm.ShowOnlyN4();
    this.txtTrd.ShowOnlyN4();
    this.txtTc.ShowOnlyN4();
    this.txtLambda.ShowOnlyN4();
    this.txtRSF.ShowOnlyN4();
    this.txtminRSFi.ShowOnlyN4();
    this.txtMAWPValue.ShowOnlyN4();
    this.txtMAWPrL1.ShowOnlyN4();
    this.txtMAWPrL2.ShowOnlyN4();
  }
}
