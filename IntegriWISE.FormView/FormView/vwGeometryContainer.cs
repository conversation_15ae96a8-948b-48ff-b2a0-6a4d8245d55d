// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.vwGeometryContainer
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Record;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView;

public class vwGeometryContainer : RecordTabControl, IGeometryContainerView, IView
{
  private IContainer components;
  private PanelControl FigureContainer;
  private PanelControl GeometryContainer;
  private IGeometryBaseView _geometryView;
  private IRecordView _recordView;
  private GeometryContainerPresenter _presenter;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.FigureContainer = new PanelControl();
    this.GeometryContainer = new PanelControl();
    this.FigureContainer.BeginInit();
    this.GeometryContainer.BeginInit();
    this.SuspendLayout();
    this.FigureContainer.AutoSize = true;
    this.FigureContainer.BorderStyle = BorderStyles.NoBorder;
    this.FigureContainer.Dock = DockStyle.Top;
    this.FigureContainer.Location = new Point(0, 0);
    this.FigureContainer.Name = "FigureContainer";
    this.FigureContainer.Size = new Size(459, 0);
    this.FigureContainer.TabIndex = 0;
    this.GeometryContainer.AutoSize = true;
    this.GeometryContainer.BorderStyle = BorderStyles.NoBorder;
    this.GeometryContainer.Dock = DockStyle.Top;
    this.GeometryContainer.Location = new Point(0, 0);
    this.GeometryContainer.Name = "GeometryContainer";
    this.GeometryContainer.Size = new Size(459, 0);
    this.GeometryContainer.TabIndex = 1;
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.AutoScroll = true;
    this.AutoSize = true;
    this.Controls.Add((Control) this.GeometryContainer);
    this.Controls.Add((Control) this.FigureContainer);
    this.Name = nameof (vwGeometryContainer);
    this.Size = new Size(459, 555);
    this.FigureContainer.EndInit();
    this.GeometryContainer.EndInit();
    this.ResumeLayout(false);
    this.PerformLayout();
  }

  public vwGeometryContainer(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._presenter = new GeometryContainerPresenter(this._recordView, (IGeometryContainerView) this);
  }

  public void InitGeometryView() => this._presenter.InitGeometryView();

  public void InitGeometryView(string namespaceString)
  {
    object instance = Activator.CreateInstance(System.Type.GetType($"IntegriWISE.FormView.Assessment.{namespaceString}.vwGeometry"), (object) this._recordView);
    this._geometryView = (IGeometryBaseView) instance;
    XtraUserControl xtraUserControl = instance as XtraUserControl;
    this.GeometryContainer.Controls.Add((Control) xtraUserControl);
    xtraUserControl.BringToFront();
    xtraUserControl.Dock = DockStyle.Fill;
  }

  public bool ValidateGeometry() => this._geometryView.ValidateGeometry();

  public void Save() => this._geometryView.Save();
}
