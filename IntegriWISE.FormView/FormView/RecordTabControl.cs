// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.RecordTabControl
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Record;
using System.ComponentModel;
using System.Windows.Forms;
using System.Windows.Forms.Layout;

#nullable disable
namespace IntegriWISE.FormView;

public class RecordTabControl : XtraUserControl, IRecordTabControlView, IView
{
  private IContainer components;

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.components = (IContainer) new System.ComponentModel.Container();
    this.AutoScaleMode = AutoScaleMode.Font;
  }

  public RecordTabControl() => this.InitializeComponent();

  public void SetReadOnly()
  {
    foreach (Control control in (ArrangedElementCollection) this.Controls)
      ControlFunctions.DisableControl(control);
  }
}
