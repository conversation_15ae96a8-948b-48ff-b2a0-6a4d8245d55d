// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Array2DWrapper`1
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using IntegriWISE.DataTransferObjects;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;

#nullable disable
namespace IntegriWISE.FormView;

public class Array2DWrapper<T> : IList, ICollection, IEnumerable, ITypedList
{
  private T[,] array;
  private List<Array2DWrapper<T>.RowWrapper> list;
  private PropertyDescriptorCollection pdc;

  public Array2DWrapper(T[,] array)
  {
    this.array = array;
    int length = array.GetLength(0);
    this.list = new List<Array2DWrapper<T>.RowWrapper>(length);
    for (int index = 0; index < length; ++index)
      this.list.Add(new Array2DWrapper<T>.RowWrapper(this));
  }

  int IList.Add(object value) => throw new Exception("The method or operation is not implemented.");

  void IList.Clear() => throw new Exception("The method or operation is not implemented.");

  bool IList.Contains(object value)
  {
    return value is Array2DWrapper<T>.RowWrapper && this.list.Contains((Array2DWrapper<T>.RowWrapper) value);
  }

  int IList.IndexOf(object value)
  {
    return value is Array2DWrapper<T>.RowWrapper ? this.list.IndexOf((Array2DWrapper<T>.RowWrapper) value) : -1;
  }

  void IList.Insert(int index, object value)
  {
    throw new Exception("The method or operation is not implemented.");
  }

  bool IList.IsFixedSize => true;

  bool IList.IsReadOnly => true;

  void IList.Remove(object value)
  {
    throw new Exception("The method or operation is not implemented.");
  }

  void IList.RemoveAt(int index)
  {
    throw new Exception("The method or operation is not implemented.");
  }

  object IList.this[int index]
  {
    get => (object) this.list[index];
    set => throw new Exception("The method or operation is not implemented.");
  }

  void ICollection.CopyTo(Array array, int index)
  {
    if (!(array is Array2DWrapper<T>.RowWrapper[]))
      return;
    this.list.CopyTo((Array2DWrapper<T>.RowWrapper[]) array, index);
  }

  int ICollection.Count => this.list.Count;

  bool ICollection.IsSynchronized => false;

  object ICollection.SyncRoot => (object) this;

  IEnumerator IEnumerable.GetEnumerator() => (IEnumerator) this.list.GetEnumerator();

  PropertyDescriptorCollection ITypedList.GetItemProperties(PropertyDescriptor[] listAccessors)
  {
    if (this.pdc == null)
    {
      PropertyDescriptor[] properties = new PropertyDescriptor[this.array.GetLength(1)];
      for (int index = 0; index < properties.Length; ++index)
      {
        int num = index + 1;
        properties[index] = (PropertyDescriptor) new Array2DWrapper<T>.WrapperPropertyDescriptor("C" + (object) num, index, typeof (T));
      }
      this.pdc = new PropertyDescriptorCollection(properties);
    }
    return this.pdc;
  }

  string ITypedList.GetListName(PropertyDescriptor[] listAccessors) => string.Empty;

  private double?[,] ConvertThicknessPointsReadingsListToArray(
    List<ThicknessPointReadingDTO> thicknessPointsList)
  {
    double?[,] array = new double?[500, 500];
    foreach (ThicknessPointReadingDTO thicknessPoints in thicknessPointsList)
      array[thicknessPoints.RowNo, thicknessPoints.ColNo] = new double?(thicknessPoints.ThicknessValue);
    return array;
  }

  private class WrapperPropertyDescriptor : PropertyDescriptor
  {
    private int index;
    private Type elementType;

    public WrapperPropertyDescriptor(string name, int index, Type elementType)
      : base(name, (Attribute[]) null)
    {
      this.index = index;
      this.elementType = elementType;
    }

    public override bool CanResetValue(object component) => false;

    public override Type ComponentType => typeof (Array2DWrapper<T>.RowWrapper);

    public override object GetValue(object component)
    {
      return component is Array2DWrapper<T>.RowWrapper ? ((Array2DWrapper<T>.RowWrapper) component).GetValue(this.index) : (object) null;
    }

    public override bool IsReadOnly => false;

    public override Type PropertyType => this.elementType;

    public override void ResetValue(object component)
    {
    }

    public override void SetValue(object component, object value)
    {
      if (!(component is Array2DWrapper<T>.RowWrapper))
        return;
      ((Array2DWrapper<T>.RowWrapper) component).SetValue(this.index, value);
    }

    public override bool ShouldSerializeValue(object component) => false;
  }

  private class RowWrapper : CustomTypeDescriptor
  {
    private Array2DWrapper<T> owner;

    public RowWrapper(Array2DWrapper<T> owner) => this.owner = owner;

    public override PropertyDescriptorCollection GetProperties() => base.GetProperties();

    public object GetValue(int index)
    {
      return this.owner.array.GetValue(this.owner.list.IndexOf(this), index);
    }

    public void SetValue(int index, object value)
    {
      this.owner.array.SetValue(value, this.owner.list.IndexOf(this), index);
    }

    public override PropertyDescriptorCollection GetProperties(Attribute[] attributes)
    {
      return this.owner.pdc;
    }
  }
}
