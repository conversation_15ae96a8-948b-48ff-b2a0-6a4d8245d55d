// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.Properties.Resources
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using System.CodeDom.Compiler;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Resources;
using System.Runtime.CompilerServices;

#nullable disable
namespace IntegriWISE.FormView.Properties;

[CompilerGenerated]
[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
[DebuggerNonUserCode]
internal class Resources
{
  private static ResourceManager resourceMan;
  private static CultureInfo resourceCulture;

  internal Resources()
  {
  }

  [EditorBrowsable(EditorBrowsableState.Advanced)]
  internal static ResourceManager ResourceManager
  {
    get
    {
      if (object.ReferenceEquals((object) IntegriWISE.FormView.Properties.Resources.resourceMan, (object) null))
        IntegriWISE.FormView.Properties.Resources.resourceMan = new ResourceManager("IntegriWISE.FormView.Properties.Resources", typeof (IntegriWISE.FormView.Properties.Resources).Assembly);
      return IntegriWISE.FormView.Properties.Resources.resourceMan;
    }
  }

  [EditorBrowsable(EditorBrowsableState.Advanced)]
  internal static CultureInfo Culture
  {
    get => IntegriWISE.FormView.Properties.Resources.resourceCulture;
    set => IntegriWISE.FormView.Properties.Resources.resourceCulture = value;
  }

  internal static Bitmap ClearLeft
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (ClearLeft), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap ClearLeft16
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (ClearLeft16), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap Close_2_icon
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (Close_2_icon), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap delete_icon
  {
    get => (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject("delete-icon", IntegriWISE.FormView.Properties.Resources.resourceCulture);
  }

  internal static Bitmap filenew_small
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (filenew_small), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap importitems
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (importitems), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_Blister_1
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_Blister_1), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_Blister_2
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_Blister_2), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_CylindricalSection
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_CylindricalSection), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_CylindricalShell
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_CylindricalShell), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_Elbow
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_Elbow), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_EllipticalHead
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_EllipticalHead), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_GeneralMetalLossCylindricalSection
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_GeneralMetalLossCylindricalSection), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_GeneralMetalLossCylindricalShell
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_GeneralMetalLossCylindricalShell), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_GML_Elbow
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_GML_Elbow), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_GML_Tanks
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_GML_Tanks), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_GML_Vessels_Reducers
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_GML_Vessels_Reducers), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_HemisphericalHead
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_HemisphericalHead), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_HIC
  {
    get => (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_HIC), IntegriWISE.FormView.Properties.Resources.resourceCulture);
  }

  internal static Bitmap IW_Lamination_1
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_Lamination_1), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_Lamination_2
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_Lamination_2), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_Pitting_1
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_Pitting_1), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_Pitting_2
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_Pitting_2), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_Pitting_3
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_Pitting_3), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_PittingGrade1
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_PittingGrade1), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_PittingGrade2
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_PittingGrade2), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_PittingGrade3
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_PittingGrade3), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_PittingGrade4
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_PittingGrade4), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_PittingGrade5
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_PittingGrade5), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_PittingGrade6
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_PittingGrade6), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_PittingGrade7
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_PittingGrade7), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_PittingGrade8
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_PittingGrade8), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_Reducer
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_Reducer), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_SphericalShell
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_SphericalShell), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap IW_TorisphericalHead
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (IW_TorisphericalHead), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap Line_Chart_icon
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject("Line-Chart-icon", IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap pasteThickness24
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (pasteThickness24), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap PittingGradeInfo
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (PittingGradeInfo), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }

  internal static Bitmap ThicknessPaste
  {
    get
    {
      return (Bitmap) IntegriWISE.FormView.Properties.Resources.ResourceManager.GetObject(nameof (ThicknessPaste), IntegriWISE.FormView.Properties.Resources.resourceCulture);
    }
  }
}
