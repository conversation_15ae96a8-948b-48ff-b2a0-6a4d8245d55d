// Decompiled with JetBrains decompiler
// Type: IntegriWISE.FormView.vwThicknessReadings
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Mask;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Views.Grid;
using IntegriWISE.Common;
using IntegriWISE.Common.Tools;
using IntegriWISE.DataTransferObjects;
using IntegriWISE.FormView.Properties;
using IntegriWISE.FormView.Utilities;
using IntegriWISE.UserInterface;
using IntegriWISE.UserInterface.Assessment;
using IntegriWISE.UserInterface.Record;
using IntegriWISE.UserInterface.ThicknessReadings;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.FormView;

public class vwThicknessReadings : RecordTabControl, IThicknessReadingsView, IView
{
  private IRecordView _recordView;
  private ThicknessReadingsPresenter _presenter;
  private FormDirtyTracker _dirtyTracker;
  private IThicknessAssessmentBaseView _thicknessAssessmentView;
  private CultureInfo _CurrentCulture;
  private double?[,] _thicknessMatrix;
  private double?[] _thicknessMatrixPoints;
  private ThicknessReadingsDTO _thicknessReadings;
  private double range_a;
  private double range_b;
  private double range_c;
  private double range_d;
  private double range_e;
  private IContainer components;
  private TextEdit txtTrd;
  private GridControl gcThicknessReadingsGrid;
  private GridView ThicknessReadingsView;
  private LabelControl lbTrd;
  private SimpleButton btnClear;
  private SimpleButton btnFillWithTRD;
  private PanelControl panelControl2;
  private SimpleButton btnDeleteSelectedCells;
  private RepositoryItemTextEdit riThicknessValue;
  private SimpleButton btnPaste;
  private LabelControl lblThicknessUnit;

  public vwThicknessReadings(IRecordView recordView)
  {
    this.InitializeComponent();
    this._recordView = recordView;
    this._presenter = new ThicknessReadingsPresenter(recordView, (IThicknessReadingsView) this);
    this._presenter.SetUnitMeasure();
    if (!(this._recordView.AssessmentView is IThicknessAssessmentBaseView))
    {
      int num = (int) XtraMessageBox.Show("System Error: Invalid Thickness Assessment Base View");
    }
    this._thicknessAssessmentView = this._recordView.AssessmentView as IThicknessAssessmentBaseView;
  }

  private void vwThicknessReadings_Load(object sender, EventArgs e)
  {
    this._dirtyTracker = new FormDirtyTracker((XtraUserControl) this, (IBaseView) this._recordView);
    this._dirtyTracker.IsHandled = true;
    this._CurrentCulture = Thread.CurrentThread.CurrentCulture;
    this.txtTrd.ShowOnlyN4();
    this._presenter.LoadThicknessReadings();
    this._dirtyTracker.IsHandled = false;
    this.ChangeCellColor();
  }

  public ThicknessReadingsDTO ThicknessReadings
  {
    get
    {
      List<ThicknessPointReadingDTO> thicknessPointReadingDtoList = new List<ThicknessPointReadingDTO>();
      if (this._thicknessReadings.ReadingsType == "GeneralMetalLossThicknessProfile" || this._thicknessReadings.ReadingsType == "LocalMetalLossThinArea" || this._thicknessReadings.ReadingsType == "LocalMetalLossASMEB31G")
      {
        this._thicknessMatrix.GetLength(0);
        this._thicknessMatrix.GetLength(1);
        for (int index1 = 0; index1 < this._thicknessMatrix.GetLength(0); ++index1)
        {
          for (int index2 = 0; index2 < this._thicknessMatrix.GetLength(1); ++index2)
          {
            double? nullable = this._thicknessMatrix[index1, index2];
            if (nullable.HasValue)
              thicknessPointReadingDtoList.Add(new ThicknessPointReadingDTO()
              {
                ColNo = index2,
                RowNo = index1,
                ThicknessValue = nullable.Value
              });
          }
        }
      }
      else if (this._thicknessReadings.ReadingsType == "GeneralMetalLossThicknessReading")
      {
        this._thicknessMatrix.GetLength(0);
        this._thicknessMatrix.GetLength(1);
        for (int index3 = 0; index3 < this._thicknessMatrix.GetLength(0); ++index3)
        {
          for (int index4 = 0; index4 < this._thicknessMatrix.GetLength(1); ++index4)
          {
            double? nullable = this._thicknessMatrix[index3, index4];
            if (nullable.HasValue)
              thicknessPointReadingDtoList.Add(new ThicknessPointReadingDTO()
              {
                ColNo = index4,
                RowNo = index3,
                ThicknessValue = nullable.Value
              });
          }
        }
      }
      this._thicknessReadings.ThicknessPointReadings = thicknessPointReadingDtoList;
      return this._thicknessReadings;
    }
    set
    {
      this._thicknessReadings = value;
      this.BindThicknessReadings();
    }
  }

  private void BindThicknessReadings()
  {
    if (this._thicknessReadings.ReadingsType == "GeneralMetalLossThicknessProfile" || this._thicknessReadings.ReadingsType == "LocalMetalLossThinArea" || this._thicknessReadings.ReadingsType == "LocalMetalLossASMEB31G")
    {
      this._thicknessMatrix = new double?[500, 500];
      foreach (ThicknessPointReadingDTO thicknessPointReading in this._thicknessReadings.ThicknessPointReadings)
        this._thicknessMatrix[thicknessPointReading.RowNo, thicknessPointReading.ColNo] = new double?(thicknessPointReading.ThicknessValue);
      this.gcThicknessReadingsGrid.DataSource = (object) new Array2DWrapper<double?>(this._thicknessMatrix);
    }
    else if (this._thicknessReadings.ReadingsType == "GeneralMetalLossThicknessReading")
    {
      this._thicknessMatrix = new double?[1, 500];
      foreach (ThicknessPointReadingDTO thicknessPointReading in this._thicknessReadings.ThicknessPointReadings)
        this._thicknessMatrix[0, thicknessPointReading.ColNo] = new double?(thicknessPointReading.ThicknessValue);
      this.gcThicknessReadingsGrid.DataSource = (object) new Array2DWrapper<double?>(this._thicknessMatrix);
    }
    this.SetTrd();
  }

  public void Save() => this._presenter.Save();

  public string UM_Thickness
  {
    set => this.lblThicknessUnit.Text = value;
  }

  private void ThicknessReadingsView_ValidatingEditor(
    object sender,
    BaseContainerValidateEditorEventArgs e)
  {
    if (e.Value == null || e.Value.ToString() == "")
    {
      e.Value = (object) null;
      e.Valid = true;
    }
    else
    {
      double result = 0.0;
      if (!double.TryParse(e.Value.ToString(), out result))
      {
        e.Valid = false;
        e.ErrorText = "Incorrect input. Must be numeric";
      }
      else
      {
        if (result > 0.0)
          return;
        e.Valid = false;
        e.ErrorText = "Incorrect input. Thickness reading should be larger than 0 ";
      }
    }
  }

  private void btnFillWithTRD_Click(object sender, EventArgs e)
  {
    if (this._thicknessAssessmentView == null)
      return;
    this._thicknessAssessmentView.CalculateTRD();
    if (this._recordView.ThicknessReadings != null && this._recordView.ThicknessReadings.Trd.HasValue)
    {
      double? trd = this._recordView.ThicknessReadings.Trd;
      if ((trd.GetValueOrDefault() != 0.0 ? 0 : (trd.HasValue ? 1 : 0)) == 0)
        goto label_6;
    }
    if (this._recordView.ThicknessReadings.ReadingsType != "LocalMetalLossASMEB31G")
    {
      int num = (int) XtraMessageBox.Show("Please fill in the Geometry Values Nominal Thickness, External and Internal Uniform MetalLoss before entering Thickness Reading Values.");
      return;
    }
label_6:
    if (this._recordView.ThicknessReadings != null && this._recordView.ThicknessReadings.Trd.HasValue)
    {
      double? trd = this._recordView.ThicknessReadings.Trd;
      if ((trd.GetValueOrDefault() != 0.0 ? 0 : (trd.HasValue ? 1 : 0)) == 0)
        goto label_10;
    }
    if (this._recordView.ThicknessReadings.ReadingsType == "LocalMetalLossASMEB31G")
    {
      int num = (int) XtraMessageBox.Show("Please fill in the Nominal Thicknes before entering Thickness Reading Values.");
      return;
    }
label_10:
    this._thicknessAssessmentView.CalculateTRD();
    double? nullable1 = this._recordView.ThicknessReadings.Trd.ThicknessFromIW();
    double? nullable2 = nullable1;
    if ((nullable2.GetValueOrDefault() > 0.0 ? 0 : (nullable2.HasValue ? 1 : 0)) != 0)
    {
      int num1 = (int) XtraMessageBox.Show("The current Trd  (tnom - LOSS) is negative.Please correct your tnom or Loss Values.");
    }
    else
    {
      if (this.ThicknessReadingsView.GetSelectedCells().Length <= 0)
        return;
      try
      {
        this.ThicknessReadingsView.BeginUpdate();
        foreach (GridCell selectedCell in this.ThicknessReadingsView.GetSelectedCells())
        {
          if (this.ThicknessReadingsView.GetRowCellValue(selectedCell.RowHandle, selectedCell.Column) == null)
            this.ThicknessReadingsView.SetRowCellValue(selectedCell.RowHandle, selectedCell.Column, (object) nullable1);
        }
      }
      catch
      {
      }
      finally
      {
        this.ThicknessReadingsView.EndUpdate();
      }
    }
  }

  private void ThicknessReadingsView_CustomDrawRowIndicator(
    object sender,
    RowIndicatorCustomDrawEventArgs e)
  {
    int rowHandle = e.RowHandle;
    if (rowHandle >= 0)
    {
      int num = rowHandle + 1;
      e.Info.DisplayText = "M" + num.ToString();
    }
    e.Info.ImageIndex = -1;
  }

  private void btnClear_Click(object sender, EventArgs e)
  {
    if (this._thicknessReadings == null || XtraMessageBox.Show(string.Format("Do you want to clear All the Thickness Readings from the Grid?"), Global.ApplicationName, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) != DialogResult.Yes)
      return;
    this._thicknessReadings.ThicknessPointReadings = new List<ThicknessPointReadingDTO>();
    this.BindThicknessReadings();
    this._dirtyTracker.SetAsDirty();
    this._recordView.IsDirty = true;
  }

  private void ThicknessReadingsView_KeyUp(object sender, KeyEventArgs e)
  {
    switch (e.KeyCode)
    {
      case Keys.Delete:
        this.DeleteSelectedCells();
        break;
      case Keys.C:
        if (e.Modifiers != Keys.Control)
          break;
        e.Handled = true;
        break;
    }
  }

  private void DeleteSelectedCells()
  {
    GridCell[] selectedCells = this.ThicknessReadingsView.GetSelectedCells();
    if (selectedCells != null && selectedCells.Length > 1 && XtraMessageBox.Show(string.Format("Do you want to delete the Selected Thickness Readings from the Grid?"), Global.ApplicationName, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) != DialogResult.Yes)
      return;
    this.ThicknessReadingsView.RowCellStyle -= new RowCellStyleEventHandler(this.ThicknessReadingsView_RowCellStyle);
    this.ThicknessReadingsView.CellValueChanged -= new CellValueChangedEventHandler(this.ThicknessReadingsView_CellValueChanged);
    for (int index = 0; index < selectedCells.Length; ++index)
      this.ThicknessReadingsView.SetRowCellValue(selectedCells[index].RowHandle, selectedCells[index].Column, (object) null);
    this.ThicknessReadingsView.CellValueChanged += new CellValueChangedEventHandler(this.ThicknessReadingsView_CellValueChanged);
    this.ThicknessReadingsView.RowCellStyle += new RowCellStyleEventHandler(this.ThicknessReadingsView_RowCellStyle);
    this.ThicknessReadingsView.RefreshData();
  }

  private void btnDeleteSelectedCells_Click(object sender, EventArgs e)
  {
    this.DeleteSelectedCells();
  }

  private void SetTrd()
  {
    if (this._recordView.ThicknessReadings == null || this._thicknessAssessmentView == null)
      return;
    this._thicknessAssessmentView.CalculateTRD();
    this.txtTrd.Text = this._recordView.ThicknessReadings.Trd.ThicknessFromIW().ToString();
    if (!(this._thicknessReadings.ReadingsType == "LocalMetalLossASMEB31G"))
      return;
    this.lbTrd.Text = "Tnom";
  }

  private void vwThicknessReadings_VisibleChanged(object sender, EventArgs e) => this.SetTrd();

  private void ThicknessReadingsView_CustomRowCellEdit(object sender, CustomRowCellEditEventArgs e)
  {
    e.RepositoryItem = (RepositoryItem) this.riThicknessValue;
  }

  private void btnPaste_Click(object sender, EventArgs e)
  {
    this.ThicknessReadingsView.CellValueChanged -= new CellValueChangedEventHandler(this.ThicknessReadingsView_CellValueChanged);
    using (GridControlPaste gridControlPaste = new GridControlPaste())
      gridControlPaste.PasteToGridControl(this.ThicknessReadingsView);
    this.ChangeCellColor();
    this.ThicknessReadingsView.CellValueChanged += new CellValueChangedEventHandler(this.ThicknessReadingsView_CellValueChanged);
    this.ThicknessReadingsView.PostEditor();
  }

  private void ThicknessReadingsView_CellValueChanged(object sender, CellValueChangedEventArgs e)
  {
    this.ChangeCellColor();
    this.ThicknessReadingsView.PostEditor();
    this.gcThicknessReadingsGrid.Refresh();
    this.ThicknessReadingsView.RefreshData();
  }

  private void ThicknessReadingsView_RowCellStyle(object sender, RowCellStyleEventArgs e)
  {
    if (e.RowHandle < 0 || ((IEnumerable<GridCell>) this.ThicknessReadingsView.GetSelectedCells()).Any<GridCell>((Func<GridCell, bool>) (p => p.Column == e.Column && p.RowHandle == e.RowHandle)) || !Helpers.ParseNullDouble(e.CellValue).HasValue)
      return;
    if (this.range_a == this.range_e)
    {
      e.Appearance.BackColor = Color.Green;
      e.Appearance.BackColor2 = Color.WhiteSmoke;
    }
    else if ((double) e.CellValue <= this.range_a)
    {
      e.Appearance.BackColor = Color.Red;
      e.Appearance.BackColor2 = Color.WhiteSmoke;
    }
    else if ((double) e.CellValue <= this.range_b)
    {
      e.Appearance.BackColor = Color.Orange;
      e.Appearance.BackColor2 = Color.WhiteSmoke;
    }
    else if ((double) e.CellValue <= this.range_c)
    {
      e.Appearance.BackColor = Color.Yellow;
      e.Appearance.BackColor2 = Color.WhiteSmoke;
    }
    else if ((double) e.CellValue <= this.range_d)
    {
      e.Appearance.BackColor = Color.LightGreen;
      e.Appearance.BackColor2 = Color.WhiteSmoke;
    }
    else
    {
      if ((double) e.CellValue > this.range_e)
        return;
      e.Appearance.BackColor = Color.Green;
      e.Appearance.BackColor2 = Color.WhiteSmoke;
    }
  }

  private void ChangeCellColor()
  {
    this.ThicknessReadingsView.PostEditor();
    double? nullable1 = this._presenter.HighestReading();
    double? nullable2 = this._presenter.LowestReading();
    if (!nullable1.HasValue || !nullable2.HasValue)
      return;
    double num = (nullable1.Value - nullable2.Value) / 5.0;
    this.range_a = nullable2.Value + num;
    this.range_b = nullable2.Value + num * 2.0;
    this.range_c = nullable2.Value + num * 3.0;
    this.range_d = nullable2.Value + num * 4.0;
    this.range_e = nullable2.Value + num * 5.0;
  }

  protected override void Dispose(bool disposing)
  {
    if (disposing && this.components != null)
      this.components.Dispose();
    base.Dispose(disposing);
  }

  private void InitializeComponent()
  {
    this.txtTrd = new TextEdit();
    this.gcThicknessReadingsGrid = new GridControl();
    this.ThicknessReadingsView = new GridView();
    this.riThicknessValue = new RepositoryItemTextEdit();
    this.lbTrd = new LabelControl();
    this.btnClear = new SimpleButton();
    this.btnFillWithTRD = new SimpleButton();
    this.panelControl2 = new PanelControl();
    this.lblThicknessUnit = new LabelControl();
    this.btnPaste = new SimpleButton();
    this.btnDeleteSelectedCells = new SimpleButton();
    this.txtTrd.Properties.BeginInit();
    this.gcThicknessReadingsGrid.BeginInit();
    this.ThicknessReadingsView.BeginInit();
    this.riThicknessValue.BeginInit();
    this.panelControl2.BeginInit();
    this.panelControl2.SuspendLayout();
    this.SuspendLayout();
    this.txtTrd.Location = new Point(242, 5);
    this.txtTrd.Name = "txtTrd";
    this.txtTrd.Properties.Appearance.BackColor = Color.White;
    this.txtTrd.Properties.Appearance.Options.UseBackColor = true;
    this.txtTrd.Properties.ReadOnly = true;
    this.txtTrd.Size = new Size(70, 20);
    this.txtTrd.TabIndex = 5;
    this.gcThicknessReadingsGrid.Dock = DockStyle.Fill;
    this.gcThicknessReadingsGrid.Location = new Point(0, 31 /*0x1F*/);
    this.gcThicknessReadingsGrid.MainView = (BaseView) this.ThicknessReadingsView;
    this.gcThicknessReadingsGrid.Name = "gcThicknessReadingsGrid";
    this.gcThicknessReadingsGrid.RepositoryItems.AddRange(new RepositoryItem[1]
    {
      (RepositoryItem) this.riThicknessValue
    });
    this.gcThicknessReadingsGrid.Size = new Size(900, 469);
    this.gcThicknessReadingsGrid.TabIndex = 1;
    this.gcThicknessReadingsGrid.ViewCollection.AddRange(new BaseView[1]
    {
      (BaseView) this.ThicknessReadingsView
    });
    this.ThicknessReadingsView.GridControl = this.gcThicknessReadingsGrid;
    this.ThicknessReadingsView.IndicatorWidth = 50;
    this.ThicknessReadingsView.Name = "ThicknessReadingsView";
    this.ThicknessReadingsView.OptionsBehavior.EditorShowMode = EditorShowMode.MouseDownFocused;
    this.ThicknessReadingsView.OptionsCustomization.AllowColumnMoving = false;
    this.ThicknessReadingsView.OptionsCustomization.AllowFilter = false;
    this.ThicknessReadingsView.OptionsCustomization.AllowGroup = false;
    this.ThicknessReadingsView.OptionsCustomization.AllowSort = false;
    this.ThicknessReadingsView.OptionsMenu.EnableColumnMenu = false;
    this.ThicknessReadingsView.OptionsMenu.EnableFooterMenu = false;
    this.ThicknessReadingsView.OptionsMenu.EnableGroupPanelMenu = false;
    this.ThicknessReadingsView.OptionsSelection.MultiSelect = true;
    this.ThicknessReadingsView.OptionsSelection.MultiSelectMode = GridMultiSelectMode.CellSelect;
    this.ThicknessReadingsView.OptionsView.ColumnAutoWidth = false;
    this.ThicknessReadingsView.OptionsView.ShowGroupPanel = false;
    this.ThicknessReadingsView.CustomDrawRowIndicator += new RowIndicatorCustomDrawEventHandler(this.ThicknessReadingsView_CustomDrawRowIndicator);
    this.ThicknessReadingsView.RowCellStyle += new RowCellStyleEventHandler(this.ThicknessReadingsView_RowCellStyle);
    this.ThicknessReadingsView.CustomRowCellEdit += new CustomRowCellEditEventHandler(this.ThicknessReadingsView_CustomRowCellEdit);
    this.ThicknessReadingsView.CellValueChanged += new CellValueChangedEventHandler(this.ThicknessReadingsView_CellValueChanged);
    this.ThicknessReadingsView.KeyUp += new KeyEventHandler(this.ThicknessReadingsView_KeyUp);
    this.ThicknessReadingsView.ValidatingEditor += new BaseContainerValidateEditorEventHandler(this.ThicknessReadingsView_ValidatingEditor);
    this.riThicknessValue.AutoHeight = false;
    this.riThicknessValue.Mask.EditMask = "\\d{0,9}(\\R.\\d{0,4})?";
    this.riThicknessValue.Mask.MaskType = MaskType.RegEx;
    this.riThicknessValue.Mask.UseMaskAsDisplayFormat = true;
    this.riThicknessValue.Name = "riThicknessValue";
    this.lbTrd.Appearance.TextOptions.HAlignment = HorzAlignment.Far;
    this.lbTrd.AutoSizeMode = LabelAutoSizeMode.None;
    this.lbTrd.Location = new Point(150, 8);
    this.lbTrd.Name = "lbTrd";
    this.lbTrd.Size = new Size(86, 13);
    this.lbTrd.TabIndex = 4;
    this.lbTrd.Text = "Trd (tnom - LOSS)";
    this.btnClear.Image = (Image) Resources.filenew_small;
    this.btnClear.Location = new Point(5, 4);
    this.btnClear.Name = "btnClear";
    this.btnClear.Size = new Size(24, 23);
    this.btnClear.TabIndex = 0;
    this.btnClear.Text = "Clear Readings from Grid";
    this.btnClear.ToolTip = "Clear Readings from Grid";
    this.btnClear.Click += new EventHandler(this.btnClear_Click);
    this.btnFillWithTRD.Image = (Image) Resources.importitems;
    this.btnFillWithTRD.Location = new Point(65, 2);
    this.btnFillWithTRD.Name = "btnFillWithTRD";
    this.btnFillWithTRD.Size = new Size(24, 23);
    this.btnFillWithTRD.TabIndex = 2;
    this.btnFillWithTRD.Text = "Fill";
    this.btnFillWithTRD.ToolTip = "Fill selected readings with trd";
    this.btnFillWithTRD.Click += new EventHandler(this.btnFillWithTRD_Click);
    this.panelControl2.Controls.Add((Control) this.lblThicknessUnit);
    this.panelControl2.Controls.Add((Control) this.btnPaste);
    this.panelControl2.Controls.Add((Control) this.btnDeleteSelectedCells);
    this.panelControl2.Controls.Add((Control) this.txtTrd);
    this.panelControl2.Controls.Add((Control) this.lbTrd);
    this.panelControl2.Controls.Add((Control) this.btnClear);
    this.panelControl2.Controls.Add((Control) this.btnFillWithTRD);
    this.panelControl2.Dock = DockStyle.Top;
    this.panelControl2.Location = new Point(0, 0);
    this.panelControl2.Name = "panelControl2";
    this.panelControl2.Size = new Size(900, 31 /*0x1F*/);
    this.panelControl2.TabIndex = 0;
    this.lblThicknessUnit.Appearance.Font = new Font("Tahoma", 8.25f, FontStyle.Regular, GraphicsUnit.Point, (byte) 0);
    this.lblThicknessUnit.Location = new Point(315, 8);
    this.lblThicknessUnit.Name = "lblThicknessUnit";
    this.lblThicknessUnit.Size = new Size(114, 13);
    this.lblThicknessUnit.TabIndex = 6;
    this.lblThicknessUnit.Text = "Thickness Measurement";
    this.btnPaste.Image = (Image) Resources.pasteThickness24;
    this.btnPaste.ImageLocation = ImageLocation.MiddleCenter;
    this.btnPaste.Location = new Point(95, 2);
    this.btnPaste.Name = "btnPaste";
    this.btnPaste.Size = new Size(28, 23);
    this.btnPaste.TabIndex = 3;
    this.btnPaste.Text = "Fill";
    this.btnPaste.ToolTip = "Paste Thickness Readings from Clipboard";
    this.btnPaste.Click += new EventHandler(this.btnPaste_Click);
    this.btnDeleteSelectedCells.Image = (Image) Resources.delete_icon;
    this.btnDeleteSelectedCells.Location = new Point(35, 4);
    this.btnDeleteSelectedCells.Name = "btnDeleteSelectedCells";
    this.btnDeleteSelectedCells.Size = new Size(24, 23);
    this.btnDeleteSelectedCells.TabIndex = 1;
    this.btnDeleteSelectedCells.Text = "Delete Selected Readings from Grid";
    this.btnDeleteSelectedCells.ToolTip = "Delete Selected Readings from Grid";
    this.btnDeleteSelectedCells.Click += new EventHandler(this.btnDeleteSelectedCells_Click);
    this.AutoScaleDimensions = new SizeF(6f, 13f);
    this.AutoScaleMode = AutoScaleMode.Font;
    this.Controls.Add((Control) this.gcThicknessReadingsGrid);
    this.Controls.Add((Control) this.panelControl2);
    this.Name = nameof (vwThicknessReadings);
    this.Size = new Size(900, 500);
    this.Load += new EventHandler(this.vwThicknessReadings_Load);
    this.VisibleChanged += new EventHandler(this.vwThicknessReadings_VisibleChanged);
    this.txtTrd.Properties.EndInit();
    this.gcThicknessReadingsGrid.EndInit();
    this.ThicknessReadingsView.EndInit();
    this.riThicknessValue.EndInit();
    this.panelControl2.EndInit();
    this.panelControl2.ResumeLayout(false);
    this.panelControl2.PerformLayout();
    this.ResumeLayout(false);
  }

  bool IThicknessReadingsView.Validate() => this.Validate();
}
