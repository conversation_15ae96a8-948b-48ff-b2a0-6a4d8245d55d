// Decompiled with JetBrains decompiler
// Type: IntegriWISE.Utilities.KeyPressHelpers
// Assembly: IntegriWISE.FormView, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: CD700C87-9673-4C3E-8F45-344F47AEBCC6
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.FormView.dll

using DevExpress.XtraEditors;
using System.Windows.Forms;

#nullable disable
namespace IntegriWISE.Utilities;

public class KeyPressHelpers
{
  public static void IntTextBoxKeyPress(object sender, KeyPressEventArgs e)
  {
    e.Handled = !char.IsDigit(e.<PERSON>har) && !char.IsControl(e.KeyChar);
    if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
      e.Handled = true;
    if (e.KeyChar != '.')
      return;
    e.Handled = true;
  }

  public static void DblTextBoxKeyPress(object sender, KeyPressEventArgs e)
  {
    if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
      e.Handled = true;
    if (e.KeyChar != '.' || (sender as TextEdit).Text.IndexOf('.') <= -1)
      return;
    e.Handled = true;
  }

  public static void DblNegTextBoxKeyPress(object sender, KeyPressEventArgs e)
  {
    if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.' && e.KeyChar != '-')
      e.Handled = true;
    if (e.KeyChar == '.' && (sender as TextEdit).Text.IndexOf('.') > -1)
      e.Handled = true;
    if ((e.KeyChar != '-' || (sender as TextEdit).Text.LastIndexOf('-') <= -1) && (e.KeyChar != '-' || !((sender as TextEdit).Text != "")))
      return;
    e.Handled = true;
  }
}
