<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IntegriWISE Web - Complete Fitness-for-Service Assessment Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .nav-menu {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
            cursor: pointer;
        }
        
        .nav-menu a:hover, .nav-menu a.active {
            background: rgba(255,255,255,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page {
            display: none;
        }
        
        .page.active {
            display: block;
        }
        
        .assessment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .assessment-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #1976d2;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        
        .assessment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .card-icon {
            font-size: 1.5rem;
            color: #1976d2;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #1976d2;
        }
        
        .card-description {
            color: #666;
            margin-bottom: 1rem;
            line-height: 1.5;
        }
        
        .btn {
            background: #1976d2;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
            font-size: 0.9rem;
        }
        
        .btn:hover {
            background: #1565c0;
        }
        
        .btn-secondary {
            background: #666;
        }
        
        .btn-secondary:hover {
            background: #555;
        }
        
        .form-container {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 2rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #1976d2;
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
        }
        
        .results-container {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 2rem;
            margin-top: 2rem;
            display: none;
        }
        
        .results-container.show {
            display: block;
        }
        
        .result-item {
            background: white;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 5px;
            border-left: 3px solid #1976d2;
        }
        
        .result-label {
            font-weight: bold;
            color: #1976d2;
        }
        
        .result-value {
            font-size: 1.1rem;
            margin-top: 0.25rem;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .error {
            background: #ffebee;
            color: #c62828;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            border-left: 3px solid #c62828;
        }
        
        .success {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
            border-left: 3px solid #4caf50;
        }
        
        .back-btn {
            margin-bottom: 2rem;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-shield-alt"></i>
                IntegriWISE Web
            </div>
            <nav>
                <ul class="nav-menu">
                    <li><a href="#" onclick="showPage('dashboard')" class="nav-link active"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="#" onclick="showPage('assessments')" class="nav-link"><i class="fas fa-clipboard-check"></i> Assessments</a></li>
                    <li><a href="#" onclick="showPage('about')" class="nav-link"><i class="fas fa-info-circle"></i> About</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="container">
        <!-- Dashboard Page -->
        <div id="dashboard" class="page active">
            <h1><i class="fas fa-tachometer-alt"></i> IntegriWISE Dashboard</h1>
            <p style="color: #666; margin: 1rem 0;">Welcome to the complete Fitness-for-Service assessment platform. Select an assessment type below to begin your analysis.</p>
            
            <div style="background: #e3f2fd; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                <strong>🚀 Real Backend Connected!</strong><br>
                Server: http://localhost:5000<br>
                Status: <span id="server-status">Checking...</span>
            </div>

            <div class="assessment-grid">
                <div class="assessment-card" onclick="showAssessment('asme-b31g')">
                    <div class="card-header">
                        <i class="fas fa-pipe card-icon"></i>
                        <div class="card-title">ASME B31G Assessment</div>
                    </div>
                    <div class="card-description">
                        Original and Modified ASME B31G methods for pipeline metal loss assessment. Real calculation engine with full functionality.
                    </div>
                    <div class="btn">Start Real Assessment</div>
                </div>

                <div class="assessment-card">
                    <div class="card-header">
                        <i class="fas fa-industry card-icon"></i>
                        <div class="card-title">DNV F101 Part B</div>
                    </div>
                    <div class="card-description">
                        Pipeline assessment according to DNV F101 Part B standards. Coming soon with full calculation engine.
                    </div>
                    <div class="btn btn-secondary">Coming Soon</div>
                </div>

                <div class="assessment-card">
                    <div class="card-header">
                        <i class="fas fa-shield-alt card-icon"></i>
                        <div class="card-title">API 579 FFS</div>
                    </div>
                    <div class="card-description">
                        Complete API 579 Fitness-for-Service assessment. Full implementation in development.
                    </div>
                    <div class="btn btn-secondary">Coming Soon</div>
                </div>
            </div>
        </div>

        <!-- Assessments Page -->
        <div id="assessments" class="page">
            <h1><i class="fas fa-clipboard-check"></i> Assessment Types</h1>
            <p>Choose from our comprehensive suite of fitness-for-service assessment methods:</p>
            
            <div class="assessment-grid">
                <div class="assessment-card" onclick="showAssessment('asme-b31g')">
                    <div class="card-header">
                        <i class="fas fa-pipe card-icon"></i>
                        <div class="card-title">ASME B31G Local Metal Loss</div>
                    </div>
                    <div class="card-description">
                        Complete implementation with real calculation engine. Both Original and Modified methods available.
                    </div>
                    <div class="btn">Launch Assessment</div>
                </div>
            </div>
        </div>

        <!-- About Page -->
        <div id="about" class="page">
            <h1><i class="fas fa-info-circle"></i> About IntegriWISE Web</h1>
            <div class="form-container">
                <h2>Complete Fitness-for-Service Assessment Platform</h2>
                <p style="line-height: 1.6; margin: 1rem 0;">
                    IntegriWISE Web is a comprehensive web-based platform for fitness-for-service assessments, 
                    featuring real calculation engines and professional-grade analysis tools.
                </p>
                
                <h3 style="margin-top: 2rem;">Features:</h3>
                <ul style="margin: 1rem 0; padding-left: 2rem;">
                    <li>Real ASME B31G calculation engine (Original & Modified)</li>
                    <li>Professional web interface</li>
                    <li>Complete API backend</li>
                    <li>Comprehensive input validation</li>
                    <li>Detailed calculation results</li>
                    <li>Export capabilities</li>
                </ul>
                
                <h3 style="margin-top: 2rem;">Technical Stack:</h3>
                <ul style="margin: 1rem 0; padding-left: 2rem;">
                    <li>Backend: Node.js + TypeScript + Express</li>
                    <li>Frontend: Modern HTML5 + JavaScript</li>
                    <li>Real-time API communication</li>
                    <li>Professional UI/UX design</li>
                </ul>
                
                <div style="margin-top: 2rem; padding: 1rem; background: #f8f9fa; border-radius: 5px;">
                    <strong>✨ Developed by Hassan Hany</strong><br>
                    🏢 Scimitar Production Egypt Ltd<br>
                    🔗 <a href="https://linkedin.com/in/whereishassan" target="_blank">LinkedIn Profile</a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        const API_BASE = 'http://localhost:5000/api/v1';
        let currentPage = 'dashboard';

        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // Show selected page
            document.getElementById(pageId).classList.add('active');
            
            // Update navigation
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            event.target.classList.add('active');
            
            currentPage = pageId;
        }

        // Check server status
        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE.replace('/api/v1', '')}/health`);
                const data = await response.json();
                document.getElementById('server-status').innerHTML = 
                    '<span style="color: #4caf50;">✓ Online</span>';
            } catch (error) {
                document.getElementById('server-status').innerHTML = 
                    '<span style="color: #f44336;">✗ Offline</span>';
            }
        }

        // Show assessment form
        function showAssessment(type) {
            if (type === 'asme-b31g') {
                createASMEB31GForm();
            } else {
                alert('This assessment type is coming soon!');
            }
        }

        // Create ASME B31G assessment form
        function createASMEB31GForm() {
            const container = document.querySelector('.container');

            // Hide all pages
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // Create form HTML
            const formHTML = `
                <div id="asme-b31g-form" class="page active">
                    <div class="back-btn">
                        <button class="btn btn-secondary" onclick="showPage('dashboard')">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </button>
                    </div>

                    <h1><i class="fas fa-pipe"></i> ASME B31G Local Metal Loss Assessment</h1>
                    <p style="color: #666; margin: 1rem 0;">
                        Complete ASME B31G assessment with real calculation engine. Enter your parameters below.
                    </p>

                    <div class="form-container">
                        <form id="asmeb31g-form" onsubmit="submitASMEB31G(event)">
                            <div class="form-grid">
                                <div>
                                    <h3 style="margin-bottom: 1rem; color: #1976d2;">Geometry Parameters</h3>
                                    <div class="form-group">
                                        <label for="nominalOutsideDiameter">Nominal Outside Diameter (mm)</label>
                                        <input type="number" id="nominalOutsideDiameter" name="nominalOutsideDiameter"
                                               value="508.0" step="0.1" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="nominalThickness">Nominal Thickness (mm)</label>
                                        <input type="number" id="nominalThickness" name="nominalThickness"
                                               value="12.7" step="0.1" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="longitudinalExtent">Longitudinal Extent (mm)</label>
                                        <input type="number" id="longitudinalExtent" name="longitudinalExtent"
                                               value="200.0" step="0.1" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="depthOfCorrodedArea">Depth of Corroded Area (mm)</label>
                                        <input type="number" id="depthOfCorrodedArea" name="depthOfCorrodedArea"
                                               value="6.35" step="0.1" required>
                                    </div>
                                </div>

                                <div>
                                    <h3 style="margin-bottom: 1rem; color: #1976d2;">Operating Conditions</h3>
                                    <div class="form-group">
                                        <label for="designPressure">Design Pressure (MPa)</label>
                                        <input type="number" id="designPressure" name="designPressure"
                                               value="7.0" step="0.1" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="operatingPressure">Operating Pressure (MPa)</label>
                                        <input type="number" id="operatingPressure" name="operatingPressure"
                                               value="5.5" step="0.1" required>
                                    </div>

                                    <h3 style="margin: 2rem 0 1rem; color: #1976d2;">Material Properties</h3>
                                    <div class="form-group">
                                        <label for="specifiedMinimumYieldStrength">SMYS (MPa)</label>
                                        <input type="number" id="specifiedMinimumYieldStrength" name="specifiedMinimumYieldStrength"
                                               value="358.0" step="0.1" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="specifiedMinimumTensileStrength">SMTS (MPa)</label>
                                        <input type="number" id="specifiedMinimumTensileStrength" name="specifiedMinimumTensileStrength"
                                               value="455.0" step="0.1" required>
                                    </div>
                                </div>

                                <div>
                                    <h3 style="margin-bottom: 1rem; color: #1976d2;">Assessment Options</h3>
                                    <div class="form-group">
                                        <label for="assessmentLevel">Assessment Level</label>
                                        <select id="assessmentLevel" name="assessmentLevel" required>
                                            <option value="level1">Level 1</option>
                                            <option value="level2">Level 2</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="flowStressOption">Flow Stress Option</label>
                                        <select id="flowStressOption" name="flowStressOption" required>
                                            <option value="original">Original ASME B31G</option>
                                            <option value="modified" selected>Modified ASME B31G</option>
                                        </select>
                                    </div>

                                    <h3 style="margin: 2rem 0 1rem; color: #1976d2;">Optional Parameters</h3>
                                    <div class="form-group">
                                        <label for="weldJointEfficiency">Weld Joint Efficiency</label>
                                        <input type="number" id="weldJointEfficiency" name="weldJointEfficiency"
                                               value="1.0" step="0.01" min="0" max="1">
                                    </div>
                                    <div class="form-group">
                                        <label for="temperatureFactor">Temperature Factor</label>
                                        <input type="number" id="temperatureFactor" name="temperatureFactor"
                                               value="1.0" step="0.01" min="0">
                                    </div>
                                    <div class="form-group">
                                        <label for="safetyFactor">Safety Factor</label>
                                        <input type="number" id="safetyFactor" name="safetyFactor"
                                               value="1.0" step="0.01" min="0">
                                    </div>
                                </div>
                            </div>

                            <div style="text-align: center; margin-top: 2rem;">
                                <button type="submit" class="btn" style="font-size: 1.1rem; padding: 1rem 2rem;">
                                    <i class="fas fa-calculator"></i> Calculate ASME B31G Assessment
                                </button>
                            </div>
                        </form>
                    </div>

                    <div id="calculation-results" class="results-container">
                        <h2><i class="fas fa-chart-bar"></i> Calculation Results</h2>
                        <div id="results-content"></div>
                    </div>
                </div>
            `;

            // Remove existing form if it exists
            const existingForm = document.getElementById('asme-b31g-form');
            if (existingForm) {
                existingForm.remove();
            }

            // Add form to container
            container.insertAdjacentHTML('beforeend', formHTML);
        }

        // Submit ASME B31G calculation
        async function submitASMEB31G(event) {
            event.preventDefault();

            const form = document.getElementById('asmeb31g-form');
            const formData = new FormData(form);
            const resultsContainer = document.getElementById('calculation-results');
            const resultsContent = document.getElementById('results-content');

            // Convert form data to object
            const data = {};
            for (let [key, value] of formData.entries()) {
                if (key === 'assessmentLevel' || key === 'flowStressOption') {
                    data[key] = value;
                } else {
                    data[key] = parseFloat(value);
                }
            }

            // Show loading
            resultsContent.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Calculating...</div>';
            resultsContainer.classList.add('show');

            try {
                const response = await fetch(`${API_BASE}/calculations/asme-b31g`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    displayResults(result.data.results);
                } else {
                    throw new Error(result.error || 'Calculation failed');
                }

            } catch (error) {
                console.error('Calculation error:', error);
                resultsContent.innerHTML = `
                    <div class="error">
                        <strong>Calculation Error:</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }

        // Display calculation results
        function displayResults(results) {
            const resultsContent = document.getElementById('results-content');

            const resultsHTML = `
                <div class="success">
                    <strong>✓ Calculation completed successfully!</strong>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                    <div class="result-item">
                        <div class="result-label">Safe Working Pressure (Original)</div>
                        <div class="result-value">${results.SafeWorkingPressureOriginal?.toFixed(3) || 'N/A'} MPa</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">Safe Working Pressure (Modified)</div>
                        <div class="result-value">${results.SafeWorkingPressureModified?.toFixed(3) || 'N/A'} MPa</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">Failure Stress (Original L1)</div>
                        <div class="result-value">${results.SFailureOriginalL1?.toFixed(3) || 'N/A'} MPa</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">Failure Stress (Modified L1)</div>
                        <div class="result-value">${results.SFailureModifiedL1?.toFixed(3) || 'N/A'} MPa</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">Flow Stress (Original)</div>
                        <div class="result-value">${results.SflowOriginal?.toFixed(3) || 'N/A'} MPa</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">Flow Stress (Modified)</div>
                        <div class="result-value">${results.SflowModified?.toFixed(3) || 'N/A'} MPa</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">Folias Factor (M Original)</div>
                        <div class="result-value">${results.MOriginal?.toFixed(3) || 'N/A'}</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">Folias Factor (M Modified)</div>
                        <div class="result-value">${results.MModified?.toFixed(3) || 'N/A'}</div>
                    </div>
                </div>

                <div style="margin-top: 2rem; text-align: center;">
                    <button class="btn" onclick="exportResults()">
                        <i class="fas fa-download"></i> Export Results
                    </button>
                </div>
            `;

            resultsContent.innerHTML = resultsHTML;
        }

        // Export results (placeholder)
        function exportResults() {
            alert('Export functionality will be implemented in the full version!');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkServerStatus();
            setInterval(checkServerStatus, 30000); // Check every 30 seconds

            console.log('🚀 IntegriWISE Web Application Loaded!');
            console.log('📡 Backend API:', API_BASE);
        });
    </script>
</body>
</html>
