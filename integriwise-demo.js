const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

// Complete IntegriWISE Web Application HTML
const integriWiseApp = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IntegriWISE Web - Complete Fitness-for-Service Assessment Platform</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .nav-menu {
            display: flex;
            gap: 2rem;
            list-style: none;
        }
        
        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-menu a:hover {
            background: rgba(255,255,255,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .sidebar {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .main-content {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .assessment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .assessment-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #1976d2;
            transition: transform 0.3s, box-shadow 0.3s;
        }
        
        .assessment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .card-icon {
            font-size: 1.5rem;
            color: #1976d2;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #1976d2;
        }
        
        .card-description {
            color: #666;
            margin-bottom: 1rem;
            line-height: 1.5;
        }
        
        .btn {
            background: #1976d2;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
            font-size: 0.9rem;
        }
        
        .btn:hover {
            background: #1565c0;
        }
        
        .btn-secondary {
            background: #666;
        }
        
        .btn-secondary:hover {
            background: #555;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-online { background: #4caf50; }
        .status-warning { background: #ff9800; }
        .status-offline { background: #f44336; }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #1976d2;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 2rem;
            margin-top: 3rem;
        }
        
        .footer a {
            color: #1976d2;
            text-decoration: none;
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-shield-alt"></i>
                IntegriWISE Web
            </div>
            <nav>
                <ul class="nav-menu">
                    <li><a href="#dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="#assessments"><i class="fas fa-clipboard-check"></i> Assessments</a></li>
                    <li><a href="#equipment"><i class="fas fa-cogs"></i> Equipment</a></li>
                    <li><a href="#reports"><i class="fas fa-chart-bar"></i> Reports</a></li>
                    <li><a href="#settings"><i class="fas fa-user-cog"></i> Settings</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="quick-stats">
            <div class="stat-card">
                <div class="stat-number">247</div>
                <div class="stat-label">Active Assessments</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">1,834</div>
                <div class="stat-label">Equipment Items</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">98.7%</div>
                <div class="stat-label">System Uptime</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">156</div>
                <div class="stat-label">Reports Generated</div>
            </div>
        </div>

        <div class="dashboard">
            <div class="sidebar">
                <h3><i class="fas fa-server"></i> System Status</h3>
                <div style="margin: 1rem 0;">
                    <div><span class="status-indicator status-online"></span>API Server: Online</div>
                    <div><span class="status-indicator status-online"></span>Database: Connected</div>
                    <div><span class="status-indicator status-warning"></span>Backup: Scheduled</div>
                    <div><span class="status-indicator status-online"></span>Authentication: Active</div>
                </div>
                
                <h3 style="margin-top: 2rem;"><i class="fas fa-bell"></i> Recent Activity</h3>
                <div style="margin: 1rem 0; font-size: 0.9rem; color: #666;">
                    <div style="margin: 0.5rem 0;">✓ ASME B31G assessment completed</div>
                    <div style="margin: 0.5rem 0;">📊 Monthly report generated</div>
                    <div style="margin: 0.5rem 0;">🔧 Equipment data updated</div>
                    <div style="margin: 0.5rem 0;">👤 New user registered</div>
                </div>
            </div>

            <div class="main-content">
                <h2><i class="fas fa-tachometer-alt"></i> IntegriWISE Dashboard</h2>
                <p style="color: #666; margin: 1rem 0;">Welcome to the complete Fitness-for-Service assessment platform. Select an assessment type below to begin your analysis.</p>
                
                <div style="background: #e3f2fd; padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                    <strong>🎯 Demo Credentials:</strong><br>
                    Email: <EMAIL><br>
                    Password: admin123
                </div>
            </div>
        </div>

        <div class="assessment-grid">
            <div class="assessment-card">
                <div class="card-header">
                    <i class="fas fa-pipe card-icon"></i>
                    <div class="card-title">ASME B31G Assessment</div>
                </div>
                <div class="card-description">
                    Original and Modified ASME B31G methods for pipeline metal loss assessment. Includes groove-like flaws and general metal loss evaluation.
                </div>
                <a href="#" class="btn">Start Assessment</a>
                <a href="#" class="btn btn-secondary" style="margin-left: 0.5rem;">View Examples</a>
            </div>

            <div class="assessment-card">
                <div class="card-header">
                    <i class="fas fa-industry card-icon"></i>
                    <div class="card-title">DNV F101 Part B</div>
                </div>
                <div class="card-description">
                    Pipeline assessment according to DNV F101 Part B standards. Comprehensive analysis for offshore and onshore pipeline systems.
                </div>
                <a href="#" class="btn">Start Assessment</a>
                <a href="#" class="btn btn-secondary" style="margin-left: 0.5rem;">Documentation</a>
            </div>

            <div class="assessment-card">
                <div class="card-header">
                    <i class="fas fa-shield-alt card-icon"></i>
                    <div class="card-title">API 579 FFS</div>
                </div>
                <div class="card-description">
                    Complete API 579 Fitness-for-Service assessment including all parts and assessment levels for pressure vessels and piping.
                </div>
                <a href="#" class="btn">Start Assessment</a>
                <a href="#" class="btn btn-secondary" style="margin-left: 0.5rem;">Guidelines</a>
            </div>

            <div class="assessment-card">
                <div class="card-header">
                    <i class="fas fa-exclamation-triangle card-icon"></i>
                    <div class="card-title">Thin Area Assessment</div>
                </div>
                <div class="card-description">
                    Specialized assessment for thin areas, pitting, and localized metal loss. Includes statistical analysis and remaining life calculations.
                </div>
                <a href="#" class="btn">Start Assessment</a>
                <a href="#" class="btn btn-secondary" style="margin-left: 0.5rem;">Methodology</a>
            </div>

            <div class="assessment-card">
                <div class="card-header">
                    <i class="fas fa-thermometer-half card-icon"></i>
                    <div class="card-title">Brittle Fracture</div>
                </div>
                <div class="card-description">
                    Brittle fracture assessment and MAWP calculations according to various design codes including ASME B31.3, B31.4, and B31.8.
                </div>
                <a href="#" class="btn">Start Assessment</a>
                <a href="#" class="btn btn-secondary" style="margin-left: 0.5rem;">Standards</a>
            </div>

            <div class="assessment-card">
                <div class="card-header">
                    <i class="fas fa-circle-notch card-icon"></i>
                    <div class="card-title">Dent & Dent-Gouge</div>
                </div>
                <div class="card-description">
                    Comprehensive dent and dent-gouge assessment according to ASME B31.4 and B31.8 standards with advanced analysis capabilities.
                </div>
                <a href="#" class="btn">Start Assessment</a>
                <a href="#" class="btn btn-secondary" style="margin-left: 0.5rem;">Examples</a>
            </div>

            <div class="assessment-card">
                <div class="card-header">
                    <i class="fas fa-layer-group card-icon"></i>
                    <div class="card-title">HIC & Lamination</div>
                </div>
                <div class="card-description">
                    Hydrogen Induced Cracking (HIC), lamination, and blister assessment with detailed crack analysis and fitness evaluation.
                </div>
                <a href="#" class="btn">Start Assessment</a>
                <a href="#" class="btn btn-secondary" style="margin-left: 0.5rem;">Research</a>
            </div>

            <div class="assessment-card">
                <div class="card-header">
                    <i class="fas fa-database card-icon"></i>
                    <div class="card-title">Material Database</div>
                </div>
                <div class="card-description">
                    Comprehensive material property database with support for ASME, API, PD 5500, and other international standards.
                </div>
                <a href="#" class="btn">Browse Materials</a>
                <a href="#" class="btn btn-secondary" style="margin-left: 0.5rem;">Add Custom</a>
            </div>

            <div class="assessment-card">
                <div class="card-header">
                    <i class="fas fa-chart-line card-icon"></i>
                    <div class="card-title">Advanced Analytics</div>
                </div>
                <div class="card-description">
                    Statistical analysis, trend monitoring, and predictive maintenance capabilities with comprehensive reporting features.
                </div>
                <a href="#" class="btn">View Analytics</a>
                <a href="#" class="btn btn-secondary" style="margin-left: 0.5rem;">Configure</a>
            </div>
        </div>
    </div>

    <footer class="footer">
        <p><strong>IntegriWISE Web</strong> - Complete Fitness-for-Service Assessment Platform</p>
        <p>✨ Developed by <a href="https://linkedin.com/in/whereishassan" target="_blank">Hassan Hany</a> | 🏢 Scimitar Production Egypt Ltd</p>
        <p style="margin-top: 1rem; font-size: 0.9rem;">
            Supporting ASME B31.3/B31.4/B31.8, ASME VIII, API 579, API 620/650, PD 5500, and DNV F101 standards
        </p>
    </footer>

    <script>
        // Simple interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers for assessment cards
            const assessmentCards = document.querySelectorAll('.assessment-card');
            assessmentCards.forEach(card => {
                card.addEventListener('click', function(e) {
                    if (e.target.classList.contains('btn')) {
                        e.preventDefault();
                        const assessmentType = this.querySelector('.card-title').textContent;
                        alert('Starting ' + assessmentType + ' assessment...\\n\\nThis would open the assessment wizard in the full application.');
                    }
                });
            });

            // Update stats periodically (demo)
            setInterval(() => {
                const statNumbers = document.querySelectorAll('.stat-number');
                statNumbers.forEach(stat => {
                    if (stat.textContent.includes('%')) return;
                    const current = parseInt(stat.textContent.replace(',', ''));
                    const change = Math.floor(Math.random() * 3) - 1; // -1, 0, or 1
                    const newValue = Math.max(0, current + change);
                    stat.textContent = newValue.toLocaleString();
                });
            }, 5000);

            console.log('🚀 IntegriWISE Web Application Loaded Successfully!');
            console.log('📡 Server: http://localhost:${PORT}');
            console.log('🎯 Demo Login: <EMAIL> / admin123');
        });
    </script>
</body>
</html>
`;

const server = http.createServer((req, res) => {
    const url = req.url;
    const method = req.method;

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // Health check endpoint
    if (url === '/health' || url === '/api/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'OK',
            message: 'IntegriWISE Web Server is running!',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            server: 'Node.js HTTP Server',
            port: PORT,
            features: [
                'ASME B31G Assessment',
                'DNV F101 Part B',
                'API 579 FFS',
                'Thin Area Assessment',
                'Brittle Fracture Analysis',
                'Dent & Dent-Gouge Assessment',
                'HIC & Lamination Analysis',
                'Material Database',
                'Advanced Analytics'
            ]
        }, null, 2));
        return;
    }

    // API endpoints
    if (url.startsWith('/api/')) {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        
        if (url === '/api/auth/login') {
            res.end(JSON.stringify({
                success: true,
                data: {
                    user: {
                        id: '1',
                        username: 'admin',
                        email: '<EMAIL>',
                        first_name: 'System',
                        last_name: 'Administrator',
                        role: 'admin',
                        permissions: ['read', 'write', 'admin']
                    },
                    tokens: {
                        accessToken: 'demo-token-123',
                        refreshToken: 'demo-refresh-token-456'
                    }
                },
                message: 'Login successful'
            }, null, 2));
        } else {
            res.end(JSON.stringify({
                success: true,
                data: {
                    endpoint: url,
                    message: 'IntegriWISE Web API is working!',
                    availableEndpoints: [
                        '/api/auth/login',
                        '/api/assessments',
                        '/api/equipment',
                        '/api/materials',
                        '/api/reports',
                        '/api/analytics'
                    ]
                }
            }, null, 2));
        }
        return;
    }

    // Serve main application
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(integriWiseApp);
});

server.listen(PORT, () => {
    console.log('🚀 IntegriWISE Web Application Started!');
    console.log('=====================================');
    console.log('📡 Server running on http://localhost:' + PORT);
    console.log('🏥 Health check: http://localhost:' + PORT + '/health');
    console.log('📚 API Base: http://localhost:' + PORT + '/api');
    console.log('');
    console.log('🎯 Demo Login Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('');
    console.log('✨ Features Available:');
    console.log('   • Complete Dashboard Interface');
    console.log('   • 9 Assessment Types');
    console.log('   • Real-time Statistics');
    console.log('   • Professional UI/UX');
    console.log('   • Responsive Design');
    console.log('   • API Endpoints');
    console.log('');
    console.log('✨ Courtesy of Hassan Hany - Scimitar Production Egypt Ltd');
    console.log('=====================================');
});
