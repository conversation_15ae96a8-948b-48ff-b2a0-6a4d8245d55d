const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

const server = http.createServer((req, res) => {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // Serve the main application
    if (req.url === '/' || req.url === '/index.html') {
        fs.readFile('integriwise-full.html', 'utf8', (err, data) => {
            if (err) {
                res.writeHead(500, { 'Content-Type': 'text/plain' });
                res.end('Error loading application');
                return;
            }
            
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(data);
        });
        return;
    }

    // 404 for other routes
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Not Found');
});

server.listen(PORT, () => {
    console.log('🚀 IntegriWISE Web Application Server Started!');
    console.log('=====================================');
    console.log(`📡 Frontend: http://localhost:${PORT}`);
    console.log('🔧 Backend: http://localhost:5000');
    console.log('');
    console.log('✨ Features:');
    console.log('   • Complete ASME B31G Assessment');
    console.log('   • Real Calculation Engine');
    console.log('   • Professional Interface');
    console.log('   • Live API Integration');
    console.log('');
    console.log('🎯 Ready to use - Open your browser!');
    console.log('=====================================');
});
