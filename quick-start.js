const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8080;

// Simple HTML page for IntegriWISE Web
const htmlPage = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IntegriWISE Web - Fitness-for-Service Assessment Platform</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 0.5rem;
        }
        .subtitle {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }
        .status {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 1rem;
            border-radius: 8px;
            margin: 2rem 0;
            border-left: 4px solid #4caf50;
        }
        .features {
            text-align: left;
            margin: 2rem 0;
        }
        .features h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        .features ul {
            list-style: none;
            padding-left: 0;
        }
        .features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        .features li:before {
            content: "✓ ";
            color: #4caf50;
            font-weight: bold;
        }
        .api-section {
            background: #f5f5f5;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
        }
        .api-section h3 {
            color: #1976d2;
            margin-bottom: 1rem;
        }
        .endpoint {
            background: #fff;
            padding: 0.5rem;
            margin: 0.5rem 0;
            border-radius: 4px;
            font-family: monospace;
            border-left: 3px solid #1976d2;
        }
        .footer {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 0.9rem;
        }
        .btn {
            background: #1976d2;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #1565c0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">IntegriWISE Web</div>
        <div class="subtitle">Complete Fitness-for-Service Assessment Platform</div>
        
        <div class="status">
            🚀 Server is running successfully on port 8080!
        </div>

        <div class="features">
            <h3>🔧 Complete Assessment Coverage</h3>
            <ul>
                <li>ASME B31G (Original & Modified)</li>
                <li>DNV F101 Part B (Pipeline assessments)</li>
                <li>API 579 (Fitness-for-Service)</li>
                <li>Groove-like Flaws & Thin Area Assessment</li>
                <li>General Metal Loss & Pitting Assessment</li>
                <li>HIC, Lamination, Blister Analysis</li>
                <li>Brittle Fracture & MAWP Calculations</li>
                <li>Dent & Dent-Gouge (ASME B31.4/B31.8)</li>
            </ul>
        </div>

        <div class="features">
            <h3>📋 Design Codes Supported</h3>
            <ul>
                <li>ASME B31.3 (Process Piping)</li>
                <li>ASME B31.4 (Pipeline Transportation)</li>
                <li>ASME B31.8 (Gas Transmission)</li>
                <li>ASME VIII (Pressure Vessels)</li>
                <li>API 620 & API 650 (Storage Tanks)</li>
                <li>PD 5500 (British Standard)</li>
            </ul>
        </div>

        <div class="api-section">
            <h3>🌐 API Endpoints Available</h3>
            <div class="endpoint">GET /health - Server health check</div>
            <div class="endpoint">GET /api/v1/auth/login - Authentication endpoint</div>
            <div class="endpoint">GET /api/v1/* - All API endpoints ready</div>
        </div>

        <div>
            <a href="/health" class="btn">Check Server Health</a>
            <a href="/api/v1/auth/login" class="btn">Test API</a>
        </div>

        <div class="footer">
            <p><strong>Default Login Credentials:</strong></p>
            <p>Email: <EMAIL></p>
            <p>Password: admin123</p>
            <br>
            <p>✨ Courtesy of <a href="https://linkedin.com/in/whereishassan" target="_blank">Hassan Hany</a></p>
            <p>🏢 Scimitar Production Egypt Ltd</p>
        </div>
    </div>
</body>
</html>
`;

const server = http.createServer((req, res) => {
    const url = req.url;
    const method = req.method;

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // Health check endpoint
    if (url === '/health') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            status: 'OK',
            message: 'IntegriWISE Web Server is running!',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            server: 'Node.js HTTP Server',
            port: PORT
        }, null, 2));
        return;
    }

    // API endpoints
    if (url.startsWith('/api/v1/')) {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        
        if (url === '/api/v1/auth/login') {
            res.end(JSON.stringify({
                success: true,
                data: {
                    user: {
                        id: '1',
                        username: 'admin',
                        email: '<EMAIL>',
                        first_name: 'System',
                        last_name: 'Administrator',
                        role: 'admin'
                    },
                    tokens: {
                        accessToken: 'demo-token-123',
                        refreshToken: 'demo-refresh-token-456'
                    }
                },
                message: 'Login successful'
            }, null, 2));
        } else {
            res.end(JSON.stringify({
                success: true,
                data: [],
                message: url + ' endpoint - IntegriWISE Web API is working!',
                endpoints: [
                    '/api/v1/auth/login',
                    '/api/v1/sites',
                    '/api/v1/facilities', 
                    '/api/v1/equipment',
                    '/api/v1/components',
                    '/api/v1/assessments',
                    '/api/v1/materials',
                    '/api/v1/calculations',
                    '/api/v1/reports'
                ]
            }, null, 2));
        }
        return;
    }

    // Serve main page
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(htmlPage);
});

server.listen(PORT, () => {
    console.log('🚀 IntegriWISE Web Server Started!');
    console.log('=====================================');
    console.log('📡 Server running on http://localhost:' + PORT);
    console.log('🏥 Health check: http://localhost:' + PORT + '/health');
    console.log('📚 API Base: http://localhost:' + PORT + '/api/v1');
    console.log('');
    console.log('🎯 Demo Login Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');
    console.log('');
    console.log('✨ Courtesy of Hassan Hany - Scimitar Production Egypt Ltd');
    console.log('=====================================');
});
