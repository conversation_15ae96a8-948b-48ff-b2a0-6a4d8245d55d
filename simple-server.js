const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'client/build')));

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'IntegriWISE Web Server is running!',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API Routes
app.get('/api/v1/auth/login', (req, res) => {
  res.json({
    success: true,
    data: {
      user: {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        first_name: 'System',
        last_name: 'Administrator',
        role: 'admin'
      },
      tokens: {
        accessToken: 'demo-token-123',
        refreshToken: 'demo-refresh-token-456'
      }
    },
    message: 'Login successful'
  });
});

app.get('/api/v1/*', (req, res) => {
  res.json({
    success: true,
    data: [],
    message: `${req.path} endpoint - IntegriWISE Web API is working!`
  });
});

// Serve React app
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'client/build', 'index.html'));
});

app.listen(PORT, () => {
  console.log('🚀 IntegriWISE Web Server Started!');
  console.log(`📡 Server running on http://localhost:${PORT}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`📚 API Base: http://localhost:${PORT}/api/v1`);
  console.log('');
  console.log('🎯 Demo Login Credentials:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: admin123');
  console.log('');
  console.log('✨ Courtesy of Hassan Hany - Scimitar Production Egypt Ltd');
});
