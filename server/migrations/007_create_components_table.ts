import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('components', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('equipment_id').notNullable();
    table.string('name', 255).notNullable();
    table.text('description').nullable();
    table.enum('component_type', [
      'shell',
      'head',
      'nozzle',
      'pipe_section',
      'elbow',
      'reducer',
      'tee',
      'flange',
      'other'
    ]).notNullable();
    table.enum('geometry_type', [
      'cylindrical_shell',
      'spherical_shell',
      'elliptical_head',
      'hemispherical_head',
      'torispherical_head',
      'flat_head',
      'conical_section'
    ]).notNullable();
    table.decimal('nominal_thickness', 8, 3).notNullable();
    table.decimal('nominal_diameter', 10, 3).nullable();
    table.decimal('length', 10, 3).nullable();
    table.uuid('material_id').notNullable();
    table.decimal('weld_joint_efficiency', 5, 3).nullable();
    table.decimal('corrosion_allowance_internal', 8, 3).nullable();
    table.decimal('corrosion_allowance_external', 8, 3).nullable();
    table.boolean('is_active').notNullable().defaultTo(true);
    table.uuid('created_by').notNullable();
    table.timestamps(true, true);
    
    // Foreign keys
    table.foreign('equipment_id').references('id').inTable('equipment').onDelete('CASCADE');
    table.foreign('material_id').references('id').inTable('materials').onDelete('RESTRICT');
    table.foreign('created_by').references('id').inTable('users').onDelete('RESTRICT');
    
    // Indexes
    table.index(['equipment_id']);
    table.index(['component_type']);
    table.index(['geometry_type']);
    table.index(['material_id']);
    table.index(['is_active']);
    table.index(['created_by']);
    
    // Unique constraint within equipment
    table.unique(['equipment_id', 'name']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('components');
}
