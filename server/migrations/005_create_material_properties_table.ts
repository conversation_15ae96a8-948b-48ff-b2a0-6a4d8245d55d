import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('material_properties', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('material_id').notNullable();
    table.decimal('temperature', 8, 2).notNullable();
    table.decimal('yield_strength', 10, 2).notNullable();
    table.decimal('tensile_strength', 10, 2).notNullable();
    table.decimal('allowable_stress', 10, 2).nullable();
    table.timestamps(true, true);
    
    // Foreign keys
    table.foreign('material_id').references('id').inTable('materials').onDelete('CASCADE');
    
    // Indexes
    table.index(['material_id']);
    table.index(['temperature']);
    
    // Unique constraint
    table.unique(['material_id', 'temperature']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('material_properties');
}
