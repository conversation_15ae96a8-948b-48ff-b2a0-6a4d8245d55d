import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('facilities', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('site_id').notNullable();
    table.string('name', 255).notNullable();
    table.text('description').nullable();
    table.enum('facility_type', [
      'refinery',
      'chemical_plant',
      'power_plant',
      'offshore_platform',
      'pipeline',
      'storage_terminal',
      'other'
    ]).notNullable();
    table.boolean('is_active').notNullable().defaultTo(true);
    table.uuid('created_by').notNullable();
    table.timestamps(true, true);
    
    // Foreign keys
    table.foreign('site_id').references('id').inTable('sites').onDelete('CASCADE');
    table.foreign('created_by').references('id').inTable('users').onDelete('RESTRICT');
    
    // Indexes
    table.index(['site_id']);
    table.index(['name']);
    table.index(['facility_type']);
    table.index(['is_active']);
    table.index(['created_by']);
    
    // Unique constraint within site
    table.unique(['site_id', 'name']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('facilities');
}
