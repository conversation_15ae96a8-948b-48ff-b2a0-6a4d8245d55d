import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('equipment', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('facility_id').notNullable();
    table.string('tag_number', 100).notNullable();
    table.string('name', 255).notNullable();
    table.text('description').nullable();
    table.enum('equipment_type', [
      'pressure_vessel',
      'storage_tank',
      'pipeline',
      'heat_exchanger',
      'reactor',
      'column',
      'boiler',
      'other'
    ]).notNullable();
    table.enum('design_code', [
      'ASME_B31_3',
      'ASME_B31_4',
      'ASME_B31_8',
      'ASME_VIII',
      'API_620',
      'API_650',
      'PD_5500'
    ]).notNullable();
    table.decimal('design_pressure', 10, 3).notNullable();
    table.decimal('design_temperature', 8, 2).notNullable();
    table.decimal('operating_pressure', 10, 3).notNullable();
    table.decimal('operating_temperature', 8, 2).notNullable();
    table.date('installation_date').nullable();
    table.date('last_inspection_date').nullable();
    table.date('next_inspection_date').nullable();
    table.boolean('is_active').notNullable().defaultTo(true);
    table.uuid('created_by').notNullable();
    table.timestamps(true, true);
    
    // Foreign keys
    table.foreign('facility_id').references('id').inTable('facilities').onDelete('CASCADE');
    table.foreign('created_by').references('id').inTable('users').onDelete('RESTRICT');
    
    // Indexes
    table.index(['facility_id']);
    table.index(['tag_number']);
    table.index(['equipment_type']);
    table.index(['design_code']);
    table.index(['is_active']);
    table.index(['created_by']);
    table.index(['next_inspection_date']);
    
    // Unique constraint
    table.unique(['facility_id', 'tag_number']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('equipment');
}
