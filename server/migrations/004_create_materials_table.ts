import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('materials', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name', 255).notNullable();
    table.string('specification', 100).notNullable();
    table.string('grade', 100).notNullable();
    table.enum('material_type', [
      'carbon_steel',
      'stainless_steel',
      'alloy_steel',
      'aluminum',
      'copper',
      'titanium',
      'composite',
      'other'
    ]).notNullable();
    table.decimal('density', 10, 2).nullable();
    table.decimal('elastic_modulus', 12, 2).nullable();
    table.decimal('poisson_ratio', 5, 4).nullable();
    table.decimal('thermal_expansion_coefficient', 15, 10).nullable();
    table.boolean('is_active').notNullable().defaultTo(true);
    table.uuid('created_by').notNullable();
    table.timestamps(true, true);
    
    // Foreign keys
    table.foreign('created_by').references('id').inTable('users').onDelete('RESTRICT');
    
    // Indexes
    table.index(['name']);
    table.index(['specification']);
    table.index(['material_type']);
    table.index(['is_active']);
    table.index(['created_by']);
    
    // Unique constraint
    table.unique(['specification', 'grade']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('materials');
}
