import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('sites', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.string('name', 255).notNullable();
    table.text('description').nullable();
    table.string('location', 255).nullable();
    table.string('contact_person', 255).nullable();
    table.string('contact_email', 255).nullable();
    table.string('contact_phone', 50).nullable();
    table.boolean('is_active').notNullable().defaultTo(true);
    table.uuid('created_by').notNullable();
    table.timestamps(true, true);
    
    // Foreign keys
    table.foreign('created_by').references('id').inTable('users').onDelete('RESTRICT');
    
    // Indexes
    table.index(['name']);
    table.index(['location']);
    table.index(['is_active']);
    table.index(['created_by']);
    
    // Unique constraint
    table.unique(['name']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('sites');
}
