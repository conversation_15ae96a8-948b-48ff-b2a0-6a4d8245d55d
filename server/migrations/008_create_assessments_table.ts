import { Knex } from 'knex';

export async function up(knex: Knex): Promise<void> {
  return knex.schema.createTable('assessments', (table) => {
    table.uuid('id').primary().defaultTo(knex.raw('gen_random_uuid()'));
    table.uuid('component_id').notNullable();
    table.enum('assessment_type', [
      'local_metal_loss_asme_b31g',
      'local_metal_loss_groove_like',
      'local_metal_loss_thin_area',
      'local_metal_loss_dnv_f101',
      'general_metal_loss_thickness_profile',
      'general_metal_loss_thickness_reading',
      'pitting',
      'pitting_l2',
      'hic',
      'lamination',
      'blister',
      'brittle_fracture',
      'membrane_stress',
      'mawp',
      'minimum_thickness',
      'mfh',
      'dent',
      'dent_gouge'
    ]).notNullable();
    table.enum('assessment_level', ['level_1', 'level_2']).notNullable();
    table.enum('design_code', [
      'ASME_B31_3',
      'ASME_B31_4',
      'ASME_B31_8',
      'ASME_VIII',
      'API_620',
      'API_650',
      'PD_5500'
    ]).notNullable();
    table.date('assessment_date').notNullable();
    table.string('assessor_name', 255).notNullable();
    table.enum('status', [
      'draft',
      'in_progress',
      'completed',
      'reviewed',
      'approved',
      'rejected'
    ]).notNullable().defaultTo('draft');
    table.jsonb('input_data').notNullable();
    table.jsonb('calculation_results').nullable();
    table.text('conclusion').nullable();
    table.text('recommendations').nullable();
    table.date('next_assessment_date').nullable();
    table.boolean('is_active').notNullable().defaultTo(true);
    table.uuid('created_by').notNullable();
    table.timestamps(true, true);
    
    // Foreign keys
    table.foreign('component_id').references('id').inTable('components').onDelete('CASCADE');
    table.foreign('created_by').references('id').inTable('users').onDelete('RESTRICT');
    
    // Indexes
    table.index(['component_id']);
    table.index(['assessment_type']);
    table.index(['assessment_level']);
    table.index(['design_code']);
    table.index(['status']);
    table.index(['assessment_date']);
    table.index(['next_assessment_date']);
    table.index(['is_active']);
    table.index(['created_by']);
  });
}

export async function down(knex: Knex): Promise<void> {
  return knex.schema.dropTable('assessments');
}
