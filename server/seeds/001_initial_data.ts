import { Knex } from 'knex';
import bcrypt from 'bcryptjs';

export async function seed(knex: Knex): Promise<void> {
  // Clear existing data
  await knex('assessments').del();
  await knex('components').del();
  await knex('equipment').del();
  await knex('facilities').del();
  await knex('sites').del();
  await knex('material_properties').del();
  await knex('materials').del();
  await knex('users').del();

  // Hash password for admin user
  const hashedPassword = await bcrypt.hash('admin123', 12);

  // Insert users
  const [adminUser] = await knex('users').insert([
    {
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      first_name: 'System',
      last_name: 'Administrator',
      role: 'admin',
      is_active: true,
    },
  ]).returning('*');

  const [engineerUser] = await knex('users').insert([
    {
      username: 'engineer1',
      email: '<EMAIL>',
      password: hashedPassword,
      first_name: '<PERSON>',
      last_name: 'Engineer',
      role: 'engineer',
      is_active: true,
    },
  ]).returning('*');

  // Insert materials
  const materials = await knex('materials').insert([
    {
      name: 'A106 Grade B',
      specification: 'ASTM A106',
      grade: 'Grade B',
      material_type: 'carbon_steel',
      density: 7850,
      elastic_modulus: 200000,
      poisson_ratio: 0.3,
      thermal_expansion_coefficient: 11.7e-6,
      is_active: true,
      created_by: adminUser.id,
    },
    {
      name: 'A516 Grade 70',
      specification: 'ASTM A516',
      grade: 'Grade 70',
      material_type: 'carbon_steel',
      density: 7850,
      elastic_modulus: 200000,
      poisson_ratio: 0.3,
      thermal_expansion_coefficient: 11.7e-6,
      is_active: true,
      created_by: adminUser.id,
    },
    {
      name: 'A312 TP316L',
      specification: 'ASTM A312',
      grade: 'TP316L',
      material_type: 'stainless_steel',
      density: 8000,
      elastic_modulus: 200000,
      poisson_ratio: 0.3,
      thermal_expansion_coefficient: 16.0e-6,
      is_active: true,
      created_by: adminUser.id,
    },
  ]).returning('*');

  // Insert material properties
  await knex('material_properties').insert([
    // A106 Grade B properties
    {
      material_id: materials[0].id,
      temperature: 20,
      yield_strength: 240,
      tensile_strength: 415,
      allowable_stress: 138,
    },
    {
      material_id: materials[0].id,
      temperature: 100,
      yield_strength: 240,
      tensile_strength: 415,
      allowable_stress: 138,
    },
    {
      material_id: materials[0].id,
      temperature: 200,
      yield_strength: 240,
      tensile_strength: 415,
      allowable_stress: 138,
    },
    // A516 Grade 70 properties
    {
      material_id: materials[1].id,
      temperature: 20,
      yield_strength: 260,
      tensile_strength: 485,
      allowable_stress: 138,
    },
    {
      material_id: materials[1].id,
      temperature: 100,
      yield_strength: 260,
      tensile_strength: 485,
      allowable_stress: 138,
    },
    // A312 TP316L properties
    {
      material_id: materials[2].id,
      temperature: 20,
      yield_strength: 205,
      tensile_strength: 515,
      allowable_stress: 138,
    },
    {
      material_id: materials[2].id,
      temperature: 100,
      yield_strength: 170,
      tensile_strength: 515,
      allowable_stress: 138,
    },
  ]);

  // Insert sites
  const sites = await knex('sites').insert([
    {
      name: 'Refinery Complex A',
      description: 'Main refinery complex with multiple processing units',
      location: 'Houston, TX',
      contact_person: 'Jane Smith',
      contact_email: '<EMAIL>',
      contact_phone: '******-0123',
      is_active: true,
      created_by: adminUser.id,
    },
    {
      name: 'Chemical Plant B',
      description: 'Petrochemical processing facility',
      location: 'Baton Rouge, LA',
      contact_person: 'Bob Johnson',
      contact_email: '<EMAIL>',
      contact_phone: '******-0124',
      is_active: true,
      created_by: adminUser.id,
    },
  ]).returning('*');

  // Insert facilities
  const facilities = await knex('facilities').insert([
    {
      site_id: sites[0].id,
      name: 'Crude Distillation Unit',
      description: 'Primary crude oil distillation unit',
      facility_type: 'refinery',
      is_active: true,
      created_by: adminUser.id,
    },
    {
      site_id: sites[0].id,
      name: 'Hydrocracker Unit',
      description: 'Heavy oil hydrocracking unit',
      facility_type: 'refinery',
      is_active: true,
      created_by: adminUser.id,
    },
    {
      site_id: sites[1].id,
      name: 'Ethylene Plant',
      description: 'Ethylene production facility',
      facility_type: 'chemical_plant',
      is_active: true,
      created_by: adminUser.id,
    },
  ]).returning('*');

  // Insert equipment
  const equipment = await knex('equipment').insert([
    {
      facility_id: facilities[0].id,
      tag_number: 'V-101',
      name: 'Crude Distillation Column',
      description: 'Main distillation column for crude oil separation',
      equipment_type: 'column',
      design_code: 'ASME_VIII',
      design_pressure: 2.5,
      design_temperature: 400,
      operating_pressure: 2.0,
      operating_temperature: 350,
      installation_date: new Date('2010-01-15'),
      last_inspection_date: new Date('2023-06-01'),
      next_inspection_date: new Date('2025-06-01'),
      is_active: true,
      created_by: adminUser.id,
    },
    {
      facility_id: facilities[0].id,
      tag_number: 'E-102',
      name: 'Crude Preheater',
      description: 'Heat exchanger for crude oil preheating',
      equipment_type: 'heat_exchanger',
      design_code: 'ASME_VIII',
      design_pressure: 4.0,
      design_temperature: 450,
      operating_pressure: 3.5,
      operating_temperature: 400,
      installation_date: new Date('2010-01-15'),
      last_inspection_date: new Date('2023-06-01'),
      next_inspection_date: new Date('2025-06-01'),
      is_active: true,
      created_by: adminUser.id,
    },
  ]).returning('*');

  // Insert components
  const components = await knex('components').insert([
    {
      equipment_id: equipment[0].id,
      name: 'Shell Section 1',
      description: 'Lower shell section of distillation column',
      component_type: 'shell',
      geometry_type: 'cylindrical_shell',
      nominal_thickness: 25.4,
      nominal_diameter: 3000,
      length: 6000,
      material_id: materials[1].id,
      weld_joint_efficiency: 1.0,
      corrosion_allowance_internal: 3.0,
      corrosion_allowance_external: 1.5,
      is_active: true,
      created_by: adminUser.id,
    },
    {
      equipment_id: equipment[0].id,
      name: 'Bottom Head',
      description: 'Bottom elliptical head of distillation column',
      component_type: 'head',
      geometry_type: 'elliptical_head',
      nominal_thickness: 30.0,
      nominal_diameter: 3000,
      material_id: materials[1].id,
      weld_joint_efficiency: 1.0,
      corrosion_allowance_internal: 3.0,
      corrosion_allowance_external: 1.5,
      is_active: true,
      created_by: adminUser.id,
    },
    {
      equipment_id: equipment[1].id,
      name: 'Shell',
      description: 'Heat exchanger shell',
      component_type: 'shell',
      geometry_type: 'cylindrical_shell',
      nominal_thickness: 20.0,
      nominal_diameter: 1200,
      length: 8000,
      material_id: materials[0].id,
      weld_joint_efficiency: 0.85,
      corrosion_allowance_internal: 2.0,
      corrosion_allowance_external: 1.0,
      is_active: true,
      created_by: adminUser.id,
    },
  ]).returning('*');

  // Insert sample assessments
  await knex('assessments').insert([
    {
      component_id: components[0].id,
      assessment_type: 'local_metal_loss_asme_b31g',
      assessment_level: 'level_1',
      design_code: 'ASME_VIII',
      assessment_date: new Date('2023-06-15'),
      assessor_name: 'John Engineer',
      status: 'completed',
      input_data: JSON.stringify({
        nominalOutsideDiameter: 3000,
        nominalThickness: 25.4,
        longitudinalExtent: 150,
        depthOfCorrodedArea: 8.5,
        designPressure: 2.5,
        operatingPressure: 2.0,
        specifiedMinimumYieldStrength: 260,
        specifiedMinimumTensileStrength: 485,
        assessmentLevel: 'level1',
        flowStressOption: 'modified',
      }),
      calculation_results: JSON.stringify({
        Z: 2.95,
        So: 147.6,
        level1Passed: true,
        SFailureModifiedL1: 285.4,
        SafeWorkingPressureModified: 285.4,
      }),
      conclusion: 'Level 1 assessment PASSED. The component is acceptable for continued service.',
      recommendations: 'Continue monitoring. Next assessment due in 2 years.',
      next_assessment_date: new Date('2025-06-15'),
      is_active: true,
      created_by: engineerUser.id,
    },
  ]);

  console.log('✅ Database seeded successfully with initial data');
  console.log('👤 Admin user created: <EMAIL> / admin123');
  console.log('👤 Engineer user created: <EMAIL> / admin123');
  console.log('🏭 Sample sites, facilities, equipment, and components created');
  console.log('🧪 Material database populated with common materials');
  console.log('📊 Sample assessment created');
}
