# Server Configuration
NODE_ENV=development
PORT=5000
API_VERSION=v1

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=integriwise_web
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=IntegriWISE Web

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,csv,txt,jpg,jpeg,png

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# PDF Generation
PUPPETEER_EXECUTABLE_PATH=
PUPPETEER_ARGS=--no-sandbox,--disable-setuid-sandbox

# Backup Configuration
BACKUP_PATH=./backups
BACKUP_RETENTION_DAYS=30

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# Application Settings
APP_NAME=IntegriWISE Web
APP_URL=http://localhost:3000
ADMIN_EMAIL=<EMAIL>

# Security
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this-in-production

# Feature Flags
ENABLE_SWAGGER=true
ENABLE_METRICS=true
ENABLE_AUDIT_LOG=true
