import { BaseModel } from '@/utils/database';
import { User, UserRole } from '@/types';
import bcrypt from 'bcryptjs';

export class UserModel extends BaseModel {
  protected static tableName = 'users';

  static async findByEmail(email: string): Promise<User | undefined> {
    return this.table.where('email', email).first();
  }

  static async findByUsername(username: string): Promise<User | undefined> {
    return this.table.where('username', username).first();
  }

  static async createUser(userData: {
    username: string;
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    role?: UserRole;
  }): Promise<User> {
    const hashedPassword = await bcrypt.hash(userData.password, 12);
    
    const user = await this.create({
      ...userData,
      password: hashedPassword,
      role: userData.role || UserRole.VIEWER,
      is_active: true,
    });

    // Remove password from returned user
    delete user.password;
    return user;
  }

  static async updatePassword(userId: string, newPassword: string): Promise<void> {
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    await this.update(userId, { password: hashedPassword });
  }

  static async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  static async updateLastLogin(userId: string): Promise<void> {
    await this.update(userId, { last_login: new Date() });
  }

  static async findActiveUsers(): Promise<User[]> {
    return this.table.where('is_active', true).select('*');
  }

  static async deactivateUser(userId: string): Promise<User> {
    return this.update(userId, { is_active: false });
  }

  static async activateUser(userId: string): Promise<User> {
    return this.update(userId, { is_active: true });
  }

  static async findByRole(role: UserRole): Promise<User[]> {
    return this.table.where('role', role).where('is_active', true);
  }

  static async searchUsers(searchTerm: string): Promise<User[]> {
    return this.table
      .where('is_active', true)
      .andWhere(function() {
        this.where('username', 'ilike', `%${searchTerm}%`)
          .orWhere('email', 'ilike', `%${searchTerm}%`)
          .orWhere('first_name', 'ilike', `%${searchTerm}%`)
          .orWhere('last_name', 'ilike', `%${searchTerm}%`);
      })
      .select('id', 'username', 'email', 'first_name', 'last_name', 'role', 'created_at');
  }

  static async getUserStats(): Promise<{
    total: number;
    active: number;
    byRole: Record<UserRole, number>;
  }> {
    const total = await this.count();
    const active = await this.count({ is_active: true });
    
    const roleStats = await this.table
      .select('role')
      .count('* as count')
      .where('is_active', true)
      .groupBy('role');

    const byRole = roleStats.reduce((acc, stat) => {
      acc[stat.role as UserRole] = parseInt(stat.count as string);
      return acc;
    }, {} as Record<UserRole, number>);

    return { total, active, byRole };
  }
}
