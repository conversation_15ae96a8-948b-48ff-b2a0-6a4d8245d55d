import { BaseModel } from '@/utils/database';
import { Site } from '@/types';

export class SiteModel extends BaseModel {
  protected static tableName = 'sites';

  static async findActiveSites(): Promise<Site[]> {
    return this.table.where('is_active', true).orderBy('name');
  }

  static async findByName(name: string): Promise<Site | undefined> {
    return this.table.where('name', name).first();
  }

  static async createSite(siteData: {
    name: string;
    description?: string;
    location?: string;
    contact_person?: string;
    contact_email?: string;
    contact_phone?: string;
    created_by: string;
  }): Promise<Site> {
    return this.create({
      ...siteData,
      is_active: true,
    });
  }

  static async updateSite(id: string, siteData: Partial<Site>): Promise<Site> {
    return this.update(id, siteData);
  }

  static async deactivateSite(id: string): Promise<Site> {
    return this.update(id, { is_active: false });
  }

  static async activateSite(id: string): Promise<Site> {
    return this.update(id, { is_active: true });
  }

  static async searchSites(searchTerm: string): Promise<Site[]> {
    return this.table
      .where('is_active', true)
      .andWhere(function() {
        this.where('name', 'ilike', `%${searchTerm}%`)
          .orWhere('description', 'ilike', `%${searchTerm}%`)
          .orWhere('location', 'ilike', `%${searchTerm}%`);
      })
      .orderBy('name');
  }

  static async getSiteWithFacilities(siteId: string) {
    const site = await this.findById(siteId);
    if (!site) return null;

    const facilities = await this.db()('facilities')
      .where('site_id', siteId)
      .where('is_active', true)
      .orderBy('name');

    return {
      ...site,
      facilities,
    };
  }

  static async getSiteStats(): Promise<{
    total: number;
    active: number;
    byLocation: Array<{ location: string; count: number }>;
  }> {
    const total = await this.count();
    const active = await this.count({ is_active: true });
    
    const locationStats = await this.table
      .select('location')
      .count('* as count')
      .where('is_active', true)
      .whereNotNull('location')
      .groupBy('location')
      .orderBy('count', 'desc');

    const byLocation = locationStats.map(stat => ({
      location: stat.location,
      count: parseInt(stat.count as string),
    }));

    return { total, active, byLocation };
  }

  static async getSitesCreatedByUser(userId: string): Promise<Site[]> {
    return this.table
      .where('created_by', userId)
      .where('is_active', true)
      .orderBy('created_at', 'desc');
  }
}
