import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { UserModel } from '@/models/User';
import { AuthTokens, JWTPayload, UserRole } from '@/types';
import { asyncHandler, AppError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';

const generateTokens = (userId: string, email: string, role: UserRole): AuthTokens => {
  const payload: Omit<JWTPayload, 'iat' | 'exp'> = { userId, email, role };
  
  const accessToken = jwt.sign(payload, process.env.JWT_SECRET!, {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  });
  
  const refreshToken = jwt.sign(payload, process.env.JWT_REFRESH_SECRET!, {
    expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  });
  
  return { accessToken, refreshToken };
};

export const login = asyncHandler(async (req: Request, res: Response) => {
  const { email, password } = req.body;
  
  if (!email || !password) {
    throw new AppError('Email and password are required', 400);
  }
  
  // Find user by email
  const user = await UserModel.table.where('email', email).first();
  
  if (!user) {
    throw new AppError('Invalid credentials', 401);
  }
  
  if (!user.is_active) {
    throw new AppError('Account is deactivated', 401);
  }
  
  // Verify password
  const isPasswordValid = await UserModel.verifyPassword(password, user.password);
  
  if (!isPasswordValid) {
    throw new AppError('Invalid credentials', 401);
  }
  
  // Update last login
  await UserModel.updateLastLogin(user.id);
  
  // Generate tokens
  const tokens = generateTokens(user.id, user.email, user.role);
  
  // Remove password from response
  const { password: _, ...userWithoutPassword } = user;
  
  logger.info(`User logged in: ${user.email}`);
  
  res.json({
    success: true,
    data: {
      user: userWithoutPassword,
      tokens,
    },
    message: 'Login successful',
  });
});

export const register = asyncHandler(async (req: Request, res: Response) => {
  const { username, email, password, first_name, last_name, role } = req.body;
  
  if (!username || !email || !password || !first_name || !last_name) {
    throw new AppError('All required fields must be provided', 400);
  }
  
  // Check if user already exists
  const existingUser = await UserModel.findByEmail(email);
  if (existingUser) {
    throw new AppError('User with this email already exists', 409);
  }
  
  const existingUsername = await UserModel.findByUsername(username);
  if (existingUsername) {
    throw new AppError('Username already taken', 409);
  }
  
  // Create user
  const user = await UserModel.createUser({
    username,
    email,
    password,
    first_name,
    last_name,
    role: role || UserRole.VIEWER,
  });
  
  // Generate tokens
  const tokens = generateTokens(user.id, user.email, user.role);
  
  logger.info(`New user registered: ${user.email}`);
  
  res.status(201).json({
    success: true,
    data: {
      user,
      tokens,
    },
    message: 'Registration successful',
  });
});

export const refreshToken = asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;
  
  if (!refreshToken) {
    throw new AppError('Refresh token is required', 400);
  }
  
  try {
    const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET!) as JWTPayload;
    
    // Verify user still exists and is active
    const user = await UserModel.findById(decoded.userId);
    if (!user || !user.is_active) {
      throw new AppError('User not found or inactive', 401);
    }
    
    // Generate new tokens
    const tokens = generateTokens(user.id, user.email, user.role);
    
    res.json({
      success: true,
      data: { tokens },
      message: 'Token refreshed successfully',
    });
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new AppError('Refresh token expired', 401);
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw new AppError('Invalid refresh token', 401);
    }
    throw error;
  }
});

export const getProfile = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.userId;
  
  const user = await UserModel.findById(userId);
  if (!user) {
    throw new AppError('User not found', 404);
  }
  
  // Remove password from response
  const { password: _, ...userWithoutPassword } = user;
  
  res.json({
    success: true,
    data: { user: userWithoutPassword },
  });
});

export const updateProfile = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.userId;
  const { first_name, last_name, email } = req.body;
  
  // Check if email is already taken by another user
  if (email) {
    const existingUser = await UserModel.findByEmail(email);
    if (existingUser && existingUser.id !== userId) {
      throw new AppError('Email already taken by another user', 409);
    }
  }
  
  const updatedUser = await UserModel.update(userId, {
    first_name,
    last_name,
    email,
  });
  
  // Remove password from response
  const { password: _, ...userWithoutPassword } = updatedUser;
  
  logger.info(`User profile updated: ${updatedUser.email}`);
  
  res.json({
    success: true,
    data: { user: userWithoutPassword },
    message: 'Profile updated successfully',
  });
});

export const changePassword = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.userId;
  const { currentPassword, newPassword } = req.body;
  
  if (!currentPassword || !newPassword) {
    throw new AppError('Current password and new password are required', 400);
  }
  
  // Get user with password
  const user = await UserModel.table.where('id', userId).first();
  if (!user) {
    throw new AppError('User not found', 404);
  }
  
  // Verify current password
  const isCurrentPasswordValid = await UserModel.verifyPassword(currentPassword, user.password);
  if (!isCurrentPasswordValid) {
    throw new AppError('Current password is incorrect', 400);
  }
  
  // Update password
  await UserModel.updatePassword(userId, newPassword);
  
  logger.info(`Password changed for user: ${user.email}`);
  
  res.json({
    success: true,
    message: 'Password changed successfully',
  });
});

export const logout = asyncHandler(async (req: Request, res: Response) => {
  // In a more sophisticated implementation, you might want to blacklist the token
  // For now, we'll just return a success response
  logger.info(`User logged out: ${req.user!.email}`);
  
  res.json({
    success: true,
    message: 'Logout successful',
  });
});
