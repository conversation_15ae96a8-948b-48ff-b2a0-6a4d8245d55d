import { BaseCalculator } from '../BaseCalculator';
import { CalculationResult } from '@/types';

export interface ASMEB31GInput {
  // Geometry
  nominalOutsideDiameter: number;
  nominalThickness: number;
  longitudinalExtent: number;
  depthOfCorrodedArea: number;
  
  // Operating Conditions
  designPressure: number;
  operatingPressure: number;
  
  // Material Properties
  specifiedMinimumYieldStrength: number;
  specifiedMinimumTensileStrength: number;
  
  // Assessment Options
  assessmentLevel: 'level1' | 'level2';
  flowStressOption: 'original' | 'modified';
  
  // Optional
  weldJointEfficiency?: number;
  temperatureFactor?: number;
  safetyFactor?: number;
}

export class LocalMetalLossASMEB31G extends BaseCalculator {
  private input: ASMEB31GInput;
  
  // Calculated values
  private Z: number = 0;
  private So: number = 0;
  private SflowOriginal: number = 0;
  private SflowModified: number = 0;
  private MOriginal: number = 0;
  private MModified: number = 0;
  private SFailureOriginalL1: number = 0;
  private SFailureModifiedL1: number = 0;
  private SFailureOriginalL2: number = 0;
  private SFailureModifiedL2: number = 0;
  private SafeWorkingPressureOriginal: number = 0;
  private SafeWorkingPressureModified: number = 0;
  private DoverNominalThickness: number = 0;
  
  constructor(input: ASMEB31GInput) {
    super();
    this.input = input;
  }

  public calculateAssessment(): CalculationResult {
    this.clearMessages();
    
    try {
      // Validate inputs
      if (!this.validateInputs()) {
        return this.createResult(false, {}, 'Input validation failed');
      }
      
      // Perform calculations
      this.performCalculations();
      
      // Determine results
      const level1Passed = this.evaluateLevel1();
      const level2Passed = this.input.assessmentLevel === 'level2' ? this.evaluateLevel2() : undefined;
      
      const conclusion = this.generateConclusion(level1Passed, level2Passed);
      
      const results = {
        // Input parameters
        nominalOutsideDiameter: this.input.nominalOutsideDiameter,
        nominalThickness: this.input.nominalThickness,
        longitudinalExtent: this.input.longitudinalExtent,
        depthOfCorrodedArea: this.input.depthOfCorrodedArea,
        designPressure: this.input.designPressure,
        operatingPressure: this.input.operatingPressure,
        specifiedMinimumYieldStrength: this.input.specifiedMinimumYieldStrength,
        specifiedMinimumTensileStrength: this.input.specifiedMinimumTensileStrength,
        
        // Calculated intermediate values
        Z: this.roundToDecimalPlaces(this.Z, 4),
        So: this.roundToDecimalPlaces(this.So, 2),
        DoverNominalThickness: this.roundToDecimalPlaces(this.DoverNominalThickness, 4),
        
        // Flow stress values
        SflowOriginal: this.roundToDecimalPlaces(this.SflowOriginal, 2),
        SflowModified: this.roundToDecimalPlaces(this.SflowModified, 2),
        
        // M factors
        MOriginal: this.roundToDecimalPlaces(this.MOriginal, 4),
        MModified: this.roundToDecimalPlaces(this.MModified, 4),
        
        // Failure stress values
        SFailureOriginalL1: this.roundToDecimalPlaces(this.SFailureOriginalL1, 2),
        SFailureModifiedL1: this.roundToDecimalPlaces(this.SFailureModifiedL1, 2),
        
        // Safe working pressures
        SafeWorkingPressureOriginal: this.roundToDecimalPlaces(this.SafeWorkingPressureOriginal, 2),
        SafeWorkingPressureModified: this.roundToDecimalPlaces(this.SafeWorkingPressureModified, 2),
        
        // Assessment results
        level1Passed,
        level2Passed,
        assessmentLevel: this.input.assessmentLevel,
        flowStressOption: this.input.flowStressOption,
      };
      
      if (this.input.assessmentLevel === 'level2') {
        results.SFailureOriginalL2 = this.roundToDecimalPlaces(this.SFailureOriginalL2, 2);
        results.SFailureModifiedL2 = this.roundToDecimalPlaces(this.SFailureModifiedL2, 2);
      }
      
      return this.createResult(true, results, conclusion, level1Passed, level2Passed);
      
    } catch (error) {
      this.addError(`Calculation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return this.createResult(false, {}, 'Calculation failed due to error');
    }
  }

  private validateInputs(): boolean {
    let isValid = true;
    
    // Required field validation
    isValid = this.validateRequired(this.input.nominalOutsideDiameter, 'Nominal Outside Diameter') && isValid;
    isValid = this.validateRequired(this.input.nominalThickness, 'Nominal Thickness') && isValid;
    isValid = this.validateRequired(this.input.longitudinalExtent, 'Longitudinal Extent') && isValid;
    isValid = this.validateRequired(this.input.depthOfCorrodedArea, 'Depth of Corroded Area') && isValid;
    isValid = this.validateRequired(this.input.designPressure, 'Design Pressure') && isValid;
    isValid = this.validateRequired(this.input.operatingPressure, 'Operating Pressure') && isValid;
    isValid = this.validateRequired(this.input.specifiedMinimumYieldStrength, 'SMYS') && isValid;
    isValid = this.validateRequired(this.input.specifiedMinimumTensileStrength, 'SMTS') && isValid;
    
    // Positive value validation
    if (this.input.nominalOutsideDiameter !== undefined) {
      isValid = this.validatePositive(this.input.nominalOutsideDiameter, 'Nominal Outside Diameter') && isValid;
    }
    if (this.input.nominalThickness !== undefined) {
      isValid = this.validatePositive(this.input.nominalThickness, 'Nominal Thickness') && isValid;
    }
    if (this.input.longitudinalExtent !== undefined) {
      isValid = this.validatePositive(this.input.longitudinalExtent, 'Longitudinal Extent') && isValid;
    }
    if (this.input.depthOfCorrodedArea !== undefined) {
      isValid = this.validatePositive(this.input.depthOfCorrodedArea, 'Depth of Corroded Area') && isValid;
    }
    
    // Range validation
    if (this.input.depthOfCorrodedArea !== undefined && this.input.nominalThickness !== undefined) {
      if (this.input.depthOfCorrodedArea >= this.input.nominalThickness) {
        this.addError('Depth of corroded area must be less than nominal thickness');
        isValid = false;
      }
    }
    
    // Material property validation
    if (this.input.specifiedMinimumYieldStrength !== undefined && this.input.specifiedMinimumTensileStrength !== undefined) {
      if (this.input.specifiedMinimumYieldStrength >= this.input.specifiedMinimumTensileStrength) {
        this.addError('Yield strength must be less than tensile strength');
        isValid = false;
      }
    }
    
    return isValid;
  }

  private performCalculations(): void {
    // Calculate Z factor
    this.Z = Math.pow(this.input.longitudinalExtent, 2) / 
             (this.input.nominalOutsideDiameter * this.input.nominalThickness);
    
    // Calculate So (hoop stress)
    this.So = this.input.designPressure * this.input.nominalOutsideDiameter / 
              (2 * this.input.nominalThickness);
    
    // Calculate d/t ratio
    this.DoverNominalThickness = this.input.depthOfCorrodedArea / this.input.nominalThickness;
    
    // Calculate flow stress values
    this.SflowOriginal = 1.1 * this.input.specifiedMinimumYieldStrength;
    this.SflowModified = this.input.specifiedMinimumYieldStrength + 69; // 69 MPa = 10 ksi
    
    // Calculate M factors
    this.MOriginal = Math.sqrt(1 + 0.8 * this.Z);
    
    if (this.Z > 50) {
      this.MModified = 0.032 * this.Z + 3.3;
    } else {
      this.MModified = Math.sqrt(1 + (251/400) * this.Z - 0.003375 * Math.pow(this.Z, 2));
    }
    
    // Calculate Level 1 failure stress
    if (this.Z > 20) {
      this.SFailureOriginalL1 = this.SflowOriginal * (1 - this.DoverNominalThickness);
      this.SFailureModifiedL1 = this.SflowModified * (1 - this.DoverNominalThickness);
    } else {
      this.SFailureOriginalL1 = this.SflowOriginal * 
        (1 - (2/3) * this.DoverNominalThickness) / 
        (1 - (2/3) * this.DoverNominalThickness / this.MOriginal);
      
      this.SFailureModifiedL1 = this.SflowModified * 
        (1 - (2/3) * this.DoverNominalThickness) / 
        (1 - (2/3) * this.DoverNominalThickness / this.MModified);
    }
    
    // Calculate Level 2 failure stress (if needed)
    if (this.input.assessmentLevel === 'level2') {
      // Level 2 uses more sophisticated calculations
      this.SFailureOriginalL2 = this.calculateLevel2FailureStress('original');
      this.SFailureModifiedL2 = this.calculateLevel2FailureStress('modified');
    }
    
    // Calculate safe working pressures
    this.SafeWorkingPressureOriginal = this.SFailureOriginalL1 * 
      (this.input.safetyFactor || 1.0);
    this.SafeWorkingPressureModified = this.SFailureModifiedL1 * 
      (this.input.safetyFactor || 1.0);
  }

  private calculateLevel2FailureStress(option: 'original' | 'modified'): number {
    // Level 2 calculations are more complex and would include:
    // - More accurate stress analysis
    // - Consideration of crack tip plasticity
    // - More sophisticated failure criteria
    
    const Sflow = option === 'original' ? this.SflowOriginal : this.SflowModified;
    const M = option === 'original' ? this.MOriginal : this.MModified;
    
    // Simplified Level 2 calculation (actual implementation would be more complex)
    return Sflow * (1 - this.DoverNominalThickness / M);
  }

  private evaluateLevel1(): boolean {
    const selectedFailureStress = this.input.flowStressOption === 'original' 
      ? this.SFailureOriginalL1 
      : this.SFailureModifiedL1;
    
    return this.So <= selectedFailureStress;
  }

  private evaluateLevel2(): boolean {
    if (this.input.assessmentLevel !== 'level2') return false;
    
    const selectedFailureStress = this.input.flowStressOption === 'original' 
      ? this.SFailureOriginalL2 
      : this.SFailureModifiedL2;
    
    return this.So <= selectedFailureStress;
  }

  private generateConclusion(level1Passed: boolean, level2Passed?: boolean): string {
    if (this.input.assessmentLevel === 'level1') {
      return level1Passed 
        ? 'Level 1 assessment PASSED. The component is acceptable for continued service.'
        : 'Level 1 assessment FAILED. Further evaluation or repair is required.';
    } else {
      if (level1Passed) {
        return 'Level 1 assessment PASSED. The component is acceptable for continued service.';
      } else if (level2Passed) {
        return 'Level 1 assessment FAILED but Level 2 assessment PASSED. The component is acceptable for continued service.';
      } else {
        return 'Both Level 1 and Level 2 assessments FAILED. Repair or replacement is required.';
      }
    }
  }
}
