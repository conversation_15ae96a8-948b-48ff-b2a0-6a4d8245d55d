// User and Authentication Types
export interface User {
  id: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: UserRole;
  is_active: boolean;
  last_login?: Date;
  created_at: Date;
  updated_at: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  ENGINEER = 'engineer',
  VIEWER = 'viewer',
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
}

// Asset Hierarchy Types
export interface Site {
  id: string;
  name: string;
  description?: string;
  location?: string;
  contact_person?: string;
  contact_email?: string;
  contact_phone?: string;
  is_active: boolean;
  created_by: string;
  created_at: Date;
  updated_at: Date;
}

export interface Facility {
  id: string;
  site_id: string;
  name: string;
  description?: string;
  facility_type: FacilityType;
  is_active: boolean;
  created_by: string;
  created_at: Date;
  updated_at: Date;
}

export enum FacilityType {
  REFINERY = 'refinery',
  CHEMICAL_PLANT = 'chemical_plant',
  POWER_PLANT = 'power_plant',
  OFFSHORE_PLATFORM = 'offshore_platform',
  PIPELINE = 'pipeline',
  STORAGE_TERMINAL = 'storage_terminal',
  OTHER = 'other',
}

export interface Equipment {
  id: string;
  facility_id: string;
  tag_number: string;
  name: string;
  description?: string;
  equipment_type: EquipmentType;
  design_code: DesignCode;
  design_pressure: number;
  design_temperature: number;
  operating_pressure: number;
  operating_temperature: number;
  installation_date?: Date;
  last_inspection_date?: Date;
  next_inspection_date?: Date;
  is_active: boolean;
  created_by: string;
  created_at: Date;
  updated_at: Date;
}

export enum EquipmentType {
  PRESSURE_VESSEL = 'pressure_vessel',
  STORAGE_TANK = 'storage_tank',
  PIPELINE = 'pipeline',
  HEAT_EXCHANGER = 'heat_exchanger',
  REACTOR = 'reactor',
  COLUMN = 'column',
  BOILER = 'boiler',
  OTHER = 'other',
}

export enum DesignCode {
  ASME_B31_3 = 'ASME_B31_3',
  ASME_B31_4 = 'ASME_B31_4',
  ASME_B31_8 = 'ASME_B31_8',
  ASME_VIII = 'ASME_VIII',
  API_620 = 'API_620',
  API_650 = 'API_650',
  PD_5500 = 'PD_5500',
}

export interface Component {
  id: string;
  equipment_id: string;
  name: string;
  description?: string;
  component_type: ComponentType;
  geometry_type: GeometryType;
  nominal_thickness: number;
  nominal_diameter?: number;
  length?: number;
  material_id: string;
  weld_joint_efficiency?: number;
  corrosion_allowance_internal?: number;
  corrosion_allowance_external?: number;
  is_active: boolean;
  created_by: string;
  created_at: Date;
  updated_at: Date;
}

export enum ComponentType {
  SHELL = 'shell',
  HEAD = 'head',
  NOZZLE = 'nozzle',
  PIPE_SECTION = 'pipe_section',
  ELBOW = 'elbow',
  REDUCER = 'reducer',
  TEE = 'tee',
  FLANGE = 'flange',
  OTHER = 'other',
}

export enum GeometryType {
  CYLINDRICAL_SHELL = 'cylindrical_shell',
  SPHERICAL_SHELL = 'spherical_shell',
  ELLIPTICAL_HEAD = 'elliptical_head',
  HEMISPHERICAL_HEAD = 'hemispherical_head',
  TORISPHERICAL_HEAD = 'torispherical_head',
  FLAT_HEAD = 'flat_head',
  CONICAL_SECTION = 'conical_section',
}

// Material Types
export interface Material {
  id: string;
  name: string;
  specification: string;
  grade: string;
  material_type: MaterialType;
  density?: number;
  elastic_modulus?: number;
  poisson_ratio?: number;
  thermal_expansion_coefficient?: number;
  is_active: boolean;
  created_by: string;
  created_at: Date;
  updated_at: Date;
}

export enum MaterialType {
  CARBON_STEEL = 'carbon_steel',
  STAINLESS_STEEL = 'stainless_steel',
  ALLOY_STEEL = 'alloy_steel',
  ALUMINUM = 'aluminum',
  COPPER = 'copper',
  TITANIUM = 'titanium',
  COMPOSITE = 'composite',
  OTHER = 'other',
}

export interface MaterialProperty {
  id: string;
  material_id: string;
  temperature: number;
  yield_strength: number;
  tensile_strength: number;
  allowable_stress?: number;
  created_at: Date;
  updated_at: Date;
}

// Assessment Types
export interface Assessment {
  id: string;
  component_id: string;
  assessment_type: AssessmentType;
  assessment_level: AssessmentLevel;
  design_code: DesignCode;
  assessment_date: Date;
  assessor_name: string;
  status: AssessmentStatus;
  input_data: Record<string, any>;
  calculation_results: Record<string, any>;
  conclusion: string;
  recommendations?: string;
  next_assessment_date?: Date;
  is_active: boolean;
  created_by: string;
  created_at: Date;
  updated_at: Date;
}

export enum AssessmentType {
  LOCAL_METAL_LOSS_ASME_B31G = 'local_metal_loss_asme_b31g',
  LOCAL_METAL_LOSS_GROOVE_LIKE = 'local_metal_loss_groove_like',
  LOCAL_METAL_LOSS_THIN_AREA = 'local_metal_loss_thin_area',
  LOCAL_METAL_LOSS_DNV_F101 = 'local_metal_loss_dnv_f101',
  GENERAL_METAL_LOSS_THICKNESS_PROFILE = 'general_metal_loss_thickness_profile',
  GENERAL_METAL_LOSS_THICKNESS_READING = 'general_metal_loss_thickness_reading',
  PITTING = 'pitting',
  PITTING_L2 = 'pitting_l2',
  HIC = 'hic',
  LAMINATION = 'lamination',
  BLISTER = 'blister',
  BRITTLE_FRACTURE = 'brittle_fracture',
  MEMBRANE_STRESS = 'membrane_stress',
  MAWP = 'mawp',
  MINIMUM_THICKNESS = 'minimum_thickness',
  MFH = 'mfh',
  DENT = 'dent',
  DENT_GOUGE = 'dent_gouge',
}

export enum AssessmentLevel {
  LEVEL_1 = 'level_1',
  LEVEL_2 = 'level_2',
}

export enum AssessmentStatus {
  DRAFT = 'draft',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  REVIEWED = 'reviewed',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

// Calculation Input/Output Types
export interface CalculationInput {
  assessmentType: AssessmentType;
  designCode: DesignCode;
  geometryType: GeometryType;
  inputParameters: Record<string, any>;
  materialProperties: MaterialProperty[];
}

export interface CalculationResult {
  success: boolean;
  results: Record<string, any>;
  warnings: string[];
  errors: string[];
  conclusion: string;
  level1Passed?: boolean;
  level2Passed?: boolean;
}

// Report Types
export interface Report {
  id: string;
  assessment_id: string;
  report_type: ReportType;
  title: string;
  generated_by: string;
  generated_at: Date;
  file_path: string;
  file_size: number;
  is_active: boolean;
}

export enum ReportType {
  ASSESSMENT_REPORT = 'assessment_report',
  SUMMARY_REPORT = 'summary_report',
  DETAILED_CALCULATION = 'detailed_calculation',
  COMPARISON_REPORT = 'comparison_report',
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ErrorResponse {
  success: false;
  message: string;
  errors?: string[];
  stack?: string;
}
