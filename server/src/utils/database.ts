import knex, { Knex } from 'knex';
import config from '../../knexfile';
import { logger } from './logger';

const environment = process.env.NODE_ENV || 'development';
const dbConfig = config[environment];

let db: Knex;

export const connectDatabase = async (): Promise<Knex> => {
  try {
    db = knex(dbConfig);
    
    // Test the connection
    await db.raw('SELECT 1');
    logger.info(`Database connected successfully (${environment})`);
    
    return db;
  } catch (error) {
    logger.error('Database connection failed:', error);
    throw error;
  }
};

export const getDatabase = (): Knex => {
  if (!db) {
    throw new Error('Database not initialized. Call connectDatabase() first.');
  }
  return db;
};

export const closeDatabase = async (): Promise<void> => {
  if (db) {
    await db.destroy();
    logger.info('Database connection closed');
  }
};

// Transaction helper
export const withTransaction = async <T>(
  callback: (trx: Knex.Transaction) => Promise<T>
): Promise<T> => {
  const database = getDatabase();
  return database.transaction(callback);
};

// Query builder helpers
export class BaseModel {
  protected static tableName: string;
  protected static db = () => getDatabase();

  static get table() {
    return this.db()(this.tableName);
  }

  static async findById(id: string | number) {
    return this.table.where('id', id).first();
  }

  static async findAll(filters: Record<string, any> = {}) {
    let query = this.table;
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query = query.where(key, value);
      }
    });
    
    return query;
  }

  static async create(data: Record<string, any>) {
    const [result] = await this.table.insert(data).returning('*');
    return result;
  }

  static async update(id: string | number, data: Record<string, any>) {
    const [result] = await this.table
      .where('id', id)
      .update({
        ...data,
        updated_at: new Date(),
      })
      .returning('*');
    return result;
  }

  static async delete(id: string | number) {
    return this.table.where('id', id).del();
  }

  static async exists(id: string | number): Promise<boolean> {
    const result = await this.table.where('id', id).first();
    return !!result;
  }

  static async count(filters: Record<string, any> = {}): Promise<number> {
    let query = this.table;
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query = query.where(key, value);
      }
    });
    
    const result = await query.count('* as count').first();
    return parseInt(result?.count as string) || 0;
  }
}

// Pagination helper
export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export const paginate = async <T>(
  query: Knex.QueryBuilder,
  options: PaginationOptions = {}
): Promise<PaginatedResult<T>> => {
  const page = Math.max(1, options.page || 1);
  const limit = Math.min(100, Math.max(1, options.limit || 10));
  const offset = (page - 1) * limit;

  // Clone query for count
  const countQuery = query.clone().clearSelect().clearOrder().count('* as count');
  const totalResult = await countQuery.first();
  const total = parseInt(totalResult?.count as string) || 0;

  // Apply sorting
  if (options.sortBy) {
    query = query.orderBy(options.sortBy, options.sortOrder || 'asc');
  }

  // Apply pagination
  const data = await query.offset(offset).limit(limit);

  const totalPages = Math.ceil(total / limit);

  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
};

export default db;
