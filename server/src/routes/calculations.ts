import { Router } from 'express';
import { LocalMetalLossASMEB31G, ASMEB31GInput } from '../calculations/ASMEB31G/LocalMetalLossASMEB31G';

const router = Router();

// Get available calculation types
router.get('/', (req, res) => {
  res.json({
    success: true,
    data: {
      availableCalculations: [
        {
          id: 'asme-b31g',
          name: 'ASME B31G Local Metal Loss',
          description: 'Original and Modified ASME B31G assessment for pipeline metal loss',
          methods: ['original', 'modified'],
          levels: ['level1', 'level2']
        },
        {
          id: 'dnv-f101',
          name: 'DNV F101 Part B',
          description: 'Pipeline assessment according to DNV F101 Part B standards',
          status: 'coming-soon'
        },
        {
          id: 'api-579',
          name: 'API 579 FFS',
          description: 'Complete API 579 Fitness-for-Service assessment',
          status: 'coming-soon'
        }
      ]
    },
    message: 'IntegriWISE Calculation Engine - Ready'
  });
});

// ASME B31G Local Metal Loss Assessment
router.post('/asme-b31g', (req, res) => {
  try {
    const input: ASMEB31GInput = req.body;

    // Validate required fields
    const requiredFields = [
      'nominalOutsideDiameter',
      'nominalThickness',
      'longitudinalExtent',
      'depthOfCorrodedArea',
      'designPressure',
      'operatingPressure',
      'specifiedMinimumYieldStrength',
      'specifiedMinimumTensileStrength',
      'assessmentLevel',
      'flowStressOption'
    ];

    for (const field of requiredFields) {
      if (!(field in input) || input[field as keyof ASMEB31GInput] === undefined) {
        return res.status(400).json({
          success: false,
          error: `Missing required field: ${field}`
        });
      }
    }

    // Create calculator instance and perform calculation
    const calculator = new LocalMetalLossASMEB31G(input);
    const result = calculator.calculate();

    res.json({
      success: true,
      data: {
        input: input,
        results: result,
        calculationType: 'ASME B31G Local Metal Loss',
        timestamp: new Date().toISOString()
      },
      message: 'Calculation completed successfully'
    });

  } catch (error) {
    console.error('ASME B31G Calculation Error:', error);
    res.status(500).json({
      success: false,
      error: 'Calculation failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get ASME B31G input template
router.get('/asme-b31g/template', (req, res) => {
  const template: ASMEB31GInput = {
    // Geometry
    nominalOutsideDiameter: 508.0,  // mm
    nominalThickness: 12.7,         // mm
    longitudinalExtent: 200.0,      // mm
    depthOfCorrodedArea: 6.35,      // mm

    // Operating Conditions
    designPressure: 7.0,            // MPa
    operatingPressure: 5.5,         // MPa

    // Material Properties
    specifiedMinimumYieldStrength: 358.0,  // MPa
    specifiedMinimumTensileStrength: 455.0, // MPa

    // Assessment Options
    assessmentLevel: 'level1',
    flowStressOption: 'modified',

    // Optional parameters with defaults
    weldJointEfficiency: 1.0,
    temperatureFactor: 1.0,
    safetyFactor: 1.0
  };

  res.json({
    success: true,
    data: {
      template: template,
      description: 'ASME B31G input template with example values',
      units: {
        nominalOutsideDiameter: 'mm',
        nominalThickness: 'mm',
        longitudinalExtent: 'mm',
        depthOfCorrodedArea: 'mm',
        designPressure: 'MPa',
        operatingPressure: 'MPa',
        specifiedMinimumYieldStrength: 'MPa',
        specifiedMinimumTensileStrength: 'MPa'
      }
    }
  });
});

export default router;
