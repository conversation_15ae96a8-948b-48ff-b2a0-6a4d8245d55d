From: "Saved by Windows Internet Explorer 9"
Subject: 
Date: Tue, 10 Feb 2015 13:34:23 -0000
MIME-Version: 1.0
Content-Type: text/html;
	charset="iso-8859-1"
Content-Transfer-Encoding: quoted-printable
Content-Location: mhtml:file://N:\Corporate\IMG\Group Sections\AFM - <PERSON><PERSON>\IntegriWISE Validation\Help\P4 Thickness Profiles & P5\ASME B31G_V2.0.mht
X-MimeOLE: Produced By Microsoft MimeOLE V6.1.7601.17609

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML xmlns=3D"http://www.w3.org/TR/REC-html40" xmlns:v =3D=20
"urn:schemas-microsoft-com:vml" xmlns:o =3D=20
"urn:schemas-microsoft-com:office:office" xmlns:w =3D=20
"urn:schemas-microsoft-com:office:word" xmlns:m =3D=20
"http://schemas.microsoft.com/office/2004/12/omml"><HEAD>
<META content=3D"text/html; charset=3Dwindows-1252" =
http-equiv=3DContent-Type>
<META name=3DProgId content=3DWord.Document>
<META name=3DGENERATOR content=3D"MSHTML 9.00.8112.16599">
<META name=3DOriginator content=3D"Microsoft Word 14"><LINK =
rel=3DFile-List=20
href=3D"ASMEB31G_V2.0_files/filelist.xml"><!--[if gte mso 9]><xml>
 <o:DocumentProperties>
  <o:Author>Carmen Espinosa</o:Author>
  <o:Template>70441BD3</o:Template>
  <o:LastAuthor>Stephen Humphrey</o:LastAuthor>
  <o:Revision>2</o:Revision>
  <o:TotalTime>68</o:TotalTime>
  <o:Created>2013-03-19T14:30:00Z</o:Created>
  <o:LastSaved>2013-03-19T14:30:00Z</o:LastSaved>
  <o:Pages>1</o:Pages>
  <o:Words>446</o:Words>
  <o:Characters>2548</o:Characters>
  <o:Company>TWI LTD</o:Company>
  <o:Lines>21</o:Lines>
  <o:Paragraphs>5</o:Paragraphs>
  <o:CharactersWithSpaces>2989</o:CharactersWithSpaces>
  <o:Version>14.00</o:Version>
 </o:DocumentProperties>
 <o:OfficeDocumentSettings>
  <o:AllowPNG/>
 </o:OfficeDocumentSettings>
</xml><![endif]--><LINK rel=3DthemeData=20
href=3D"ASMEB31G_V2.0_files/themedata.thmx"><LINK =
rel=3DcolorSchemeMapping=20
href=3D"ASMEB31G_V2.0_files/colorschememapping.xml"><!--[if gte mso =
9]><xml>
 <w:WordDocument>
  <w:SpellingState>Clean</w:SpellingState>
  <w:GrammarState>Clean</w:GrammarState>
  <w:TrackMoves/>
  <w:TrackFormatting/>
  <w:PunctuationKerning/>
  <w:DrawingGridHorizontalSpacing>5 pt</w:DrawingGridHorizontalSpacing>
  =
<w:DisplayHorizontalDrawingGridEvery>2</w:DisplayHorizontalDrawingGridEve=
ry>
  =
<w:DisplayVerticalDrawingGridEvery>2</w:DisplayVerticalDrawingGridEvery>
  <w:ValidateAgainstSchemas/>
  <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>
  <w:IgnoreMixedContent>false</w:IgnoreMixedContent>
  <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>
  <w:DoNotPromoteQF/>
  <w:LidThemeOther>EN-GB</w:LidThemeOther>
  <w:LidThemeAsian>X-NONE</w:LidThemeAsian>
  <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>
  <w:Compatibility>
   <w:BreakWrappedTables/>
   <w:SnapToGridInCell/>
   <w:WrapTextWithPunct/>
   <w:UseAsianBreakRules/>
   <w:DontGrowAutofit/>
   <w:SplitPgBreakAndParaMark/>
   <w:EnableOpenTypeKerning/>
   <w:DontFlipMirrorIndents/>
   <w:OverrideTableStyleHps/>
  </w:Compatibility>
  <m:mathPr>
   <m:mathFont m:val=3D"Cambria Math"/>
   <m:brkBin m:val=3D"before"/>
   <m:brkBinSub m:val=3D"&#45;-"/>
   <m:smallFrac m:val=3D"off"/>
   <m:dispDef/>
   <m:lMargin m:val=3D"0"/>
   <m:rMargin m:val=3D"0"/>
   <m:defJc m:val=3D"centerGroup"/>
   <m:wrapIndent m:val=3D"1440"/>
   <m:intLim m:val=3D"subSup"/>
   <m:naryLim m:val=3D"undOvr"/>
  </m:mathPr></w:WordDocument>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <w:LatentStyles DefLockedState=3D"false" DefUnhideWhenUsed=3D"true"
  DefSemiHidden=3D"true" DefQFormat=3D"false" DefPriority=3D"99"
  LatentStyleCount=3D"267">
  <w:LsdException Locked=3D"false" Priority=3D"0" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"Normal"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"heading 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" QFormat=3D"true" =
Name=3D"heading 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" QFormat=3D"true" =
Name=3D"heading 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" QFormat=3D"true" =
Name=3D"heading 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" QFormat=3D"true" =
Name=3D"heading 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" QFormat=3D"true" =
Name=3D"heading 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" QFormat=3D"true" =
Name=3D"heading 7"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" QFormat=3D"true" =
Name=3D"heading 8"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" QFormat=3D"true" =
Name=3D"heading 9"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" Name=3D"toc 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" Name=3D"toc 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" Name=3D"toc 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" Name=3D"toc 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" Name=3D"toc 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" Name=3D"toc 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" Name=3D"toc 7"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" Name=3D"toc 8"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" Name=3D"toc 9"/>
  <w:LsdException Locked=3D"false" Priority=3D"35" QFormat=3D"true" =
Name=3D"caption"/>
  <w:LsdException Locked=3D"false" Priority=3D"10" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"Title"/>
  <w:LsdException Locked=3D"false" Priority=3D"1" Name=3D"Default =
Paragraph Font"/>
  <w:LsdException Locked=3D"false" Priority=3D"11" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"Subtitle"/>
  <w:LsdException Locked=3D"false" Priority=3D"22" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"Strong"/>
  <w:LsdException Locked=3D"false" Priority=3D"20" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"Emphasis"/>
  <w:LsdException Locked=3D"false" Priority=3D"59" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Table Grid"/>
  <w:LsdException Locked=3D"false" UnhideWhenUsed=3D"false" =
Name=3D"Placeholder Text"/>
  <w:LsdException Locked=3D"false" Priority=3D"1" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"No Spacing"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Shading"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light List"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Grid"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Dark List"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Shading"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful List"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Grid"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Shading Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light List Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Grid Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 1 Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 2 Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 1 Accent 1"/>
  <w:LsdException Locked=3D"false" UnhideWhenUsed=3D"false" =
Name=3D"Revision"/>
  <w:LsdException Locked=3D"false" Priority=3D"34" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"List Paragraph"/>
  <w:LsdException Locked=3D"false" Priority=3D"29" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"Quote"/>
  <w:LsdException Locked=3D"false" Priority=3D"30" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"Intense Quote"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 2 Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 1 Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 2 Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 3 Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Dark List Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Shading Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful List Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Grid Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Shading Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light List Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Grid Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 1 Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 2 Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 1 Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 2 Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 1 Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 2 Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 3 Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Dark List Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Shading Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful List Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Grid Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Shading Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light List Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Grid Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 1 Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 2 Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 1 Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 2 Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 1 Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 2 Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 3 Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Dark List Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Shading Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful List Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Grid Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Shading Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light List Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Grid Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 1 Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 2 Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 1 Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 2 Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 1 Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 2 Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 3 Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Dark List Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Shading Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful List Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Grid Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Shading Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light List Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Grid Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 1 Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 2 Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 1 Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 2 Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 1 Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 2 Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 3 Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Dark List Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Shading Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful List Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Grid Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Shading Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light List Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Light Grid Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 1 Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Shading 2 Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 1 Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium List 2 Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 1 Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 2 Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Medium Grid 3 Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Dark List Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Shading Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful List Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" Name=3D"Colorful Grid Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"19" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"Subtle Emphasis"/>
  <w:LsdException Locked=3D"false" Priority=3D"21" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"Intense Emphasis"/>
  <w:LsdException Locked=3D"false" Priority=3D"31" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"Subtle Reference"/>
  <w:LsdException Locked=3D"false" Priority=3D"32" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"Intense =
Reference"/>
  <w:LsdException Locked=3D"false" Priority=3D"33" SemiHidden=3D"false"
   UnhideWhenUsed=3D"false" QFormat=3D"true" Name=3D"Book Title"/>
  <w:LsdException Locked=3D"false" Priority=3D"37" =
Name=3D"Bibliography"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" QFormat=3D"true" =
Name=3D"TOC Heading"/>
 </w:LatentStyles>
</xml><![endif]-->
<STYLE>@font-face {
	font-family: Calibri;
}
@page WordSection1 {size: 595.3pt 841.9pt; margin: 72.0pt 72.0pt 36.0pt =
72.0pt; mso-header-margin: 35.45pt; mso-footer-margin: 35.45pt; =
mso-paper-source: 0; }
P.MsoNormal {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 10pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-style-parent: ""; mso-pagination: =
widow-orphan; mso-bidi-font-size: 11.0pt; mso-fareast-font-family: =
Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: =
"Times New Roman"; mso-bidi-theme-font: minor-bidi; =
mso-fareast-language: EN-US
}
LI.MsoNormal {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 10pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-style-parent: ""; mso-pagination: =
widow-orphan; mso-bidi-font-size: 11.0pt; mso-fareast-font-family: =
Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: =
"Times New Roman"; mso-bidi-theme-font: minor-bidi; =
mso-fareast-language: EN-US
}
DIV.MsoNormal {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 10pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-style-parent: ""; mso-pagination: =
widow-orphan; mso-bidi-font-size: 11.0pt; mso-fareast-font-family: =
Calibri; mso-fareast-theme-font: minor-latin; mso-bidi-font-family: =
"Times New Roman"; mso-bidi-theme-font: minor-bidi; =
mso-fareast-language: EN-US
}
H1 {
	FONT-FAMILY: "Times New Roman","serif"; MARGIN-LEFT: 0cm; FONT-SIZE: =
24pt; FONT-WEIGHT: bold; MARGIN-RIGHT: 0cm; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-fareast-font-family: "Times New Roman"; mso-style-priority: 9; =
mso-style-link: "Heading 1 Char"; mso-margin-top-alt: auto; =
mso-margin-bottom-alt: auto; mso-outline-level: 1
}
H2 {
	FONT-FAMILY: "Times New Roman","serif"; MARGIN-LEFT: 0cm; FONT-SIZE: =
18pt; FONT-WEIGHT: bold; MARGIN-RIGHT: 0cm; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-fareast-font-family: "Times New Roman"; mso-style-priority: 9; =
mso-style-link: "Heading 2 Char"; mso-margin-top-alt: auto; =
mso-margin-bottom-alt: auto; mso-outline-level: 2
}
H3 {
	FONT-FAMILY: "Times New Roman","serif"; MARGIN-LEFT: 0cm; FONT-SIZE: =
13.5pt; FONT-WEIGHT: bold; MARGIN-RIGHT: 0cm; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-fareast-font-family: "Times New Roman"; mso-style-priority: 9; =
mso-style-link: "Heading 3 Char"; mso-margin-top-alt: auto; =
mso-margin-bottom-alt: auto; mso-outline-level: 3
}
P {
	FONT-FAMILY: "Times New Roman","serif"; MARGIN-LEFT: 0cm; FONT-SIZE: =
12pt; MARGIN-RIGHT: 0cm; mso-pagination: widow-orphan; =
mso-fareast-font-family: "Times New Roman"; mso-style-priority: 99; =
mso-margin-top-alt: auto; mso-margin-bottom-alt: auto; mso-style-noshow: =
yes
}
P.MsoListParagraph {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 10pt 36pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-bidi-font-size: 11.0pt; mso-fareast-font-family: Calibri; =
mso-fareast-theme-font: minor-latin; mso-bidi-font-family: "Times New =
Roman"; mso-bidi-theme-font: minor-bidi; mso-fareast-language: EN-US; =
mso-style-priority: 34; mso-add-space: auto
}
LI.MsoListParagraph {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 10pt 36pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-bidi-font-size: 11.0pt; mso-fareast-font-family: Calibri; =
mso-fareast-theme-font: minor-latin; mso-bidi-font-family: "Times New =
Roman"; mso-bidi-theme-font: minor-bidi; mso-fareast-language: EN-US; =
mso-style-priority: 34; mso-add-space: auto
}
DIV.MsoListParagraph {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 10pt 36pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-bidi-font-size: 11.0pt; mso-fareast-font-family: Calibri; =
mso-fareast-theme-font: minor-latin; mso-bidi-font-family: "Times New =
Roman"; mso-bidi-theme-font: minor-bidi; mso-fareast-language: EN-US; =
mso-style-priority: 34; mso-add-space: auto
}
P.MsoListParagraphCxSpFirst {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 0pt 36pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-bidi-font-size: 11.0pt; mso-fareast-font-family: Calibri; =
mso-fareast-theme-font: minor-latin; mso-bidi-font-family: "Times New =
Roman"; mso-bidi-theme-font: minor-bidi; mso-fareast-language: EN-US; =
mso-style-priority: 34; mso-add-space: auto; mso-style-type: export-only
}
LI.MsoListParagraphCxSpFirst {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 0pt 36pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-bidi-font-size: 11.0pt; mso-fareast-font-family: Calibri; =
mso-fareast-theme-font: minor-latin; mso-bidi-font-family: "Times New =
Roman"; mso-bidi-theme-font: minor-bidi; mso-fareast-language: EN-US; =
mso-style-priority: 34; mso-add-space: auto; mso-style-type: export-only
}
DIV.MsoListParagraphCxSpFirst {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 0pt 36pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-bidi-font-size: 11.0pt; mso-fareast-font-family: Calibri; =
mso-fareast-theme-font: minor-latin; mso-bidi-font-family: "Times New =
Roman"; mso-bidi-theme-font: minor-bidi; mso-fareast-language: EN-US; =
mso-style-priority: 34; mso-add-space: auto; mso-style-type: export-only
}
P.MsoListParagraphCxSpMiddle {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 0pt 36pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-bidi-font-size: 11.0pt; mso-fareast-font-family: Calibri; =
mso-fareast-theme-font: minor-latin; mso-bidi-font-family: "Times New =
Roman"; mso-bidi-theme-font: minor-bidi; mso-fareast-language: EN-US; =
mso-style-priority: 34; mso-add-space: auto; mso-style-type: export-only
}
LI.MsoListParagraphCxSpMiddle {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 0pt 36pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-bidi-font-size: 11.0pt; mso-fareast-font-family: Calibri; =
mso-fareast-theme-font: minor-latin; mso-bidi-font-family: "Times New =
Roman"; mso-bidi-theme-font: minor-bidi; mso-fareast-language: EN-US; =
mso-style-priority: 34; mso-add-space: auto; mso-style-type: export-only
}
DIV.MsoListParagraphCxSpMiddle {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 0pt 36pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-bidi-font-size: 11.0pt; mso-fareast-font-family: Calibri; =
mso-fareast-theme-font: minor-latin; mso-bidi-font-family: "Times New =
Roman"; mso-bidi-theme-font: minor-bidi; mso-fareast-language: EN-US; =
mso-style-priority: 34; mso-add-space: auto; mso-style-type: export-only
}
P.MsoListParagraphCxSpLast {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 10pt 36pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-bidi-font-size: 11.0pt; mso-fareast-font-family: Calibri; =
mso-fareast-theme-font: minor-latin; mso-bidi-font-family: "Times New =
Roman"; mso-bidi-theme-font: minor-bidi; mso-fareast-language: EN-US; =
mso-style-priority: 34; mso-add-space: auto; mso-style-type: export-only
}
LI.MsoListParagraphCxSpLast {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 10pt 36pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-bidi-font-size: 11.0pt; mso-fareast-font-family: Calibri; =
mso-fareast-theme-font: minor-latin; mso-bidi-font-family: "Times New =
Roman"; mso-bidi-theme-font: minor-bidi; mso-fareast-language: EN-US; =
mso-style-priority: 34; mso-add-space: auto; mso-style-type: export-only
}
DIV.MsoListParagraphCxSpLast {
	LINE-HEIGHT: 115%; MARGIN: 0cm 0cm 10pt 36pt; FONT-FAMILY: =
"Arial","sans-serif"; FONT-SIZE: 10pt; mso-style-unhide: no; =
mso-style-qformat: yes; mso-pagination: widow-orphan; =
mso-bidi-font-size: 11.0pt; mso-fareast-font-family: Calibri; =
mso-fareast-theme-font: minor-latin; mso-bidi-font-family: "Times New =
Roman"; mso-bidi-theme-font: minor-bidi; mso-fareast-language: EN-US; =
mso-style-priority: 34; mso-add-space: auto; mso-style-type: export-only
}
SPAN.Heading1Char {
	FONT-FAMILY: "Times New Roman","serif"; FONT-WEIGHT: bold; =
mso-style-unhide: no; mso-bidi-font-size: 24.0pt; =
mso-fareast-font-family: "Times New Roman"; mso-bidi-font-family: "Times =
New Roman"; mso-fareast-language: EN-GB; mso-style-priority: 9; =
mso-style-link: "Heading 1"; mso-style-name: "Heading 1 Char"; =
mso-style-locked: yes; mso-ansi-font-size: 24.0pt; =
mso-ascii-font-family: "Times New Roman"; mso-hansi-font-family: "Times =
New Roman"; mso-font-kerning: 18.0pt
}
SPAN.Heading2Char {
	FONT-FAMILY: "Times New Roman","serif"; FONT-WEIGHT: bold; =
mso-style-unhide: no; mso-bidi-font-size: 18.0pt; =
mso-fareast-font-family: "Times New Roman"; mso-bidi-font-family: "Times =
New Roman"; mso-fareast-language: EN-GB; mso-style-priority: 9; =
mso-style-link: "Heading 2"; mso-style-name: "Heading 2 Char"; =
mso-style-locked: yes; mso-ansi-font-size: 18.0pt; =
mso-ascii-font-family: "Times New Roman"; mso-hansi-font-family: "Times =
New Roman"
}
SPAN.Heading3Char {
	FONT-FAMILY: "Times New Roman","serif"; FONT-WEIGHT: bold; =
mso-style-unhide: no; mso-bidi-font-size: 13.5pt; =
mso-fareast-font-family: "Times New Roman"; mso-bidi-font-family: "Times =
New Roman"; mso-fareast-language: EN-GB; mso-style-priority: 9; =
mso-style-link: "Heading 3"; mso-style-name: "Heading 3 Char"; =
mso-style-locked: yes; mso-ansi-font-size: 13.5pt; =
mso-ascii-font-family: "Times New Roman"; mso-hansi-font-family: "Times =
New Roman"
}
SPAN.SpellE {
	mso-style-name: ""; mso-spl-e: yes
}
SPAN.GramE {
	mso-style-name: ""; mso-gram-e: yes
}
.MsoChpDefault {
	FONT-FAMILY: "Arial","sans-serif"; FONT-SIZE: 10pt; =
mso-fareast-font-family: Calibri; mso-fareast-theme-font: minor-latin; =
mso-bidi-font-family: "Times New Roman"; mso-bidi-theme-font: =
minor-bidi; mso-fareast-language: EN-US; mso-style-type: export-only; =
mso-ansi-font-size: 10.0pt; mso-ascii-font-family: Arial; =
mso-hansi-font-family: Arial; mso-default-props: yes
}
.MsoPapDefault {
	LINE-HEIGHT: 115%; MARGIN-BOTTOM: 10pt; mso-style-type: export-only
}
DIV.WordSection1 {
	page: WordSection1
}
OL {
	MARGIN-BOTTOM: 0cm
}
UL {
	MARGIN-BOTTOM: 0cm
}
</STYLE>
<!--[if gte mso 10]>
<style>
 /* Style Definitions */
 table.MsoNormalTable
	{mso-style-name:"Table Normal";
	mso-tstyle-rowband-size:0;
	mso-tstyle-colband-size:0;
	mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-parent:"";
	mso-padding-alt:0cm 5.4pt 0cm 5.4pt;
	mso-para-margin-top:0cm;
	mso-para-margin-right:0cm;
	mso-para-margin-bottom:10.0pt;
	mso-para-margin-left:0cm;
	line-height:115%;
	mso-pagination:widow-orphan;
	font-size:10.0pt;
	mso-bidi-font-size:11.0pt;
	font-family:"Arial","sans-serif";
	mso-bidi-font-family:"Times New Roman";
	mso-bidi-theme-font:minor-bidi;
	mso-fareast-language:EN-US;}
</style>
<![endif]--><!--[if gte mso 9]><xml>
 <o:shapedefaults v:ext=3D"edit" spidmax=3D"1026"/>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <o:shapelayout v:ext=3D"edit">
  <o:idmap v:ext=3D"edit" data=3D"1"/>
 </o:shapelayout></xml><![endif]--></HEAD>
<BODY style=3D"tab-interval: 36.0pt" lang=3DEN-GB>
<DIV class=3DWordSection1>
<P=20
style=3D"TEXT-ALIGN: justify; LINE-HEIGHT: normal; mso-margin-top-alt: =
auto; mso-margin-bottom-alt: auto; mso-outline-level: 2"=20
class=3DMsoNormal><B><SPAN=20
style=3D"FONT-FAMILY: 'Times New Roman','serif'; mso-bidi-font-size: =
10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">ASME=20
B31G: Local Metal Loss <o:p></o:p></SPAN></B></P>
<P style=3D"TEXT-ALIGN: justify" class=3DMsoNormal><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">The=20
B31G-2009 procedure is applicable to all pipelines covered by the =
ANSI/ASME B31=20
code for pressure piping, and can also be applied to pipelines and =
piping=20
designed to a wide range of national and international standards. The =
procedure=20
was developed from a joint industry project starting in the late 1960's. =
The=20
procedure assesses the ability of a cylindrical pipe with a corrosion =
defect to=20
withstand internal pressure, and is underpinned by an extensive series =
of=20
full-sized tests.<SPAN style=3D"mso-spacerun: yes">&nbsp;=20
</SPAN><o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify" class=3DMsoNormal><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">A=20
Level 1 evaluation is a simple calculation based on the characterisation =
of an=20
area of metal loss in terms of the maximum depth and the axial extent of =
the=20
defect. This procedure assumes that:<o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify; TEXT-INDENT: -18pt; mso-list: l0 level1 =
lfo3"=20
class=3DMsoListParagraphCxSpFirst><![if !supportLists]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB"><SPAN=20
style=3D"mso-list: Ignore">1.<SPAN=20
style=3D"FONT: 7pt 'Times New =
Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=20
</SPAN></SPAN></SPAN><![endif]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">The=20
metal loss damage is on a <SPAN class=3DSpellE>weldable</SPAN> carbon =
steel or=20
high strength low alloy steel pipeline.<o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify; TEXT-INDENT: -18pt; mso-list: l0 level1 =
lfo3"=20
class=3DMsoListParagraphCxSpMiddle><![if !supportLists]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB"><SPAN=20
style=3D"mso-list: Ignore">2.<SPAN=20
style=3D"FONT: 7pt 'Times New =
Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=20
</SPAN></SPAN></SPAN><![endif]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">The=20
metal loss has a relatively smooth contour which causes low stress=20
concentrations.<o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify; TEXT-INDENT: -18pt; mso-list: l0 level1 =
lfo3"=20
class=3DMsoListParagraphCxSpMiddle><![if !supportLists]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB"><SPAN=20
style=3D"mso-list: Ignore">3.<SPAN=20
style=3D"FONT: 7pt 'Times New =
Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=20
</SPAN></SPAN></SPAN><![endif]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">Girth=20
or longitudinal welds which are affected by the metal loss area must be =
free=20
from workmanship flaws, and must have sufficient toughness to avoid =
brittle=20
failure.<o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify; TEXT-INDENT: -18pt; mso-list: l0 level1 =
lfo3"=20
class=3DMsoListParagraphCxSpMiddle><![if !supportLists]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB"><SPAN=20
style=3D"mso-list: Ignore">4.<SPAN=20
style=3D"FONT: 7pt 'Times New =
Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=20
</SPAN></SPAN></SPAN><![endif]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">Defects=20
caused by mechanical damage, and crack-like flaws, must be completely =
removed to=20
a smooth contour by grinding.<o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify; TEXT-INDENT: -18pt; mso-list: l0 level1 =
lfo3"=20
class=3DMsoListParagraphCxSpLast><![if !supportLists]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB"><SPAN=20
style=3D"mso-list: Ignore">5.<SPAN=20
style=3D"FONT: 7pt 'Times New =
Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=20
</SPAN></SPAN></SPAN><![endif]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">The=20
criteria are based on internal pressure only. Pipelines subjected to =
high=20
longitudinal stress or bending stresses should be evaluated using a more =

comprehensive assessment technique.<o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify" class=3DMsoNormal><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">Refer=20
to the ASME B31G document for a complete applicability and exclusions=20
list.<o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify" class=3DMsoNormal><B><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">Assessment=20
Information<o:p></o:p></SPAN></B></P>
<P style=3D"TEXT-ALIGN: justify" class=3DMsoNormal><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">The=20
<I style=3D"mso-bidi-font-style: normal">minimum</I> material properties =
shall be=20
used when conducting Level 1 evaluation for the purpose of determining =
the need=20
for a repair. <B style=3D"mso-bidi-font-weight: normal">Flow stress</B> =
is a=20
concept relevant to fracture mechanics and is used in Level 1 =
evaluation. It is=20
not a property specified in a material grade or finished product =
standard.=20
Research indicates that it may be defined variously as given below:<SPAN =

style=3D"mso-spacerun: yes">&nbsp;&nbsp; </SPAN><o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify; TEXT-INDENT: -18pt; mso-list: l1 level1 =
lfo2"=20
class=3DMsoListParagraphCxSpFirst><![if !supportLists]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB"><SPAN=20
style=3D"mso-list: Ignore">1.<SPAN=20
style=3D"FONT: 7pt 'Times New =
Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=20
</SPAN></SPAN></SPAN><![endif]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB"><SPAN=20
style=3D"mso-spacerun: yes">&nbsp;</SPAN><SPAN=20
class=3DSpellE>S<SUB>flow</SUB></SPAN> for plain carbon steel operating =
at=20
temperatures below 250=B0F (120=B0C) may be defined by <SPAN=20
class=3DSpellE>S<SUB>flow</SUB></SPAN> =3D 1.1 x =
SMYS.<o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify; TEXT-INDENT: -18pt; mso-list: l1 level1 =
lfo2"=20
class=3DMsoListParagraphCxSpMiddle><![if !supportLists]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB"><SPAN=20
style=3D"mso-list: Ignore">2.<SPAN=20
style=3D"FONT: 7pt 'Times New =
Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=20
</SPAN></SPAN></SPAN><![endif]><SPAN class=3DSpellE><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">S<SUB>flow</SUB></SPAN></SPAN><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">=20
for plain carbon and low-alloy steel having SMYS not in excess of 70 =
<SPAN=20
class=3DSpellE>ksi</SPAN> (483MPa)<SPAN style=3D"mso-spacerun: =
yes">&nbsp;=20
</SPAN>and operating at temperatures below 250=B0F (120=B0C) may be =
defined by <SPAN=20
class=3DSpellE>S<SUB>flow</SUB></SPAN> =3D SMYS + 10 <SPAN =
class=3DSpellE>ksi</SPAN>=20
(69 <SPAN class=3DSpellE>MPa</SPAN>).<o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify; TEXT-INDENT: -18pt; mso-list: l1 level1 =
lfo2"=20
class=3DMsoListParagraphCxSpLast><![if !supportLists]><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB"><SPAN=20
style=3D"mso-list: Ignore">3.<SPAN=20
style=3D"FONT: 7pt 'Times New =
Roman'">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=20
</SPAN></SPAN></SPAN><![endif]><SPAN class=3DSpellE><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">S<SUB>flow</SUB></SPAN></SPAN><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">=20
for plain carbon and low-alloy steel having SMYS not in excess of 80 =
<SPAN=20
class=3DSpellE>ksi</SPAN> (551 <SPAN class=3DSpellE>MPa</SPAN>) may be =
defined by=20
<SPAN class=3DSpellE>S<SUB>flow</SUB></SPAN> =3D (SYT+SUT)/2 where SYT =
and SUT are=20
specified at the operating temperature in accordance with the ASME =
Boiler and=20
Pressure Vessel Code, Section II, Part D.<SPAN=20
style=3D"mso-spacerun: yes">&nbsp;&nbsp; </SPAN><o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify" class=3DMsoNormal><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">Note=20
that the previous edition of B31G incorporated the first definition for =
flow=20
stress. Any of the above definitions are acceptable, according to the =
2009=20
edition of ASME B31G.<o:p></o:p></SPAN></P>
<P style=3D"TEXT-ALIGN: justify" class=3DMsoNormal><SPAN=20
style=3D"LINE-HEIGHT: 115%; FONT-FAMILY: 'Times New Roman','serif'; =
mso-bidi-font-size: 10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB; mso-bidi-font-weight: bold">An=20
acceptable safety factor (SF) should be defined for the assessment. ASME =
B31G=20
paragraph 1.9 provides guidance on the selection of appropriate safety=20
factors.<A name=3DJointFactor><o:p></o:p></A></SPAN></P>
<P=20
style=3D"TEXT-ALIGN: justify; LINE-HEIGHT: normal; mso-margin-top-alt: =
auto; mso-margin-bottom-alt: auto; mso-outline-level: 1"=20
class=3DMsoNormal><SPAN style=3D"mso-bookmark: JointFactor"><B><SPAN=20
style=3D"FONT-FAMILY: 'Times New Roman','serif'; mso-bidi-font-size: =
10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB; mso-font-kerning: =
18.0pt">References<o:p></o:p></SPAN></B></SPAN></P>
<P=20
style=3D"TEXT-ALIGN: justify; LINE-HEIGHT: normal; mso-margin-top-alt: =
auto; mso-margin-bottom-alt: auto"=20
class=3DMsoNormal><SPAN style=3D"mso-bookmark: JointFactor"><SPAN=20
style=3D"FONT-FAMILY: 'Times New Roman','serif'; mso-bidi-font-size: =
10.0pt; mso-fareast-font-family: 'Times New Roman'; =
mso-fareast-language: EN-GB">ASME=20
B31G - 2009 (Revision of ASME B31G - 1991): "Manual for Determining the=20
Remaining Strength of Corroded Pipelines. Supplement to ASME B31 Code =
for=20
Pressure Piping". <SPAN class=3DGramE>The American Society of Mechanical =

Engineers.</SPAN><o:p></o:p></SPAN></SPAN></P><SPAN=20
style=3D"mso-bookmark: JointFactor"></SPAN>
<P class=3DMsoNormal><SPAN=20
style=3D"LINE-HEIGHT: 115%; mso-bidi-font-size: =
10.0pt"><o:p>&nbsp;</o:p></SPAN></P></DIV></BODY></HTML>
