<?xml version="1.0"?>
<configuration>
    <configSections>
      <sectionGroup name="common">
        <section name="logging" type="Common.Logging.ConfigurationSectionHandler, Common.Logging"/>
      </sectionGroup>
      <section name="nlog" type="NLog.Config.ConfigSectionHand<PERSON>, NLog"/>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="IntegriWISE.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
        </sectionGroup>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="IntegriWISE.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <connectionStrings>
        <add name="IntegriWISE.Properties.Settings.IntegriWISE_LocalDBConnectionString"
            connectionString="Data Source=.\SQLEXPRESS;AttachDbFilename=|DataDirectory|\Data\IntegriWISE_LocalDB.mdf;Integrated Security=True;User Instance=True"
            providerName="System.Data.SqlClient" />
    </connectionStrings>
    <applicationSettings>
        <IntegriWISE.Properties.Settings>
            <setting name="ApplicationName" serializeAs="String">
                <value>IntegriWISE</value>
            </setting>
        </IntegriWISE.Properties.Settings>
    </applicationSettings>
    <userSettings>
        <IntegriWISE.Properties.Settings>
            <setting name="UpgradeSettings" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="NewInstall" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="DatabaseUsername" serializeAs="String">
                <value />
            </setting>
            <setting name="DatabaseType" serializeAs="String">
                <value>SQL Server</value>
            </setting>
            <setting name="Theme" serializeAs="String">
                <value>Office 2010 Silver</value>
            </setting>
            <setting name="MainWindowPlacement" serializeAs="String">
                <value />
            </setting>
            <setting name="DatabasePassword" serializeAs="String">
                <value />
            </setting>
            <setting name="LastUsername" serializeAs="String">
                <value />
            </setting>
            <setting name="SQLInstance" serializeAs="String">
                <value>.\SQLEXPRESS</value>
            </setting>
            <setting name="SQLUsername" serializeAs="String">
                <value />
            </setting>
            <setting name="LocalDBName" serializeAs="String">
                <value>IntegriWISE_LocalDB</value>
            </setting>
            <setting name="UserInstanceDBName" serializeAs="String">
                <value>IntegriWISE_UserDB</value>
            </setting>
            <setting name="UpdateCheckDate" serializeAs="String">
                <value />
            </setting>
        </IntegriWISE.Properties.Settings>
    </userSettings>
<startup useLegacyV2RuntimeActivationPolicy="true">
  <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/>
</startup>
  <common>
    <logging>
      <factoryAdapter type="Common.Logging.NLog.NLogLoggerFactoryAdapter, Common.Logging.NLog20">
        <arg key="configType" value="INLINE"/>
        <arg key="level" value="DEBUG"/>
        <arg key="showLogName" value="true"/>
        <arg key="showDataTime" value="true"/>
        <arg key="dateTimeFormat" value="yyyy/MM/dd HH:mm:ss:fff"/>
      </factoryAdapter>
    </logging>
  </common>
  <nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <variable name="appTitle" value="IntegriWISE"/>
    <variable name="logFilePath" value="${specialfolder:folder=ApplicationData}/TWI Software/IntegriWISE/IntegriWISE.log"/>
    <targets>
      <target name="archivedFile" xsi:type="File" fileName="${logFilePath}" archiveFileName="${logFilePath}.{#####}" layout="${longdate} ${level:upperCase=true}: ${logger} ${message}" archiveAboveSize="10240" archiveNumbering="Sequence" concurrentWrites="true"/>
      <target name="fileAsInfo" xsi:type="File" fileName="${logFilePath}" layout="${longdate} ${level:upperCase=true}: ${message}"/>
      <target name="file1" xsi:type="File" fileName="${logFilePath}.exceptions" layout="${longdate} ${level:upperCase=true}: ${message}${newline}(${stacktrace}) ${exception:format=ToString}"/>
    </targets>
    <rules>
      <logger name="*" minlevel="Debug" writeTo="archivedFile"/>
      <logger name="*" minlevel="Error" writeTo="file1"/>
    </rules>
  </nlog>
  <runtime>
       <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <dependentAssembly>
                 <assemblyIdentity name="NLog" publicKeyToken="5120e14c03d0593c" culture="neutral"/>
                 <bindingRedirect oldVersion="0.0.0.0-2.1.0.0" newVersion="2.1.0.0"/>
            </dependentAssembly>
         <dependentAssembly>
           <assemblyIdentity name="Common.Logging" publicKeyToken="af08829b84f0328e" culture="neutral"/>
           <bindingRedirect oldVersion="0.0.0.0-2.2.0.0" newVersion="2.2.0.0"/>
         </dependentAssembly>
       	<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.SqlEnum" publicKeyToken="89845DCD8080CC91" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.Management.SmoMetadataProvider" publicKeyToken="89845DCD8080CC91" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.ConnectionInfo" publicKeyToken="89845DCD8080CC91" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.Dmf" publicKeyToken="89845DCD8080CC91" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.SqlClrProvider" publicKeyToken="89845DCD8080CC91" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.Diagnostics.STrace" publicKeyToken="89845DCD8080CC91" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.Management.Sdk.Sfc" publicKeyToken="89845DCD8080CC91" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.ServiceBrokerEnum" publicKeyToken="89845DCD8080CC91" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.Smo" publicKeyToken="89845DCD8080CC91" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.SqlServer.Management.SqlParser" publicKeyToken="89845DCD8080CC91" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-11.0.0.0" newVersion="11.0.0.0"/>
			</dependentAssembly>
		</assemblyBinding>
  
  </runtime>
</configuration>
