SET NUMERIC_ROUNDABORT OFF

SET ANSI_PADDING, ANSI_WARNINGS, CONCAT_NULL_YIELDS_NULL, ARITHABORT, QUOTED_IDENTIFIER, ANSI_NULLS ON

IF EXISTS (SELECT * FROM tempdb..sysobjects WHERE id=OBJECT_ID('tempdb..#tmpErrors')) DROP TABLE #tmpErrors

CREATE TABLE #tmpErrors (Error int)

SET XACT_ABORT ON

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE

BEGIN TRANSACTION

PRINT N'Altering [dbo].[IW_SETTINGS]'

ALTER TABLE [dbo].[IW_SETTINGS] ADD
[UnlockCode] [nvarchar] (max) COLLATE Latin1_General_CI_AI NULL,
[CompanyName] [nvarchar] (250) COLLATE Latin1_General_CI_AI NULL

IF @@ERROR<>0 AND @@TRANCOUNT>0 R<PERSON>L<PERSON><PERSON>K TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

IF EXISTS (SELECT * FROM #tmpErrors) ROLLBACK TRANSACTION

IF @@TRANCOUNT>0 BEGIN
PRINT 'The database update succeeded'
COMMIT TRANSACTION
END
ELSE PRINT 'The database update failed'

DROP TABLE #tmpErrors

