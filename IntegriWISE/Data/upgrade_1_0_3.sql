SET NUMERIC_ROUNDABORT OFF

SET ANSI_PADDING, ANSI_WARNINGS, CONCAT_NULL_YIELDS_NULL, ARITHABORT, QUOTED_IDENTIFIER, ANSI_NULLS ON

IF EXISTS (SELECT * FROM tempdb..sysobjects WHERE id=OBJECT_ID('tempdb..#tmpErrors')) DROP TABLE #tmpErrors

CREATE TABLE #tmpErrors (Error int)

SET XACT_ABORT ON

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE

BEGIN TRANSACTION

PRINT N'Creating [dbo].[IW_ASSESS_IMAGE]'

CREATE TABLE [dbo].[IW_ASSESS_IMAGE]
(
[ImageID] [int] NOT NULL IDENTITY(1, 1),
[AssessmentID] [int] NOT NULL,
[ImageName] [nvarchar] (50) COLLATE Latin1_General_CI_AI NOT NULL,
[ImageDescription] [nvarchar] (500) COLLATE Latin1_General_CI_AI NULL,
[ImageBinary] [image] NOT NULL,
[ImageBinarySmall] [image] NOT NULL,
[Created] [datetime] NULL CONSTRAINT [DF_64CE793D_IW_ASSESS_IMAGE_Created] DEFAULT (getdate()),
[CreatedBy] [nvarchar] (50) COLLATE Latin1_General_CI_AI NULL,
[Modified] [datetime] NULL,
[ModifiedBy] [nvarchar] (50) COLLATE Latin1_General_CI_AI NULL,
[AuditingID] [uniqueidentifier] NOT NULL CONSTRAINT [DF_64CE793D_IW_ASSESS_IMAGE_AuditingID] DEFAULT (newid())
)

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Creating primary key [PK_IW_ASSESS_IMAGE] on [dbo].[IW_ASSESS_IMAGE]'

ALTER TABLE [dbo].[IW_ASSESS_IMAGE] ADD CONSTRAINT [PK_IW_ASSESS_IMAGE] PRIMARY KEY CLUSTERED  ([ImageID])

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Creating index [IX_IW_ASSESS_IMAGE] on [dbo].[IW_ASSESS_IMAGE]'

CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_IMAGE] ON [dbo].[IW_ASSESS_IMAGE] ([AuditingID])

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Creating [dbo].[IW_ASSESS_FILE]'

CREATE TABLE [dbo].[IW_ASSESS_FILE]
(
[FileID] [int] NOT NULL IDENTITY(1, 1),
[AssessmentID] [int] NOT NULL,
[FileDocName] [nvarchar] (100) COLLATE Latin1_General_CI_AI NOT NULL,
[FileType] [int] NOT NULL,
[FileDescription] [nvarchar] (500) COLLATE Latin1_General_CI_AI NULL,
[OriFileName] [nvarchar] (100) COLLATE Latin1_General_CI_AI NOT NULL,
[FileBinary] [varbinary] (max) NOT NULL,
[FileSize] [nvarchar] (50) COLLATE Latin1_General_CI_AI NOT NULL,
[FileExt] [nvarchar] (50) COLLATE Latin1_General_CI_AI NOT NULL,
[DateUploaded] [datetime] NOT NULL,
[Created] [datetime] NULL CONSTRAINT [DF_256D6B9D_IW_ASSESS_FILE_Created] DEFAULT (getdate()),
[CreatedBy] [nvarchar] (50) COLLATE Latin1_General_CI_AI NULL,
[Modified] [datetime] NULL,
[ModifiedBy] [nvarchar] (50) COLLATE Latin1_General_CI_AI NULL,
[AuditingID] [uniqueidentifier] NOT NULL CONSTRAINT [DF_256D6B9D_IW_ASSESS_FILE_AuditingID] DEFAULT (newid())
)

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Creating primary key [PK_IW_ASSESS_FILE] on [dbo].[IW_ASSESS_FILE]'

ALTER TABLE [dbo].[IW_ASSESS_FILE] ADD CONSTRAINT [PK_IW_ASSESS_FILE] PRIMARY KEY CLUSTERED  ([FileID])

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Creating index [IX_IW_ASSESS_FILE] on [dbo].[IW_ASSESS_FILE]'

CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_FILE] ON [dbo].[IW_ASSESS_FILE] ([AuditingID])

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Adding foreign keys to [dbo].[IW_ASSESS_FILE]'

ALTER TABLE [dbo].[IW_ASSESS_FILE] ADD CONSTRAINT [FK_IW_ASSESS_FILE_INTERFACE_ASSESSMENT] FOREIGN KEY ([AssessmentID]) REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID]) ON DELETE CASCADE

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Adding foreign keys to [dbo].[IW_ASSESS_IMAGE]'

ALTER TABLE [dbo].[IW_ASSESS_IMAGE] ADD CONSTRAINT [FK_IW_ASSESS_IMAGE_INTERFACE_ASSESSMENT] FOREIGN KEY ([AssessmentID]) REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID]) ON DELETE CASCADE

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

IF EXISTS (SELECT * FROM #tmpErrors) ROLLBACK TRANSACTION

IF @@TRANCOUNT>0 BEGIN
PRINT 'The database update succeeded'
COMMIT TRANSACTION
END
ELSE PRINT 'The database update failed'

DROP TABLE #tmpErrors
