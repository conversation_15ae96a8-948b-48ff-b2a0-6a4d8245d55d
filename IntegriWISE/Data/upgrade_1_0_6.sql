SET NUMERIC_ROUNDABORT OFF

SET ANSI_PADDING, ANSI_WARNINGS, CONCAT_NULL_YIELDS_NULL, ARITHABORT, QUOTED_IDENTIFIER, ANSI_NULLS ON

IF EXISTS (SELECT * FROM tempdb..sysobjects WHERE id=OBJECT_ID('tempdb..#tmpErrors')) DROP TABLE #tmpErrors

CREATE TABLE #tmpErrors (Error int)

SET XACT_ABORT ON

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE

BEGIN TRANSACTION

PRINT N'Creating [dbo].[IW_ASSESS_P5_DNVF101_PART_B]'

CREATE TABLE [dbo].[IW_ASSESS_P5_DNVF101_PART_B]
(
[ID] [int] NOT NULL,
[CombinedLoad] [bit] NOT NULL CONSTRAINT [DF_IW_ASSESS_P5_DNVF101_PART_B_CombinedLoad] DEFAULT ((0)),
[MaxCorrodedDepth] [float] NULL,
[l] [float] NULL,
[c] [float] NULL,
[s] [float] NULL,
[Phi] [float] NULL,
[FX] [float] NULL,
[MY] [float] NULL,
[Created] [datetime] NULL CONSTRAINT [DF_7C7F1C1A_IW_ASSESS_P5_DNVF101_PART_B_Created] DEFAULT (getdate()),
[CreatedBy] [nvarchar] (50) COLLATE Latin1_General_CI_AI NULL,
[Modified] [datetime] NULL,
[ModifiedBy] [nvarchar] (50) COLLATE Latin1_General_CI_AI NULL,
[AuditingID] [uniqueidentifier] NOT NULL CONSTRAINT [DF_7C7F1C1A_IW_ASSESS_P5_DNVF101_PART_B_AuditingID] DEFAULT (newid())
)

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Creating primary key [PK_IW_ASSESS_P5_DNVF101_PART_B] on [dbo].[IW_ASSESS_P5_DNVF101_PART_B]'

ALTER TABLE [dbo].[IW_ASSESS_P5_DNVF101_PART_B] ADD CONSTRAINT [PK_IW_ASSESS_P5_DNVF101_PART_B] PRIMARY KEY CLUSTERED  ([ID])

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Creating index [IX_IW_ASSESS_P5_DNVF101_PART_B] on [dbo].[IW_ASSESS_P5_DNVF101_PART_B]'

CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P5_DNVF101_PART_B] ON [dbo].[IW_ASSESS_P5_DNVF101_PART_B] ([AuditingID])

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Adding foreign keys to [dbo].[IW_ASSESS_P5_DNVF101_PART_B]'

ALTER TABLE [dbo].[IW_ASSESS_P5_DNVF101_PART_B] ADD CONSTRAINT [FK_IW_ASSESS_P5_DNVF101_PART_B_INTERFACE_ASSESSMENT] FOREIGN KEY ([ID]) REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID]) ON DELETE CASCADE

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

IF EXISTS (SELECT * FROM #tmpErrors) ROLLBACK TRANSACTION

IF @@TRANCOUNT>0 BEGIN
PRINT 'The database update succeeded'
COMMIT TRANSACTION
END
ELSE PRINT 'The database update failed'

DROP TABLE #tmpErrors

INSERT INTO [dbo].[ASSESSMENT_TYPE] ([AssessmentTypeID], [AssessmentTypeName], [AssessmentTypeCode], [AssessmentCategoryID]) VALUES (23, N'Local Metal Loss to DNV-RP-F101 Part B', N'LocalMetalLossDNVF101PartB', 4)
INSERT INTO [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentTypeID]) VALUES (1, 1, 23)
INSERT INTO [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentTypeID]) VALUES (1, 6, 23)
INSERT INTO [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentTypeID]) VALUES (1, 11, 23)
INSERT INTO [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentTypeID]) VALUES (2, 1, 23)
INSERT INTO [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentTypeID]) VALUES (2, 6, 23)
INSERT INTO [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentTypeID]) VALUES (2, 11, 23)