SET NUMERIC_ROUNDABORT OFF

SET ANSI_PADDING, ANSI_WARNINGS, CONCAT_NULL_YIELDS_NULL, ARITHABORT, QUOTED_IDENTIFIER, ANSI_NULLS ON

IF EXISTS (SELECT * FROM tempdb..sysobjects WHERE id=OBJECT_ID('tempdb..#tmpErrors')) DROP TABLE #tmpErrors

CREATE TABLE #tmpErrors (Error int)

SET XACT_ABORT ON

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE

BEGIN TRANSACTION

PRINT N'Altering [dbo].[COMPONENT_DETAIL]'

ALTER TABLE [dbo].[COMPONENT_DETAIL] ADD
[OperatingPressure] [float] NULL,
[OperatingTemperature] [float] NULL

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Creating [dbo].[IW_COMPONENT_DETAIL]'

CREATE TABLE [dbo].[IW_COMPONENT_DETAIL]
(
[ID] [int] NOT NULL,
[OperatingPressure] [float] NULL,
[OperatingTemperature] [float] NULL,
[Created] [datetime] NULL CONSTRAINT [DF_AD71B10F_IW_COMPONENT_DETAIL_Created] DEFAULT (getdate()),
[CreatedBy] [nvarchar] (50) COLLATE Latin1_General_CI_AI NULL,
[Modified] [datetime] NULL,
[ModifiedBy] [nvarchar] (50) COLLATE Latin1_General_CI_AI NULL,
[AuditingID] [uniqueidentifier] NOT NULL CONSTRAINT [DF_AD71B10F_IW_COMPONENT_DETAIL_AuditingID] DEFAULT (newid())
)

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Creating primary key [PK_IW_COMPONENT_DETAIL] on [dbo].[IW_COMPONENT_DETAIL]'

ALTER TABLE [dbo].[IW_COMPONENT_DETAIL] ADD CONSTRAINT [PK_IW_COMPONENT_DETAIL] PRIMARY KEY CLUSTERED  ([ID])

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Creating index [IX_IW_COMPONENT_DETAIL] on [dbo].[IW_COMPONENT_DETAIL]'

CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_COMPONENT_DETAIL] ON [dbo].[IW_COMPONENT_DETAIL] ([AuditingID])

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Adding foreign keys to [dbo].[IW_COMPONENT_DETAIL]'

ALTER TABLE [dbo].[IW_COMPONENT_DETAIL] ADD CONSTRAINT [FK_IW_COMPONENT_DETAIL_INTERFACE_ASSESSMENT] FOREIGN KEY ([ID]) REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID]) ON DELETE CASCADE

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

IF EXISTS (SELECT * FROM #tmpErrors) ROLLBACK TRANSACTION

IF @@TRANCOUNT>0 BEGIN
PRINT 'The database update succeeded'
COMMIT TRANSACTION
END
ELSE PRINT 'The database update failed'

DROP TABLE #tmpErrors

