SET NUMERIC_ROUNDABORT OFF

SET ANSI_PADDING, ANSI_WARNINGS, CONCAT_NULL_YIELDS_NULL, ARITHABORT, QUOTED_IDENTIFIER, ANSI_NULLS ON

IF EXISTS (SELECT * FROM tempdb..sysobjects WHERE id=OBJECT_ID('tempdb..#tmpErrors')) DROP TABLE #tmpErrors

CREATE TABLE #tmpErrors (Error int)

SET XACT_ABORT ON

SET TRANSACTION ISOLATION LEVEL SERIALIZABLE

BEGIN TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

PRINT N'Altering [dbo].[IW_ASSESS_P12_DENT_GOUGE]'

ALTER TABLE [dbo].[IW_ASSESS_P12_DENT_GOUGE] ADD
[Ey] [float] NULL,
[CVN] [float] NULL,
[dg] [float] NULL,
[ddp] [float] NULL,
[dd0] [float] NULL,
[Lw] [float] NULL,
[Lmsd] [float] NULL,
[PressureCycle] [bit] NOT NULL CONSTRAINT [DF_IW_ASSESS_P12_DENT_UGE_PressureCycle] DEFAULT ((0)),
[NoOfCycle] [int] NULL,
[rd] [float] NULL,
[Pmax] [float] NULL,
[Pmin] [float] NULL,
[FCAi] [float] NULL,
[FCAe] [float] NULL,
[LOSSi] [float] NULL,
[LOSSe] [float] NULL,
[Level2] [bit] NOT NULL CONSTRAINT [DF_IW_ASSESS_P12_DENT_UGE_Level2] DEFAULT ((0))

IF @@ERROR<>0 AND @@TRANCOUNT>0 ROLLBACK TRANSACTION

IF @@TRANCOUNT=0 BEGIN INSERT INTO #tmpErrors (Error) SELECT 1 BEGIN TRANSACTION END

IF EXISTS (SELECT * FROM #tmpErrors) ROLLBACK TRANSACTION

IF @@TRANCOUNT>0 BEGIN
PRINT 'The database update succeeded'
COMMIT TRANSACTION
END
ELSE PRINT 'The database update failed'

DROP TABLE #tmpErrors



INSERT INTO [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentCategoryID]) VALUES (1, 1, 10)
INSERT INTO [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentCategoryID]) VALUES (1, 6, 10)
INSERT INTO [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentCategoryID]) VALUES (1, 11, 10)
INSERT INTO [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentCategoryID]) VALUES (2, 1, 10)
INSERT INTO [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentCategoryID]) VALUES (2, 6, 10)
INSERT INTO [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentCategoryID]) VALUES (2, 11, 10)

INSERT INTO [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentTypeID]) VALUES (1, 1, 7)
INSERT INTO [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentTypeID]) VALUES (1, 6, 7)
INSERT INTO [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentTypeID]) VALUES (1, 11, 7)
INSERT INTO [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentTypeID]) VALUES (2, 1, 7)
INSERT INTO [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentTypeID]) VALUES (2, 6, 7)
INSERT INTO [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ([DesignCodeID], [ComponentTypeID], [AssessmentTypeID]) VALUES (2, 11, 7)

