/****** Object:  Table [dbo].[INTERFACE_GEOMETRY]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[INTERFACE_GEOMETRY](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[DesignCodeApp] [nvarchar](100) NULL,
	[GeometryCode] [nvarchar](50) NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_INTERFACE_GEOMETRY] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_INTERFACE_GEOMETRY] ON [dbo].[INTERFACE_GEOMETRY] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[DESIGN_CODE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DESIGN_CODE](
	[DesignCodeID] [int] NOT NULL,
	[DesignCode] [nvarchar](100) NOT NULL,
	[DesignCodeApp] [nvarchar](100) NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_DESIGN_CODE] PRIMARY KEY CLUSTERED 
(
	[DesignCodeID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_DESIGN_CODE] ON [dbo].[DESIGN_CODE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[COMPONENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[COMPONENT_TYPE](
	[ComponentTypeID] [int] NOT NULL,
	[ComponentTypeName] [nvarchar](50) NOT NULL,
	[ComponentTypeCode] [nvarchar](50) NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_COMPONENT_TYPE] PRIMARY KEY CLUSTERED 
(
	[ComponentTypeID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_COMPONENT_TYPE] ON [dbo].[COMPONENT_TYPE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[EQUIPMENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[EQUIPMENT_TYPE](
	[EquipmentTypeID] [int] NOT NULL,
	[EquipmentTypeName] [nvarchar](50) NOT NULL,
	[EquipmentTypeCode] [nvarchar](50) NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_EQUIPMENT_TYPE] PRIMARY KEY CLUSTERED 
(
	[EquipmentTypeID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_EQUIPMENT_TYPE] ON [dbo].[EQUIPMENT_TYPE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ASSESSMENT_CATEGORY]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ASSESSMENT_CATEGORY](
	[AssessmentCategoryID] [int] NOT NULL,
	[AssessmentCategoryName] [nvarchar](100) NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_ASSESSMENT_CATEGORY] PRIMARY KEY CLUSTERED 
(
	[AssessmentCategoryID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_ASSESSMENT_CATEGORY] ON [dbo].[ASSESSMENT_CATEGORY] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_ASMEB31_3_MIN_TEMPERATURE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_ASMEB31_3_MIN_TEMPERATURE](
	[MaterialID] [int] NOT NULL,
	[NominalThickness] [float] NULL,
	[A] [float] NULL,
	[B] [float] NULL,
	[C] [float] NULL,
	[D] [float] NULL,
 CONSTRAINT [PK_IW_MATERIALS_ASMEB31_3_MIN_TEMPERATURE] PRIMARY KEY CLUSTERED 
(
	[MaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_ASMEB31_3_COEFFICIENT_Y]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_ASMEB31_3_COEFFICIENT_Y](
	[MaterialID] [int] NOT NULL,
	[MaterialType] [nvarchar](255) NULL,
	[Tlower] [float] NULL,
	[Tupper] [float] NULL,
	[Tlower_SI] [float] NULL,
	[Tupper_SI] [float] NULL,
	[Ylower] [float] NULL,
	[Yupper] [float] NULL,
 CONSTRAINT [PK_IW_MATERIALS_ASMEB31_3_COEFFICIENT_Y] PRIMARY KEY CLUSTERED 
(
	[MaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_ASMEB31_3]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_ASMEB31_3](
	[MaterialID] [int] IDENTITY(1,1) NOT NULL,
	[UserDefined] [bit] NOT NULL,
	[CodeEdition] [nvarchar](25) NULL,
	[Page] [int] NULL,
	[RowNo] [int] NULL,
	[MaterialStringId] [nvarchar](255) NULL,
	[NominalComposition] [nvarchar](255) NULL,
	[ProductForm] [nvarchar](255) NULL,
	[Material] [nvarchar](255) NULL,
	[MaterialSpecificationNumber] [nvarchar](255) NULL,
	[PNoOrSNo] [nvarchar](50) NULL,
	[MaterialGrade] [nvarchar](255) NULL,
	[Notes] [nvarchar](255) NULL,
	[MinimumTemperatureC] [nvarchar](255) NULL,
	[MinimumTensileStrengthMpa] [float] NULL,
	[MinimumYieldStrengthMpa] [float] NULL,
	[MinimumTensileStrength_SI] [float] NULL,
	[MinimumYieldStrength_SI] [float] NULL,
	[MaterialType] [nvarchar](255) NULL,
	[StressTemperatureLimit] [float] NULL,
	[IsNew] [bit] NOT NULL,
 CONSTRAINT [PK_IW_MATERIALS_ASMEB31_3] PRIMARY KEY CLUSTERED 
(
	[MaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_API650]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_API650](
	[MaterialID] [int] IDENTITY(1,1) NOT NULL,
	[UserDefined] [bit] NOT NULL,
	[MaterialSpecificationNumber] [nvarchar](255) NULL,
	[MaterialGrade] [nvarchar](255) NULL,
	[MinimumYieldStrengthMpa] [float] NULL,
	[MinimumYieldStrength_SI] [float] NULL,
	[MinimumTensileStrengthMpa] [float] NULL,
	[MinimumTensileStrength_SI] [float] NULL,
	[ProductDesignStressSMPa] [float] NULL,
	[ProductDesignStressS_SI] [float] NULL,
	[CodeEdition] [nvarchar](25) NULL,
 CONSTRAINT [PK_IW_MATERIALS_API650] PRIMARY KEY CLUSTERED 
(
	[MaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_API620]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_API620](
	[MaterialID] [int] IDENTITY(1,1) NOT NULL,
	[UserDefined] [bit] NOT NULL,
	[MaterialSpecificationNumber] [nvarchar](255) NULL,
	[MaterialGrade] [nvarchar](255) NULL,
	[CodeEdition] [nvarchar](25) NULL,
	[ProductForm] [nvarchar](255) NULL,
	[Notes] [nvarchar](255) NULL,
	[MinimumYieldStrengthPsi] [float] NULL,
	[MinimumTensileStrengthPsi] [float] NULL,
	[ProductTensileStressSPsi] [float] NULL,
	[MinimumYieldStrengthMpa] [float] NULL,
	[MinimumTensileStrengthMpa] [float] NULL,
	[MaximumAllowableTensileStressSMPa] [float] NULL,
	[MinimumYieldStrength_SI] [float] NULL,
	[MinimumTensileStrength_SI] [float] NULL,
	[MaximumAllowableTensileStressS_SI] [float] NULL,
 CONSTRAINT [PK_IW_MATERIALS_API620] PRIMARY KEY CLUSTERED 
(
	[MaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[SITES]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[SITES](
	[SiteID] [int] IDENTITY(1,1) NOT NULL,
	[SiteName] [nvarchar](100) NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_SITES] PRIMARY KEY CLUSTERED 
(
	[SiteID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_SITES] ON [dbo].[SITES] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MATERIAL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MATERIAL](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[MaterialXML] [nvarchar](max) NULL,
	[DesignCode] [nvarchar](100) NULL,
	[SpecNo] [nvarchar](100) NULL,
	[NominalComposition] [nvarchar](100) NULL,
	[MaterialGrade] [nvarchar](100) NULL,
	[PNoSNo] [nvarchar](100) NULL,
	[ProductForm] [nvarchar](100) NULL,
	[MaterialType] [nvarchar](100) NULL,
	[CodeEdition] [nvarchar](10) NULL,
	[TensileStrength] [float] NULL,
	[YieldStrength] [float] NULL,
	[AllowableStress] [float] NULL,
	[TUpper] [float] NULL,
	[TLower] [float] NULL,
	[SUpper] [float] NULL,
	[SLower] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_MATERIAL] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_MATERIAL] ON [dbo].[MATERIAL] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[MANUFACTURER]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[MANUFACTURER](
	[ManufacturerID] [int] IDENTITY(1,1) NOT NULL,
	[ManufacturerName] [nvarchar](100) NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_MANUFACTURER] PRIMARY KEY CLUSTERED 
(
	[ManufacturerID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_MANUFACTURER] ON [dbo].[MANUFACTURER] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_ASMEVIII_DIV1]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_ASMEVIII_DIV1](
	[MaterialID] [int] IDENTITY(1,1) NOT NULL,
	[UserDefined] [bit] NOT NULL,
	[NominalComposition] [nvarchar](255) NULL,
	[ProductForm] [nvarchar](255) NULL,
	[MaterialSpecificationNumber] [nvarchar](255) NULL,
	[MaterialGrade] [nvarchar](255) NULL,
	[AlloyDestinationUNSNo] [nvarchar](255) NULL,
	[ClassConditionTemper] [nvarchar](255) NULL,
	[SizeThicknessMM] [nvarchar](255) NULL,
	[PNo] [int] NULL,
	[GroupNo] [int] NULL,
	[MinimumTensileStrengthMpa] [float] NULL,
	[MinimumYieldStrengthMpa] [float] NULL,
	[MinimumTensileStrength_SI] [float] NULL,
	[MinimumYieldStrength_SI] [float] NULL,
	[ApplicabilityI] [nvarchar](255) NULL,
	[ApplicabilityIII] [nvarchar](255) NULL,
	[ApplicabilityVIIIDiv1] [nvarchar](255) NULL,
	[ApplicabilityXII] [nvarchar](255) NULL,
	[ExternalPressureCharNo] [nvarchar](255) NULL,
	[Notes] [nvarchar](255) NULL,
	[CreepTemperature] [float] NULL,
	[CodeEdition] [nvarchar](255) NULL,
 CONSTRAINT [PK_IW_MATERIALS_ASMEVIII_DIV1] PRIMARY KEY CLUSTERED 
(
	[MaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_ASMEB31_8_LONGITUDINAL_JOINT_FACTOR]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_ASMEB31_8_LONGITUDINAL_JOINT_FACTOR](
	[MaterialID] [int] NOT NULL,
	[MaterialSpecificationNumber] [nvarchar](255) NULL,
	[PipeClass] [nvarchar](255) NULL,
	[EFactor] [float] NULL,
 CONSTRAINT [PK_IW_MATERIAL_ASMEB31_8_LONGITUDINAL_JOINT_FACTOR] PRIMARY KEY CLUSTERED 
(
	[MaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_ASMEB31_8]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_ASMEB31_8](
	[MaterialID] [int] IDENTITY(1,1) NOT NULL,
	[UserDefined] [bit] NOT NULL,
	[MaterialSpecificationNumber] [nvarchar](255) NULL,
	[MaterialGrade] [nvarchar](255) NULL,
	[Type] [nvarchar](255) NULL,
	[MinimumYieldStrengthPsi] [float] NULL,
	[MinimumYieldStrengthMpa] [float] NULL,
	[MinimumYieldStrength_SI] [float] NULL,
	[TensileStrengthKsi] [float] NULL,
	[TensileStrengthMpa] [float] NULL,
	[TensileStrength_SI] [float] NULL,
	[CodeEdition] [nvarchar](25) NULL,
 CONSTRAINT [PK_IW_MATERIALS_ASMEB31_8] PRIMARY KEY CLUSTERED 
(
	[MaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_ASMEB31_4]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_ASMEB31_4](
	[MaterialID] [int] IDENTITY(1,1) NOT NULL,
	[UserDefined] [bit] NOT NULL,
	[MaterialSpecificationNumber] [nvarchar](50) NULL,
	[MaterialGrade] [nvarchar](50) NULL,
	[CodeEdition] [nvarchar](50) NULL,
	[SMYS_PSI] [float] NULL,
	[SMYS_MPA] [float] NULL,
	[SMYS_SI] [float] NULL,
	[UTS_PSI] [float] NULL,
	[UTS_MPA] [float] NULL,
	[UTS_SI] [float] NULL,
	[WeldJointEfficiency] [float] NULL,
	[AllowableStress_S_PSI] [float] NULL,
	[AllowableStress_S_MPA] [float] NULL,
	[AllowableStress_S_SI] [float] NULL,
 CONSTRAINT [PK_IW_MATERIALS_ASMEB31_4] PRIMARY KEY CLUSTERED 
(
	[MaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_PD5500]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_PD5500](
	[MaterialID] [int] IDENTITY(1,1) NOT NULL,
	[UserDefined] [bit] NOT NULL,
	[TableNumber] [nvarchar](20) NULL,
	[CodeEdition] [nvarchar](20) NULL,
	[Page] [nvarchar](20) NULL,
	[ProductForm] [nvarchar](255) NULL,
	[MaterialTypeAndMethodOfManufacture] [nvarchar](255) NULL,
	[Grade] [nvarchar](20) NULL,
	[SpecifiedMinTensileStrengthMpa] [float] NULL,
	[SpecifiedMinYieldStrengthMpa] [float] NULL,
	[SpecifiedMinTensileStrength_SI] [float] NULL,
	[SpecifiedMinYieldStrength_SI] [float] NULL,
	[MaterialGroup] [float] NULL,
	[ThicknessMM] [nvarchar](20) NULL,
	[Notes] [nvarchar](255) NULL,
	[DesignLifetime] [float] NULL,
	[RulingSection] [nvarchar](20) NULL,
	[Diameter] [nvarchar](20) NULL,
 CONSTRAINT [PK_IW_MATERIALS_PD5500] PRIMARY KEY CLUSTERED 
(
	[MaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_FRACTURE_TOUGHNESS_CURVE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_FRACTURE_TOUGHNESS_CURVE](
	[MaterialID] [int] NOT NULL,
	[Material] [nvarchar](150) NOT NULL,
	[Curve] [nvarchar](1) NULL,
 CONSTRAINT [PK_IW_MATERIAL_FRACTURE_TOUGHNESS_CURVE] PRIMARY KEY CLUSTERED 
(
	[MaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_SETTINGS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_SETTINGS](
	[ID] [int] NOT NULL,
	[SchemaVersion] [nvarchar](500) NOT NULL,
	[MinimumAppVersion] [nvarchar](500) NOT NULL,
	[LocalDB] [bit] NOT NULL,
	[UnlockCode] [nvarchar](max) NULL,
	[CompanyName] [nvarchar](250) NULL,
	[Created] [datetime] NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_SETTINGS] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_TSF_CURVE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_TSF_CURVE](
	[TSFID] [int] NOT NULL,
	[TSF] [float] NULL,
	[LambdaCMinus02] [float] NULL,
	[C1] [float] NULL,
	[C2] [float] NULL,
	[C3] [float] NULL,
	[C4] [float] NULL,
	[C5] [float] NULL,
	[C6] [float] NULL,
 CONSTRAINT [PK_IW_MATERIAL_TSF_CURVE] PRIMARY KEY CLUSTERED 
(
	[TSFID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_STEEL_PIPE_DIMENSIONS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_STEEL_PIPE_DIMENSIONS](
	[MaterialID] [int] NOT NULL,
	[NominalSize] [float] NULL,
	[StringNominalSize] [float] NULL,
	[OutsideDiameter] [float] NULL,
	[OutsideDiameter_SI] [float] NULL,
	[WallThickness] [float] NULL,
	[WallThickness_SI] [float] NULL,
	[IdentificationThickness] [nvarchar](255) NULL,
	[Schedule] [nvarchar](255) NULL,
 CONSTRAINT [PK_IW_MATERIALS_STEEL_PIPE_DIMENSIONS] PRIMARY KEY CLUSTERED 
(
	[MaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_PITTING_GRADE_CHART]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_PITTING_GRADE_CHART](
	[GradeID] [int] NOT NULL,
	[PittingGrade] [int] NOT NULL,
	[Rwt] [float] NOT NULL,
	[CylinderRSF] [float] NOT NULL,
	[SphereRSF] [float] NOT NULL,
 CONSTRAINT [PK_IW_MATERIALS_PITTING_GRADE_CHART] PRIMARY KEY CLUSTERED 
(
	[GradeID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[USERS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[USERS](
	[UserID] [nvarchar](50) NOT NULL,
	[Title] [nvarchar](50) NULL,
	[FirstName] [nvarchar](50) NULL,
	[LastName] [nvarchar](50) NULL,
	[JobTitle] [nvarchar](50) NULL,
	[Department] [nvarchar](50) NULL,
	[Company] [nvarchar](50) NULL,
	[UserGroupName] [nvarchar](50) NULL,
	[ADAuthentication] [bit] NOT NULL,
	[IsActive] [bit] NOT NULL,
	[SysUser] [bit] NOT NULL,
	[LicenseKey] [nvarchar](100) NULL,
	[Password] [nvarchar](500) NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_USERS] PRIMARY KEY CLUSTERED 
(
	[UserID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_USERS] ON [dbo].[USERS] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[USER_GROUPS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[USER_GROUPS](
	[UserGroupID] [int] NOT NULL,
	[UserGroup] [nvarchar](50) NOT NULL,
	[SysGroup] [bit] NOT NULL,
	[Disabled] [bit] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_USER_GROUPS] PRIMARY KEY CLUSTERED 
(
	[UserGroupID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_USER_GROUPS] ON [dbo].[USER_GROUPS] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UNITS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UNITS](
	[UnitID] [int] NOT NULL,
	[UnitName] [nvarchar](50) NOT NULL,
	[SelectedUnit] [nvarchar](20) NOT NULL,
	[ISOUnit] [nvarchar](20) NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_UNITS] PRIMARY KEY CLUSTERED 
(
	[UnitID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_UNITS] ON [dbo].[UNITS] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Trigger [trMod_USERS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_USERS] ON [dbo].[USERS]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.USERS AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_USER_GROUPS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_USER_GROUPS] ON [dbo].[USER_GROUPS]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.USER_GROUPS AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_UNITS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_UNITS] ON [dbo].[UNITS]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.UNITS AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_SITES]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_SITES] ON [dbo].[SITES]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.SITES AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_MATERIAL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_MATERIAL] ON [dbo].[MATERIAL]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.MATERIAL AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_MANUFACTURER]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_MANUFACTURER] ON [dbo].[MANUFACTURER]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.MANUFACTURER AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_EQUIPMENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_EQUIPMENT_TYPE] ON [dbo].[EQUIPMENT_TYPE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.EQUIPMENT_TYPE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_INTERFACE_GEOMETRY]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_INTERFACE_GEOMETRY] ON [dbo].[INTERFACE_GEOMETRY]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.INTERFACE_GEOMETRY AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_SETTINGS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_SETTINGS] ON [dbo].[IW_SETTINGS]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_SETTINGS AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_ASSESSMENT_CATEGORY]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_ASSESSMENT_CATEGORY] ON [dbo].[ASSESSMENT_CATEGORY]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.ASSESSMENT_CATEGORY AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Table [dbo].[IW_MATERIALS_PD5500_STRESS_VALUES]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_PD5500_STRESS_VALUES](
	[StressMaterialID] [int] IDENTITY(1,1) NOT NULL,
	[MaterialID] [int] NULL,
	[TLower] [float] NULL,
	[TUpper] [float] NULL,
	[SLower] [float] NULL,
	[SUpper] [float] NULL,
 CONSTRAINT [PK_IW_MATERIALS_PD5500_STRESS_VALUES] PRIMARY KEY CLUSTERED 
(
	[StressMaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_ASMEVIII_DIV1_STRESS_VALUES]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_ASMEVIII_DIV1_STRESS_VALUES](
	[StressMaterialID] [int] IDENTITY(1,1) NOT NULL,
	[MaterialID] [int] NULL,
	[TLower] [float] NULL,
	[TUpper] [float] NULL,
	[SLower] [float] NULL,
	[SUpper] [float] NULL,
 CONSTRAINT [PK_IW_MATERIALS_ASMEVIII_DIV1_STRESS_VALUES] PRIMARY KEY CLUSTERED 
(
	[StressMaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIALS_ASMEB31_3_STRESS_VALUES]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIALS_ASMEB31_3_STRESS_VALUES](
	[StressMaterialID] [int] IDENTITY(1,1) NOT NULL,
	[MaterialID] [int] NOT NULL,
	[TLower] [float] NULL,
	[TUpper] [float] NULL,
	[SLower] [float] NULL,
	[SUpper] [float] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[IsNew] [bit] NOT NULL,
 CONSTRAINT [PK_IW_MATERIALS_ASMEB31_3_STRESS_VALUES] PRIMARY KEY CLUSTERED 
(
	[StressMaterialID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[EQUIPMENT_DESIGN_CODE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[EQUIPMENT_DESIGN_CODE](
	[DesignCodeID] [int] NOT NULL,
	[EquipmentTypeID] [int] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_EQUIPMENT_DESIGN_CODE] PRIMARY KEY CLUSTERED 
(
	[DesignCodeID] ASC,
	[EquipmentTypeID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_EQUIPMENT_DESIGN_CODE] ON [dbo].[EQUIPMENT_DESIGN_CODE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[EQUIPMENT_COMPONENT]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[EQUIPMENT_COMPONENT](
	[EquipmentTypeID] [int] NOT NULL,
	[ComponentTypeID] [int] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_EQUIPMENT_COMPONENT] PRIMARY KEY CLUSTERED 
(
	[EquipmentTypeID] ASC,
	[ComponentTypeID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_EQUIPMENT_COMPONENT] ON [dbo].[EQUIPMENT_COMPONENT] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Trigger [trMod_DESIGN_CODE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_DESIGN_CODE] ON [dbo].[DESIGN_CODE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.DESIGN_CODE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_COMPONENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_COMPONENT_TYPE] ON [dbo].[COMPONENT_TYPE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.COMPONENT_TYPE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Table [dbo].[GEOMETRY_SHELL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[GEOMETRY_SHELL](
	[ID] [int] NOT NULL,
	[NominalID] [float] NULL,
	[NominalThickness] [float] NULL,
	[NominalHeight] [float] NULL,
	[SupplementalThickness] [float] NULL,
	[MinReqThickness] [float] NULL,
	[WeldJointEfficiency] [float] NULL,
	[LongJointEfficiency] [float] NULL,
	[CircJointEfficiency] [float] NULL,
	[SpecificGravity] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_GEOMETRY_SHELL] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_GEOMETRY_SHELL] ON [dbo].[GEOMETRY_SHELL] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[GEOMETRY_SECTION]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[GEOMETRY_SECTION](
	[ID] [int] NOT NULL,
	[NominalOD] [float] NULL,
	[NominalThickness] [float] NULL,
	[SupplementalThickness] [float] NULL,
	[MechanicalAllowance] [float] NULL,
	[WeldJointEfficiency] [float] NULL,
	[LongJointEfficiency] [float] NULL,
	[CircJointEfficiency] [float] NULL,
	[DesignFactor] [float] NULL,
	[LocationClass] [nvarchar](50) NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_GEOMETRY_SECTION] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_GEOMETRY_SECTION] ON [dbo].[GEOMETRY_SECTION] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[GEOMETRY_REDUCER]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[GEOMETRY_REDUCER](
	[ID] [int] NOT NULL,
	[NominalOD] [float] NULL,
	[NominalOD2] [float] NULL,
	[NominalThickness] [float] NULL,
	[SupplementalThickness] [float] NULL,
	[MechanicalAllowance] [float] NULL,
	[OneHalfApexAngle] [float] NULL,
	[WeldJointEfficiency] [float] NULL,
	[LongJointEfficiency] [float] NULL,
	[CircJointEfficiency] [float] NULL,
	[DesignFactor] [float] NULL,
	[LocationClass] [nvarchar](50) NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_GEOMETRY_REDUCER] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_GEOMETRY_REDUCER] ON [dbo].[GEOMETRY_REDUCER] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[GEOMETRY_HEAD]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[GEOMETRY_HEAD](
	[ID] [int] NOT NULL,
	[NominalID] [float] NULL,
	[NominalThickness] [float] NULL,
	[WeldJointEfficiency] [float] NULL,
	[InsideCrownRadius] [float] NULL,
	[InsideKnuckleRadius] [float] NULL,
	[Height] [float] NULL,
	[CenteredCorroded] [bit] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_GEOMETRY_HEAD] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_GEOMETRY_HEAD] ON [dbo].[GEOMETRY_HEAD] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[GEOMETRY_ELBOW]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[GEOMETRY_ELBOW](
	[ID] [int] NOT NULL,
	[NominalOD] [float] NULL,
	[NominalThickness] [float] NULL,
	[SupplementalThickness] [float] NULL,
	[BendRadius] [float] NULL,
	[BendAngle] [float] NULL,
	[MechanicalAllowance] [float] NULL,
	[WeldJointEfficiency] [float] NULL,
	[LongJointEfficiency] [float] NULL,
	[CircJointEfficiency] [float] NULL,
	[LocationClass] [nvarchar](50) NULL,
	[DesignFactor] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_GEOMETRY_ELBOW] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_GEOMETRY_ELBOW] ON [dbo].[GEOMETRY_ELBOW] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[FACILITY]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FACILITY](
	[FacilityID] [int] IDENTITY(1,1) NOT NULL,
	[FacilityName] [nvarchar](100) NULL,
	[SiteID] [int] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_FACILITY] PRIMARY KEY CLUSTERED 
(
	[FacilityID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_FACILITY] ON [dbo].[FACILITY] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ASSESSMENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ASSESSMENT_TYPE](
	[AssessmentTypeID] [int] NOT NULL,
	[AssessmentTypeName] [nvarchar](100) NOT NULL,
	[AssessmentTypeCode] [nvarchar](100) NOT NULL,
	[AssessmentCategoryID] [int] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_ASSESSMENT_TYPE] PRIMARY KEY CLUSTERED 
(
	[AssessmentTypeID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_ASSESSMENT_TYPE] ON [dbo].[ASSESSMENT_TYPE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE](
	[DesignCodeID] [int] NOT NULL,
	[ComponentTypeID] [int] NOT NULL,
	[AssessmentCategoryID] [int] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_ASSESSMENT_CATEGORY_DESIGN_CODE] PRIMARY KEY CLUSTERED 
(
	[DesignCodeID] ASC,
	[ComponentTypeID] ASC,
	[AssessmentCategoryID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_ASSESSMENT_CATEGORY_DESIGN_CODE] ON [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[INTERFACE_ASSESSMENT](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[AssessmentTypeID] [int] NOT NULL,
	[AssessmentName] [nvarchar](100) NOT NULL,
	[AssessmentDesc] [nvarchar](100) NULL,
	[AssessmentCode] [nvarchar](100) NULL,
	[AssessmentDate] [datetime] NOT NULL,
	[GeometryCode] [nvarchar](50) NULL,
	[DesignCodeApp] [nvarchar](100) NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_INTERFACE_ASSESSMENT] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_INTERFACE_ASSESSMENT] ON [dbo].[INTERFACE_ASSESSMENT] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[EQUIPMENT_MASTER]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[EQUIPMENT_MASTER](
	[EquipmentID] [int] IDENTITY(1,1) NOT NULL,
	[EquipmentNumber] [nvarchar](100) NOT NULL,
	[EquipmentName] [nvarchar](150) NULL,
	[EquipmentTypeID] [int] NOT NULL,
	[EquipmentDesc] [nvarchar](250) NULL,
	[DesignCodeID] [int] NOT NULL,
	[FacilityID] [int] NOT NULL,
	[SiteID] [int] NOT NULL,
	[ManufacturerID] [int] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_EQUIPMENT_MASTER] PRIMARY KEY CLUSTERED 
(
	[EquipmentID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_EQUIPMENT_MASTER] ON [dbo].[EQUIPMENT_MASTER] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Trigger [trMod_ASSESSMENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_ASSESSMENT_TYPE] ON [dbo].[ASSESSMENT_TYPE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.ASSESSMENT_TYPE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_ASSESSMENT_CATEGORY_DESIGN_CODE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_ASSESSMENT_CATEGORY_DESIGN_CODE] ON [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.ASSESSMENT_CATEGORY_DESIGN_CODE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Table [dbo].[ASSESSMENT_TYPE_DESIGN_CODE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ASSESSMENT_TYPE_DESIGN_CODE](
	[DesignCodeID] [int] NOT NULL,
	[ComponentTypeID] [int] NOT NULL,
	[AssessmentTypeID] [int] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_ASSESSMENT_TYPE_DESIGN_CODE] PRIMARY KEY CLUSTERED 
(
	[DesignCodeID] ASC,
	[ComponentTypeID] ASC,
	[AssessmentTypeID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_ASSESSMENT_TYPE_DESIGN_CODE] ON [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Trigger [trMod_GEOMETRY_SHELL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_GEOMETRY_SHELL] ON [dbo].[GEOMETRY_SHELL]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.GEOMETRY_SHELL AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_GEOMETRY_SECTION]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_GEOMETRY_SECTION] ON [dbo].[GEOMETRY_SECTION]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.GEOMETRY_SECTION AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_GEOMETRY_REDUCER]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_GEOMETRY_REDUCER] ON [dbo].[GEOMETRY_REDUCER]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.GEOMETRY_REDUCER AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_GEOMETRY_HEAD]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_GEOMETRY_HEAD] ON [dbo].[GEOMETRY_HEAD]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.GEOMETRY_HEAD AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_GEOMETRY_ELBOW]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_GEOMETRY_ELBOW] ON [dbo].[GEOMETRY_ELBOW]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.GEOMETRY_ELBOW AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_FACILITY]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_FACILITY] ON [dbo].[FACILITY]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.FACILITY AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_EQUIPMENT_DESIGN_CODE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_EQUIPMENT_DESIGN_CODE] ON [dbo].[EQUIPMENT_DESIGN_CODE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.EQUIPMENT_DESIGN_CODE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_EQUIPMENT_COMPONENT]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_EQUIPMENT_COMPONENT] ON [dbo].[EQUIPMENT_COMPONENT]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.EQUIPMENT_COMPONENT AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_ASSESSMENT_TYPE_DESIGN_CODE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_ASSESSMENT_TYPE_DESIGN_CODE] ON [dbo].[ASSESSMENT_TYPE_DESIGN_CODE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.ASSESSMENT_TYPE_DESIGN_CODE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Table [dbo].[IW_THICKNESS_READINGS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_THICKNESS_READINGS](
	[AssessmentID] [int] NOT NULL,
	[ReadingsType] [nvarchar](50) NULL,
	[Trd] [float] NULL,
	[ThicknessPointsReadingsXML] [nvarchar](max) NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[Created] [datetime] NULL,
	[Modified] [datetime] NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_THICKNESS_READINGS] PRIMARY KEY CLUSTERED 
(
	[AssessmentID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_THICKNESS_READINGS] ON [dbo].[IW_THICKNESS_READINGS] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Trigger [trMod_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_INTERFACE_ASSESSMENT] ON [dbo].[INTERFACE_ASSESSMENT]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.INTERFACE_ASSESSMENT AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_EQUIPMENT_MASTER]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_EQUIPMENT_MASTER] ON [dbo].[EQUIPMENT_MASTER]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.EQUIPMENT_MASTER AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Table [dbo].[EQUIPMENT_DETAIL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[EQUIPMENT_DETAIL](
	[EquipmentID] [int] NOT NULL,
	[DesignPressure] [float] NULL,
	[DesignTemperature] [float] NULL,
	[MinOperatingTemperature] [float] NULL,
	[HydrotestPressure] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_EQUIPMENT_DETAIL] PRIMARY KEY CLUSTERED 
(
	[EquipmentID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_EQUIPMENT_DETAIL] ON [dbo].[EQUIPMENT_DETAIL] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[COMPONENT_MASTER]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[COMPONENT_MASTER](
	[ComponentID] [int] IDENTITY(1,1) NOT NULL,
	[ComponentNumber] [nvarchar](100) NOT NULL,
	[EquipmentID] [int] NOT NULL,
	[ComponentTypeID] [int] NOT NULL,
	[ComponentName] [nvarchar](150) NOT NULL,
	[ComponentDesc] [nvarchar](250) NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_COMPONENT_MASTER] PRIMARY KEY CLUSTERED 
(
	[ComponentID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_COMPONENT_MASTER] ON [dbo].[COMPONENT_MASTER] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P6_PITTING]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P6_PITTING](
	[ID] [int] NOT NULL,
	[Wmax] [float] NULL,
	[Grade] [int] NULL,
	[Hf] [float] NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[MFH] [bit] NOT NULL,
	[MAWP] [bit] NOT NULL,
	[Level2] [bit] NOT NULL,
	[DamageType] [nvarchar](50) NULL,
	[DamageSide] [nvarchar](50) NULL,
	[ni] [int] NULL,
	[ne] [int] NULL,
	[LMSD] [float] NULL,
	[TMM] [float] NULL,
	[LTAs] [float] NULL,
	[LTAc] [float] NULL,
	[EqLTAs] [float] NULL,
	[EqLTAc] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P6_PITTING] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P6_PITTING] ON [dbo].[IW_ASSESS_P6_PITTING] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P5_THIN_AREA]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P5_THIN_AREA](
	[ID] [int] NOT NULL,
	[MAWPMFH] [bit] NOT NULL,
	[Hf] [float] NULL,
	[Lc] [float] NULL,
	[Lm] [float] NULL,
	[Lmsd] [float] NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Level2] [bit] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P5_THIN_AREA] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P5_THIN_AREA] ON [dbo].[IW_ASSESS_P5_THIN_AREA] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P5_GROOVE_LIKE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P5_GROOVE_LIKE](
	[ID] [int] NOT NULL,
	[dg] [float] NULL,
	[dl] [float] NULL,
	[dw] [float] NULL,
	[dr] [float] NULL,
	[dAngle] [float] NULL,
	[Lmsd] [float] NULL,
	[Hf] [float] NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[MAWP] [bit] NOT NULL,
	[MFH] [bit] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P5_GROOVE_LIKE] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P5_GROOVE_LIKE] ON [dbo].[IW_ASSESS_P5_GROOVE_LIKE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P5_DNVF101_PART_B]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P5_DNVF101_PART_B](
	[ID] [int] NOT NULL,
	[CombinedLoad] [bit] NOT NULL,
	[AdjacentDefectPresent] [bit] NOT NULL,
	[MaxCorrodedDepth] [float] NULL,
	[l] [float] NULL,
	[c] [float] NULL,
	[s] [float] NULL,
	[Phi] [float] NULL,
	[FX] [float] NULL,
	[MY] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P5_DNVF101_PART_B] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P5_DNVF101_PART_B] ON [dbo].[IW_ASSESS_P5_DNVF101_PART_B] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P5_B31G]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P5_B31G](
	[ID] [int] NOT NULL,
	[Level2] [bit] NOT NULL,
	[MaxCorrodedDepth] [float] NULL,
	[LongitudinalExtent] [float] NULL,
	[SafetyFactor] [float] NULL,
	[SflowLookup] [nvarchar](50) NULL,
	[Lc] [float] NULL,
	[Lm] [float] NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P5_B31G] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P5_B31G] ON [dbo].[IW_ASSESS_P5_B31G] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P4_THINKNESS_PROFILE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P4_THINKNESS_PROFILE](
	[ID] [int] NOT NULL,
	[Level2] [bit] NOT NULL,
	[Lc] [float] NULL,
	[Lm] [float] NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P4_THINKNESS_PROFILE] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P4_THINKNESS_PROFILE] ON [dbo].[IW_ASSESS_P4_THINKNESS_PROFILE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P4_THICKNESS_READING]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P4_THICKNESS_READING](
	[ID] [int] NOT NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Level2] [bit] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P4_THICKNESS_READING] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P4_THICKNESS_READING] ON [dbo].[IW_ASSESS_P4_THICKNESS_READING] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P3_BRITTLE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P3_BRITTLE](
	[ID] [int] NOT NULL,
	[Material] [nvarchar](250) NULL,
	[ToughnessCurve] [nvarchar](2) NULL,
	[tg] [float] NULL,
	[CET] [float] NULL,
	[PWHT] [bit] NOT NULL,
	[LODMT] [bit] NOT NULL,
	[ImpactTested] [bit] NOT NULL,
	[ImpactTemperature] [float] NULL,
	[HydrotestPerformed] [bit] NOT NULL,
	[HydrotestTemperature] [float] NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Level2] [bit] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P3_BRITTLE] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P3_BRITTLE] ON [dbo].[IW_ASSESS_P3_BRITTLE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P13_LAMINATION]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P13_LAMINATION](
	[ID] [int] NOT NULL,
	[Level2] [bit] NOT NULL,
	[Hf] [float] NULL,
	[c] [float] NULL,
	[s] [float] NULL,
	[Multiple] [nvarchar](50) NULL,
	[Lh] [float] NULL,
	[Ls] [float] NULL,
	[tmm] [float] NULL,
	[Lw] [float] NULL,
	[Lmsd] [float] NULL,
	[OpInHydrogen] [bit] NOT NULL,
	[MAWP] [bit] NOT NULL,
	[MFH] [bit] NOT NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P13_LAMINATION] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P13_LAMINATION] ON [dbo].[IW_ASSESS_P13_LAMINATION] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P12_GOUGE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P12_GOUGE](
	[ID] [int] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P12_GOUGE] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P12_GOUGE] ON [dbo].[IW_ASSESS_P12_GOUGE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P12_DENT_GOUGE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P12_DENT_GOUGE](
	[ID] [int] NOT NULL,
	[Ey] [float] NULL,
	[CVN] [float] NULL,
	[dg] [float] NULL,
	[ddp] [float] NULL,
	[dd0] [float] NULL,
	[Lw] [float] NULL,
	[Lmsd] [float] NULL,
	[PressureCycle] [bit] NOT NULL,
	[NoOfCycle] [int] NULL,
	[rd] [float] NULL,
	[Pmax] [float] NULL,
	[Pmin] [float] NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Level2] [bit] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P12_DENT_GOUGE] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P12_DENT_GOUGE] ON [dbo].[IW_ASSESS_P12_DENT_GOUGE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P12_DENT]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P12_DENT](
	[ID] [int] NOT NULL,
	[ddp] [float] NULL,
	[dd0] [float] NULL,
	[Lw] [float] NULL,
	[Lmsd] [float] NULL,
	[PressureCycle] [bit] NOT NULL,
	[NoOfCycle] [int] NULL,
	[rd] [float] NULL,
	[Pmax] [float] NULL,
	[Pmin] [float] NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Level2] [bit] NOT NULL,
	[MAWP] [bit] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P12_DENT] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P12_DENT] ON [dbo].[IW_ASSESS_P12_DENT] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P10_CREEP]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P10_CREEP](
	[ID] [int] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P10_CREEP] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P10_CREEP] ON [dbo].[IW_ASSESS_P10_CREEP] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_MFH]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_MFH](
	[ID] [int] NOT NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_MFH] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_MEMBRANE_STRESS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_MEMBRANE_STRESS](
	[ID] [int] NOT NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_MEMBRANE_STRESS] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_MEMBRANE_STRESS] ON [dbo].[IW_ASSESS_MEMBRANE_STRESS] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_MAWP]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_MAWP](
	[ID] [int] NOT NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_MAWP] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_MAWP] ON [dbo].[IW_ASSESS_MAWP] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_IMAGE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_IMAGE](
	[ImageID] [int] IDENTITY(1,1) NOT NULL,
	[AssessmentID] [int] NOT NULL,
	[ImageName] [nvarchar](50) NOT NULL,
	[ImageDescription] [nvarchar](500) NULL,
	[ImageBinary] [image] NOT NULL,
	[ImageBinarySmall] [image] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_IMAGE] PRIMARY KEY CLUSTERED 
(
	[ImageID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_IMAGE] ON [dbo].[IW_ASSESS_IMAGE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_FILE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[IW_ASSESS_FILE](
	[FileID] [int] IDENTITY(1,1) NOT NULL,
	[AssessmentID] [int] NOT NULL,
	[FileDocName] [nvarchar](100) NOT NULL,
	[FileType] [int] NOT NULL,
	[FileDescription] [nvarchar](500) NULL,
	[OriFileName] [nvarchar](100) NOT NULL,
	[FileBinary] [varbinary](max) NOT NULL,
	[FileSize] [nvarchar](50) NOT NULL,
	[FileExt] [nvarchar](50) NOT NULL,
	[DateUploaded] [datetime] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_FILE] PRIMARY KEY CLUSTERED 
(
	[FileID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
SET ANSI_PADDING OFF
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_FILE] ON [dbo].[IW_ASSESS_FILE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_TMIN]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_TMIN](
	[ID] [int] NOT NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_TMIN] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_TMIN] ON [dbo].[IW_ASSESS_TMIN] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P9_CRACK_LIKE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P9_CRACK_LIKE](
	[ID] [int] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P9_CRACK_LIKE] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P9_CRACK_LIKE] ON [dbo].[IW_ASSESS_P9_CRACK_LIKE] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P8_WELD_MISALIGN]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P8_WELD_MISALIGN](
	[ID] [int] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P8_WELD_MISALIGN] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P8_WELD_MISALIGN] ON [dbo].[IW_ASSESS_P8_WELD_MISALIGN] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P8_SHELL_DISTORTION]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P8_SHELL_DISTORTION](
	[ID] [int] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P8_SHELL_DISTORTION] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P8_SHELL_DISTORTION] ON [dbo].[IW_ASSESS_P8_SHELL_DISTORTION] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P7_HIC]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P7_HIC](
	[ID] [int] NOT NULL,
	[c] [float] NULL,
	[Hf] [float] NULL,
	[s] [float] NULL,
	[Lh] [float] NULL,
	[Lw] [float] NULL,
	[Lmsd] [float] NULL,
	[tmmMinusID] [float] NULL,
	[tmmMinusOD] [float] NULL,
	[LHs] [float] NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Level2] [bit] NOT NULL,
	[MAWP] [bit] NOT NULL,
	[MFH] [bit] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P7_HIC] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P7_HIC] ON [dbo].[IW_ASSESS_P7_HIC] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P7_BLISTER]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P7_BLISTER](
	[ID] [int] NOT NULL,
	[c] [float] NULL,
	[s] [float] NULL,
	[LB] [float] NULL,
	[Bp] [float] NULL,
	[Hf] [float] NULL,
	[tmm] [float] NULL,
	[Vented] [int] NOT NULL,
	[Sc] [float] NULL,
	[Lw] [float] NULL,
	[Lmsd] [float] NULL,
	[FCAi] [float] NULL,
	[FCAe] [float] NULL,
	[LOSSi] [float] NULL,
	[LOSSe] [float] NULL,
	[Level2] [bit] NOT NULL,
	[MAWP] [bit] NOT NULL,
	[MFH] [bit] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P7_BLISTER] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_ASSESS_P7_BLISTER] ON [dbo].[IW_ASSESS_P7_BLISTER] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_MATERIAL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_MATERIAL](
	[ID] [int] NOT NULL,
	[MaterialXML] [nvarchar](max) NULL,
	[DesignCode] [nvarchar](100) NULL,
	[SpecNo] [nvarchar](100) NULL,
	[NominalComposition] [nvarchar](100) NULL,
	[MaterialGrade] [nvarchar](100) NULL,
	[PNoSNo] [nvarchar](100) NULL,
	[ProductForm] [nvarchar](100) NULL,
	[MaterialType] [nvarchar](100) NULL,
	[CodeEdition] [nvarchar](10) NULL,
	[TensileStrength] [float] NOT NULL,
	[YieldStrength] [float] NULL,
	[AllowableStress] [float] NULL,
	[TUpper] [float] NULL,
	[TLower] [float] NULL,
	[SUpper] [float] NULL,
	[SLower] [float] NULL,
	[UserOverride] [bit] NULL,
	[TensileStrengthUser] [float] NULL,
	[YieldStrengthUser] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_MATERIAL_1] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_MATERIAL] ON [dbo].[IW_MATERIAL] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_GEOMETRY_SHELL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_GEOMETRY_SHELL](
	[ID] [int] NOT NULL,
	[NominalID] [float] NULL,
	[NominalThickness] [float] NULL,
	[SupplementalThickness] [float] NULL,
	[NominalHeight] [float] NULL,
	[MinReqThickness] [float] NULL,
	[WeldJointEfficiency] [float] NULL,
	[LongJointEfficiency] [float] NULL,
	[CircJointEfficiency] [float] NULL,
	[SpecificGravity] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_GEOMETRY_SHELL] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_GEOMETRY_SHELL] ON [dbo].[IW_GEOMETRY_SHELL] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_GEOMETRY_SECTION]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_GEOMETRY_SECTION](
	[ID] [int] NOT NULL,
	[NominalOD] [float] NULL,
	[NominalThickness] [float] NULL,
	[SupplementalThickness] [float] NULL,
	[MechanicalAllowance] [float] NULL,
	[WeldJointEfficiency] [float] NULL,
	[LongJointEfficiency] [float] NULL,
	[CircJointEfficiency] [float] NULL,
	[DesignFactor] [float] NULL,
	[LocationClass] [nvarchar](50) NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_GEOMETRY_SECTION] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_GEOMETRY_SECTION] ON [dbo].[IW_GEOMETRY_SECTION] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_GEOMETRY_REDUCER]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_GEOMETRY_REDUCER](
	[ID] [int] NOT NULL,
	[NominalOD] [float] NULL,
	[NominalOD2] [float] NULL,
	[NominalThickness] [float] NULL,
	[SupplementalThickness] [float] NULL,
	[MechanicalAllowance] [float] NULL,
	[OneHalfApexAngle] [float] NULL,
	[WeldJointEfficiency] [float] NULL,
	[LongJointEfficiency] [float] NULL,
	[CircJointEfficiency] [float] NULL,
	[DesignFactor] [float] NULL,
	[LocationClass] [nvarchar](50) NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_GEOMETRY_REDUCER] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_GEOMETRY_REDUCER] ON [dbo].[IW_GEOMETRY_REDUCER] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_GEOMETRY_HEAD]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_GEOMETRY_HEAD](
	[ID] [int] NOT NULL,
	[NominalID] [float] NULL,
	[NominalThickness] [float] NULL,
	[WeldJointEfficiency] [float] NULL,
	[InsideCrownRadius] [float] NULL,
	[InsideKnuckleRadius] [float] NULL,
	[Height] [float] NULL,
	[CenteredCorroded] [bit] NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_GEOMETRY_HEAD] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_GEOMETRY_HEAD] ON [dbo].[IW_GEOMETRY_HEAD] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_GEOMETRY_ELBOW]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_GEOMETRY_ELBOW](
	[ID] [int] NOT NULL,
	[NominalOD] [float] NULL,
	[NominalThickness] [float] NULL,
	[SupplementalThickness] [float] NULL,
	[BendRadius] [float] NULL,
	[BendAngle] [float] NULL,
	[MechanicalAllowance] [float] NULL,
	[WeldJointEfficiency] [float] NULL,
	[LongJointEfficiency] [float] NULL,
	[CircJointEfficiency] [float] NULL,
	[LocationClass] [nvarchar](50) NULL,
	[DesignFactor] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_GEOMETRY_ELBOW] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_GEOMETRY_ELBOW] ON [dbo].[IW_GEOMETRY_ELBOW] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_EQUIPMENT_DETAIL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_EQUIPMENT_DETAIL](
	[ID] [int] NOT NULL,
	[DesignPressure] [float] NULL,
	[DesignTemperature] [float] NULL,
	[MinOperatingTemperature] [float] NULL,
	[HydrotestPressure] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_EQUIPMENT_DETAIL] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_EQUIPMENT_DETAIL] ON [dbo].[IW_EQUIPMENT_DETAIL] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_COMPONENT_DETAIL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_COMPONENT_DETAIL](
	[ID] [int] NOT NULL,
	[OperatingPressure] [float] NULL,
	[OperatingTemperature] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_COMPONENT_DETAIL] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_COMPONENT_DETAIL] ON [dbo].[IW_COMPONENT_DETAIL] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_COMPONENT_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_COMPONENT_ASSESSMENT](
	[AssessmentID] [int] NOT NULL,
	[ComponentID] [int] NOT NULL,
	[RecordType] [nvarchar](2) NOT NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_IW_COMPONENT_ASSESSMENT] PRIMARY KEY CLUSTERED 
(
	[AssessmentID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_IW_COMPONENT_ASSESSMENT] ON [dbo].[IW_COMPONENT_ASSESSMENT] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[IW_ASSESS_P6_PITTING_DETAIL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[IW_ASSESS_P6_PITTING_DETAIL](
	[DetailID] [int] IDENTITY(1,1) NOT NULL,
	[ID] [int] NOT NULL,
	[Di] [float] NOT NULL,
	[Dj] [float] NOT NULL,
	[Wi] [float] NOT NULL,
	[Wj] [float] NOT NULL,
	[DistanceP] [float] NOT NULL,
	[AngleTeta] [float] NOT NULL,
	[IsInside] [bit] NOT NULL,
 CONSTRAINT [PK_IW_ASSESS_P6_PITTING_DETAIL] PRIMARY KEY CLUSTERED 
(
	[DetailID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[COMPONENT_DETAIL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[COMPONENT_DETAIL](
	[ComponentID] [int] NOT NULL,
	[GeometryID] [int] NOT NULL,
	[MaterialID] [int] NULL,
	[OperatingPressure] [float] NULL,
	[OperatingTemperature] [float] NULL,
	[Created] [datetime] NULL,
	[CreatedBy] [nvarchar](50) NULL,
	[Modified] [datetime] NULL,
	[ModifiedBy] [nvarchar](50) NULL,
	[AuditingID] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_COMPONENT_DETAIL] PRIMARY KEY CLUSTERED 
(
	[ComponentID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
CREATE UNIQUE NONCLUSTERED INDEX [IX_COMPONENT_DETAIL] ON [dbo].[COMPONENT_DETAIL] 
(
	[AuditingID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
GO
/****** Object:  Trigger [trMod_EQUIPMENT_DETAIL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_EQUIPMENT_DETAIL] ON [dbo].[EQUIPMENT_DETAIL]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.EQUIPMENT_DETAIL AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_THICKNESS_READINGS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_THICKNESS_READINGS] ON [dbo].[IW_THICKNESS_READINGS]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_THICKNESS_READINGS AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_COMPONENT_MASTER]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_COMPONENT_MASTER] ON [dbo].[COMPONENT_MASTER]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.COMPONENT_MASTER AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_TMIN]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_TMIN] ON [dbo].[IW_ASSESS_TMIN]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_TMIN AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P9_CRACK_LIKE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P9_CRACK_LIKE] ON [dbo].[IW_ASSESS_P9_CRACK_LIKE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P9_CRACK_LIKE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P8_WELD_MISALIGN]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P8_WELD_MISALIGN] ON [dbo].[IW_ASSESS_P8_WELD_MISALIGN]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P8_WELD_MISALIGN AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P8_SHELL_DISTORTION]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P8_SHELL_DISTORTION] ON [dbo].[IW_ASSESS_P8_SHELL_DISTORTION]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P8_SHELL_DISTORTION AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P7_HIC]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P7_HIC] ON [dbo].[IW_ASSESS_P7_HIC]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P7_HIC AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P7_BLISTER]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P7_BLISTER] ON [dbo].[IW_ASSESS_P7_BLISTER]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P7_BLISTER AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P6_PITTING]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P6_PITTING] ON [dbo].[IW_ASSESS_P6_PITTING]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P6_PITTING AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P5_THIN_AREA]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P5_THIN_AREA] ON [dbo].[IW_ASSESS_P5_THIN_AREA]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P5_THIN_AREA AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P5_GROOVE_LIKE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P5_GROOVE_LIKE] ON [dbo].[IW_ASSESS_P5_GROOVE_LIKE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P5_GROOVE_LIKE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P5_DNVF101_PART_B]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P5_DNVF101_PART_B] ON [dbo].[IW_ASSESS_P5_DNVF101_PART_B]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P5_DNVF101_PART_B AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P5_B31G]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P5_B31G] ON [dbo].[IW_ASSESS_P5_B31G]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P5_B31G AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P4_THINKNESS_PROFILE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P4_THINKNESS_PROFILE] ON [dbo].[IW_ASSESS_P4_THINKNESS_PROFILE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P4_THINKNESS_PROFILE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P4_THICKNESS_READING]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P4_THICKNESS_READING] ON [dbo].[IW_ASSESS_P4_THICKNESS_READING]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P4_THICKNESS_READING AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P3_BRITTLE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P3_BRITTLE] ON [dbo].[IW_ASSESS_P3_BRITTLE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P3_BRITTLE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P13_LAMINATION]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P13_LAMINATION] ON [dbo].[IW_ASSESS_P13_LAMINATION]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P13_LAMINATION AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P12_GOUGE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P12_GOUGE] ON [dbo].[IW_ASSESS_P12_GOUGE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P12_GOUGE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P12_DENT_GOUGE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P12_DENT_GOUGE] ON [dbo].[IW_ASSESS_P12_DENT_GOUGE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P12_DENT_GOUGE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P12_DENT]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P12_DENT] ON [dbo].[IW_ASSESS_P12_DENT]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P12_DENT AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_P10_CREEP]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_P10_CREEP] ON [dbo].[IW_ASSESS_P10_CREEP]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_P10_CREEP AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_MFH]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_MFH] ON [dbo].[IW_ASSESS_MFH]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_MFH AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_MEMBRANE_STRESS]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_MEMBRANE_STRESS] ON [dbo].[IW_ASSESS_MEMBRANE_STRESS]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_MEMBRANE_STRESS AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_MAWP]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_MAWP] ON [dbo].[IW_ASSESS_MAWP]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_MAWP AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_IMAGE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_IMAGE] ON [dbo].[IW_ASSESS_IMAGE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_IMAGE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_ASSESS_FILE]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_ASSESS_FILE] ON [dbo].[IW_ASSESS_FILE]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_ASSESS_FILE AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_MATERIAL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_MATERIAL] ON [dbo].[IW_MATERIAL]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_MATERIAL AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_GEOMETRY_SHELL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_GEOMETRY_SHELL] ON [dbo].[IW_GEOMETRY_SHELL]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_GEOMETRY_SHELL AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_GEOMETRY_SECTION]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_GEOMETRY_SECTION] ON [dbo].[IW_GEOMETRY_SECTION]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_GEOMETRY_SECTION AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_GEOMETRY_REDUCER]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_GEOMETRY_REDUCER] ON [dbo].[IW_GEOMETRY_REDUCER]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_GEOMETRY_REDUCER AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_GEOMETRY_HEAD]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_GEOMETRY_HEAD] ON [dbo].[IW_GEOMETRY_HEAD]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_GEOMETRY_HEAD AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_GEOMETRY_ELBOW]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_GEOMETRY_ELBOW] ON [dbo].[IW_GEOMETRY_ELBOW]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_GEOMETRY_ELBOW AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_EQUIPMENT_DETAIL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_EQUIPMENT_DETAIL] ON [dbo].[IW_EQUIPMENT_DETAIL]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_EQUIPMENT_DETAIL AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_COMPONENT_DETAIL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_COMPONENT_DETAIL] ON [dbo].[IW_COMPONENT_DETAIL]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_COMPONENT_DETAIL AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_IW_COMPONENT_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_IW_COMPONENT_ASSESSMENT] ON [dbo].[IW_COMPONENT_ASSESSMENT]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.IW_COMPONENT_ASSESSMENT AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Trigger [trMod_COMPONENT_DETAIL]    Script Date: 06/19/2015 13:37:42 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TRIGGER [dbo].[trMod_COMPONENT_DETAIL] ON [dbo].[COMPONENT_DETAIL]
		AFTER UPDATE
	AS
		BEGIN
			SET NOCOUNT ON ;

                            UPDATE  U
            SET     Modified = GETDATE()
            FROM    inserted AS I
                    JOIN dbo.COMPONENT_DETAIL AS U ON U.AuditingID = I.AuditingID
 END
GO
/****** Object:  Default [DF_33E5A8C6_ASSESSMENT_CATEGORY_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_CATEGORY] ADD  CONSTRAINT [DF_33E5A8C6_ASSESSMENT_CATEGORY_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_33E5A8C6_ASSESSMENT_CATEGORY_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_CATEGORY] ADD  CONSTRAINT [DF_33E5A8C6_ASSESSMENT_CATEGORY_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_F64B3464_ASSESSMENT_CATEGORY_DESIGN_CODE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE] ADD  CONSTRAINT [DF_F64B3464_ASSESSMENT_CATEGORY_DESIGN_CODE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_F64B3464_ASSESSMENT_CATEGORY_DESIGN_CODE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE] ADD  CONSTRAINT [DF_F64B3464_ASSESSMENT_CATEGORY_DESIGN_CODE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_40F939CF_ASSESSMENT_TYPE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_TYPE] ADD  CONSTRAINT [DF_40F939CF_ASSESSMENT_TYPE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_40F939CF_ASSESSMENT_TYPE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_TYPE] ADD  CONSTRAINT [DF_40F939CF_ASSESSMENT_TYPE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_10FBE91D_ASSESSMENT_TYPE_DESIGN_CODE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ADD  CONSTRAINT [DF_10FBE91D_ASSESSMENT_TYPE_DESIGN_CODE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_10FBE91D_ASSESSMENT_TYPE_DESIGN_CODE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] ADD  CONSTRAINT [DF_10FBE91D_ASSESSMENT_TYPE_DESIGN_CODE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_5F9FEAE2_COMPONENT_DETAIL_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[COMPONENT_DETAIL] ADD  CONSTRAINT [DF_5F9FEAE2_COMPONENT_DETAIL_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_5F9FEAE2_COMPONENT_DETAIL_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[COMPONENT_DETAIL] ADD  CONSTRAINT [DF_5F9FEAE2_COMPONENT_DETAIL_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_5391FCFF_COMPONENT_MASTER_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[COMPONENT_MASTER] ADD  CONSTRAINT [DF_5391FCFF_COMPONENT_MASTER_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_5391FCFF_COMPONENT_MASTER_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[COMPONENT_MASTER] ADD  CONSTRAINT [DF_5391FCFF_COMPONENT_MASTER_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_74DEC436_COMPONENT_TYPE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[COMPONENT_TYPE] ADD  CONSTRAINT [DF_74DEC436_COMPONENT_TYPE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_74DEC436_COMPONENT_TYPE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[COMPONENT_TYPE] ADD  CONSTRAINT [DF_74DEC436_COMPONENT_TYPE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_595CD64C_DESIGN_CODE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[DESIGN_CODE] ADD  CONSTRAINT [DF_595CD64C_DESIGN_CODE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_595CD64C_DESIGN_CODE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[DESIGN_CODE] ADD  CONSTRAINT [DF_595CD64C_DESIGN_CODE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_8BCF608E_EQUIPMENT_COMPONENT_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_COMPONENT] ADD  CONSTRAINT [DF_8BCF608E_EQUIPMENT_COMPONENT_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_8BCF608E_EQUIPMENT_COMPONENT_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_COMPONENT] ADD  CONSTRAINT [DF_8BCF608E_EQUIPMENT_COMPONENT_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_056D445B_EQUIPMENT_DESIGN_CODE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_DESIGN_CODE] ADD  CONSTRAINT [DF_056D445B_EQUIPMENT_DESIGN_CODE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_056D445B_EQUIPMENT_DESIGN_CODE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_DESIGN_CODE] ADD  CONSTRAINT [DF_056D445B_EQUIPMENT_DESIGN_CODE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_1368DC4B_EQUIPMENT_DETAIL_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_DETAIL] ADD  CONSTRAINT [DF_1368DC4B_EQUIPMENT_DETAIL_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_1368DC4B_EQUIPMENT_DETAIL_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_DETAIL] ADD  CONSTRAINT [DF_1368DC4B_EQUIPMENT_DETAIL_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_44D7A0B4_EQUIPMENT_MASTER_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_MASTER] ADD  CONSTRAINT [DF_44D7A0B4_EQUIPMENT_MASTER_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_44D7A0B4_EQUIPMENT_MASTER_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_MASTER] ADD  CONSTRAINT [DF_44D7A0B4_EQUIPMENT_MASTER_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_ED27EFB3_EQUIPMENT_TYPE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_TYPE] ADD  CONSTRAINT [DF_ED27EFB3_EQUIPMENT_TYPE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_ED27EFB3_EQUIPMENT_TYPE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_TYPE] ADD  CONSTRAINT [DF_ED27EFB3_EQUIPMENT_TYPE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_9E2F680A_FACILITY_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[FACILITY] ADD  CONSTRAINT [DF_9E2F680A_FACILITY_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_9E2F680A_FACILITY_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[FACILITY] ADD  CONSTRAINT [DF_9E2F680A_FACILITY_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_547D52BF_GEOMETRY_ELBOW_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_ELBOW] ADD  CONSTRAINT [DF_547D52BF_GEOMETRY_ELBOW_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_547D52BF_GEOMETRY_ELBOW_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_ELBOW] ADD  CONSTRAINT [DF_547D52BF_GEOMETRY_ELBOW_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_GEOMETRY_HEAD_CenteredCorroded]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_HEAD] ADD  CONSTRAINT [DF_GEOMETRY_HEAD_CenteredCorroded]  DEFAULT ((0)) FOR [CenteredCorroded]
GO
/****** Object:  Default [DF_068387DE_GEOMETRY_HEAD_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_HEAD] ADD  CONSTRAINT [DF_068387DE_GEOMETRY_HEAD_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_068387DE_GEOMETRY_HEAD_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_HEAD] ADD  CONSTRAINT [DF_068387DE_GEOMETRY_HEAD_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_CC0A8B8E_GEOMETRY_REDUCER_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_REDUCER] ADD  CONSTRAINT [DF_CC0A8B8E_GEOMETRY_REDUCER_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_CC0A8B8E_GEOMETRY_REDUCER_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_REDUCER] ADD  CONSTRAINT [DF_CC0A8B8E_GEOMETRY_REDUCER_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_23BE78D5_GEOMETRY_SECTION_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_SECTION] ADD  CONSTRAINT [DF_23BE78D5_GEOMETRY_SECTION_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_23BE78D5_GEOMETRY_SECTION_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_SECTION] ADD  CONSTRAINT [DF_23BE78D5_GEOMETRY_SECTION_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_AED94594_GEOMETRY_SHELL_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_SHELL] ADD  CONSTRAINT [DF_AED94594_GEOMETRY_SHELL_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_AED94594_GEOMETRY_SHELL_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_SHELL] ADD  CONSTRAINT [DF_AED94594_GEOMETRY_SHELL_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_7198E762_INTERFACE_ASSESSMENT_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[INTERFACE_ASSESSMENT] ADD  CONSTRAINT [DF_7198E762_INTERFACE_ASSESSMENT_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_7198E762_INTERFACE_ASSESSMENT_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[INTERFACE_ASSESSMENT] ADD  CONSTRAINT [DF_7198E762_INTERFACE_ASSESSMENT_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_C9BDF246_INTERFACE_GEOMETRY_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[INTERFACE_GEOMETRY] ADD  CONSTRAINT [DF_C9BDF246_INTERFACE_GEOMETRY_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_C9BDF246_INTERFACE_GEOMETRY_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[INTERFACE_GEOMETRY] ADD  CONSTRAINT [DF_C9BDF246_INTERFACE_GEOMETRY_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_256D6B9D_IW_ASSESS_FILE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_FILE] ADD  CONSTRAINT [DF_256D6B9D_IW_ASSESS_FILE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_256D6B9D_IW_ASSESS_FILE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_FILE] ADD  CONSTRAINT [DF_256D6B9D_IW_ASSESS_FILE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_64CE793D_IW_ASSESS_IMAGE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_IMAGE] ADD  CONSTRAINT [DF_64CE793D_IW_ASSESS_IMAGE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_64CE793D_IW_ASSESS_IMAGE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_IMAGE] ADD  CONSTRAINT [DF_64CE793D_IW_ASSESS_IMAGE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_F4B97EAE_IW_ASSESS_MAWP_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_MAWP] ADD  CONSTRAINT [DF_F4B97EAE_IW_ASSESS_MAWP_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_F4B97EAE_IW_ASSESS_MAWP_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_MAWP] ADD  CONSTRAINT [DF_F4B97EAE_IW_ASSESS_MAWP_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_55C44343_IW_ASSESS_MEMBRANE_STRESS_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_MEMBRANE_STRESS] ADD  CONSTRAINT [DF_55C44343_IW_ASSESS_MEMBRANE_STRESS_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_55C44343_IW_ASSESS_MEMBRANE_STRESS_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_MEMBRANE_STRESS] ADD  CONSTRAINT [DF_55C44343_IW_ASSESS_MEMBRANE_STRESS_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_F4B97EAE_IW_ASSESS_MFH_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_MFH] ADD  CONSTRAINT [DF_F4B97EAE_IW_ASSESS_MFH_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_F4B97EAE_IW_ASSESS_MFH_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_MFH] ADD  CONSTRAINT [DF_F4B97EAE_IW_ASSESS_MFH_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_895714BB_IW_ASSESS_P10_CREEP_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P10_CREEP] ADD  CONSTRAINT [DF_895714BB_IW_ASSESS_P10_CREEP_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_895714BB_IW_ASSESS_P10_CREEP_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P10_CREEP] ADD  CONSTRAINT [DF_895714BB_IW_ASSESS_P10_CREEP_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P12_DENT_PressureCycle]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT] ADD  CONSTRAINT [DF_IW_ASSESS_P12_DENT_PressureCycle]  DEFAULT ((0)) FOR [PressureCycle]
GO
/****** Object:  Default [DF_IW_ASSESS_P12_DENT_Level2]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT] ADD  CONSTRAINT [DF_IW_ASSESS_P12_DENT_Level2]  DEFAULT ((0)) FOR [Level2]
GO
/****** Object:  Default [DF_IW_ASSESS_P12_DENT_MAWP]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT] ADD  CONSTRAINT [DF_IW_ASSESS_P12_DENT_MAWP]  DEFAULT ((0)) FOR [MAWP]
GO
/****** Object:  Default [DF_1187181B_IW_ASSESS_P12_DENT_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT] ADD  CONSTRAINT [DF_1187181B_IW_ASSESS_P12_DENT_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_1187181B_IW_ASSESS_P12_DENT_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT] ADD  CONSTRAINT [DF_1187181B_IW_ASSESS_P12_DENT_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P12_DENT_GOUGE_PressureCycle]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT_GOUGE] ADD  CONSTRAINT [DF_IW_ASSESS_P12_DENT_GOUGE_PressureCycle]  DEFAULT ((0)) FOR [PressureCycle]
GO
/****** Object:  Default [DF_IW_ASSESS_P12_DENT_GOUGE_Level2]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT_GOUGE] ADD  CONSTRAINT [DF_IW_ASSESS_P12_DENT_GOUGE_Level2]  DEFAULT ((0)) FOR [Level2]
GO
/****** Object:  Default [DF_E2FE81B9_IW_ASSESS_P12_DENT_GOUGE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT_GOUGE] ADD  CONSTRAINT [DF_E2FE81B9_IW_ASSESS_P12_DENT_GOUGE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_E2FE81B9_IW_ASSESS_P12_DENT_GOUGE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT_GOUGE] ADD  CONSTRAINT [DF_E2FE81B9_IW_ASSESS_P12_DENT_GOUGE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_973BD50E_IW_ASSESS_P12_GOUGE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_GOUGE] ADD  CONSTRAINT [DF_973BD50E_IW_ASSESS_P12_GOUGE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_973BD50E_IW_ASSESS_P12_GOUGE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_GOUGE] ADD  CONSTRAINT [DF_973BD50E_IW_ASSESS_P12_GOUGE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P13_LAMINATION_Level2]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P13_LAMINATION] ADD  CONSTRAINT [DF_IW_ASSESS_P13_LAMINATION_Level2]  DEFAULT ((0)) FOR [Level2]
GO
/****** Object:  Default [DF_IW_ASSESS_P13_LAMINATION_MAWP]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P13_LAMINATION] ADD  CONSTRAINT [DF_IW_ASSESS_P13_LAMINATION_MAWP]  DEFAULT ((0)) FOR [MAWP]
GO
/****** Object:  Default [DF_IW_ASSESS_P13_LAMINATION_MFH]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P13_LAMINATION] ADD  CONSTRAINT [DF_IW_ASSESS_P13_LAMINATION_MFH]  DEFAULT ((0)) FOR [MFH]
GO
/****** Object:  Default [DF_20830310_IW_ASSESS_P13_LAMINATION_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P13_LAMINATION] ADD  CONSTRAINT [DF_20830310_IW_ASSESS_P13_LAMINATION_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_20830310_IW_ASSESS_P13_LAMINATION_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P13_LAMINATION] ADD  CONSTRAINT [DF_20830310_IW_ASSESS_P13_LAMINATION_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P3_BRITTLE_LODMT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P3_BRITTLE] ADD  CONSTRAINT [DF_IW_ASSESS_P3_BRITTLE_LODMT]  DEFAULT ((0)) FOR [LODMT]
GO
/****** Object:  Default [DF_IW_ASSESS_P3_BRITTLE_Level2]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P3_BRITTLE] ADD  CONSTRAINT [DF_IW_ASSESS_P3_BRITTLE_Level2]  DEFAULT ((0)) FOR [Level2]
GO
/****** Object:  Default [DF_570DCEA0_IW_ASSESS_P3_BRITTLE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P3_BRITTLE] ADD  CONSTRAINT [DF_570DCEA0_IW_ASSESS_P3_BRITTLE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_570DCEA0_IW_ASSESS_P3_BRITTLE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P3_BRITTLE] ADD  CONSTRAINT [DF_570DCEA0_IW_ASSESS_P3_BRITTLE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P4_THICKNESS_READING_Level2]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P4_THICKNESS_READING] ADD  CONSTRAINT [DF_IW_ASSESS_P4_THICKNESS_READING_Level2]  DEFAULT ((0)) FOR [Level2]
GO
/****** Object:  Default [DF_C9C10883_IW_ASSESS_P4_THICKNESS_READING_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P4_THICKNESS_READING] ADD  CONSTRAINT [DF_C9C10883_IW_ASSESS_P4_THICKNESS_READING_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_C9C10883_IW_ASSESS_P4_THICKNESS_READING_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P4_THICKNESS_READING] ADD  CONSTRAINT [DF_C9C10883_IW_ASSESS_P4_THICKNESS_READING_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P4_THINKNESS_PROFILE_Level2]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P4_THINKNESS_PROFILE] ADD  CONSTRAINT [DF_IW_ASSESS_P4_THINKNESS_PROFILE_Level2]  DEFAULT ((0)) FOR [Level2]
GO
/****** Object:  Default [DF_E3F85244_IW_ASSESS_P4_THINKNESS_PROFILE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P4_THINKNESS_PROFILE] ADD  CONSTRAINT [DF_E3F85244_IW_ASSESS_P4_THINKNESS_PROFILE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_E3F85244_IW_ASSESS_P4_THINKNESS_PROFILE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P4_THINKNESS_PROFILE] ADD  CONSTRAINT [DF_E3F85244_IW_ASSESS_P4_THINKNESS_PROFILE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P5_B31G_Level2]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_B31G] ADD  CONSTRAINT [DF_IW_ASSESS_P5_B31G_Level2]  DEFAULT ((0)) FOR [Level2]
GO
/****** Object:  Default [DF_CFFC4876_IW_ASSESS_P5_B31G_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_B31G] ADD  CONSTRAINT [DF_CFFC4876_IW_ASSESS_P5_B31G_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_CFFC4876_IW_ASSESS_P5_B31G_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_B31G] ADD  CONSTRAINT [DF_CFFC4876_IW_ASSESS_P5_B31G_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P5_DNVF101_PART_B_CombinedLoad]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_DNVF101_PART_B] ADD  CONSTRAINT [DF_IW_ASSESS_P5_DNVF101_PART_B_CombinedLoad]  DEFAULT ((0)) FOR [CombinedLoad]
GO
/****** Object:  Default [DF_IW_ASSESS_P5_DNVF101_PART_B_AdjacentDefectPresent]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_DNVF101_PART_B] ADD  CONSTRAINT [DF_IW_ASSESS_P5_DNVF101_PART_B_AdjacentDefectPresent]  DEFAULT ((0)) FOR [AdjacentDefectPresent]
GO
/****** Object:  Default [DF_7C7F1C1A_IW_ASSESS_P5_DNVF101_PART_B_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_DNVF101_PART_B] ADD  CONSTRAINT [DF_7C7F1C1A_IW_ASSESS_P5_DNVF101_PART_B_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_7C7F1C1A_IW_ASSESS_P5_DNVF101_PART_B_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_DNVF101_PART_B] ADD  CONSTRAINT [DF_7C7F1C1A_IW_ASSESS_P5_DNVF101_PART_B_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P5_GROOVE_LIKE_MAWP]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_GROOVE_LIKE] ADD  CONSTRAINT [DF_IW_ASSESS_P5_GROOVE_LIKE_MAWP]  DEFAULT ((0)) FOR [MAWP]
GO
/****** Object:  Default [DF_IW_ASSESS_P5_GROOVE_LIKE_MFH]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_GROOVE_LIKE] ADD  CONSTRAINT [DF_IW_ASSESS_P5_GROOVE_LIKE_MFH]  DEFAULT ((0)) FOR [MFH]
GO
/****** Object:  Default [DF_7CD43DC6_IW_ASSESS_P5_GROOVE_LIKE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_GROOVE_LIKE] ADD  CONSTRAINT [DF_7CD43DC6_IW_ASSESS_P5_GROOVE_LIKE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_7CD43DC6_IW_ASSESS_P5_GROOVE_LIKE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_GROOVE_LIKE] ADD  CONSTRAINT [DF_7CD43DC6_IW_ASSESS_P5_GROOVE_LIKE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P5_THIN_AREA_MAWP]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_THIN_AREA] ADD  CONSTRAINT [DF_IW_ASSESS_P5_THIN_AREA_MAWP]  DEFAULT ((0)) FOR [MAWPMFH]
GO
/****** Object:  Default [DF_IW_ASSESS_P5_THIN_AREA_Level2]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_THIN_AREA] ADD  CONSTRAINT [DF_IW_ASSESS_P5_THIN_AREA_Level2]  DEFAULT ((0)) FOR [Level2]
GO
/****** Object:  Default [DF_4B059DDF_IW_ASSESS_P5_THIN_AREA_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_THIN_AREA] ADD  CONSTRAINT [DF_4B059DDF_IW_ASSESS_P5_THIN_AREA_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_4B059DDF_IW_ASSESS_P5_THIN_AREA_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_THIN_AREA] ADD  CONSTRAINT [DF_4B059DDF_IW_ASSESS_P5_THIN_AREA_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P6_PITTING_MFH]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P6_PITTING] ADD  CONSTRAINT [DF_IW_ASSESS_P6_PITTING_MFH]  DEFAULT ((0)) FOR [MFH]
GO
/****** Object:  Default [DF_IW_ASSESS_P6_PITTING_MAWP]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P6_PITTING] ADD  CONSTRAINT [DF_IW_ASSESS_P6_PITTING_MAWP]  DEFAULT ((0)) FOR [MAWP]
GO
/****** Object:  Default [DF_IW_ASSESS_P6_PITTING_Level2]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P6_PITTING] ADD  CONSTRAINT [DF_IW_ASSESS_P6_PITTING_Level2]  DEFAULT ((0)) FOR [Level2]
GO
/****** Object:  Default [DF_D2513C13_IW_ASSESS_P6_PITTING_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P6_PITTING] ADD  CONSTRAINT [DF_D2513C13_IW_ASSESS_P6_PITTING_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_D2513C13_IW_ASSESS_P6_PITTING_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P6_PITTING] ADD  CONSTRAINT [DF_D2513C13_IW_ASSESS_P6_PITTING_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P7_BLISTER_Vented]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P7_BLISTER] ADD  CONSTRAINT [DF_IW_ASSESS_P7_BLISTER_Vented]  DEFAULT ((0)) FOR [Vented]
GO
/****** Object:  Default [DF_44A235BD_IW_ASSESS_P7_BLISTER_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P7_BLISTER] ADD  CONSTRAINT [DF_44A235BD_IW_ASSESS_P7_BLISTER_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_44A235BD_IW_ASSESS_P7_BLISTER_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P7_BLISTER] ADD  CONSTRAINT [DF_44A235BD_IW_ASSESS_P7_BLISTER_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_ASSESS_P7_HIC_Level2]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P7_HIC] ADD  CONSTRAINT [DF_IW_ASSESS_P7_HIC_Level2]  DEFAULT ((0)) FOR [Level2]
GO
/****** Object:  Default [DF_IW_ASSESS_P7_HIC_MAWP]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P7_HIC] ADD  CONSTRAINT [DF_IW_ASSESS_P7_HIC_MAWP]  DEFAULT ((0)) FOR [MAWP]
GO
/****** Object:  Default [DF_141DDFB2_IW_ASSESS_P7_HIC_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P7_HIC] ADD  CONSTRAINT [DF_141DDFB2_IW_ASSESS_P7_HIC_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_141DDFB2_IW_ASSESS_P7_HIC_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P7_HIC] ADD  CONSTRAINT [DF_141DDFB2_IW_ASSESS_P7_HIC_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_A3CDDFBC_IW_ASSESS_P8_SHELL_DISTORTION_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P8_SHELL_DISTORTION] ADD  CONSTRAINT [DF_A3CDDFBC_IW_ASSESS_P8_SHELL_DISTORTION_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_A3CDDFBC_IW_ASSESS_P8_SHELL_DISTORTION_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P8_SHELL_DISTORTION] ADD  CONSTRAINT [DF_A3CDDFBC_IW_ASSESS_P8_SHELL_DISTORTION_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_69CC3257_IW_ASSESS_P8_WELD_MISALIGN_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P8_WELD_MISALIGN] ADD  CONSTRAINT [DF_69CC3257_IW_ASSESS_P8_WELD_MISALIGN_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_69CC3257_IW_ASSESS_P8_WELD_MISALIGN_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P8_WELD_MISALIGN] ADD  CONSTRAINT [DF_69CC3257_IW_ASSESS_P8_WELD_MISALIGN_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_2CF89F8E_IW_ASSESS_P9_CRACK_LIKE_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P9_CRACK_LIKE] ADD  CONSTRAINT [DF_2CF89F8E_IW_ASSESS_P9_CRACK_LIKE_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_2CF89F8E_IW_ASSESS_P9_CRACK_LIKE_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P9_CRACK_LIKE] ADD  CONSTRAINT [DF_2CF89F8E_IW_ASSESS_P9_CRACK_LIKE_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_8FA5AA8D_IW_ASSESS_TMIN_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_TMIN] ADD  CONSTRAINT [DF_8FA5AA8D_IW_ASSESS_TMIN_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_8FA5AA8D_IW_ASSESS_TMIN_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_TMIN] ADD  CONSTRAINT [DF_8FA5AA8D_IW_ASSESS_TMIN_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_0437C4B1_IW_COMPONENT_ASSESSMENT_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_COMPONENT_ASSESSMENT] ADD  CONSTRAINT [DF_0437C4B1_IW_COMPONENT_ASSESSMENT_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_0437C4B1_IW_COMPONENT_ASSESSMENT_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_COMPONENT_ASSESSMENT] ADD  CONSTRAINT [DF_0437C4B1_IW_COMPONENT_ASSESSMENT_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_AD71B10F_IW_COMPONENT_DETAIL_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_COMPONENT_DETAIL] ADD  CONSTRAINT [DF_AD71B10F_IW_COMPONENT_DETAIL_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_AD71B10F_IW_COMPONENT_DETAIL_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_COMPONENT_DETAIL] ADD  CONSTRAINT [DF_AD71B10F_IW_COMPONENT_DETAIL_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_38EB0BDA_IW_EQUIPMENT_DETAIL_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_EQUIPMENT_DETAIL] ADD  CONSTRAINT [DF_38EB0BDA_IW_EQUIPMENT_DETAIL_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_38EB0BDA_IW_EQUIPMENT_DETAIL_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_EQUIPMENT_DETAIL] ADD  CONSTRAINT [DF_38EB0BDA_IW_EQUIPMENT_DETAIL_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_3E0E329C_IW_GEOMETRY_ELBOW_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_ELBOW] ADD  CONSTRAINT [DF_3E0E329C_IW_GEOMETRY_ELBOW_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_3E0E329C_IW_GEOMETRY_ELBOW_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_ELBOW] ADD  CONSTRAINT [DF_3E0E329C_IW_GEOMETRY_ELBOW_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_GEOMETRY_HEAD_CenteredCorroded]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_HEAD] ADD  CONSTRAINT [DF_IW_GEOMETRY_HEAD_CenteredCorroded]  DEFAULT ((0)) FOR [CenteredCorroded]
GO
/****** Object:  Default [DF_FA2BDCD9_IW_GEOMETRY_HEAD_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_HEAD] ADD  CONSTRAINT [DF_FA2BDCD9_IW_GEOMETRY_HEAD_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_FA2BDCD9_IW_GEOMETRY_HEAD_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_HEAD] ADD  CONSTRAINT [DF_FA2BDCD9_IW_GEOMETRY_HEAD_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_C7B5AEB8_IW_GEOMETRY_REDUCER_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_REDUCER] ADD  CONSTRAINT [DF_C7B5AEB8_IW_GEOMETRY_REDUCER_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_C7B5AEB8_IW_GEOMETRY_REDUCER_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_REDUCER] ADD  CONSTRAINT [DF_C7B5AEB8_IW_GEOMETRY_REDUCER_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_23F53781_IW_GEOMETRY_SECTION_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_SECTION] ADD  CONSTRAINT [DF_23F53781_IW_GEOMETRY_SECTION_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_23F53781_IW_GEOMETRY_SECTION_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_SECTION] ADD  CONSTRAINT [DF_23F53781_IW_GEOMETRY_SECTION_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_6DE9FB1D_IW_GEOMETRY_SHELL_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_SHELL] ADD  CONSTRAINT [DF_6DE9FB1D_IW_GEOMETRY_SHELL_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_6DE9FB1D_IW_GEOMETRY_SHELL_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_SHELL] ADD  CONSTRAINT [DF_6DE9FB1D_IW_GEOMETRY_SHELL_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_MATERIAL_UserOveride]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_MATERIAL] ADD  CONSTRAINT [DF_IW_MATERIAL_UserOveride]  DEFAULT ((0)) FOR [UserOverride]
GO
/****** Object:  Default [DF_8EA4EAAC_IW_MATERIAL_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_MATERIAL] ADD  CONSTRAINT [DF_8EA4EAAC_IW_MATERIAL_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_8EA4EAAC_IW_MATERIAL_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_MATERIAL] ADD  CONSTRAINT [DF_8EA4EAAC_IW_MATERIAL_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_IW_SETTINGS_NetworkDB]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_SETTINGS] ADD  CONSTRAINT [DF_IW_SETTINGS_NetworkDB]  DEFAULT ((0)) FOR [LocalDB]
GO
/****** Object:  Default [DF_IW_SETTINGS_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_SETTINGS] ADD  CONSTRAINT [DF_IW_SETTINGS_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_13AA7A22_IW_THICKNESS_READINGS_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_THICKNESS_READINGS] ADD  CONSTRAINT [DF_13AA7A22_IW_THICKNESS_READINGS_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_13AA7A22_IW_THICKNESS_READINGS_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_THICKNESS_READINGS] ADD  CONSTRAINT [DF_13AA7A22_IW_THICKNESS_READINGS_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_D9B0FE5C_MANUFACTURER_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[MANUFACTURER] ADD  CONSTRAINT [DF_D9B0FE5C_MANUFACTURER_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_D9B0FE5C_MANUFACTURER_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[MANUFACTURER] ADD  CONSTRAINT [DF_D9B0FE5C_MANUFACTURER_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_A5B53C96_MATERIAL_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[MATERIAL] ADD  CONSTRAINT [DF_A5B53C96_MATERIAL_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_A5B53C96_MATERIAL_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[MATERIAL] ADD  CONSTRAINT [DF_A5B53C96_MATERIAL_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_8CAC26C2_SITES_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[SITES] ADD  CONSTRAINT [DF_8CAC26C2_SITES_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_8CAC26C2_SITES_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[SITES] ADD  CONSTRAINT [DF_8CAC26C2_SITES_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_B48E634B_UNITS_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[UNITS] ADD  CONSTRAINT [DF_B48E634B_UNITS_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_B48E634B_UNITS_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[UNITS] ADD  CONSTRAINT [DF_B48E634B_UNITS_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_3735BD90_USER_GROUPS_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[USER_GROUPS] ADD  CONSTRAINT [DF_3735BD90_USER_GROUPS_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_3735BD90_USER_GROUPS_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[USER_GROUPS] ADD  CONSTRAINT [DF_3735BD90_USER_GROUPS_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  Default [DF_ECE21C76_USERS_Created]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[USERS] ADD  CONSTRAINT [DF_ECE21C76_USERS_Created]  DEFAULT (getdate()) FOR [Created]
GO
/****** Object:  Default [DF_ECE21C76_USERS_AuditingID]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[USERS] ADD  CONSTRAINT [DF_ECE21C76_USERS_AuditingID]  DEFAULT (newid()) FOR [AuditingID]
GO
/****** Object:  ForeignKey [FK_ASSESSMENT_CATEGORY_DESIGN_CODE_ASSESSMENT_CATEGORY]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE]  WITH CHECK ADD  CONSTRAINT [FK_ASSESSMENT_CATEGORY_DESIGN_CODE_ASSESSMENT_CATEGORY] FOREIGN KEY([AssessmentCategoryID])
REFERENCES [dbo].[ASSESSMENT_CATEGORY] ([AssessmentCategoryID])
GO
ALTER TABLE [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE] CHECK CONSTRAINT [FK_ASSESSMENT_CATEGORY_DESIGN_CODE_ASSESSMENT_CATEGORY]
GO
/****** Object:  ForeignKey [FK_ASSESSMENT_CATEGORY_DESIGN_CODE_COMPONENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE]  WITH CHECK ADD  CONSTRAINT [FK_ASSESSMENT_CATEGORY_DESIGN_CODE_COMPONENT_TYPE] FOREIGN KEY([ComponentTypeID])
REFERENCES [dbo].[COMPONENT_TYPE] ([ComponentTypeID])
GO
ALTER TABLE [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE] CHECK CONSTRAINT [FK_ASSESSMENT_CATEGORY_DESIGN_CODE_COMPONENT_TYPE]
GO
/****** Object:  ForeignKey [FK_ASSESSMENT_CATEGORY_DESIGN_CODE_DESIGN_CODE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE]  WITH CHECK ADD  CONSTRAINT [FK_ASSESSMENT_CATEGORY_DESIGN_CODE_DESIGN_CODE] FOREIGN KEY([DesignCodeID])
REFERENCES [dbo].[DESIGN_CODE] ([DesignCodeID])
GO
ALTER TABLE [dbo].[ASSESSMENT_CATEGORY_DESIGN_CODE] CHECK CONSTRAINT [FK_ASSESSMENT_CATEGORY_DESIGN_CODE_DESIGN_CODE]
GO
/****** Object:  ForeignKey [FK_ASSESSMENT_TYPE_ASSESSMENT_CATEGORY]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_TYPE]  WITH CHECK ADD  CONSTRAINT [FK_ASSESSMENT_TYPE_ASSESSMENT_CATEGORY] FOREIGN KEY([AssessmentCategoryID])
REFERENCES [dbo].[ASSESSMENT_CATEGORY] ([AssessmentCategoryID])
GO
ALTER TABLE [dbo].[ASSESSMENT_TYPE] CHECK CONSTRAINT [FK_ASSESSMENT_TYPE_ASSESSMENT_CATEGORY]
GO
/****** Object:  ForeignKey [FK_ASSESSMENT_TYPE_DESIGN_CODE_ASSESSMENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_TYPE_DESIGN_CODE]  WITH CHECK ADD  CONSTRAINT [FK_ASSESSMENT_TYPE_DESIGN_CODE_ASSESSMENT_TYPE] FOREIGN KEY([AssessmentTypeID])
REFERENCES [dbo].[ASSESSMENT_TYPE] ([AssessmentTypeID])
GO
ALTER TABLE [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] CHECK CONSTRAINT [FK_ASSESSMENT_TYPE_DESIGN_CODE_ASSESSMENT_TYPE]
GO
/****** Object:  ForeignKey [FK_ASSESSMENT_TYPE_DESIGN_CODE_COMPONENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_TYPE_DESIGN_CODE]  WITH CHECK ADD  CONSTRAINT [FK_ASSESSMENT_TYPE_DESIGN_CODE_COMPONENT_TYPE] FOREIGN KEY([ComponentTypeID])
REFERENCES [dbo].[COMPONENT_TYPE] ([ComponentTypeID])
GO
ALTER TABLE [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] CHECK CONSTRAINT [FK_ASSESSMENT_TYPE_DESIGN_CODE_COMPONENT_TYPE]
GO
/****** Object:  ForeignKey [FK_ASSESSMENT_TYPE_DESIGN_CODE_DESIGN_CODE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[ASSESSMENT_TYPE_DESIGN_CODE]  WITH CHECK ADD  CONSTRAINT [FK_ASSESSMENT_TYPE_DESIGN_CODE_DESIGN_CODE] FOREIGN KEY([DesignCodeID])
REFERENCES [dbo].[DESIGN_CODE] ([DesignCodeID])
GO
ALTER TABLE [dbo].[ASSESSMENT_TYPE_DESIGN_CODE] CHECK CONSTRAINT [FK_ASSESSMENT_TYPE_DESIGN_CODE_DESIGN_CODE]
GO
/****** Object:  ForeignKey [FK_COMPONENT_DETAIL_COMPONENT_MASTER]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[COMPONENT_DETAIL]  WITH CHECK ADD  CONSTRAINT [FK_COMPONENT_DETAIL_COMPONENT_MASTER] FOREIGN KEY([ComponentID])
REFERENCES [dbo].[COMPONENT_MASTER] ([ComponentID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[COMPONENT_DETAIL] CHECK CONSTRAINT [FK_COMPONENT_DETAIL_COMPONENT_MASTER]
GO
/****** Object:  ForeignKey [FK_COMPONENT_DETAIL_INTERFACE_GEOMETRY]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[COMPONENT_DETAIL]  WITH CHECK ADD  CONSTRAINT [FK_COMPONENT_DETAIL_INTERFACE_GEOMETRY] FOREIGN KEY([GeometryID])
REFERENCES [dbo].[INTERFACE_GEOMETRY] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[COMPONENT_DETAIL] CHECK CONSTRAINT [FK_COMPONENT_DETAIL_INTERFACE_GEOMETRY]
GO
/****** Object:  ForeignKey [FK_COMPONENT_DETAIL_MATERIAL]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[COMPONENT_DETAIL]  WITH CHECK ADD  CONSTRAINT [FK_COMPONENT_DETAIL_MATERIAL] FOREIGN KEY([MaterialID])
REFERENCES [dbo].[MATERIAL] ([ID])
ON DELETE SET NULL
GO
ALTER TABLE [dbo].[COMPONENT_DETAIL] CHECK CONSTRAINT [FK_COMPONENT_DETAIL_MATERIAL]
GO
/****** Object:  ForeignKey [FK_COMPONENT_MASTER_COMPONENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[COMPONENT_MASTER]  WITH CHECK ADD  CONSTRAINT [FK_COMPONENT_MASTER_COMPONENT_TYPE] FOREIGN KEY([ComponentTypeID])
REFERENCES [dbo].[COMPONENT_TYPE] ([ComponentTypeID])
GO
ALTER TABLE [dbo].[COMPONENT_MASTER] CHECK CONSTRAINT [FK_COMPONENT_MASTER_COMPONENT_TYPE]
GO
/****** Object:  ForeignKey [FK_COMPONENT_MASTER_EQUIPMENT_MASTER]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[COMPONENT_MASTER]  WITH CHECK ADD  CONSTRAINT [FK_COMPONENT_MASTER_EQUIPMENT_MASTER] FOREIGN KEY([EquipmentID])
REFERENCES [dbo].[EQUIPMENT_MASTER] ([EquipmentID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[COMPONENT_MASTER] CHECK CONSTRAINT [FK_COMPONENT_MASTER_EQUIPMENT_MASTER]
GO
/****** Object:  ForeignKey [FK_EQUIPMENT_COMPONENT_COMPONENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_COMPONENT]  WITH CHECK ADD  CONSTRAINT [FK_EQUIPMENT_COMPONENT_COMPONENT_TYPE] FOREIGN KEY([ComponentTypeID])
REFERENCES [dbo].[COMPONENT_TYPE] ([ComponentTypeID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[EQUIPMENT_COMPONENT] CHECK CONSTRAINT [FK_EQUIPMENT_COMPONENT_COMPONENT_TYPE]
GO
/****** Object:  ForeignKey [FK_EQUIPMENT_COMPONENT_EQUIPMENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_COMPONENT]  WITH CHECK ADD  CONSTRAINT [FK_EQUIPMENT_COMPONENT_EQUIPMENT_TYPE] FOREIGN KEY([EquipmentTypeID])
REFERENCES [dbo].[EQUIPMENT_TYPE] ([EquipmentTypeID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[EQUIPMENT_COMPONENT] CHECK CONSTRAINT [FK_EQUIPMENT_COMPONENT_EQUIPMENT_TYPE]
GO
/****** Object:  ForeignKey [FK_EQUIPMENT_DESIGN_CODE_DESIGN_CODE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_DESIGN_CODE]  WITH CHECK ADD  CONSTRAINT [FK_EQUIPMENT_DESIGN_CODE_DESIGN_CODE] FOREIGN KEY([DesignCodeID])
REFERENCES [dbo].[DESIGN_CODE] ([DesignCodeID])
GO
ALTER TABLE [dbo].[EQUIPMENT_DESIGN_CODE] CHECK CONSTRAINT [FK_EQUIPMENT_DESIGN_CODE_DESIGN_CODE]
GO
/****** Object:  ForeignKey [FK_EQUIPMENT_DESIGN_CODE_EQUIPMENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_DESIGN_CODE]  WITH CHECK ADD  CONSTRAINT [FK_EQUIPMENT_DESIGN_CODE_EQUIPMENT_TYPE] FOREIGN KEY([EquipmentTypeID])
REFERENCES [dbo].[EQUIPMENT_TYPE] ([EquipmentTypeID])
GO
ALTER TABLE [dbo].[EQUIPMENT_DESIGN_CODE] CHECK CONSTRAINT [FK_EQUIPMENT_DESIGN_CODE_EQUIPMENT_TYPE]
GO
/****** Object:  ForeignKey [FK_EQUIPMENT_DETAIL_EQUIPMENT_MASTER]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_DETAIL]  WITH CHECK ADD  CONSTRAINT [FK_EQUIPMENT_DETAIL_EQUIPMENT_MASTER] FOREIGN KEY([EquipmentID])
REFERENCES [dbo].[EQUIPMENT_MASTER] ([EquipmentID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[EQUIPMENT_DETAIL] CHECK CONSTRAINT [FK_EQUIPMENT_DETAIL_EQUIPMENT_MASTER]
GO
/****** Object:  ForeignKey [FK_EQUIPMENT_MASTER_DESIGN_CODE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_MASTER]  WITH CHECK ADD  CONSTRAINT [FK_EQUIPMENT_MASTER_DESIGN_CODE] FOREIGN KEY([DesignCodeID])
REFERENCES [dbo].[DESIGN_CODE] ([DesignCodeID])
GO
ALTER TABLE [dbo].[EQUIPMENT_MASTER] CHECK CONSTRAINT [FK_EQUIPMENT_MASTER_DESIGN_CODE]
GO
/****** Object:  ForeignKey [FK_EQUIPMENT_MASTER_EQUIPMENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_MASTER]  WITH CHECK ADD  CONSTRAINT [FK_EQUIPMENT_MASTER_EQUIPMENT_TYPE] FOREIGN KEY([EquipmentTypeID])
REFERENCES [dbo].[EQUIPMENT_TYPE] ([EquipmentTypeID])
GO
ALTER TABLE [dbo].[EQUIPMENT_MASTER] CHECK CONSTRAINT [FK_EQUIPMENT_MASTER_EQUIPMENT_TYPE]
GO
/****** Object:  ForeignKey [FK_EQUIPMENT_MASTER_FACILITY]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_MASTER]  WITH CHECK ADD  CONSTRAINT [FK_EQUIPMENT_MASTER_FACILITY] FOREIGN KEY([FacilityID])
REFERENCES [dbo].[FACILITY] ([FacilityID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[EQUIPMENT_MASTER] CHECK CONSTRAINT [FK_EQUIPMENT_MASTER_FACILITY]
GO
/****** Object:  ForeignKey [FK_EQUIPMENT_MASTER_MANUFACTURER]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_MASTER]  WITH CHECK ADD  CONSTRAINT [FK_EQUIPMENT_MASTER_MANUFACTURER] FOREIGN KEY([ManufacturerID])
REFERENCES [dbo].[MANUFACTURER] ([ManufacturerID])
GO
ALTER TABLE [dbo].[EQUIPMENT_MASTER] CHECK CONSTRAINT [FK_EQUIPMENT_MASTER_MANUFACTURER]
GO
/****** Object:  ForeignKey [FK_EQUIPMENT_MASTER_SITES]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[EQUIPMENT_MASTER]  WITH CHECK ADD  CONSTRAINT [FK_EQUIPMENT_MASTER_SITES] FOREIGN KEY([SiteID])
REFERENCES [dbo].[SITES] ([SiteID])
GO
ALTER TABLE [dbo].[EQUIPMENT_MASTER] CHECK CONSTRAINT [FK_EQUIPMENT_MASTER_SITES]
GO
/****** Object:  ForeignKey [FK_FACILITY_SITES]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[FACILITY]  WITH CHECK ADD  CONSTRAINT [FK_FACILITY_SITES] FOREIGN KEY([SiteID])
REFERENCES [dbo].[SITES] ([SiteID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[FACILITY] CHECK CONSTRAINT [FK_FACILITY_SITES]
GO
/****** Object:  ForeignKey [FK_GEOMETRY_ELBOW_INTERFACE_GEOMETRY]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_ELBOW]  WITH CHECK ADD  CONSTRAINT [FK_GEOMETRY_ELBOW_INTERFACE_GEOMETRY] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_GEOMETRY] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[GEOMETRY_ELBOW] CHECK CONSTRAINT [FK_GEOMETRY_ELBOW_INTERFACE_GEOMETRY]
GO
/****** Object:  ForeignKey [FK_GEOMETRY_HEAD_INTERFACE_GEOMETRY]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_HEAD]  WITH CHECK ADD  CONSTRAINT [FK_GEOMETRY_HEAD_INTERFACE_GEOMETRY] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_GEOMETRY] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[GEOMETRY_HEAD] CHECK CONSTRAINT [FK_GEOMETRY_HEAD_INTERFACE_GEOMETRY]
GO
/****** Object:  ForeignKey [FK_GEOMETRY_REDUCER_INTERFACE_GEOMETRY]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_REDUCER]  WITH CHECK ADD  CONSTRAINT [FK_GEOMETRY_REDUCER_INTERFACE_GEOMETRY] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_GEOMETRY] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[GEOMETRY_REDUCER] CHECK CONSTRAINT [FK_GEOMETRY_REDUCER_INTERFACE_GEOMETRY]
GO
/****** Object:  ForeignKey [FK_GEOMETRY_SECTION_INTERFACE_GEOMETRY]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_SECTION]  WITH CHECK ADD  CONSTRAINT [FK_GEOMETRY_SECTION_INTERFACE_GEOMETRY] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_GEOMETRY] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[GEOMETRY_SECTION] CHECK CONSTRAINT [FK_GEOMETRY_SECTION_INTERFACE_GEOMETRY]
GO
/****** Object:  ForeignKey [FK_GEOMETRY_SHELL_INTERFACE_GEOMETRY]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[GEOMETRY_SHELL]  WITH CHECK ADD  CONSTRAINT [FK_GEOMETRY_SHELL_INTERFACE_GEOMETRY] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_GEOMETRY] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[GEOMETRY_SHELL] CHECK CONSTRAINT [FK_GEOMETRY_SHELL_INTERFACE_GEOMETRY]
GO
/****** Object:  ForeignKey [FK_INTERFACE_ASSESSMENT_ASSESSMENT_TYPE]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[INTERFACE_ASSESSMENT]  WITH CHECK ADD  CONSTRAINT [FK_INTERFACE_ASSESSMENT_ASSESSMENT_TYPE] FOREIGN KEY([AssessmentTypeID])
REFERENCES [dbo].[ASSESSMENT_TYPE] ([AssessmentTypeID])
GO
ALTER TABLE [dbo].[INTERFACE_ASSESSMENT] CHECK CONSTRAINT [FK_INTERFACE_ASSESSMENT_ASSESSMENT_TYPE]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_FILE_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_FILE]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_FILE_INTERFACE_ASSESSMENT] FOREIGN KEY([AssessmentID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_FILE] CHECK CONSTRAINT [FK_IW_ASSESS_FILE_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_IMAGE_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_IMAGE]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_IMAGE_INTERFACE_ASSESSMENT] FOREIGN KEY([AssessmentID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_IMAGE] CHECK CONSTRAINT [FK_IW_ASSESS_IMAGE_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_MAWP_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_MAWP]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_MAWP_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_MAWP] CHECK CONSTRAINT [FK_IW_ASSESS_MAWP_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_MEMBRANE_STRESS_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_MEMBRANE_STRESS]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_MEMBRANE_STRESS_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_MEMBRANE_STRESS] CHECK CONSTRAINT [FK_IW_ASSESS_MEMBRANE_STRESS_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_MFH_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_MFH]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_MFH_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_MFH] CHECK CONSTRAINT [FK_IW_ASSESS_MFH_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P10_CREEP_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P10_CREEP]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P10_CREEP_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P10_CREEP] CHECK CONSTRAINT [FK_IW_ASSESS_P10_CREEP_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P12_DENT_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P12_DENT_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT] CHECK CONSTRAINT [FK_IW_ASSESS_P12_DENT_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P12_DENT_GOUGE_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT_GOUGE]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P12_DENT_GOUGE_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P12_DENT_GOUGE] CHECK CONSTRAINT [FK_IW_ASSESS_P12_DENT_GOUGE_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P12_GOUGE_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P12_GOUGE]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P12_GOUGE_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P12_GOUGE] CHECK CONSTRAINT [FK_IW_ASSESS_P12_GOUGE_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P13_LAMINATION_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P13_LAMINATION]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P13_LAMINATION_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P13_LAMINATION] CHECK CONSTRAINT [FK_IW_ASSESS_P13_LAMINATION_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P3_BRITTLE_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P3_BRITTLE]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P3_BRITTLE_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P3_BRITTLE] CHECK CONSTRAINT [FK_IW_ASSESS_P3_BRITTLE_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P4_THICKNESS_READING_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P4_THICKNESS_READING]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P4_THICKNESS_READING_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P4_THICKNESS_READING] CHECK CONSTRAINT [FK_IW_ASSESS_P4_THICKNESS_READING_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P4_THINKNESS_PROFILE_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P4_THINKNESS_PROFILE]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P4_THINKNESS_PROFILE_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P4_THINKNESS_PROFILE] CHECK CONSTRAINT [FK_IW_ASSESS_P4_THINKNESS_PROFILE_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P5_B31G_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_B31G]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P5_B31G_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P5_B31G] CHECK CONSTRAINT [FK_IW_ASSESS_P5_B31G_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P5_DNVF101_PART_B_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_DNVF101_PART_B]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P5_DNVF101_PART_B_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P5_DNVF101_PART_B] CHECK CONSTRAINT [FK_IW_ASSESS_P5_DNVF101_PART_B_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P5_GROOVE_LIKE_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_GROOVE_LIKE]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P5_GROOVE_LIKE_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P5_GROOVE_LIKE] CHECK CONSTRAINT [FK_IW_ASSESS_P5_GROOVE_LIKE_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P5_THIN_AREA_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P5_THIN_AREA]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P5_THIN_AREA_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P5_THIN_AREA] CHECK CONSTRAINT [FK_IW_ASSESS_P5_THIN_AREA_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P6_PITTING_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P6_PITTING]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P6_PITTING_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P6_PITTING] CHECK CONSTRAINT [FK_IW_ASSESS_P6_PITTING_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P6_PITTING_DETAIL_IW_ASSESS_P6_PITTING]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P6_PITTING_DETAIL]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P6_PITTING_DETAIL_IW_ASSESS_P6_PITTING] FOREIGN KEY([ID])
REFERENCES [dbo].[IW_ASSESS_P6_PITTING] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P6_PITTING_DETAIL] CHECK CONSTRAINT [FK_IW_ASSESS_P6_PITTING_DETAIL_IW_ASSESS_P6_PITTING]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P7_BLISTER_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P7_BLISTER]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P7_BLISTER_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P7_BLISTER] CHECK CONSTRAINT [FK_IW_ASSESS_P7_BLISTER_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P7_HIC_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P7_HIC]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P7_HIC_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P7_HIC] CHECK CONSTRAINT [FK_IW_ASSESS_P7_HIC_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P8_SHELL_DISTORTION_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P8_SHELL_DISTORTION]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P8_SHELL_DISTORTION_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P8_SHELL_DISTORTION] CHECK CONSTRAINT [FK_IW_ASSESS_P8_SHELL_DISTORTION_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P8_WELD_MISALIGN_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P8_WELD_MISALIGN]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P8_WELD_MISALIGN_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P8_WELD_MISALIGN] CHECK CONSTRAINT [FK_IW_ASSESS_P8_WELD_MISALIGN_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_P9_CRACK_LIKE_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_P9_CRACK_LIKE]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_P9_CRACK_LIKE_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_P9_CRACK_LIKE] CHECK CONSTRAINT [FK_IW_ASSESS_P9_CRACK_LIKE_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_ASSESS_TMIN_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_ASSESS_TMIN]  WITH CHECK ADD  CONSTRAINT [FK_IW_ASSESS_TMIN_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_ASSESS_TMIN] CHECK CONSTRAINT [FK_IW_ASSESS_TMIN_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_COMPONENT_ASSESSMENT_COMPONENT_MASTER]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_COMPONENT_ASSESSMENT]  WITH CHECK ADD  CONSTRAINT [FK_IW_COMPONENT_ASSESSMENT_COMPONENT_MASTER] FOREIGN KEY([ComponentID])
REFERENCES [dbo].[COMPONENT_MASTER] ([ComponentID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_COMPONENT_ASSESSMENT] CHECK CONSTRAINT [FK_IW_COMPONENT_ASSESSMENT_COMPONENT_MASTER]
GO
/****** Object:  ForeignKey [FK_IW_COMPONENT_ASSESSMENT_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_COMPONENT_ASSESSMENT]  WITH CHECK ADD  CONSTRAINT [FK_IW_COMPONENT_ASSESSMENT_INTERFACE_ASSESSMENT] FOREIGN KEY([AssessmentID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_COMPONENT_ASSESSMENT] CHECK CONSTRAINT [FK_IW_COMPONENT_ASSESSMENT_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_COMPONENT_DETAIL_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_COMPONENT_DETAIL]  WITH CHECK ADD  CONSTRAINT [FK_IW_COMPONENT_DETAIL_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_COMPONENT_DETAIL] CHECK CONSTRAINT [FK_IW_COMPONENT_DETAIL_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_EQUIPMENT_DETAIL_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_EQUIPMENT_DETAIL]  WITH CHECK ADD  CONSTRAINT [FK_IW_EQUIPMENT_DETAIL_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_EQUIPMENT_DETAIL] CHECK CONSTRAINT [FK_IW_EQUIPMENT_DETAIL_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_GEOMETRY_ELBOW_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_ELBOW]  WITH CHECK ADD  CONSTRAINT [FK_IW_GEOMETRY_ELBOW_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_GEOMETRY_ELBOW] CHECK CONSTRAINT [FK_IW_GEOMETRY_ELBOW_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_GEOMETRY_HEAD_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_HEAD]  WITH CHECK ADD  CONSTRAINT [FK_IW_GEOMETRY_HEAD_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_GEOMETRY_HEAD] CHECK CONSTRAINT [FK_IW_GEOMETRY_HEAD_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_GEOMETRY_REDUCER_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_REDUCER]  WITH CHECK ADD  CONSTRAINT [FK_IW_GEOMETRY_REDUCER_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_GEOMETRY_REDUCER] CHECK CONSTRAINT [FK_IW_GEOMETRY_REDUCER_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_GEOMETRY_SECTION_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_SECTION]  WITH CHECK ADD  CONSTRAINT [FK_IW_GEOMETRY_SECTION_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_GEOMETRY_SECTION] CHECK CONSTRAINT [FK_IW_GEOMETRY_SECTION_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_GEOMETRY_SHELL_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_GEOMETRY_SHELL]  WITH CHECK ADD  CONSTRAINT [FK_IW_GEOMETRY_SHELL_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_GEOMETRY_SHELL] CHECK CONSTRAINT [FK_IW_GEOMETRY_SHELL_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_MATERIAL_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_MATERIAL]  WITH CHECK ADD  CONSTRAINT [FK_IW_MATERIAL_INTERFACE_ASSESSMENT] FOREIGN KEY([ID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_MATERIAL] CHECK CONSTRAINT [FK_IW_MATERIAL_INTERFACE_ASSESSMENT]
GO
/****** Object:  ForeignKey [FK_IW_MATERIALS_ASMEB31_3_STRESS_VALUES_IW_MATERIALS_ASMEB31_3]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_MATERIALS_ASMEB31_3_STRESS_VALUES]  WITH CHECK ADD  CONSTRAINT [FK_IW_MATERIALS_ASMEB31_3_STRESS_VALUES_IW_MATERIALS_ASMEB31_3] FOREIGN KEY([MaterialID])
REFERENCES [dbo].[IW_MATERIALS_ASMEB31_3] ([MaterialID])
GO
ALTER TABLE [dbo].[IW_MATERIALS_ASMEB31_3_STRESS_VALUES] CHECK CONSTRAINT [FK_IW_MATERIALS_ASMEB31_3_STRESS_VALUES_IW_MATERIALS_ASMEB31_3]
GO
/****** Object:  ForeignKey [FK_IW_MATERIALS_ASMEVIII_DIV1_STRESS_VALUES_IW_MATERIALS_ASMEVIII_DIV1]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_MATERIALS_ASMEVIII_DIV1_STRESS_VALUES]  WITH CHECK ADD  CONSTRAINT [FK_IW_MATERIALS_ASMEVIII_DIV1_STRESS_VALUES_IW_MATERIALS_ASMEVIII_DIV1] FOREIGN KEY([MaterialID])
REFERENCES [dbo].[IW_MATERIALS_ASMEVIII_DIV1] ([MaterialID])
GO
ALTER TABLE [dbo].[IW_MATERIALS_ASMEVIII_DIV1_STRESS_VALUES] CHECK CONSTRAINT [FK_IW_MATERIALS_ASMEVIII_DIV1_STRESS_VALUES_IW_MATERIALS_ASMEVIII_DIV1]
GO
/****** Object:  ForeignKey [FK_IW_MATERIALS_PD5500_STRESS_VALUES_IW_MATERIALS_PD5500]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_MATERIALS_PD5500_STRESS_VALUES]  WITH CHECK ADD  CONSTRAINT [FK_IW_MATERIALS_PD5500_STRESS_VALUES_IW_MATERIALS_PD5500] FOREIGN KEY([MaterialID])
REFERENCES [dbo].[IW_MATERIALS_PD5500] ([MaterialID])
GO
ALTER TABLE [dbo].[IW_MATERIALS_PD5500_STRESS_VALUES] CHECK CONSTRAINT [FK_IW_MATERIALS_PD5500_STRESS_VALUES_IW_MATERIALS_PD5500]
GO
/****** Object:  ForeignKey [FK_THICKNESS_READINGS_INTERFACE_ASSESSMENT]    Script Date: 06/19/2015 13:37:42 ******/
ALTER TABLE [dbo].[IW_THICKNESS_READINGS]  WITH CHECK ADD  CONSTRAINT [FK_THICKNESS_READINGS_INTERFACE_ASSESSMENT] FOREIGN KEY([AssessmentID])
REFERENCES [dbo].[INTERFACE_ASSESSMENT] ([ID])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[IW_THICKNESS_READINGS] CHECK CONSTRAINT [FK_THICKNESS_READINGS_INTERFACE_ASSESSMENT]
GO
