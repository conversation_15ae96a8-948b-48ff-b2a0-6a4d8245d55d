# IntegriWISE Web - Complete Fitness-for-Service Assessment Platform

A complete web-based replica of IntegriWISE software for fitness-for-service assessments of pressure vessels, pipelines, and storage tanks.

## Features

### Complete Assessment Coverage
- **7 Design Codes**: ASME B31.3/B31.4/B31.8, ASME VIII, API 620/650, PD 5500
- **15+ Assessment Types**: Local Metal Loss (ASME B31G, DNV F101), General Metal Loss, Pitting, HIC, Lamination, Brittle Fracture, Blister, Dent, MAWP, Minimum Thickness, and more
- **400+ Calculation Engines**: Complete mathematical implementations from original source code
- **6 Component Geometries**: Cylindrical shells, elliptical/hemispherical/torispherical heads, spherical shells, elbows, reducers

### Core Functionality
- **Asset Hierarchy Management**: Sites → Facilities → Equipment → Components
- **User Management**: Authentication, role-based access control
- **Material Database**: Comprehensive material properties and manufacturer data
- **Assessment Workflow**: Level 1 & Level 2 assessments with validation
- **Reporting System**: PDF generation with detailed calculations
- **Database Management**: Backup, restore, and data import/export

## Technology Stack

- **Frontend**: React.js 18, Material-UI, TypeScript
- **Backend**: Node.js, Express.js, TypeScript
- **Database**: PostgreSQL 15
- **Authentication**: JWT with refresh tokens
- **File Storage**: Local filesystem with cloud backup support
- **PDF Generation**: Puppeteer for reports
- **Testing**: Jest, React Testing Library, Supertest

## Quick Start

### Prerequisites
- Node.js 18+ and npm 8+
- PostgreSQL 15+
- Git

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd integriwise-web
```

2. **Install all dependencies**
```bash
npm run install:all
```

3. **Database Setup**
```bash
# Create PostgreSQL database
createdb integriwise_web

# Copy environment files
cp server/.env.example server/.env
cp client/.env.example client/.env

# Update database connection in server/.env
# Run migrations and seed data
npm run db:migrate
npm run db:seed
```

4. **Start Development Server**
```bash
npm run dev
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000

### Default Login
- **Username**: <EMAIL>
- **Password**: admin123

## Project Structure

```
integriwise-web/
├── client/                 # React frontend application
│   ├── public/
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/         # Page components
│   │   ├── services/      # API service layer
│   │   ├── utils/         # Utility functions
│   │   ├── types/         # TypeScript type definitions
│   │   └── calculations/  # Client-side calculation engines
│   └── package.json
├── server/                # Node.js backend API
│   ├── src/
│   │   ├── controllers/   # API route controllers
│   │   ├── models/        # Database models
│   │   ├── services/      # Business logic services
│   │   ├── middleware/    # Express middleware
│   │   ├── calculations/  # Server-side calculation engines
│   │   ├── utils/         # Utility functions
│   │   └── routes/        # API routes
│   ├── migrations/        # Database migrations
│   ├── seeds/            # Database seed data
│   └── package.json
├── docs/                 # Documentation
├── scripts/              # Build and deployment scripts
└── package.json         # Root package.json
```

## Development

### Running Tests
```bash
npm test
```

### Database Operations
```bash
# Create new migration
cd server && npm run migration:create <migration_name>

# Run migrations
npm run db:migrate

# Rollback migration
cd server && npm run migration:rollback

# Seed database
npm run db:seed
```

### Building for Production
```bash
npm run build
```

## API Documentation

The API documentation is available at `/api/docs` when running the development server.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Credits

**Courtesy of [Hassan Hany](https://linkedin.com/in/whereishassan)**

![Scimitar Production Egypt Ltd](./docs/scimitar-logo.png)

---

*This application is a complete web-based implementation of fitness-for-service assessment capabilities, providing engineers with comprehensive tools for integrity evaluation of industrial equipment.*
