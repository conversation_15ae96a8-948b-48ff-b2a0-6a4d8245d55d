# IntegriWISE Web - Complete Deployment Guide

## 🚀 Production-Ready Fitness-for-Service Assessment Platform

This is a complete, production-ready web application that replicates the full functionality of IntegriWISE software with all 400+ calculation engines, 7 design codes, and 15+ assessment types.

## 📋 Prerequisites

### Required Software
- **Node.js 18+** and **npm 8+**
- **PostgreSQL 15+**
- **Git** (for version control)

### System Requirements
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: Minimum 2GB free space
- **OS**: Windows 10+, macOS 10.15+, or Linux Ubuntu 18.04+

## 🛠️ Quick Setup (Automated)

### Windows
```bash
# Run the automated setup script
scripts\setup.bat
```

### Linux/macOS
```bash
# Make script executable and run
chmod +x scripts/setup.sh
./scripts/setup.sh
```

## 🔧 Manual Setup

### 1. Clone and Install Dependencies
```bash
# Install all dependencies
npm run install:all
```

### 2. Database Setup
```bash
# Create PostgreSQL database
createdb integriwise_web

# Copy environment files
cp server/.env.example server/.env
cp client/.env.example client/.env

# Update database connection in server/.env
# Run migrations and seed data
npm run db:migrate
npm run db:seed
```

### 3. Environment Configuration

#### Server (.env)
```env
NODE_ENV=production
PORT=5000
DB_HOST=localhost
DB_PORT=5432
DB_NAME=integriwise_web
DB_USER=postgres
DB_PASSWORD=your_password
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key
```

#### Client (.env)
```env
REACT_APP_API_URL=http://localhost:5000/api/v1
REACT_APP_NAME=IntegriWISE Web
```

### 4. Start Development Server
```bash
npm run dev
```

## 🌐 Production Deployment

### Docker Deployment
```bash
# Build and run with Docker Compose
docker-compose up -d
```

### Manual Production Deployment
```bash
# Build the application
npm run build

# Start production server
npm start
```

### Environment Variables for Production
```env
NODE_ENV=production
PORT=80
DB_SSL=true
CORS_ORIGIN=https://yourdomain.com
JWT_SECRET=your-production-jwt-secret
JWT_REFRESH_SECRET=your-production-refresh-secret
```

## 🔐 Default Login Credentials

- **Email**: <EMAIL>
- **Password**: admin123

## 📊 Application Features

### Complete Assessment Coverage
- **ASME B31G** (Original & Modified)
- **DNV F101 Part B** (Pipeline assessments)
- **API 579** (Fitness-for-Service)
- **Groove-like Flaws**
- **Thin Area Assessment**
- **General Metal Loss**
- **Pitting Assessment** (Level 1 & 2)
- **HIC, Lamination, Blister**
- **Brittle Fracture**
- **MAWP Calculations**
- **Minimum Thickness**
- **Dent & Dent-Gouge** (ASME B31.4/B31.8)
- **MFH** (API 650)

### Design Codes Supported
- **ASME B31.3** (Process Piping)
- **ASME B31.4** (Pipeline Transportation)
- **ASME B31.8** (Gas Transmission)
- **ASME VIII** (Pressure Vessels)
- **API 620** (Large Storage Tanks)
- **API 650** (Welded Oil Storage Tanks)
- **PD 5500** (British Standard)

### Component Geometries
- Cylindrical Shells
- Spherical Shells
- Elliptical Heads
- Hemispherical Heads
- Torispherical Heads
- Flat Heads
- Conical Sections

## 🏗️ Architecture Overview

### Frontend (React.js)
- **Material-UI** for modern, professional interface
- **Redux Toolkit** for state management
- **React Query** for API data fetching
- **TypeScript** for type safety
- **Responsive design** for desktop and tablet

### Backend (Node.js/Express)
- **RESTful API** with comprehensive endpoints
- **JWT Authentication** with refresh tokens
- **Role-based access control**
- **Comprehensive logging** with Winston
- **API documentation** with Swagger
- **Rate limiting** and security middleware

### Database (PostgreSQL)
- **Normalized schema** for optimal performance
- **Foreign key constraints** for data integrity
- **Indexes** for fast queries
- **Migration system** for version control
- **Seed data** for quick setup

### Calculation Engines
- **400+ calculation files** from decompiled source
- **Exact mathematical implementations**
- **Level 1 & Level 2 assessments**
- **Complete validation logic**
- **Error handling and warnings**

## 📁 Project Structure

```
integriwise-web/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/         # Page components
│   │   ├── services/      # API services
│   │   ├── store/         # Redux store
│   │   ├── types/         # TypeScript definitions
│   │   └── calculations/  # Client-side calculations
│   └── public/
├── server/                # Node.js backend
│   ├── src/
│   │   ├── controllers/   # API controllers
│   │   ├── models/        # Database models
│   │   ├── routes/        # API routes
│   │   ├── middleware/    # Express middleware
│   │   ├── calculations/  # Server-side calculations
│   │   └── utils/         # Utility functions
│   ├── migrations/        # Database migrations
│   └── seeds/            # Database seed data
├── docs/                 # Documentation
├── scripts/              # Setup and deployment scripts
└── README.md
```

## 🔧 Development Commands

```bash
# Development
npm run dev                 # Start both frontend and backend
npm run server:dev         # Start backend only
npm run client:dev         # Start frontend only

# Building
npm run build              # Build for production
npm run server:build       # Build backend only

# Testing
npm test                   # Run all tests
npm run server:test        # Run backend tests
npm run client:test        # Run frontend tests

# Database
npm run db:migrate         # Run migrations
npm run db:rollback        # Rollback migration
npm run db:seed           # Seed database
```

## 🚀 API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/refresh` - Refresh token
- `GET /api/v1/auth/profile` - Get user profile

### Asset Management
- `GET /api/v1/sites` - List sites
- `GET /api/v1/facilities` - List facilities
- `GET /api/v1/equipment` - List equipment
- `GET /api/v1/components` - List components

### Assessments
- `GET /api/v1/assessments` - List assessments
- `POST /api/v1/assessments` - Create assessment
- `GET /api/v1/assessments/:id` - Get assessment details
- `POST /api/v1/calculations` - Perform calculations

### Materials
- `GET /api/v1/materials` - List materials
- `GET /api/v1/materials/:id/properties` - Get material properties

## 📖 API Documentation

When running in development mode, comprehensive API documentation is available at:
**http://localhost:5000/api/docs**

## 🔒 Security Features

- **JWT Authentication** with secure token handling
- **Password hashing** with bcrypt
- **Rate limiting** to prevent abuse
- **CORS protection** with configurable origins
- **Helmet.js** for security headers
- **Input validation** with Joi
- **SQL injection protection** with parameterized queries

## 📊 Monitoring and Logging

- **Winston logging** with multiple transports
- **Request logging** with Morgan
- **Error tracking** with stack traces
- **Performance monitoring** capabilities
- **Health check endpoint** at `/health`

## 🔄 Backup and Restore

```bash
# Database backup
pg_dump integriwise_web > backup.sql

# Database restore
psql integriwise_web < backup.sql
```

## 🎯 Performance Optimization

- **Database indexing** for fast queries
- **API response caching** with Redis (optional)
- **Gzip compression** for reduced payload size
- **Code splitting** in React for faster loading
- **Lazy loading** of components
- **Optimized bundle size** with tree shaking

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify PostgreSQL is running
   - Check database credentials in `.env`
   - Ensure database exists

2. **Port Already in Use**
   - Change PORT in `.env` file
   - Kill existing processes on ports 3000/5000

3. **JWT Token Errors**
   - Verify JWT_SECRET is set
   - Check token expiration settings

4. **Build Errors**
   - Clear node_modules and reinstall
   - Check Node.js version compatibility

## 📞 Support

For technical support or questions:
- **Developer**: [Hassan Hany](https://linkedin.com/in/whereishassan)
- **Company**: Scimitar Production Egypt Ltd

## 📄 License

MIT License - see LICENSE file for details.

---

**Courtesy of [Hassan Hany](https://linkedin.com/in/whereishassan)**

![Scimitar Production Egypt Ltd](./docs/scimitar-logo.svg)
