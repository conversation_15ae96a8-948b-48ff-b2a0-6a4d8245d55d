// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.LookupPD5500Table3_5_2Result
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500;

public class LookupPD5500Table3_5_2Result
{
  public bool INFOInTheTable { get; set; }

  public double InfoTabloeRight { get; set; }

  public double H_D { get; set; }

  public double H_D_Greater { get; set; }

  public double P_S_Greater { get; set; }

  public double P_S_Smaller { get; set; }

  public double tmin_D_Greater { get; set; }

  public double tmin_D_Smaller { get; set; }

  public double tmon_D_Greater { get; set; }

  public double tmon_D_Smaller { get; set; }

  public int tnom_D_Greater_Position { get; set; }
}
