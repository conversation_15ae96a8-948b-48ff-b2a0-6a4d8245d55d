// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.EllipticalHead.GeneralMetalLossThicknessProfile.GeneralMetalLossThicknessProfile
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.EllipticalHead.GeneralMetalLossThicknessProfile;

public class GeneralMetalLossThicknessProfile : BaseCalculator
{
  private double tmin1;
  private bool CheckPLessThanSE;
  private double checkTcMin;
  private double CheckTlMin;
  private bool CheckRtGreaterThanZero;
  private double WorkingTemperature;
  private string TemperatureLowerLimit;
  private List<MaterialPD5500StressValueDTO> _allowableStrengths;
  private MaterialPD5500DTO _material;
  private ThicknessReadings _thicknessReadings;
  private IntegriWISE.AssessmentsCalculators.Calculators.PD5500.EllipticalHead.MinimumThickness.MinimumThickness _calculatorTMin;

  public bool ToLevel2 { get; set; }

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double HeightHead { get; set; }

  public double LongitudinalThicknessReadingSpacing { get; set; }

  public double CircumferentialThicknessReadingSpacing { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double S { get; set; }

  public double C { get; set; }

  public double AllowableStrength { get; set; }

  public double TMin { get; set; }

  public double Tlmin { get; set; }

  public double Tmm { get; set; }

  public double Trd { get; set; }

  public double Rt { get; set; }

  public double Q { get; set; }

  public double L { get; set; }

  public double TSam { get; set; }

  public double TCam { get; set; }

  public double TamMinusFCA { get; set; }

  public double TamMinusFCAOverRSFa { get; set; }

  public bool AverageMeasuredThicknessL1 { get; set; }

  public bool MinimumMeasuredThicknessL1 { get; set; }

  public bool Level1Passed { get; set; }

  public string Level1 { get; set; }

  public bool AverageMeasuredThicknessL2 { get; set; }

  public bool MinimumMeasuredThicknessL2 { get; set; }

  public bool Level2Passed { get; set; }

  public string Level2 { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private double diameter { get; set; }

  private bool P1_2LessThan4S { get; set; }

  private double minTamsTamc { get; set; }

  private double minTamsTamcMinusFCA { get; set; }

  private bool ConditionP { get; set; }

  private bool RtGreaterThan0 { get; set; }

  private double MinTamsTamc { get; set; }

  private double MinTamsTamcMinusFCA { get; set; }

  private double MinTamsTamcMinusFCAOverRSFa { get; set; }

  private bool LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa { get; set; }

  private double TLower { get; set; }

  private double TUpper { get; set; }

  private double SLower { get; set; }

  private double SUpper { get; set; }

  private double Tc { get; set; }

  private double Radius { get; set; }

  private double RSFa { get; set; }

  private double TamCMinusFCA { get; set; }

  private double TamSMinusFCA { get; set; }

  private double Tlim { get; set; }

  private double TmmMinusFCA { get; set; }

  private double Max0_5TminTlim { get; set; }

  private bool LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin { get; set; }

  private bool LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin { get; set; }

  private double TamSMinusFCAOverRSFa { get; set; }

  private double TamCMinusTslMinusFCAOverRSFa { get; set; }

  private bool LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa { get; set; }

  public GeneralMetalLossThicknessProfile(
    MaterialPD5500DTO material,
    List<MaterialPD5500StressValueDTO> allowableStrengths,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
    this._thicknessReadings = thicknessReadingsCalculator;
  }

  public virtual void CalculateTrd()
  {
    this.Trd = this.NominalThickness - this.ExternalUniformMetalLoss - this.InternalUniformMetalLoss;
  }

  public virtual CalculatorResult CalculateTMin()
  {
    this._calculatorTMin = new IntegriWISE.AssessmentsCalculators.Calculators.PD5500.EllipticalHead.MinimumThickness.MinimumThickness(this._material, this._allowableStrengths);
    this._calculatorTMin.DesignTemperature = this.DesignTemperature;
    this._calculatorTMin.DesignPressure = this.DesignPressure;
    this._calculatorTMin.YieldStrengthNew = this._material.YieldStrengthNew;
    this._calculatorTMin.TensileStrengthNew = this._material.TensileStrengthNew;
    this._calculatorTMin.NominalInsideDiameter = this.NominalInsideDiameter;
    this._calculatorTMin.NominalThickness = this.NominalThickness;
    this._calculatorTMin.InternalUniformMetalLoss = this.InternalUniformMetalLoss;
    this._calculatorTMin.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    this._calculatorTMin.HeightHead = this.HeightHead;
    return this._calculatorTMin.CalculateAssessment();
  }

  public CalculatorResult CalculateAssessment()
  {
    this.CalculateTrd();
    if (this.CalculateTMin() == CalculatorResult.Fail)
    {
      this.ErrorMessage = this._calculatorTMin.ErrorMessage;
      return CalculatorResult.Fail;
    }
    this.TMin = this._calculatorTMin.TMin;
    this.AllowableStrength = this._calculatorTMin.AllowableStrength;
    this.Warnings.AddRange((IEnumerable<string>) this._calculatorTMin.Warnings);
    this.Radius = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    this.Tc = this.NominalThickness - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance - this.InternalFutureCorrosionAllowance - this.InternalUniformMetalLoss;
    this.Trd = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss;
    try
    {
      this.Tmm = this._thicknessReadings.GetMinimumValue();
      if (this.Tmm >= this.Trd)
      {
        this.ErrorMessage = "All thickness readings are greater than trd = tnom - LOSS. No metal loss has been found. This assessment is not needed";
        return CalculatorResult.Fail;
      }
    }
    catch
    {
      this.ErrorMessage = "Profile readings contain multiple Tmm's - Currently unable to proceed with the assessment.";
      return CalculatorResult.Fail;
    }
    this.RSFa = 0.9;
    this.Rt = (this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.Tc;
    this.Rt = Math.Round(this.Rt, 4);
    this.RtGreaterThan0 = this.Rt > 0.0;
    if (!this.RtGreaterThan0)
    {
      this.ErrorMessage = "Remaining thickness ratio is less than or equal to zero. The assessment fails. ";
      return CalculatorResult.Fail;
    }
    this.Q = this.Rt >= this.RSFa ? 50.0 : 1.123 * Math.Pow(Math.Pow((1.0 - this.Rt) / (1.0 - this.Rt / this.RSFa), 2.0) - 1.0, 0.5);
    this.L = this.Q * Math.Pow(2.0 * this.Radius * this.Tc, 0.5);
    this._thicknessReadings.ProcessThicknessReadings(this.L, this.CircumferentialThicknessReadingSpacing, this.LongitudinalThicknessReadingSpacing, this.Trd);
    this.S = this._thicknessReadings.S;
    this.C = this._thicknessReadings.C;
    this.TCam = this._thicknessReadings.Tcam;
    this.TSam = this._thicknessReadings.Tsam;
    this.minTamsTamc = Math.Min(this.TCam, this.TSam);
    this.minTamsTamcMinusFCA = this.minTamsTamc - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.Tlim = Math.Max(0.2 * this.NominalThickness, 2.5);
    this.TmmMinusFCA = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.Max0_5TminTlim = Math.Max(0.5 * this.TMin, this.Tlim);
    this.AverageMeasuredThicknessL1 = this.minTamsTamcMinusFCA > this.TMin;
    this.MinimumMeasuredThicknessL1 = this.TmmMinusFCA >= this.Max0_5TminTlim;
    if (this.AverageMeasuredThicknessL1 && this.MinimumMeasuredThicknessL1)
    {
      this.Level1 = "Level 1 Assessment ACCEPTABLE";
      this.Level1Passed = true;
      this.Warnings.Add("This assessment assumes that the Permissible Maximum Allowable Working Pressure (based on (tam - FCA)/RSFa) is greater than or equal to the design pressure. Otherwise it fails (API 579-1/ASME FFS-1 Part 4 Table 4.4).");
      return CalculatorResult.Completed;
    }
    this.Level1 = "The Level 1 Assessment is UNACCEPTABLE";
    this.Level1Passed = false;
    if (!this.ToLevel2)
      return CalculatorResult.Completed;
    this.MinimumMeasuredThicknessL2 = this.MinimumMeasuredThicknessL1;
    this.TamMinusFCAOverRSFa = this.minTamsTamcMinusFCA / this.RSFa;
    this.AverageMeasuredThicknessL2 = this.TamMinusFCAOverRSFa >= this.TMin;
    if (this.AverageMeasuredThicknessL2 && this.MinimumMeasuredThicknessL2)
    {
      this.Level2 = "The Level 2 Assessment is ACCEPTABLE";
      this.Warnings.Add("This assessment assumes that the Permissible Maximum Allowable Working Pressure (based on (tam - FCA)/RSFa) is greater than or equal to the design pressure. Otherwise it fails (API 579-1/ASME FFS-1 Part 4 Table 4.4).");
      this.Level2Passed = true;
    }
    else
    {
      this.Level2 = "The Level 2 Assessment is UNACCEPTABLE";
      this.Level2Passed = false;
    }
    return CalculatorResult.Completed;
  }

  private MaterialStrengthLookupResult getStrengthResult(double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialPD5500StressValueDTO pd5500StressValueDto = this._allowableStrengths.Where<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 <= tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialPD5500StressValueDTO, double?>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialPD5500StressValueDTO>();
    if (pd5500StressValueDto == null)
    {
      num1 = this._allowableStrengths.Max<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        pd5500StressValueDto = new MaterialPD5500StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (pd5500StressValueDto == null)
    {
      num1 = this._allowableStrengths.Min<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TLower)).Value;
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      pd5500StressValueDto = new MaterialPD5500StressValueDTO()
      {
        TLower = new double?(0.0),
        TUpper = new double?(0.0),
        SLower = new double?(0.0),
        SUpper = new double?(0.0)
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = pd5500StressValueDto.TLower.Value,
      TUpper = pd5500StressValueDto.TUpper.Value,
      SLower = pd5500StressValueDto.SLower.Value,
      SUpper = pd5500StressValueDto.SUpper.Value
    };
  }
}
