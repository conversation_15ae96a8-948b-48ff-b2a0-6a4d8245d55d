// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.EllipticalHead.MembraneStress.MembraneStress
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.EllipticalHead.MembraneStress;

public class MembraneStress : BaseCalculator
{
  public double DesignPressure { private get; set; }

  public double NominalThickness { private get; set; }

  public double NominalInsideDiameter { private get; set; }

  public double HeightHead { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double SigmaM { get; private set; }

  public MembraneStress() => this.Warnings = new List<string>();

  public CalculatorResult CalculateAssessment()
  {
    double num1 = this.NominalInsideDiameter + 2.0 * this.NominalThickness;
    double num2 = this.HeightHead - this.ExternalUniformMetalLoss;
    double num3 = num1 - 2.0 * this.ExternalUniformMetalLoss;
    if (num2 < 0.18 * num3)
    {
      this.ErrorMessage = "Tmin cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
      return CalculatorResult.Fail;
    }
    if (this.NominalThickness < 0.002 * num3)
    {
      this.ErrorMessage = "Tmin cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
      return CalculatorResult.Fail;
    }
    double tnomOverD = this.NominalThickness / num3 * 1000.0;
    double heOverd = num2 / num3;
    if (heOverd < 0.15 || heOverd > 0.4)
    {
      this.ErrorMessage = "Tmin cannot be calculated accordinf to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
      return CalculatorResult.Fail;
    }
    double POverS = 0.0;
    LookupPD5500Table3_5_2Result pd5500Table352Result = Utilities.DoLookup2(heOverd, POverS, tnomOverD);
    if (pd5500Table352Result == null)
    {
      this.ErrorMessage = "Lookup failed on LookupPD5500Table3_5_2";
      return CalculatorResult.Fail;
    }
    double hD = pd5500Table352Result.H_D;
    double hDGreater = pd5500Table352Result.H_D_Greater;
    double pSGreater = pd5500Table352Result.P_S_Greater;
    double pSSmaller = pd5500Table352Result.P_S_Smaller;
    double infoTabloeRight = pd5500Table352Result.InfoTabloeRight;
    double tmonDGreater = pd5500Table352Result.tmon_D_Greater;
    int dGreaterPosition = pd5500Table352Result.tnom_D_Greater_Position;
    double tmonDSmaller = pd5500Table352Result.tmon_D_Smaller;
    if (pd5500Table352Result.INFOInTheTable)
      POverS = tmonDGreater != 0.0 ? Math.Pow(10.0, Math.Log10(pSGreater) - Math.Log10(pSGreater / pSSmaller) / Math.Log10(tmonDGreater / tmonDSmaller) * Math.Log10(tmonDGreater / tnomOverD)) : pSSmaller;
    this.SigmaM = !pd5500Table352Result.INFOInTheTable ? infoTabloeRight * this.DesignPressure / tnomOverD : 1.0 / POverS * this.DesignPressure;
    return CalculatorResult.Completed;
  }
}
