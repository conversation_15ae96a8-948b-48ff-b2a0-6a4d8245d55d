// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.EllipticalHead.LocalMetalLossThinArea.LocalMetalLossThinArea
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.EllipticalHead.LocalMetalLossThinArea;

public class LocalMetalLossThinArea : BaseCalculator
{
  public bool ASMEVIIIDiv1Before1999;
  private bool RtGreater0;
  private IntegriWISE.AssessmentsCalculators.Calculators.PD5500.EllipticalHead.MAWP.MAWP _calculatorMAWP;
  private ThicknessReadings _thicknessReadings;
  private List<MaterialPD5500StressValueDTO> _allowableStrengths;
  private MaterialPD5500DTO _material;
  private List<MaterialTSFCurveDTO> _api579Table5_4List;

  public bool ToLevel2 { get; set; }

  public bool MAWP { get; set; }

  public double DesignTemperature { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double HeightEllipticalHead { get; set; }

  public bool CorrodedRegionWithin0_8D { get; set; }

  public double Lmsd { get; set; }

  public double LongitudinalThicknessReadingSpacingLc { get; set; }

  public double CircumferentialThicknessReadingSpacingLm { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double LongitudinalMetalLossExtentS { get; set; }

  public double CircumferentiallMetalLossExtentC { get; set; }

  public double AllowableStrength { get; set; }

  public double Tmm { get; set; }

  public double Trd { get; set; }

  public double Tc { get; set; }

  public double Lambda { get; set; }

  public double RSF { get; set; }

  public bool RtGreater0_2 { get; set; }

  public bool TmmMinusFCAGreater2_5 { get; set; }

  public bool LmsdGreater1_8DtcPower0_5 { get; set; }

  public bool ScreeningCriteriaFigure5_6 { get; set; }

  public bool RSFGreaterRSFa { get; set; }

  public bool Level1Passed { get; set; }

  public string ConditionLongitudinalExtentL1 { get; set; }

  public string Level1AssessmentConclusion { get; set; }

  public bool min_RSFiGreaterThanOrEqualToRSFa { get; set; }

  public double minRSFi { get; set; }

  public bool Level2Passed { get; set; }

  public string ConditionLongitudinalExtentL2 { get; set; }

  public string Level2AssessmentConclusion { get; set; }

  public double MAWPValue { get; set; }

  public int iConditionLongitudinalExtentLevel1 { get; set; }

  public double MAWPrL1 { get; set; }

  public int iConditionLongitudinalExtentLevel2 { get; set; }

  public double MAWPrL2 { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private double Radius { get; set; }

  private double RtFigure5_7 { get; set; }

  private double RSFa { get; set; }

  private double Rt { get; set; }

  private double LambdaTable5_2 { get; set; }

  private double MtTable5_2 { get; set; }

  public LocalMetalLossThinArea(
    MaterialPD5500DTO material,
    List<MaterialPD5500StressValueDTO> allowableStrengths,
    List<MaterialTSFCurveDTO> api579Table5_4List,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this._api579Table5_4List = api579Table5_4List;
    this._thicknessReadings = thicknessReadingsCalculator;
    this.Warnings = new List<string>();
  }

  public virtual void CalculateTrd()
  {
    this.Trd = this.NominalThickness - this.ExternalUniformMetalLoss - this.InternalUniformMetalLoss;
  }

  public virtual CalculatorResult CalculateMAWP()
  {
    this._calculatorMAWP = new IntegriWISE.AssessmentsCalculators.Calculators.PD5500.EllipticalHead.MAWP.MAWP(this._material, this._allowableStrengths);
    this._calculatorMAWP.DesignTemperature = this.DesignTemperature;
    this._calculatorMAWP.NominalInsideDiameter = this.NominalInsideDiameter;
    this._calculatorMAWP.NominalThickness = this.NominalThickness;
    this._calculatorMAWP.HeightHead = this.HeightEllipticalHead;
    this._calculatorMAWP.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    this._calculatorMAWP.InternalUniformMetalLoss = this.InternalUniformMetalLoss;
    this._calculatorMAWP.YieldStrengthNew = this._material.YieldStrengthNew;
    this._calculatorMAWP.TensileStrengthNew = this._material.TensileStrengthNew;
    return this._calculatorMAWP.CalculateAssessment();
  }

  public CalculatorResult CalculateAssessment()
  {
    CalculatorResult calculatorResult = CalculatorResult.Fail;
    if (this.MAWP)
    {
      calculatorResult = this.CalculateMAWP();
      if (calculatorResult == CalculatorResult.Fail)
      {
        this.ErrorMessage = this._calculatorMAWP.ErrorMessage;
        return CalculatorResult.Fail;
      }
      this.MAWPValue = this._calculatorMAWP.MAWPValue;
      this.AllowableStrength = this._calculatorMAWP.AllowableStrength;
      this.Warnings.AddRange((IEnumerable<string>) this._calculatorMAWP.Warnings);
    }
    this.Radius = this.NominalInsideDiameter / 2.0 + this.InternalUniformMetalLoss + this.InternalFutureCorrosionAllowance;
    this.Tc = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.CalculateTrd();
    try
    {
      this.Tmm = this._thicknessReadings.GetMinimumValue();
      if (this.Tmm >= this.Trd)
      {
        this.ErrorMessage = "All thickness readings are greater than trd = tnom - LOSS. No metal loss has been found. This assessment is not needed";
        return CalculatorResult.Fail;
      }
    }
    catch
    {
      this.ErrorMessage = "Profile readings contain multiple Tmm's - Currently unable to proceed with the assessment.";
      return CalculatorResult.Fail;
    }
    this._thicknessReadings.ProcessP5ThicknessReadings(this.CircumferentialThicknessReadingSpacingLm, this.LongitudinalThicknessReadingSpacingLc, Math.Round(this.Trd, 4));
    this.LongitudinalMetalLossExtentS = this._thicknessReadings.S;
    this.CircumferentiallMetalLossExtentC = this._thicknessReadings.C;
    this.RSFa = 0.9;
    this.Rt = (this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.Tc;
    this.Rt = Math.Round(this.Rt, 4);
    this.RtGreater0 = this.Rt > 0.0;
    if (!this.RtGreater0)
    {
      this.ErrorMessage = "Remaining thickness ratio is less than or equal to zero. The assessment fails. ";
      return CalculatorResult.Fail;
    }
    this.Lambda = 1.285 * this.LongitudinalMetalLossExtentS / Math.Pow(2.0 * this.Radius * this.Tc, 0.5);
    this.RtGreater0_2 = this.Rt > 0.2;
    this.TmmMinusFCAGreater2_5 = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance >= 2.5;
    this.LmsdGreater1_8DtcPower0_5 = this.Lmsd > 1.8 * Math.Pow(this.Radius * 2.0 * this.Tc, 0.5);
    this.LambdaTable5_2 = this.Lambda <= 20.0 ? this.Lambda : 20.0;
    this.MtTable5_2 = (1.0005 + 0.49001 * this.LambdaTable5_2 + 0.32409 * Math.Pow(this.LambdaTable5_2, 2.0)) / (1.0 + 0.50144 * this.LambdaTable5_2 - 0.011067 * Math.Pow(this.LambdaTable5_2, 2.0));
    this.RtFigure5_7 = this.Lambda > 0.33 ? (this.Lambda >= 20.0 ? 0.9 : (this.RSFa - this.RSFa / this.MtTable5_2) * Math.Pow(1.0 - this.RSFa / this.MtTable5_2, -1.0)) : 0.2;
    this.ScreeningCriteriaFigure5_6 = this.Rt > this.RtFigure5_7;
    this.RSF = this.Rt / (1.0 - 1.0 / this.MtTable5_2 * (1.0 - this.Rt));
    this.RSFGreaterRSFa = this.RSF >= this.RSFa;
    this.iConditionLongitudinalExtentLevel1 = 0;
    if (this.RtGreater0_2 && this.TmmMinusFCAGreater2_5 && this.LmsdGreater1_8DtcPower0_5)
    {
      if (this.ScreeningCriteriaFigure5_6 || this.RSFGreaterRSFa)
      {
        this.ConditionLongitudinalExtentL1 = "The extent of the flaw is ACCEPTABLE for operation at the MAWP.";
        this.iConditionLongitudinalExtentLevel1 = 1;
        this.Level1AssessmentConclusion = "The Level 1 Assessment is ACCEPTABLE";
        this.Level1Passed = true;
        return CalculatorResult.Completed;
      }
      this.ConditionLongitudinalExtentL1 = "The extent of the flaw is UNACCEPTABLE for operation at MAWP but it is acceptable for operation at MAWPr";
      this.iConditionLongitudinalExtentLevel1 = 2;
      this.Level1AssessmentConclusion = "The Level 1 Assessment is UNACCEPTABLE. The component is unacceptable for operation at MAWP but it is ACCEPTABLE for operation at MAWPr";
      this.Level1Passed = false;
    }
    else
    {
      this.ConditionLongitudinalExtentL1 = "The extent of the flaw is UNACCEPTABLE";
      this.iConditionLongitudinalExtentLevel1 = 3;
      this.Level1AssessmentConclusion = "The Level 1 Assessment is UNACCEPTABLE";
      this.Level1Passed = false;
    }
    if (this.MAWP && calculatorResult == CalculatorResult.Completed && this.iConditionLongitudinalExtentLevel1 == 2 && !this.RSFGreaterRSFa)
      this.MAWPrL1 = this.MAWPValue * this.RSF / this.RSFa;
    if (!this.ToLevel2)
      return CalculatorResult.Completed;
    this._thicknessReadings.ProcessP5Level2(this.Trd, this.LongitudinalThicknessReadingSpacingLc, this.NominalInsideDiameter, this.Tc);
    this.minRSFi = this._thicknessReadings.GetLevel2RSF();
    this.min_RSFiGreaterThanOrEqualToRSFa = this.minRSFi >= this.RSFa;
    this.iConditionLongitudinalExtentLevel2 = 0;
    if (this.RtGreater0_2 && this.TmmMinusFCAGreater2_5 && this.LmsdGreater1_8DtcPower0_5)
    {
      if (this.min_RSFiGreaterThanOrEqualToRSFa)
      {
        this.ConditionLongitudinalExtentL2 = "The extent of the flaw is ACCEPTABLE for operation at the MAWP";
        this.iConditionLongitudinalExtentLevel2 = 1;
        this.Level2AssessmentConclusion = "The Level 2 Assessment is ACCEPTABLE.";
        this.Level2Passed = true;
      }
      else
      {
        this.ConditionLongitudinalExtentL2 = "The extent of the flaw is UNACCEPTABLE for operation at MAWP but it is acceptable for operation at MAWPr";
        this.Level2AssessmentConclusion = "The Level 2 Assessment is UNACCEPTABLE.The component is unacceptable for operation at MAWP but it is ACCEPTABLE for operation at MAWPr";
        this.iConditionLongitudinalExtentLevel2 = 2;
        this.Level2Passed = false;
      }
    }
    else
    {
      this.ConditionLongitudinalExtentL2 = "The extent of the flaw is UNACCEPTABLE";
      this.iConditionLongitudinalExtentLevel2 = 3;
      this.Level2AssessmentConclusion = "The Level 2 Assessment is UNACCEPTABLE";
      this.Level2Passed = false;
    }
    if (this.MAWP && calculatorResult == CalculatorResult.Completed && this.iConditionLongitudinalExtentLevel2 == 2 && !this.min_RSFiGreaterThanOrEqualToRSFa)
      this.MAWPrL2 = this.MAWPValue * this.minRSFi / this.RSFa;
    return CalculatorResult.Completed;
  }

  private MaterialStrengthLookupResult getStrengthResult(double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialPD5500StressValueDTO pd5500StressValueDto = this._allowableStrengths.Where<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 <= tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialPD5500StressValueDTO, double?>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialPD5500StressValueDTO>();
    if (pd5500StressValueDto == null)
    {
      num1 = this._allowableStrengths.Max<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        pd5500StressValueDto = new MaterialPD5500StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (pd5500StressValueDto == null)
    {
      num1 = this._allowableStrengths.Min<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TLower)).Value;
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      pd5500StressValueDto = new MaterialPD5500StressValueDTO()
      {
        TLower = new double?(0.0),
        TUpper = new double?(0.0),
        SLower = new double?(0.0),
        SUpper = new double?(0.0)
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = pd5500StressValueDto.TLower.Value,
      TUpper = pd5500StressValueDto.TUpper.Value,
      SLower = pd5500StressValueDto.SLower.Value,
      SUpper = pd5500StressValueDto.SUpper.Value
    };
  }
}
