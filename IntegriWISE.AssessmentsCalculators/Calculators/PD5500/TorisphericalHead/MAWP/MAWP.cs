// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.TorisphericalHead.MAWP.MAWP
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.TorisphericalHead.MAWP;

public class MAWP : BaseCalculator
{
  private MaterialPD5500DTO _material;
  private List<MaterialPD5500StressValueDTO> _allowableStrengths;

  public double? YieldStrengthNew { get; set; }

  public double? TensileStrengthNew { get; set; }

  public double DesignTemperature { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double HeightHead { get; set; }

  public double InsideCrownRadius { get; set; }

  public double InsideKnuckleRadius { get; set; }

  private double TemperatureUpLimit { get; set; }

  private double NominalOutsideDiameter { get; set; }

  private double heigth_h { get; set; }

  private double diameter_D { get; set; }

  private double he { get; set; }

  private bool rGraterThanOrEqualTo0_06D { get; set; }

  private bool RSmallerThanD { get; set; }

  private bool tnomOverDSmallerThanorEqualTo0_002 { get; set; }

  private double Tlower { get; set; }

  private double Tupper { get; set; }

  private double StrengthLower { get; set; }

  private double StrengthUpper { get; set; }

  private double tnomOverD { get; set; }

  private double heOverD { get; set; }

  private bool heoverDLimit { get; set; }

  private double h_D { get; set; }

  private double h_D_Greater { get; set; }

  private double NumberRow { get; set; }

  private double IntoTable_Right { get; set; }

  private double tnom_D_Smaller { get; set; }

  private double tnom_D_Greater { get; set; }

  private double tnom_D_Smaller_Position { get; set; }

  private double tnom_D_Greater_Position { get; set; }

  private double P_S_Smaller { get; set; }

  private double P_S_Greater { get; set; }

  private double POverS { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double MAWPValue { get; private set; }

  public double AllowableStrength { get; private set; }

  public MAWP()
  {
  }

  public MAWP(
    MaterialPD5500DTO material,
    List<MaterialPD5500StressValueDTO> allowableStrengths)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    try
    {
      if (this._material.UserDefined)
      {
        this.AllowableStrength = Math.Min(this.YieldStrengthNew.Value / 1.5, this.TensileStrengthNew.Value / 2.35);
        this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
      }
      else
      {
        this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
        MaterialStrengthLookupResult strengthResult = Utilities.GetStrengthResult(this._allowableStrengths, this.DesignTemperature);
        if (strengthResult == null)
        {
          this.ErrorMessage = "PD5500 Material Lookup failed - MaterialStrengthLookup values not found";
          return CalculatorResult.Fail;
        }
        if (strengthResult.Status == "Above Range")
        {
          this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
          return CalculatorResult.Fail;
        }
        if (strengthResult.SLower == 0.0 && strengthResult.SUpper == 0.0)
        {
          this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
          return CalculatorResult.Fail;
        }
        if (strengthResult.SUpper == 0.0 && strengthResult.TLower < this.DesignTemperature)
        {
          this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
          return CalculatorResult.Fail;
        }
        this.Tupper = strengthResult.TUpper;
        this.Tlower = strengthResult.TLower;
        this.StrengthLower = strengthResult.SLower;
        this.StrengthUpper = strengthResult.SUpper;
        this.AllowableStrength = this.DesignTemperature >= 50.0 ? (this.DesignTemperature - this.Tlower) / (this.Tupper - this.Tlower) * (this.StrengthUpper - this.StrengthLower) + this.StrengthLower : this.StrengthLower;
      }
      this.NominalOutsideDiameter = this.NominalInsideDiameter + 2.0 * this.NominalThickness;
      this.heigth_h = this.HeightHead - this.ExternalUniformMetalLoss;
      this.diameter_D = this.NominalOutsideDiameter - 2.0 * this.ExternalUniformMetalLoss;
      this.he = Math.Min(Math.Min(this.heigth_h, Math.Pow(this.diameter_D, 2.0) / (4.0 * (this.InsideCrownRadius + this.NominalThickness))), Math.Pow(this.diameter_D * (this.InsideKnuckleRadius + this.NominalThickness) / 2.0, 0.5));
      this.rGraterThanOrEqualTo0_06D = this.InsideKnuckleRadius + this.InternalUniformMetalLoss >= 0.06 * this.diameter_D;
      if (!this.rGraterThanOrEqualTo0_06D)
      {
        this.ErrorMessage = "MAWP cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
        return CalculatorResult.Fail;
      }
      this.RSmallerThanD = this.InsideCrownRadius + this.InternalUniformMetalLoss <= this.diameter_D;
      if (!this.RSmallerThanD)
      {
        this.ErrorMessage = "MAWP cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
        return CalculatorResult.Fail;
      }
      this.tnomOverDSmallerThanorEqualTo0_002 = this.NominalThickness >= 0.002 * this.diameter_D;
      if (!this.tnomOverDSmallerThanorEqualTo0_002)
      {
        this.ErrorMessage = "MAWP cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
        return CalculatorResult.Fail;
      }
      this.tnomOverD = this.NominalThickness / this.diameter_D * 1000.0;
      this.heOverD = this.he / this.diameter_D;
      this.heoverDLimit = this.heOverD >= 0.15 && this.heOverD <= 0.4;
      if (!this.heoverDLimit)
      {
        this.ErrorMessage = "MAWP cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
        return CalculatorResult.Fail;
      }
      LookupPD5500Table3_5_2Result pd5500Table352Result = Utilities.DoLookup2(this.heOverD, this.POverS, this.tnomOverD);
      if (pd5500Table352Result == null)
      {
        this.ErrorMessage = "Lookup failed on LookupPD5500Table3_5_2";
        return CalculatorResult.Fail;
      }
      this.h_D = pd5500Table352Result.H_D;
      this.h_D_Greater = pd5500Table352Result.H_D_Greater;
      this.P_S_Greater = pd5500Table352Result.P_S_Greater;
      this.P_S_Smaller = pd5500Table352Result.P_S_Smaller;
      this.IntoTable_Right = pd5500Table352Result.InfoTabloeRight;
      this.tnom_D_Greater = pd5500Table352Result.tmon_D_Greater;
      this.tnom_D_Greater_Position = (double) pd5500Table352Result.tnom_D_Greater_Position;
      this.tnom_D_Smaller = pd5500Table352Result.tmon_D_Smaller;
      if (pd5500Table352Result.INFOInTheTable)
        this.POverS = this.tnom_D_Greater_Position != 0.0 ? Math.Pow(10.0, Math.Log10(this.P_S_Greater) - Math.Log10(this.P_S_Greater / this.P_S_Smaller) / Math.Log10(this.tnom_D_Greater / this.tnom_D_Smaller) * Math.Log10(this.tnom_D_Greater / this.tnomOverD)) : this.P_S_Smaller;
      this.MAWPValue = !pd5500Table352Result.INFOInTheTable ? this.tnomOverD * this.AllowableStrength / this.IntoTable_Right : this.POverS * this.AllowableStrength;
      return CalculatorResult.Completed;
    }
    catch (Exception ex)
    {
      this.ErrorMessage = $"MAWP Torispherical Head failed: {ex.Message}";
      return CalculatorResult.Fail;
    }
  }
}
