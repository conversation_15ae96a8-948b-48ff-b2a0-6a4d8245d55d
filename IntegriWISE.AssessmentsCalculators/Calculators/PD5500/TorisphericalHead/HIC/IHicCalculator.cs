// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.TorisphericalHead.HIC.IHicCalculator
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.TorisphericalHead.HIC;

public interface IHicCalculator
{
  double NominalInsideDiameter { set; }

  double NominalThickness { set; }

  double LongitudinalHICDimension { set; }

  double CircumferentialHICDimension { set; }

  double LH { set; }

  double LW { set; }

  double LMSD { set; }

  double TMMID { set; }

  double TMMOD { set; }

  double InternalUniformMetalLoss { set; }

  double ExternalUniformMetalLoss { set; }

  double InternalFutureCorrosionAllowance { set; }

  double ExternalFutureCorrosionAllowance { set; }

  double tc { get; }

  double wHModifiedByFCA { get; }

  bool ConditionS { get; }

  bool ConditionC { get; }

  bool ConditionThroughThicknessExtend { get; }

  bool ConditionDistanceToWeldSeam { get; }

  bool ConditionDistanceMajorDiscontinuity { get; }

  bool ConditionSubsurfaceHICTmmID { get; }

  bool ConditionSubsurfaceHICTmmOD { get; }

  List<string> Warnings { get; }

  string ErrorMessage { get; }

  string Level1Conclusion { get; }

  bool Level1Passed { get; }

  CalculatorResult CalculateAssessment();

  double MAWP { get; set; }
}
