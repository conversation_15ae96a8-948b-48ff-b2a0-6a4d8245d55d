// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.TorisphericalHead.GeneralMetalLossThicknessReading.GeneralMetalLossThicknessReading
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.TorisphericalHead.GeneralMetalLossThicknessReading;

public class GeneralMetalLossThicknessReading : BaseCalculator
{
  private bool PLessThanSE;
  private double Max05TminTlim;
  private double Radius;
  private double checkTcMin;
  private double checkTlMin;
  private string TemperatureLowerLimit;
  private double WorkingTemperature;
  private List<MaterialPD5500StressValueDTO> _allowableStrengths;
  private MaterialPD5500DTO _material;
  private ThicknessReadings _thicknessReadings;
  private IntegriWISE.AssessmentsCalculators.Calculators.PD5500.TorisphericalHead.MinimumThickness.MinimumThickness _calculatorTMin;

  public bool ToLevel2 { get; set; }

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double HeightHead { private get; set; }

  public double InsideCrownRadius { private get; set; }

  public double InsideKnuckleRadius { private get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double AllowableStrength { get; set; }

  public double TMin { get; set; }

  public double Tmm { get; set; }

  public double Tam { get; set; }

  public double Cov { get; set; }

  public double Tlim { get; set; }

  public bool AverageMeasuredThicknessL1 { get; set; }

  public bool MawpL1 { get; set; }

  public bool MinimumMeasuredThicknessL1 { get; set; }

  public bool Level1Passed { get; set; }

  public string Level1ResultMsg { get; set; }

  public bool MinimumMeasuredThicknessL2 { get; set; }

  public bool AverageMeasuredThicknessL2 { get; set; }

  public bool MawpL2 { get; set; }

  public bool Level2Passed { get; set; }

  public string Level2ResultMsg { get; set; }

  private double MAWPrTamMinusFCA { get; set; }

  private double MAWPrTamMinusFCAOverRSFa { get; set; }

  private bool PLessThan2S { get; set; }

  private double Diameter { get; set; }

  private double MAWPr { get; set; }

  private bool ConditionP { get; set; }

  private double TMin1 { get; set; }

  private double TamMinusFCA { get; set; }

  private double Max0_5TminTlim { get; set; }

  private string MinimumTemperatureC { get; set; }

  private double MinimumAllowableTemperature { get; set; }

  private string TemperatureLowLimit { get; set; }

  private double TLower { get; set; }

  private double TUpper { get; set; }

  private double SLower { get; set; }

  private double SUpper { get; set; }

  private double Tlower2 { get; set; }

  private double Tupper2 { get; set; }

  private double Ylower { get; set; }

  private double Yupper { get; set; }

  private double YB31ASMEB31 { get; set; }

  private double OutsideDiameterDo { get; set; }

  private double tc { get; set; }

  private double tcmin1 { get; set; }

  private double tlmin1 { get; set; }

  private double tmin1 { get; set; }

  private double checkYB31 { get; set; }

  private double tcmin { get; set; }

  private double tlMin { get; set; }

  private double ThicknessReadingsN { get; set; }

  private double S { get; set; }

  private string CheckCOV { get; set; }

  private double tamMinusFCA { get; set; }

  private double tmmMinusFCA { get; set; }

  private double Max0_5MminTlim { get; set; }

  private double MAWPrcTamMinusFCA { get; set; }

  private double MAWPrlTamMinusFCA { get; set; }

  private double RSFa { get; set; }

  public double TamMinusFCAOverRSFa { get; set; }

  private double tamMinusTslMinusFCAOverRSFa { get; set; }

  private double MAWPrcTamMinusFCAOverRSFa { get; set; }

  private double MAWPrlTamMinusTslMinusFCAOverRSFa { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private string TemperatureUpLimit { get; set; }

  private string StrengthTemperatureLimit { get; set; }

  private double ThicknessLower { get; set; }

  private double ThicknessUpper { get; set; }

  private double TminLower { get; set; }

  private double TminUpper { get; set; }

  public GeneralMetalLossThicknessReading(
    MaterialPD5500DTO material,
    List<MaterialPD5500StressValueDTO> allowableStrengths,
    ThicknessReadings thicknessReadings)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
    this._thicknessReadings = thicknessReadings;
  }

  public virtual CalculatorResult CalculateTMin()
  {
    this._calculatorTMin = new IntegriWISE.AssessmentsCalculators.Calculators.PD5500.TorisphericalHead.MinimumThickness.MinimumThickness(this._material, this._allowableStrengths);
    this._calculatorTMin.DesignTemperature = this.DesignTemperature;
    this._calculatorTMin.DesignPressure = this.DesignPressure;
    this._calculatorTMin.YieldStrengthNew = this._material.YieldStrengthNew;
    this._calculatorTMin.TensileStrengthNew = this._material.TensileStrengthNew;
    this._calculatorTMin.NominalInsideDiameter = this.NominalInsideDiameter;
    this._calculatorTMin.NominalThickness = this.NominalThickness;
    this._calculatorTMin.HeightHead = this.HeightHead;
    this._calculatorTMin.InsideCrownRadius = this.InsideCrownRadius;
    this._calculatorTMin.InsideKnuckleRadius = this.InsideKnuckleRadius;
    this._calculatorTMin.InternalUniformMetalLoss = this.InternalUniformMetalLoss;
    this._calculatorTMin.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    return this._calculatorTMin.CalculateAssessment();
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this.CalculateTMin() == CalculatorResult.Fail)
    {
      this.ErrorMessage = this._calculatorTMin.ErrorMessage;
      return CalculatorResult.Fail;
    }
    this.TMin = this._calculatorTMin.TMin;
    this.AllowableStrength = this._calculatorTMin.AllowableStrength;
    this.Warnings.AddRange((IEnumerable<string>) this._calculatorTMin.Warnings);
    this.ThicknessReadingsN = (double) this._thicknessReadings.TotalNumThicknessPoints();
    this.S = this._thicknessReadings.SumOfThicknessReadings();
    this.Tam = this.S / this.ThicknessReadingsN;
    this.Tmm = this._thicknessReadings.MinimumThicknessPointValue();
    this.S = this._thicknessReadings.SumOfThicknessReadingsPow2();
    this.Cov = 1.0 / this.Tam * Math.Pow(this.S / (this.ThicknessReadingsN - 1.0), 0.5) * 100.0;
    if (this.Cov > 10.0)
    {
      this.ErrorMessage = "The Coefficient of Variation (COV) of the thickness readings is greater than 10%, the general metal loss procedure can not be used in this case. Then thickness profiles shall be considered for use in the assessment (see paragraph 4.3.3.3 API 579)";
      return CalculatorResult.Fail;
    }
    this.tamMinusFCA = this.Tam - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.Tlim = Math.Max(0.2 * this.NominalThickness, 2.5);
    this.tmmMinusFCA = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.Max0_5TminTlim = Math.Max(0.5 * this.TMin, this.Tlim);
    this.RSFa = 0.9;
    this.AverageMeasuredThicknessL1 = this.tamMinusFCA >= this.TMin;
    this.MinimumMeasuredThicknessL1 = this.tmmMinusFCA >= this.Max0_5TminTlim;
    if (this.AverageMeasuredThicknessL1 && this.MinimumMeasuredThicknessL1)
    {
      this.Level1ResultMsg = "Level 1 Assessment ACCEPTABLE";
      this.Level1Passed = true;
      this.Warnings.Add("This assessment assumes that the Permissible Maximum Allowable Working Pressure (based on tam - FCA) is greater than or equal to the design pressure. Otherwise it fails (API 579-1/ASME FFS-1 Part 4 Table 4.4).");
      return CalculatorResult.Completed;
    }
    this.Level1ResultMsg = "Level 1 Assessment UNACCEPTABLE";
    this.Level1Passed = false;
    if (!this.ToLevel2)
      return CalculatorResult.Completed;
    this.MinimumMeasuredThicknessL2 = this.MinimumMeasuredThicknessL1;
    this.TamMinusFCAOverRSFa = this.tamMinusFCA / this.RSFa;
    this.AverageMeasuredThicknessL2 = this.TamMinusFCAOverRSFa >= this.TMin;
    if (this.MinimumMeasuredThicknessL2 && this.AverageMeasuredThicknessL2)
    {
      this.Level2ResultMsg = "The Level 2 Assessment is ACCEPTABLE";
      this.Warnings.Add("This assessment assumes that the Permissible Maximum Allowable Working Pressure (based on (tam - FCA)/RSFa) is greater than or equal to the design pressure. Otherwise it fails (API 579-1/ASME FFS-1 Part 4 Table 4.4).");
      this.Level2Passed = true;
    }
    else
    {
      this.Level2ResultMsg = "The Level 2 Assessment is UNACCEPTABLE";
      this.Level2Passed = false;
    }
    return CalculatorResult.Completed;
  }

  private MaterialStrengthLookupResult getStrengthResult(double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialPD5500StressValueDTO pd5500StressValueDto = this._allowableStrengths.Where<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 <= tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialPD5500StressValueDTO, double?>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialPD5500StressValueDTO>();
    if (pd5500StressValueDto == null)
    {
      num1 = this._allowableStrengths.Max<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        pd5500StressValueDto = new MaterialPD5500StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (pd5500StressValueDto == null)
    {
      num1 = this._allowableStrengths.Min<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TLower)).Value;
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      pd5500StressValueDto = new MaterialPD5500StressValueDTO()
      {
        TLower = new double?(0.0),
        TUpper = new double?(0.0),
        SLower = new double?(0.0),
        SUpper = new double?(0.0)
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = pd5500StressValueDto.TLower.Value,
      TUpper = pd5500StressValueDto.TUpper.Value,
      SLower = pd5500StressValueDto.SLower.Value,
      SUpper = pd5500StressValueDto.SUpper.Value
    };
  }
}
