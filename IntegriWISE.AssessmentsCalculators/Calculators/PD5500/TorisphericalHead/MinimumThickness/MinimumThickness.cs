// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.TorisphericalHead.MinimumThickness.MinimumThickness
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.TorisphericalHead.MinimumThickness;

public class MinimumThickness : BaseCalculator
{
  private MaterialPD5500DTO _material;
  private List<MaterialPD5500StressValueDTO> _allowableStrengths;

  public double DesignTemperature { private get; set; }

  public double DesignPressure { private get; set; }

  public double? YieldStrengthNew { private get; set; }

  public double? TensileStrengthNew { private get; set; }

  public double NominalThickness { private get; set; }

  public double NominalInsideDiameter { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public double HeightHead { private get; set; }

  public double InsideCrownRadius { private get; set; }

  public double InsideKnuckleRadius { private get; set; }

  public double AllowableStrength { get; private set; }

  public double TMin { get; private set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public MinimumThickness(
    MaterialPD5500DTO material,
    List<MaterialPD5500StressValueDTO> allowableStrengths)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.UserDefined)
    {
      this.AllowableStrength = Math.Min(this.YieldStrengthNew.Value / 1.5, this.TensileStrengthNew.Value / 2.35);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      MaterialStrengthLookupResult strengthResult = Utilities.GetStrengthResult(this._allowableStrengths, this.DesignTemperature);
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.AboveRange)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      if (strengthResult.SLower == 0.0 && strengthResult.SUpper == 0.0)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      if (strengthResult.SUpper == 0.0 && strengthResult.TLower < this.DesignTemperature)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      double tlower = strengthResult.TLower;
      double tupper = strengthResult.TUpper;
      double slower = strengthResult.SLower;
      double supper = strengthResult.SUpper;
      this.AllowableStrength = this.DesignTemperature > 50.0 ? (this.DesignTemperature - tlower) / (tupper - tlower) * (supper - slower) + slower : slower;
    }
    double num1 = this.NominalInsideDiameter + 2.0 * this.NominalThickness;
    double val1 = this.HeightHead - this.ExternalUniformMetalLoss;
    double x = num1 - 2.0 * this.ExternalUniformMetalLoss;
    double num2 = Math.Min(Math.Min(val1, Math.Pow(x, 2.0) / (4.0 * (this.InsideCrownRadius + this.NominalThickness))), Math.Pow(x * (this.InsideKnuckleRadius + this.NominalThickness) / 2.0, 0.5));
    if (this.InsideKnuckleRadius + this.InternalUniformMetalLoss < 0.06 * x)
    {
      this.ErrorMessage = "Tmin cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
      return CalculatorResult.Fail;
    }
    if (this.InsideCrownRadius + this.InternalUniformMetalLoss > x)
    {
      this.ErrorMessage = "Tmin cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
      return CalculatorResult.Fail;
    }
    if (this.NominalThickness < 0.002 * x)
    {
      this.ErrorMessage = "Tmin cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
      return CalculatorResult.Fail;
    }
    double POverS = this.DesignPressure / this.AllowableStrength;
    double heOverd = num2 / x;
    bool flag1 = heOverd >= 0.15 && heOverd <= 0.4 && POverS >= 0.001;
    if (!flag1)
    {
      this.ErrorMessage = "Tmin cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
      return CalculatorResult.Fail;
    }
    LookupPD5500Table3_5_2Result pd5500Table352Result = Utilities.DoLookup(heOverd, POverS);
    if (pd5500Table352Result == null)
    {
      this.ErrorMessage = "Lookup failed on LookupPD5500Table3_5_2";
      return CalculatorResult.Fail;
    }
    double hD = pd5500Table352Result.H_D;
    double hDGreater = pd5500Table352Result.H_D_Greater;
    double pSGreater = pd5500Table352Result.P_S_Greater;
    double pSSmaller = pd5500Table352Result.P_S_Smaller;
    double num3 = 0.0;
    double d = 0.0;
    if (pd5500Table352Result.INFOInTheTable)
    {
      d = pd5500Table352Result.tmin_D_Greater;
      num3 = pd5500Table352Result.tmin_D_Smaller;
    }
    else
    {
      double infoTabloeRight = pd5500Table352Result.InfoTabloeRight;
    }
    bool flag2 = !flag1 || !pd5500Table352Result.INFOInTheTable ? flag1 : num3 != 0.0;
    if (flag2)
    {
      double num4 = !pd5500Table352Result.INFOInTheTable ? pd5500Table352Result.InfoTabloeRight : (d != 0.0 ? Math.Pow(10.0, Math.Log10(d) - Math.Log10(pSGreater / POverS) / Math.Log10(pSGreater / pSSmaller) * Math.Log10(d / num3)) : num3);
      bool flag3 = false;
      if (flag2)
      {
        flag3 = this.InsideKnuckleRadius >= 2.0 * num4;
        if (!flag3)
        {
          this.ErrorMessage = "Tmin cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
          return CalculatorResult.Fail;
        }
      }
      bool flag4 = num4 * 0.001 <= 0.12;
      if (!flag4)
      {
        this.ErrorMessage = "Tmin cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
        return CalculatorResult.Fail;
      }
      if (flag2 && flag4 && flag3)
      {
        this.TMin = num4 * 0.001 * x;
        return CalculatorResult.Completed;
      }
      this.ErrorMessage = "Tmin cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500 ";
      return CalculatorResult.Fail;
    }
    this.ErrorMessage = "Tmin cannot be calculated according to PD 5500. Some geometrical limitations for this component type are not met. See 3.5.2.2 PD 5500";
    return CalculatorResult.Fail;
  }
}
