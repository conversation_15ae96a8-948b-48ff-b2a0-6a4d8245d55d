// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.CylindricalShell.LocalMetalLossGrooveLike.LocalMetalLossGrooveLike
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.CylindricalShell.LocalMetalLossGrooveLike;

public class LocalMetalLossGrooveLike : BaseCalculator
{
  private double C1;
  private double C1Lower;
  private double C1Upper;
  private double C2;
  private double C2Lower;
  private double C2Upper;
  private double C3;
  private double C3Lower;
  private double C3Upper;
  private double C4;
  private double C4Lower;
  private double C4Upper;
  private double C5;
  private double C5Lower;
  private double C5Upper;
  private double C6;
  private double C6Lower;
  private double C6Upper;
  private double LambdaCMinus02;
  private double LambdaLower;
  private double LambdaUpper;
  public CalculatorResult MAWPResult;
  private double MtTable5_2;
  private double RtFigure5_8;
  private double RtfIGURE5_6;
  private double TSFLower;
  private double TSFUpper;
  private double lambdaTable5_2;

  public LocalMetalLossGrooveLike() => this.Warnings = new List<string>();

  public List<MaterialTSFCurveDTO> TSFLookup { get; set; }

  public List<string> Warnings { get; private set; }

  public double NominalInsideDiameter { get; set; }

  private double Radius { get; set; }

  private double RSFa { get; set; }

  private double Rt { get; set; }

  public bool BdLessThan50 { get; set; }

  public bool BlisterCrownCrackingAndVentHoles { get; set; }

  public bool BlisterProjection { get; set; }

  public bool BlisterVented { get; set; }

  public bool ConditionC { get; set; }

  public bool ConditionDistanceMajorDiscontinuity { get; set; }

  public bool ConditionS { get; set; }

  public bool ConditionsCircumferentialExtent { get; set; }

  public bool DDivTcGreaterThanEqual20 { get; set; }

  public bool DistanceToWeldSeam { get; set; }

  public bool EcBetween07And1 { get; set; }

  public bool ElBetween07And1 { get; set; }

  public bool grGreaterOrEqual1MinusRttc { get; set; }

  public bool lambdaCLessThanEqual9 { get; set; }

  public bool Level1Passed { get; set; }

  public bool LmsdGreaterOrEqual1_8Dtc { get; set; }

  public bool MAWPLevel { get; set; }

  public bool MinimumMeasuredUndamagedThickness { get; set; }

  public bool RSFBetween07And1 { get; set; }

  public bool RSFGreaterEqualRSFa { get; set; }

  public bool RtGreaterOrEqual0_20 { get; set; }

  public bool ScreeningCriteriaFigure5_6 { get; set; }

  public bool ScreeningCriteriaFigure5_8 { get; set; }

  public bool tmmMinusFCAGreaterOrEqual2_5 { get; set; }

  public double BdDiameter { get; set; }

  public double BlisterBulgeProjection { get; set; }

  public double BlisterSpacing { get; set; }

  public double C { get; set; }

  public double CircumferentialBlisterDimension { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double GrooveLikeFlawAngle { get; set; }

  public double GrooveLikeFlawDepth { get; set; }

  public double GrooveLikeFlawLength { get; set; }

  public double GrooveLikeFlawRadius { get; set; }

  public double GrooveLikeFlawWidth { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double lambda { get; set; }

  public double lambdaC { get; set; }

  public double Lmsd { get; set; }

  public double LongitudinalBlisterDimension { get; set; }

  public double LW { get; set; }

  public double MAWP { get; set; }

  public double MAWPr { get; set; }

  public double NominalThickness { get; set; }

  public double RSF { get; set; }

  public double S { get; set; }

  public double SC { get; set; }

  public double tc { get; set; }

  public double tmm { get; set; }

  public double TMMBlister { get; set; }

  public double trd { get; set; }

  public double TSF { get; set; }

  public double LongitudinalWeldJointEfficiency { get; set; }

  public double CircumferentialWeldJointEfficiency { get; set; }

  public bool IsAGrooveLikeFlaw { get; set; }

  public bool RtGreaterThanZero { get; set; }

  public double LongitudinalThicknessReadingSpacing { get; set; }

  public double CircumferentialThicknessReadingSpacing { get; set; }

  public double Level1MAWPr { get; set; }

  public string WarningFailureLevel1 { get; set; }

  public int iConditionLongitudinalExtentLevel1 { get; set; }

  public string ConditionCircumferentialExtentLevel1 { get; set; }

  public string ConditionLongitudinalExtentLevel1 { get; set; }

  public string ErrorMessage { get; private set; }

  public string Level1 { get; set; }

  public CalculatorResult CalculateAssessment()
  {
    this.Level1 = "Level 1 Assessment UNACCEPTABLE";
    this.Level1Passed = false;
    this.Radius = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    this.tc = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance;
    this.trd = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss;
    this.tmm = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss - this.GrooveLikeFlawDepth;
    this.RSFa = 0.9;
    this.Rt = (this.tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.tc;
    this.Rt = Math.Round(this.Rt, 4);
    this.RtGreaterThanZero = this.Rt > 0.0;
    if (!this.RtGreaterThanZero)
    {
      this.Level1 = "Level 1 failed - incorrect inputs (RTcheck),";
      this.Level1Passed = false;
      return CalculatorResult.Fail;
    }
    this.S = this.GrooveLikeFlawLength * Math.Cos(this.GetRadians(this.GrooveLikeFlawAngle)) + this.GrooveLikeFlawWidth * Math.Sin(this.GetRadians(this.GrooveLikeFlawAngle));
    this.C = this.GrooveLikeFlawLength * Math.Sin(this.GetRadians(this.GrooveLikeFlawAngle)) + this.GrooveLikeFlawWidth * Math.Cos(this.GetRadians(this.GrooveLikeFlawAngle));
    this.lambda = 1.285 * this.S / Math.Pow(2.0 * this.Radius * this.tc, 0.5);
    this.RtGreaterOrEqual0_20 = this.Rt >= 0.2;
    this.tmmMinusFCAGreaterOrEqual2_5 = this.tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance >= 2.5;
    this.LmsdGreaterOrEqual1_8Dtc = this.Lmsd >= 1.7999999523162842 * Math.Pow(this.Radius * 2.0 * this.tc, 0.5);
    this.grGreaterOrEqual1MinusRttc = this.GrooveLikeFlawRadius >= (1.0 - this.Rt) * this.tc;
    this.lambdaTable5_2 = this.lambda <= 20.0 ? this.lambda : 20.0;
    this.MtTable5_2 = 1001.0 / 1000.0 - 0.014195 * this.lambdaTable5_2 + 0.2909 * Math.Pow(this.lambdaTable5_2, 2.0) - 0.09642 * Math.Pow(this.lambdaTable5_2, 3.0) + 0.02089 * Math.Pow(this.lambdaTable5_2, 4.0) - 0.003054 * Math.Pow(this.lambdaTable5_2, 5.0) + 2.957 * Math.Pow(10.0, -4.0) * Math.Pow(this.lambdaTable5_2, 6.0) - 1.8462 * Math.Pow(10.0, -5.0) * Math.Pow(this.lambdaTable5_2, 7.0) + 7.1553 * Math.Pow(10.0, -7.0) * Math.Pow(this.lambdaTable5_2, 8.0) - 1.5631 * Math.Pow(10.0, -8.0) * Math.Pow(this.lambdaTable5_2, 9.0) + 916.0 / 625.0 * Math.Pow(10.0, -10.0) * Math.Pow(this.lambdaTable5_2, 10.0);
    this.RtfIGURE5_6 = this.lambda > 0.354 ? (this.lambda >= 20.0 ? 0.9 : (this.RSFa - this.RSFa / this.MtTable5_2) * Math.Pow(1.0 - this.RSFa / this.MtTable5_2, -1.0)) : 0.2;
    this.ScreeningCriteriaFigure5_6 = this.Rt >= this.RtfIGURE5_6;
    this.RSF = this.Rt / (1.0 - 1.0 / this.MtTable5_2 * (1.0 - this.Rt));
    this.RSFGreaterEqualRSFa = this.RSF >= this.RSFa;
    if (this.RtGreaterOrEqual0_20 && this.tmmMinusFCAGreaterOrEqual2_5 && this.LmsdGreaterOrEqual1_8Dtc && this.grGreaterOrEqual1MinusRttc)
    {
      if (this.ScreeningCriteriaFigure5_6 && this.RSFGreaterEqualRSFa)
      {
        this.ConditionLongitudinalExtentLevel1 = "The longitudinal extent of the flaw is ACCEPTABLE for operation at the MAWP.";
        this.iConditionLongitudinalExtentLevel1 = 1;
      }
      else
      {
        this.ConditionLongitudinalExtentLevel1 = "The longitudinal extent of the flaw is ACCEPTABLE for operation at the MAWPr.";
        this.iConditionLongitudinalExtentLevel1 = 2;
      }
    }
    else
    {
      this.ConditionLongitudinalExtentLevel1 = "The longitudinal extent of the flaw is UNACCEPTABLE";
      this.iConditionLongitudinalExtentLevel1 = 3;
    }
    this.lambdaC = 1.285 * this.C / Math.Pow(this.Radius * 2.0 * this.tc, 0.5);
    this.lambdaCLessThanEqual9 = this.lambdaC <= 9.0;
    this.DDivTcGreaterThanEqual20 = this.Radius * 2.0 / this.tc >= 20.0;
    this.RSFBetween07And1 = 0.7 <= this.RSF && 1.0 >= this.RSF;
    this.ElBetween07And1 = 0.7 <= this.LongitudinalWeldJointEfficiency && 1.0 >= this.LongitudinalWeldJointEfficiency;
    this.EcBetween07And1 = 0.7 <= this.CircumferentialWeldJointEfficiency && 1.0 >= this.CircumferentialWeldJointEfficiency;
    this.ConditionsCircumferentialExtent = this.lambdaCLessThanEqual9 && this.DDivTcGreaterThanEqual20 && this.RSFBetween07And1 && this.ElBetween07And1 && this.EcBetween07And1;
    if (this.ConditionsCircumferentialExtent)
    {
      this.TSF = this.CircumferentialWeldJointEfficiency / (2.0 * this.RSF) * (1.0 + Math.Pow(4.0 - 3.0 * Math.Pow(this.LongitudinalWeldJointEfficiency, 2.0), 0.5) / this.LongitudinalWeldJointEfficiency);
      MaterialTSFCurveDTO Uppervalues = this.TSFLookup.OrderBy<MaterialTSFCurveDTO, double?>((Func<MaterialTSFCurveDTO, double?>) (o => o.TSF)).FirstOrDefault<MaterialTSFCurveDTO>((Func<MaterialTSFCurveDTO, bool>) (fod =>
      {
        double? tsf1 = fod.TSF;
        double tsf2 = this.TSF;
        return tsf1.GetValueOrDefault() > tsf2 && tsf1.HasValue;
      }));
      if (Uppervalues == null)
      {
        this.ErrorMessage = "Outside normal operating conditions";
        return CalculatorResult.Fail;
      }
      MaterialTSFCurveDTO materialTsfCurveDto = this.TSFLookup.OrderByDescending<MaterialTSFCurveDTO, double?>((Func<MaterialTSFCurveDTO, double?>) (o => o.TSF)).FirstOrDefault<MaterialTSFCurveDTO>((Func<MaterialTSFCurveDTO, bool>) (fod =>
      {
        double? tsf3 = fod.TSF;
        double? tsf4 = Uppervalues.TSF;
        return tsf3.GetValueOrDefault() < tsf4.GetValueOrDefault() && tsf3.HasValue & tsf4.HasValue;
      })) ?? Uppervalues;
      this.TSFLower = materialTsfCurveDto.TSF.Value;
      this.TSFUpper = Uppervalues.TSF.Value;
      this.LambdaLower = materialTsfCurveDto.LambdaCMinus02.Value;
      this.LambdaUpper = Uppervalues.LambdaCMinus02.Value;
      this.C1Lower = materialTsfCurveDto.C1.Value;
      this.C2Lower = materialTsfCurveDto.C2.Value;
      this.C3Lower = materialTsfCurveDto.C3.Value;
      this.C4Lower = materialTsfCurveDto.C4.Value;
      this.C5Lower = materialTsfCurveDto.C5.Value;
      this.C6Lower = materialTsfCurveDto.C6.Value;
      this.C1Upper = Uppervalues.C1.Value;
      this.C2Upper = Uppervalues.C2.Value;
      this.C3Upper = Uppervalues.C3.Value;
      this.C4Upper = Uppervalues.C4.Value;
      this.C5Upper = Uppervalues.C5.Value;
      this.C6Upper = Uppervalues.C6.Value;
      this.LambdaCMinus02 = this.TSFLower != 2.3 ? (this.TSF - this.TSFLower) / (this.TSFUpper - this.TSFLower) * (this.LambdaUpper - this.LambdaLower) + this.LambdaLower : this.LambdaLower;
      this.C1 = this.TSFLower != 2.3 ? (this.TSF - this.TSFLower) / (this.TSFUpper - this.TSFLower) * (this.C1Upper - this.C1Lower) + this.C1Lower : this.C1Lower;
      this.C2 = this.TSFLower != 2.3 ? (this.TSF - this.TSFLower) / (this.TSFUpper - this.TSFLower) * (this.C2Upper - this.C2Lower) + this.C2Lower : this.C2Lower;
      this.C3 = this.TSFLower != 2.3 ? (this.TSF - this.TSFLower) / (this.TSFUpper - this.TSFLower) * (this.C3Upper - this.C3Lower) + this.C3Lower : this.C3Lower;
      this.C4 = this.TSFLower != 2.3 ? (this.TSF - this.TSFLower) / (this.TSFUpper - this.TSFLower) * (this.C4Upper - this.C4Lower) + this.C4Lower : this.C4Lower;
      this.C5 = this.TSFLower != 2.3 ? (this.TSF - this.TSFLower) / (this.TSFUpper - this.TSFLower) * (this.C5Upper - this.C5Lower) + this.C5Lower : this.C5Lower;
      this.C6 = this.TSFLower != 2.3 ? (this.TSF - this.TSFLower) / (this.TSFUpper - this.TSFLower) * (this.C6Upper - this.C6Lower) + this.C6Lower : this.C6Lower;
      this.RtFigure5_8 = this.lambdaC > this.LambdaCMinus02 ? this.C1 + this.C2 / this.lambdaC + this.C3 / Math.Pow(this.lambdaC, 2.0) + this.C4 / Math.Pow(this.lambdaC, 3.0) + this.C5 / Math.Pow(this.lambdaC, 4.0) + this.C6 / Math.Pow(this.lambdaC, 5.0) : 0.2;
      if (this.ConditionsCircumferentialExtent)
        this.ScreeningCriteriaFigure5_8 = this.Rt >= this.RtFigure5_8;
      this.ConditionCircumferentialExtentLevel1 = !this.ScreeningCriteriaFigure5_8 ? "The Circumferential extent of the flaw is UNACCEPTABLE" : "The Circumferential extent of the flaw is ACCEPTABLE";
      if (this.ScreeningCriteriaFigure5_8)
      {
        if (this.iConditionLongitudinalExtentLevel1 == 1)
        {
          this.Level1Passed = true;
          this.Level1 = "Level 1 Assessment ACCEPTABLE.";
        }
        else if (this.iConditionLongitudinalExtentLevel1 == 2)
        {
          this.Level1Passed = false;
          this.Level1 = "Level 1 Assessment UNACCEPTABLE. The component is unacceptable for operation at MAWP but it is ACCEPTABLE for operation at MAWPr.";
        }
        else
        {
          this.Level1Passed = false;
          this.Level1 = "Level 1 Assessment UNACCEPTABLE.";
        }
      }
    }
    else
    {
      this.ConditionCircumferentialExtentLevel1 = "The Circumferential extent of the flaw is UNACCEPTABLE";
      this.Level1Passed = false;
      this.Level1 = "Level 1 Assessment UNACCEPTABLE.";
    }
    if (!this.Level1Passed)
    {
      this.WarningFailureLevel1 = "As Level 1 assessment fails, the region of metal loss categorised as a groove can be evaluated using Level 2 assessment procedure for a Local Thin Area if the groove radius meets the condition gr >= (1-Rt)tc. Otherwise the groove shall be evaluated as an equivalent crack-like flaw using the assessment procedures in Part 9 API 579.";
      this.Warnings.Add(this.WarningFailureLevel1);
    }
    if (this.MAWPLevel)
    {
      if (this.MAWPResult == CalculatorResult.Completed)
      {
        if (this.RSF < this.RSFa)
          this.MAWPr = this.MAWP * this.RSF / this.RSFa;
      }
      else
      {
        this.ErrorMessage = "Unknow error in MAWP Calculator. Please Contact TWI Support to report this error";
        return CalculatorResult.Fail;
      }
    }
    return CalculatorResult.Completed;
  }

  private double GetRadians(double degrees) => Math.PI * degrees / 180.0;
}
