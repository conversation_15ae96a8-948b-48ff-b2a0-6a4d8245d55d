// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.CylindricalShell.HIC.HIC
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.CylindricalShell.HIC;

public class HIC : BaseCalculator, IHicCalculator
{
  public double NominalInsideDiameter { private get; set; }

  public double NominalThickness { private get; set; }

  public double LongitudinalHICDimension { private get; set; }

  public double CircumferentialHICDimension { private get; set; }

  public double LH { private get; set; }

  public double LW { private get; set; }

  public double LMSD { private get; set; }

  public double TMMID { private get; set; }

  public double TMMOD { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public double InternalFutureCorrosionAllowance { private get; set; }

  public double ExternalFutureCorrosionAllowance { private get; set; }

  public double MAWP { get; set; }

  public double tc { get; private set; }

  public double wHModifiedByFCA { get; private set; }

  public bool ConditionS { get; private set; }

  public bool ConditionC { get; private set; }

  public bool ConditionThroughThicknessExtend { get; private set; }

  public bool ConditionDistanceToWeldSeam { get; private set; }

  public bool ConditionDistanceMajorDiscontinuity { get; private set; }

  public bool ConditionSubsurfaceHICTmmID { get; private set; }

  public bool ConditionSubsurfaceHICTmmOD { get; private set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public string Level1Conclusion { get; private set; }

  public bool Level1Passed { get; private set; }

  public HIC() => this.Warnings = new List<string>();

  public CalculatorResult CalculateAssessment()
  {
    double num1 = this.NominalInsideDiameter - 2.0 * this.NominalThickness + 2.0 * (this.InternalUniformMetalLoss + this.InternalFutureCorrosionAllowance);
    this.tc = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss;
    this.wHModifiedByFCA = this.tc - this.TMMID - this.TMMOD;
    double num2 = this.TMMID - this.InternalFutureCorrosionAllowance;
    double num3 = this.TMMOD - this.ExternalFutureCorrosionAllowance;
    if (this.LH < 8.0 * this.tc)
    {
      this.ErrorMessage = "The distance between two adjacent HIC damage zones (measured edge-to-edge) is less than  eight times the corroded thickness, tc. The HIC damage dimensions s and c should be established using the procedure described in Part 4, Figure 4.7.";
      return CalculatorResult.Fail;
    }
    this.ConditionS = this.LongitudinalHICDimension <= 0.6 * Math.Pow(num1 * this.tc, 0.5);
    this.ConditionC = this.CircumferentialHICDimension <= 0.6 * Math.Pow(num1 * this.tc, 0.5);
    this.ConditionThroughThicknessExtend = this.wHModifiedByFCA <= Math.Min(this.tc / 3.0, 13.0);
    this.ConditionDistanceToWeldSeam = this.LW > Math.Max(2.0 * this.tc, 25.0);
    this.ConditionDistanceMajorDiscontinuity = this.LMSD >= 1.8 * Math.Pow(num1 * this.tc, 0.5);
    this.ConditionSubsurfaceHICTmmID = num2 >= 0.2 * this.tc;
    this.ConditionSubsurfaceHICTmmOD = num3 >= 0.2 * this.tc;
    if (this.ConditionS && this.ConditionC && this.ConditionThroughThicknessExtend && this.ConditionDistanceToWeldSeam && this.ConditionDistanceMajorDiscontinuity && this.ConditionSubsurfaceHICTmmID && this.ConditionSubsurfaceHICTmmOD)
    {
      this.Level1Conclusion = "Level 1 Assessment ACCEPTABLE";
      this.Level1Passed = true;
    }
    else
    {
      this.Level1Conclusion = "Level 1 Assessment UNACCEPTABLE";
      this.Level1Passed = false;
    }
    return CalculatorResult.Completed;
  }
}
