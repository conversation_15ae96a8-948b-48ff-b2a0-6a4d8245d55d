// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.CylindricalShell.Pitting.Pitting
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.CylindricalShell.Pitting;

public class Pitting : BaseCalculator
{
  private List<MaterialPittingGradeChartDTO> _graphPart6;
  public CalculatorResult MAWPResult;

  public double NominalThickness { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double MaximumPitDepth { get; set; }

  public int PittingGrade { get; set; }

  private double tc { get; set; }

  private double RSFa { get; set; }

  public double Rwt { get; set; }

  public bool RwtGreaterThan0_2 { get; set; }

  private double RwtUpper { get; set; }

  private double RwtLower { get; set; }

  private double RSFPitUpper { get; set; }

  private double RSFPitLower { get; set; }

  public double RSF { get; set; }

  public bool RSFGreateThanRSFa { get; set; }

  public double MAWP { get; set; }

  public double MAWPr { get; set; }

  public string Level1 { get; set; }

  public bool Level1Passed { get; set; }

  public bool MAWPLevel { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public Pitting(List<MaterialPittingGradeChartDTO> graphPart6)
  {
    this.Warnings = new List<string>();
    this._graphPart6 = graphPart6;
  }

  public CalculatorResult CalculateAssessment()
  {
    this.tc = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance;
    this.RSFa = 0.9;
    this.Rwt = (this.tc + this.InternalFutureCorrosionAllowance + this.ExternalFutureCorrosionAllowance - this.MaximumPitDepth) / this.tc;
    this.RwtGreaterThan0_2 = this.Rwt >= 0.2;
    if (!this.RwtGreaterThan0_2)
    {
      this.Level1 = "Level 1 Assessment UNACCEPTABLE";
      this.Level1Passed = false;
      return CalculatorResult.Completed;
    }
    if (this.PittingGrade > 0 && this.PittingGrade < 9)
    {
      MaterialPittingGradeChartDTO graphPart6Lower = this.GetGraphPart6Lower(this.Rwt);
      MaterialPittingGradeChartDTO graphPart6Upper = this.GetGraphPart6Upper(this.Rwt);
      if (graphPart6Lower == null || graphPart6Upper == null)
      {
        this.Level1 = "";
        this.Level1Passed = false;
        this.ErrorMessage = "GraphPart6 lookup failed.";
        return CalculatorResult.Fail;
      }
      this.RwtLower = graphPart6Lower.Rwt;
      this.RwtUpper = graphPart6Upper.Rwt;
      this.RSFPitLower = graphPart6Lower.CylinderRSF;
      this.RSFPitUpper = graphPart6Upper.CylinderRSF;
    }
    this.RSF = this.PittingGrade == 0 || this.PittingGrade == 9 ? this.Rwt : (this.Rwt - this.RwtLower) / (this.RwtUpper - this.RwtLower) * (this.RSFPitUpper - this.RSFPitLower) + this.RSFPitLower;
    this.RSFGreateThanRSFa = Math.Round(this.RSF, 4) >= this.RSFa;
    if (this.RSFGreateThanRSFa)
    {
      this.Level1 = "Level 1 Assessment ACCEPTABLE";
      this.Level1Passed = true;
    }
    else
    {
      this.Level1 = "Level 1 Assessment UNACCEPTABLE. The component is unacceptable for operation at MAWP but it is ACCEPTABLE for operation at MAWPr";
      this.Level1Passed = false;
    }
    if (this.MAWPLevel)
    {
      if (this.MAWPResult == CalculatorResult.Completed)
      {
        if (this.RSF < this.RSFa)
          this.MAWPr = this.MAWP * this.RSF / this.RSFa;
      }
      else
      {
        this.ErrorMessage = "Unknow Error in system. Please Contact TWI Support to report this error";
        return CalculatorResult.Fail;
      }
    }
    return CalculatorResult.Completed;
  }

  private MaterialPittingGradeChartDTO GetGraphPart6Lower(double rwt)
  {
    return this._graphPart6.Where<MaterialPittingGradeChartDTO>((Func<MaterialPittingGradeChartDTO, bool>) (p => p.Rwt <= rwt)).OrderByDescending<MaterialPittingGradeChartDTO, double>((Func<MaterialPittingGradeChartDTO, double>) (p => p.Rwt)).FirstOrDefault<MaterialPittingGradeChartDTO>();
  }

  private MaterialPittingGradeChartDTO GetGraphPart6Upper(double rwt)
  {
    return this._graphPart6.Where<MaterialPittingGradeChartDTO>((Func<MaterialPittingGradeChartDTO, bool>) (p => p.Rwt > rwt)).OrderBy<MaterialPittingGradeChartDTO, double>((Func<MaterialPittingGradeChartDTO, double>) (p => p.Rwt)).FirstOrDefault<MaterialPittingGradeChartDTO>();
  }
}
