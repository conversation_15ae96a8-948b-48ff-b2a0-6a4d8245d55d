// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.Utilities
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500;

public class Utilities
{
  public static LookupPD5500Table3_5_2Result DoLookup(double heOverd, double POverS)
  {
    double[] numArray1 = new double[10]
    {
      0.001,
      0.0015,
      1.0 / 400.0,
      0.004,
      0.006,
      0.01,
      0.015,
      0.025,
      0.05,
      1000000000.0
    };
    double[] numArray2 = new double[24]
    {
      0.15,
      0.16,
      0.17,
      0.18,
      0.19,
      0.2,
      0.21,
      0.22,
      0.23,
      0.24,
      0.25,
      0.26,
      0.27,
      0.28,
      0.29,
      0.3,
      0.31,
      0.32,
      0.33,
      0.34,
      0.35,
      0.36,
      0.38,
      0.4
    };
    double[][] numArray3 = new double[20][];
    LookupPD5500Table3_5_2Result pd5500Table352Result = new LookupPD5500Table3_5_2Result();
    numArray3[0] = new double[10]
    {
      0.15,
      2.13,
      2.7,
      3.73,
      5.22,
      7.2,
      10.9,
      15.4,
      24.0,
      44.5
    };
    numArray3[1] = new double[10]
    {
      0.16,
      1.95,
      2.5,
      3.5,
      4.9,
      6.7,
      10.2,
      14.3,
      22.2,
      41.5
    };
    numArray3[2] = new double[10]
    {
      0.17,
      1.8,
      2.3,
      3.24,
      4.58,
      6.3,
      9.6,
      13.5,
      21.0,
      39.2
    };
    numArray3[3] = new double[10]
    {
      0.18,
      1.65,
      2.11,
      2.99,
      4.23,
      5.8,
      8.8,
      12.6,
      19.7,
      37.0
    };
    numArray3[4] = new double[10]
    {
      0.19,
      0.0,
      1.95,
      2.77,
      3.95,
      5.43,
      8.3,
      11.8,
      18.5,
      35.0
    };
    numArray3[5] = new double[10]
    {
      0.2,
      0.0,
      1.8,
      2.55,
      3.64,
      5.0,
      7.7,
      11.0,
      17.3,
      33.0
    };
    numArray3[6] = new double[9]
    {
      0.21,
      0.0,
      1.65,
      2.39,
      3.42,
      4.75,
      7.3,
      10.4,
      16.2
    };
    numArray3[7] = new double[9]
    {
      0.22,
      0.0,
      1.52,
      2.22,
      3.2,
      4.45,
      6.84,
      9.7,
      15.4
    };
    numArray3[8] = new double[9]
    {
      0.23,
      0.0,
      1.4,
      2.08,
      2.95,
      4.12,
      6.3,
      9.1,
      14.5
    };
    numArray3[9] = new double[9]
    {
      0.24,
      0.0,
      0.0,
      1.92,
      2.76,
      3.83,
      5.9,
      8.5,
      13.6
    };
    numArray3[10] = new double[8]
    {
      0.25,
      0.0,
      0.0,
      1.75,
      2.58,
      3.56,
      5.5,
      7.8
    };
    numArray3[11] = new double[8]
    {
      0.26,
      0.0,
      0.0,
      1.64,
      2.4,
      3.34,
      5.15,
      7.35
    };
    numArray3[12] = new double[8]
    {
      0.27,
      0.0,
      0.0,
      1.52,
      2.25,
      3.12,
      4.8,
      6.8
    };
    numArray3[13] = new double[8]
    {
      0.28,
      0.0,
      0.0,
      1.41,
      2.12,
      2.93,
      4.5,
      6.45
    };
    numArray3[14] = new double[7]
    {
      0.29,
      0.0,
      0.0,
      0.0,
      2.0,
      2.73,
      4.2
    };
    numArray3[15] = new double[7]
    {
      0.3,
      0.0,
      0.0,
      0.0,
      1.86,
      2.54,
      3.95
    };
    numArray3[16 /*0x10*/] = new double[7]
    {
      0.31,
      0.0,
      0.0,
      0.0,
      1.71,
      2.41,
      3.8
    };
    numArray3[17] = new double[7]
    {
      0.32,
      0.0,
      0.0,
      0.0,
      1.61,
      2.3,
      3.65
    };
    numArray3[18] = new double[7]
    {
      0.33,
      0.0,
      0.0,
      0.0,
      1.52,
      2.2,
      3.5
    };
    numArray3[19] = new double[6]
    {
      0.34,
      0.0,
      0.0,
      0.0,
      1.45,
      2.1
    };
    int index1 = 0;
    while (numArray2[index1] < heOverd)
      ++index1;
    pd5500Table352Result.H_D = index1 - 1 <= 0 ? numArray2[index1] : numArray2[index1 - 1];
    pd5500Table352Result.H_D_Greater = numArray2[index1];
    int index2 = index1;
    int index3 = 0;
    while (numArray1[index3] < POverS)
      ++index3;
    pd5500Table352Result.P_S_Smaller = index3 - 1 <= 0 ? numArray1[index3] : numArray1[index3 - 1];
    pd5500Table352Result.P_S_Greater = numArray1[index3];
    int index4 = index3;
    pd5500Table352Result.INFOInTheTable = false;
    if (POverS > 0.05 && pd5500Table352Result.H_D_Greater == 0.15)
      pd5500Table352Result.InfoTabloeRight = 880.0 * POverS;
    else if (POverS > 0.05 && pd5500Table352Result.H_D_Greater == 0.16)
      pd5500Table352Result.InfoTabloeRight = 810.0 * POverS;
    else if (POverS > 0.05 && pd5500Table352Result.H_D_Greater == 0.17)
      pd5500Table352Result.InfoTabloeRight = 770.0 * POverS;
    else if (POverS > 0.05 && pd5500Table352Result.H_D_Greater == 0.18)
      pd5500Table352Result.InfoTabloeRight = 730.0 * POverS;
    else if (POverS > 0.05 && pd5500Table352Result.H_D_Greater == 0.19)
      pd5500Table352Result.InfoTabloeRight = 695.0 * POverS;
    else if (POverS > 0.05 && pd5500Table352Result.H_D_Greater == 0.2)
      pd5500Table352Result.InfoTabloeRight = 650.0 * POverS;
    else if (POverS > 0.025 && pd5500Table352Result.H_D_Greater == 0.21)
      pd5500Table352Result.InfoTabloeRight = 620.0 * POverS;
    else if (POverS > 0.025 && pd5500Table352Result.H_D_Greater == 0.22)
      pd5500Table352Result.InfoTabloeRight = 585.0 * POverS;
    else if (POverS > 0.025 && pd5500Table352Result.H_D_Greater == 0.23)
      pd5500Table352Result.InfoTabloeRight = 555.0 * POverS;
    else if (POverS > 0.025 && pd5500Table352Result.H_D_Greater == 0.24)
      pd5500Table352Result.InfoTabloeRight = 530.0 * POverS;
    else if (POverS > 0.015 && pd5500Table352Result.H_D_Greater == 0.25)
      pd5500Table352Result.InfoTabloeRight = 500.0 * POverS;
    else if (POverS > 0.015 && pd5500Table352Result.H_D_Greater == 0.26)
      pd5500Table352Result.InfoTabloeRight = 475.0 * POverS;
    else if (POverS > 0.015 && pd5500Table352Result.H_D_Greater == 0.27)
      pd5500Table352Result.InfoTabloeRight = 445.0 * POverS;
    else if (POverS > 0.015 && pd5500Table352Result.H_D_Greater == 0.28)
      pd5500Table352Result.InfoTabloeRight = 425.0 * POverS;
    else if (POverS > 0.01 && pd5500Table352Result.H_D_Greater == 0.29)
      pd5500Table352Result.InfoTabloeRight = 405.0 * POverS;
    else if (POverS > 0.01 && pd5500Table352Result.H_D_Greater == 0.3)
      pd5500Table352Result.InfoTabloeRight = 385.0 * POverS;
    else if (POverS > 0.01 && pd5500Table352Result.H_D_Greater == 0.31)
      pd5500Table352Result.InfoTabloeRight = 370.0 * POverS;
    else if (POverS > 0.01 && pd5500Table352Result.H_D_Greater == 0.32)
      pd5500Table352Result.InfoTabloeRight = 358.0 * POverS;
    else if (POverS > 0.01 && pd5500Table352Result.H_D_Greater == 0.33)
      pd5500Table352Result.InfoTabloeRight = 345.0 * POverS;
    else if (POverS > 0.006 && pd5500Table352Result.H_D_Greater == 0.34)
      pd5500Table352Result.InfoTabloeRight = 335.0 * POverS;
    else if (POverS > 0.004 && pd5500Table352Result.H_D_Greater == 0.35)
      pd5500Table352Result.InfoTabloeRight = 325.0 * POverS;
    else if (POverS > 0.004 && pd5500Table352Result.H_D_Greater == 0.36)
      pd5500Table352Result.InfoTabloeRight = 319.0 * POverS;
    else if (POverS > 0.004 && pd5500Table352Result.H_D_Greater == 0.38)
      pd5500Table352Result.InfoTabloeRight = 307.0 * POverS;
    else if (POverS > 0.004 && pd5500Table352Result.H_D_Greater == 0.4)
    {
      pd5500Table352Result.InfoTabloeRight = 295.0 * POverS;
    }
    else
    {
      pd5500Table352Result.INFOInTheTable = true;
      pd5500Table352Result.tmin_D_Smaller = numArray3[index2][index4];
      pd5500Table352Result.tmin_D_Greater = index4 >= numArray3[index2].GetUpperBound(0) ? numArray3[index2][index4] : numArray3[index2][index4 + 1];
    }
    return pd5500Table352Result;
  }

  public static LookupPD5500Table3_5_2Result DoLookup2(
    double heOverd,
    double POverS,
    double tnomOverD)
  {
    LookupPD5500Table3_5_2Result pd5500Table352Result = new LookupPD5500Table3_5_2Result();
    double[] numArray1 = new double[10]
    {
      0.001,
      0.0015,
      1.0 / 400.0,
      0.004,
      0.006,
      0.01,
      0.015,
      0.025,
      0.05,
      1000000000.0
    };
    double[] numArray2 = new double[24]
    {
      0.15,
      0.16,
      0.17,
      0.18,
      0.19,
      0.2,
      0.21,
      0.22,
      0.23,
      0.24,
      0.25,
      0.26,
      0.27,
      0.28,
      0.29,
      0.3,
      0.31,
      0.32,
      0.33,
      0.34,
      0.35,
      0.36,
      0.38,
      0.4
    };
    double[][] numArray3 = new double[20][]
    {
      new double[9]
      {
        2.13,
        2.7,
        3.73,
        5.22,
        7.2,
        10.9,
        15.4,
        24.0,
        44.5
      },
      new double[9]
      {
        1.95,
        2.5,
        3.5,
        4.9,
        6.7,
        10.2,
        14.3,
        22.2,
        41.5
      },
      new double[9]
      {
        1.8,
        2.3,
        3.24,
        4.58,
        6.3,
        9.6,
        13.5,
        21.0,
        39.2
      },
      new double[9]
      {
        1.65,
        2.11,
        2.99,
        4.23,
        5.8,
        8.8,
        12.6,
        19.7,
        37.0
      },
      new double[9]
      {
        0.0,
        1.95,
        2.77,
        3.95,
        5.43,
        8.3,
        11.8,
        18.5,
        35.0
      },
      new double[9]
      {
        0.0,
        1.8,
        2.55,
        3.64,
        5.0,
        7.7,
        11.0,
        17.3,
        33.0
      },
      new double[8]
      {
        0.0,
        1.65,
        2.39,
        3.42,
        4.75,
        7.3,
        10.4,
        16.2
      },
      new double[8]{ 0.0, 1.52, 2.22, 3.2, 4.45, 6.84, 9.7, 15.4 },
      new double[8]{ 0.0, 1.4, 2.08, 2.95, 4.12, 6.3, 9.1, 14.5 },
      new double[8]{ 0.0, 0.0, 1.92, 2.76, 3.83, 5.9, 8.5, 13.6 },
      new double[7]{ 0.0, 0.0, 1.75, 2.58, 3.56, 5.5, 7.8 },
      new double[7]{ 0.0, 0.0, 1.64, 2.4, 3.34, 5.15, 7.35 },
      new double[7]{ 0.0, 0.0, 1.52, 2.25, 3.12, 4.8, 6.8 },
      new double[7]{ 0.0, 0.0, 1.41, 2.12, 2.93, 4.5, 6.45 },
      new double[6]{ 0.0, 0.0, 0.0, 2.0, 2.73, 4.2 },
      new double[6]{ 0.0, 0.0, 0.0, 1.86, 2.54, 3.95 },
      new double[6]{ 0.0, 0.0, 0.0, 1.71, 2.41, 3.8 },
      new double[6]{ 0.0, 0.0, 0.0, 1.61, 2.3, 3.65 },
      new double[6]{ 0.0, 0.0, 0.0, 1.52, 2.2, 3.5 },
      new double[5]{ 0.0, 0.0, 0.0, 1.45, 2.1 }
    };
    int index1 = 0;
    while (numArray2[index1] < heOverd)
      ++index1;
    pd5500Table352Result.H_D = index1 - 1 <= 0 ? numArray2[index1] : numArray2[index1 - 1];
    pd5500Table352Result.H_D_Greater = numArray2[index1];
    int index2 = index1;
    string str = pd5500Table352Result.H_D_Greater.ToString("n2");
    pd5500Table352Result.INFOInTheTable = false;
    switch (str)
    {
      case "0.15":
        if (tnomOverD <= 44.5)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 880.0;
        break;
      case "0.16":
        if (tnomOverD <= 41.5)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 810.0;
        break;
      case "0.17":
        if (tnomOverD <= 39.2)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 770.0;
        break;
      case "0.18":
        if (tnomOverD <= 37.0)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 730.0;
        break;
      case "0.19":
        if (tnomOverD <= 35.0)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 695.0;
        break;
      case "0.20":
        if (tnomOverD <= 33.0)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 650.0;
        break;
      case "0.21":
        if (tnomOverD <= 16.2)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 620.0;
        break;
      case "0.22":
        if (tnomOverD <= 15.4)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 585.0;
        break;
      case "0.23":
        if (tnomOverD <= 14.5)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 555.0;
        break;
      case "0.24":
        if (tnomOverD <= 13.6)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 530.0;
        break;
      case "0.25":
        if (tnomOverD <= 7.8)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 500.0;
        break;
      case "0.26":
        if (tnomOverD <= 7.35)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 475.0;
        break;
      case "0.27":
        if (tnomOverD <= 6.8)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 445.0;
        break;
      case "0.28":
        if (tnomOverD <= 6.45)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 425.0;
        break;
      case "0.29":
        if (tnomOverD <= 4.2)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 405.0;
        break;
      case "0.30":
        if (tnomOverD <= 3.95)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 385.0;
        break;
      case "0.31":
        if (tnomOverD <= 3.8)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 370.0;
        break;
      case "0.32":
        if (tnomOverD <= 3.65)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 358.0;
        break;
      case "0.33":
        if (tnomOverD <= 3.5)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 345.0;
        break;
      case "0.34":
        if (tnomOverD <= 2.1)
        {
          pd5500Table352Result.INFOInTheTable = true;
          break;
        }
        pd5500Table352Result.InfoTabloeRight = 335.0;
        break;
      case "0.35":
        pd5500Table352Result.InfoTabloeRight = 325.0;
        break;
      case "0.36":
        pd5500Table352Result.InfoTabloeRight = 319.0;
        break;
      case "0.38":
        pd5500Table352Result.InfoTabloeRight = 307.0;
        break;
      case "0.40":
        pd5500Table352Result.InfoTabloeRight = 295.0;
        break;
    }
    int index3 = 0;
    if (pd5500Table352Result.INFOInTheTable)
    {
      while (numArray3[index2][index3] < tnomOverD)
        ++index3;
      pd5500Table352Result.P_S_Smaller = index3 <= 0 ? numArray1[index3] : numArray1[index3 - 1];
      pd5500Table352Result.tnom_D_Greater_Position = index3;
      pd5500Table352Result.tmon_D_Greater = numArray3[index2][index3];
      pd5500Table352Result.tmon_D_Smaller = numArray3[index2][index3 - 1];
      pd5500Table352Result.P_S_Greater = numArray1[index3];
    }
    return pd5500Table352Result;
  }

  public static MaterialStrengthLookupResult GetStrengthResult(
    List<MaterialPD5500StressValueDTO> allowableStrengths,
    double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialPD5500StressValueDTO pd5500StressValueDto1 = allowableStrengths.Where<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 <= tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialPD5500StressValueDTO, double?>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialPD5500StressValueDTO>();
    if (pd5500StressValueDto1 == null)
    {
      num1 = allowableStrengths.Max<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        pd5500StressValueDto1 = new MaterialPD5500StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (pd5500StressValueDto1 == null)
    {
      MaterialPD5500StressValueDTO pd5500StressValueDto2 = allowableStrengths.Where<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, bool>) (x =>
      {
        double num4 = temperature;
        double? tlower = x.TLower;
        return num4 < tlower.GetValueOrDefault() && tlower.HasValue;
      })).FirstOrDefault<MaterialPD5500StressValueDTO>();
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      pd5500StressValueDto1 = new MaterialPD5500StressValueDTO()
      {
        TLower = pd5500StressValueDto2.TLower,
        TUpper = pd5500StressValueDto2.TUpper,
        SLower = pd5500StressValueDto2.SLower,
        SUpper = pd5500StressValueDto2.SUpper
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = pd5500StressValueDto1.TLower.Value,
      TUpper = pd5500StressValueDto1.TUpper.Value,
      SLower = pd5500StressValueDto1.SLower.Value,
      SUpper = pd5500StressValueDto1.SUpper.Value
    };
  }
}
