// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.HemisphericalHead.MAWP.MAWP
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.HemisphericalHead.MAWP;

public class MAWP : BaseCalculator
{
  private MaterialPD5500DTO _material;
  private List<MaterialPD5500StressValueDTO> _allowableStrengths;

  public double? YieldStrengthNew { get; set; }

  public double? TensileStrengthNew { get; set; }

  public double DesignTemperature { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  private string TemperatureUpLimit { get; set; }

  private double Tlower { get; set; }

  private double Tupper { get; set; }

  private double StrengthLower { get; set; }

  private double StrengthUpper { get; set; }

  private double Diameter { get; set; }

  private double tc { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double MAWPValue { get; private set; }

  public double AllowableStrength { get; private set; }

  public MAWP()
  {
  }

  public MAWP(
    MaterialPD5500DTO material,
    List<MaterialPD5500StressValueDTO> allowableStrengths)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    try
    {
      if (this._material.UserDefined)
      {
        this.AllowableStrength = Math.Min(this.YieldStrengthNew.Value / 1.5, this.TensileStrengthNew.Value / 2.35);
        this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
      }
      else
      {
        this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
        MaterialStrengthLookupResult strengthResult = Utilities.GetStrengthResult(this._allowableStrengths, this.DesignTemperature);
        if (strengthResult == null)
        {
          this.ErrorMessage = "PD5500 Material Lookup failed - MaterialStrengthLookup values not found";
          return CalculatorResult.Fail;
        }
        if (strengthResult.Status == "Above Range")
        {
          this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
          return CalculatorResult.Fail;
        }
        if (strengthResult.SLower == 0.0 && strengthResult.SUpper == 0.0)
        {
          this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
          return CalculatorResult.Fail;
        }
        if (strengthResult.SUpper == 0.0 && strengthResult.TLower < this.DesignTemperature)
        {
          this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
          return CalculatorResult.Fail;
        }
        this.Tupper = strengthResult.TUpper;
        this.Tlower = strengthResult.TLower;
        this.StrengthLower = strengthResult.SLower;
        this.StrengthUpper = strengthResult.SUpper;
        this.AllowableStrength = this.DesignTemperature >= 50.0 ? (this.DesignTemperature - this.Tlower) / (this.Tupper - this.Tlower) * (this.StrengthUpper - this.StrengthLower) + this.StrengthLower : this.StrengthLower;
      }
      this.Diameter = this.NominalInsideDiameter + 2.0 * this.InternalUniformMetalLoss;
      this.tc = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss;
      this.MAWPValue = 4.0 * this.AllowableStrength * this.tc / (this.Diameter + 1.2 * this.tc);
      return CalculatorResult.Completed;
    }
    catch (Exception ex)
    {
      this.ErrorMessage = $"MAWP Hemispherical Head failed: {ex.Message}";
      return CalculatorResult.Fail;
    }
  }
}
