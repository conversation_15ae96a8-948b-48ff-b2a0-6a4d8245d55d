// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.HemisphericalHead.MinimumThickness.MinimumThickness
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.HemisphericalHead.MinimumThickness;

public class MinimumThickness : BaseCalculator
{
  private MaterialPD5500DTO _material;
  private List<MaterialPD5500StressValueDTO> _allowableStrengths;

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double? YieldStrengthNew { get; set; }

  public double? TensileStrengthNew { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double AllowableStrength { get; private set; }

  public double TMin { get; private set; }

  public List<string> Warnings { get; set; }

  public string ErrorMessage { get; set; }

  public MinimumThickness(
    MaterialPD5500DTO material,
    List<MaterialPD5500StressValueDTO> allowableStrengths)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.UserDefined)
    {
      this.AllowableStrength = Math.Min(this.YieldStrengthNew.Value / 1.5, this.TensileStrengthNew.Value / 2.35);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      MaterialStrengthLookupResult strengthResult = Utilities.GetStrengthResult(this._allowableStrengths, this.DesignTemperature);
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.AboveRange)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      if (strengthResult.SLower == 0.0 && strengthResult.SUpper == 0.0)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      if (strengthResult.SUpper == 0.0 && strengthResult.TLower < this.DesignTemperature)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      double tlower = strengthResult.TLower;
      double tupper = strengthResult.TUpper;
      double slower = strengthResult.SLower;
      double supper = strengthResult.SUpper;
      this.AllowableStrength = this.DesignTemperature >= 50.0 ? (this.DesignTemperature - tlower) / (tupper - tlower) * (supper - slower) + slower : slower;
    }
    double num = this.NominalInsideDiameter + 2.0 * this.InternalUniformMetalLoss;
    if (4.0 * this.AllowableStrength > 1.2 * this.DesignPressure)
    {
      this.TMin = this.DesignPressure * num / (4.0 * this.AllowableStrength - 1.2 * this.DesignPressure);
      return CalculatorResult.Completed;
    }
    this.ErrorMessage = "1.2 times design pressure must be less than 4 times the allowable stress";
    return CalculatorResult.Fail;
  }
}
