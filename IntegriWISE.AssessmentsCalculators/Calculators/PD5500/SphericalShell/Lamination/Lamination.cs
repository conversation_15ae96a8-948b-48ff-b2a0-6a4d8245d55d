// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.SphericalShell.Lamination.Lamination
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.SphericalShell.Lamination;

public class Lamination : BaseCalculator, ILaminationCalculator
{
  private bool bConditionLaminationSpacing;

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public string MultipleLaminations { get; set; }

  public double LongitudinalLaminationDimension { get; set; }

  public double CircumferentialLaminationDimension { get; set; }

  public double LaminationHeight { get; set; }

  public double LaminationSpacing { get; set; }

  public double LW { get; set; }

  public double TMM { get; set; }

  public double LMSD { get; set; }

  public bool InHydrogenChargingService { get; set; }

  public double InsideDiameterCorrectedByLOSSFCA { get; set; }

  public double tc { get; set; }

  public string ConditionLaminationSpacing { get; set; }

  public bool CrackLikeFlaw { get; set; }

  public bool LaminationIsNotSurfaceBreaking { get; set; }

  public bool DistanceToTheNearestDiscontinuity { get; set; }

  public bool DistanceToTheNearestWeldSeam { get; set; }

  public bool HydrogenChargingAndDimensions { get; set; }

  public bool HydrogenChargingAndDimensions2 { get; set; }

  public bool MAWPLevel { get; set; }

  public double MAWP { get; set; }

  public double AllowableStrength { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public string Level1 { get; set; }

  public bool Level1Passed { get; set; }

  public Lamination() => this.Warnings = new List<string>();

  public CalculatorResult CalculateAssessment()
  {
    this.InsideDiameterCorrectedByLOSSFCA = this.NominalInsideDiameter + 2.0 * (this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss);
    this.tc = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance;
    if (this.MultipleLaminations.ToLower() == "Yes, in the same plane".ToLower() && this.LaminationSpacing <= 2.0 * this.tc)
    {
      this.ConditionLaminationSpacing = "There are two or more laminations on the same plane, there is no indication of through thickness cracking and the spacing does not satisfy Ls > 2tc. The laminations should be combined into a single larger lamination before running the assessment.";
      this.Warnings.Add(this.ConditionLaminationSpacing);
      this.ErrorMessage = this.ConditionLaminationSpacing;
      this.Level1 = "Level 1 Assessment UNACCEPTABLE";
      this.Level1Passed = false;
      return CalculatorResult.Fail;
    }
    if (this.MultipleLaminations.ToLower() == "Yes, in different depths".ToLower() && this.LaminationSpacing <= 2.0 * this.tc)
    {
      this.ConditionLaminationSpacing = "There are two or more laminations at different depths in the wall thickness of the component and the spacing does not satisfy  Ls > 2tc. The group of laminations should be evaluated as equivalent HIC damage using the Level 1 assessment procedure in Part 7.";
      this.Warnings.Add(this.ConditionLaminationSpacing);
      this.ErrorMessage = this.ConditionLaminationSpacing;
      this.Level1 = "Level 1 Assessment UNACCEPTABLE";
      this.Level1Passed = false;
      return CalculatorResult.Fail;
    }
    this.bConditionLaminationSpacing = true;
    if (this.bConditionLaminationSpacing)
    {
      if (this.LaminationHeight <= 0.09 * Math.Max(this.LongitudinalLaminationDimension, this.CircumferentialLaminationDimension))
        this.CrackLikeFlaw = true;
      if (this.TMM >= 0.1 * this.tc)
        this.LaminationIsNotSurfaceBreaking = true;
      if (this.LMSD >= 1.8 * Math.Pow(this.InsideDiameterCorrectedByLOSSFCA * this.tc, 0.5))
        this.DistanceToTheNearestDiscontinuity = true;
      if (this.LW >= Math.Max(2.0 * this.tc, 25.0))
        this.DistanceToTheNearestWeldSeam = true;
      this.HydrogenChargingAndDimensions = this.InHydrogenChargingService && this.CircumferentialLaminationDimension <= 0.6 * Math.Pow(this.InsideDiameterCorrectedByLOSSFCA * this.tc, 0.5) || !this.InHydrogenChargingService;
      this.HydrogenChargingAndDimensions2 = this.InHydrogenChargingService && this.LongitudinalLaminationDimension <= 0.6 * Math.Pow(this.InsideDiameterCorrectedByLOSSFCA * this.tc, 0.5) || !this.InHydrogenChargingService;
      if (this.CrackLikeFlaw && this.LaminationIsNotSurfaceBreaking && this.DistanceToTheNearestDiscontinuity && this.DistanceToTheNearestWeldSeam && this.HydrogenChargingAndDimensions && this.HydrogenChargingAndDimensions2)
      {
        this.Level1 = "Level 1 Assessment ACCEPTABLE";
        this.Level1Passed = true;
        return CalculatorResult.Completed;
      }
      this.Level1 = "Level 1 Assessment UNACCEPTABLE";
      this.Level1Passed = false;
    }
    return CalculatorResult.Completed;
  }
}
