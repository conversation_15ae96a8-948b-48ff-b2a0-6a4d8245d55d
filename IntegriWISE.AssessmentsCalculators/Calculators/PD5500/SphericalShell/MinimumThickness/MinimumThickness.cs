// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.SphericalShell.MinimumThickness.MinimumThickness
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.SphericalShell.MinimumThickness;

public class MinimumThickness : BaseCalculator
{
  private MaterialPD5500DTO _material;
  private List<MaterialPD5500StressValueDTO> _allowableStrengths;

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double? YieldStrengthNew { get; set; }

  public double? TensileStrengthNew { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double AllowableStrength { get; private set; }

  public double TMin { get; private set; }

  public List<string> Warnings { get; set; }

  public string ErrorMessage { get; set; }

  public MinimumThickness(
    MaterialPD5500DTO material,
    List<MaterialPD5500StressValueDTO> allowableStrengths)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.UserDefined)
    {
      this.AllowableStrength = Math.Min(this.YieldStrengthNew.Value / 1.5, this.TensileStrengthNew.Value / 2.35);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      MaterialStrengthLookupResult strengthResult = this.getStrengthResult(this.DesignTemperature);
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.AboveRange)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      if (strengthResult.SLower == 0.0 && strengthResult.SUpper == 0.0)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is lower than the minimum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      if (strengthResult.SUpper == 0.0 && strengthResult.TLower < this.DesignTemperature)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      double tlower = strengthResult.TLower;
      double tupper = strengthResult.TUpper;
      double slower = strengthResult.SLower;
      double supper = strengthResult.SUpper;
      this.AllowableStrength = this.DesignTemperature >= 50.0 ? (this.DesignTemperature - tlower) / (tupper - tlower) * (supper - slower) + slower : slower;
    }
    double num = this.NominalInsideDiameter + 2.0 * this.InternalUniformMetalLoss;
    if (4.0 * this.AllowableStrength > 1.2 * this.DesignPressure)
    {
      this.TMin = this.DesignPressure * num / (4.0 * this.AllowableStrength - 1.2 * this.DesignPressure);
      return CalculatorResult.Completed;
    }
    this.ErrorMessage = "1.2 times design pressure must be less than 4 times the allowable stress";
    return CalculatorResult.Fail;
  }

  private MaterialStrengthLookupResult getStrengthResult(double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialPD5500StressValueDTO pd5500StressValueDto = this._allowableStrengths.Where<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 <= tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialPD5500StressValueDTO, double?>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialPD5500StressValueDTO>();
    if (pd5500StressValueDto == null)
    {
      num1 = this._allowableStrengths.Max<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        pd5500StressValueDto = new MaterialPD5500StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (pd5500StressValueDto == null)
    {
      num1 = this._allowableStrengths.Min<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TLower)).Value;
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      pd5500StressValueDto = new MaterialPD5500StressValueDTO()
      {
        TLower = new double?(0.0),
        TUpper = new double?(0.0),
        SLower = new double?(0.0),
        SUpper = new double?(0.0)
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = pd5500StressValueDto.TLower.Value,
      TUpper = pd5500StressValueDto.TUpper.Value,
      SLower = pd5500StressValueDto.SLower.Value,
      SUpper = pd5500StressValueDto.SUpper.Value
    };
  }
}
