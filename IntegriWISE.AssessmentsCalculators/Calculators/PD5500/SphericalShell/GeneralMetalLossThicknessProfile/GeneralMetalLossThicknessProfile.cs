// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.PD5500.SphericalShell.GeneralMetalLossThicknessProfile.GeneralMetalLossThicknessProfile
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.PD5500.SphericalShell.GeneralMetalLossThicknessProfile;

public class GeneralMetalLossThicknessProfile : BaseCalculator
{
  public bool Div1B41999;
  private double tmin1;
  private bool CheckPLessThanSE;
  private double checkTcMin;
  private double CheckTlMin;
  private bool CheckRtGreaterThanZero;
  private double WorkingTemperature;
  private string TemperatureLowerLimit;
  private MaterialPD5500DTO _material;
  private List<MaterialPD5500StressValueDTO> _allowableStrengths;
  private ThicknessReadings _thicknessReadings;

  public bool ToLevel2 { get; set; }

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double LongitudinalThicknessReadingSpacing { get; set; }

  public double CircumferentialThicknessReadingSpacing { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double S { get; set; }

  public double C { get; set; }

  public double AllowableStrength { get; set; }

  public double Tmin { get; set; }

  public double Tlmin { get; set; }

  public double Tmm { get; set; }

  public double Trd { get; set; }

  public double Rt { get; set; }

  public double Q { get; set; }

  public double L { get; set; }

  public double TSam { get; set; }

  public double TCam { get; set; }

  public double MAWPrTamMinusFCA { get; set; }

  public double MAWPrTamMinusFCAOverRSFa { get; set; }

  public bool AverageMeasuredThicknessL1 { get; set; }

  public bool MawpL1 { get; set; }

  public bool MinimumMeasuredThicknessL1 { get; set; }

  public bool Level1Passed { get; set; }

  public string Level1 { get; set; }

  public bool AverageMeasuredThicknessL2 { get; set; }

  public bool MawpL2 { get; set; }

  public bool MinimumMeasuredThicknessL2 { get; set; }

  public bool Level2Passed { get; set; }

  public string Level2 { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private double diameter { get; set; }

  private bool P1_2LessThan4S { get; set; }

  private double minTamsTamc { get; set; }

  private double minTamsTamcMinusFCA { get; set; }

  private bool ConditionP { get; set; }

  private bool RtGreaterThan0 { get; set; }

  private double MinTamsTamc { get; set; }

  private double MinTamsTamcMinusFCA { get; set; }

  private double MinTamsTamcMinusFCAOverRSFa { get; set; }

  private bool LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa { get; set; }

  private double TLower { get; set; }

  private double TUpper { get; set; }

  private double SLower { get; set; }

  private double SUpper { get; set; }

  private double Tc { get; set; }

  private double Radius { get; set; }

  private double RSFa { get; set; }

  private double TamCMinusFCA { get; set; }

  private double TamSMinusFCA { get; set; }

  private double Tlim { get; set; }

  private double TmmMinusFCA { get; set; }

  private double Max0_5TminTlim { get; set; }

  private bool LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin { get; set; }

  private bool LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin { get; set; }

  private double TamSMinusFCAOverRSFa { get; set; }

  private double TamCMinusTslMinusFCAOverRSFa { get; set; }

  private bool LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa { get; set; }

  public GeneralMetalLossThicknessProfile(
    MaterialPD5500DTO material,
    List<MaterialPD5500StressValueDTO> allowableStrengths,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
    this._thicknessReadings = thicknessReadingsCalculator;
  }

  public virtual void CalculateTrd()
  {
    this.Trd = this.NominalThickness - this.ExternalUniformMetalLoss - this.InternalUniformMetalLoss;
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.UserDefined)
    {
      this.AllowableStrength = Math.Min(this._material.YieldStrengthNew.Value / 1.5, this._material.TensileStrengthNew.Value / 2.35);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      MaterialStrengthLookupResult strengthResult = this.getStrengthResult(this.DesignTemperature);
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.AboveRange)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      if (strengthResult.SLower == 0.0 && strengthResult.SUpper == 0.0)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is lower than the minimum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      if (strengthResult.SUpper == 0.0 && strengthResult.TLower < this.DesignTemperature)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      this.TLower = strengthResult.TLower;
      this.TUpper = strengthResult.TUpper;
      this.SLower = strengthResult.SLower;
      this.SUpper = strengthResult.SUpper;
      this.AllowableStrength = this.DesignTemperature >= 50.0 ? (this.DesignTemperature - this.TLower) / (this.TUpper - this.TLower) * (this.SUpper - this.SLower) + this.SLower : this.SLower;
    }
    this.diameter = this.NominalInsideDiameter + 2.0 * this.InternalUniformMetalLoss;
    this.P1_2LessThan4S = 4.0 * this.AllowableStrength > 1.2 * this.DesignPressure;
    if (this.P1_2LessThan4S)
    {
      this.Tmin = this.DesignPressure * this.diameter / (4.0 * this.AllowableStrength - 1.2 * this.DesignPressure);
      this.Radius = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
      this.Tc = this.NominalThickness - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance - this.InternalFutureCorrosionAllowance - this.InternalUniformMetalLoss;
      this.Trd = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss;
      try
      {
        this.Tmm = this._thicknessReadings.GetMinimumValue();
        if (this.Tmm >= this.Trd)
        {
          this.ErrorMessage = "All thickness readings are greater than trd = tnom - LOSS. No metal loss has been found. This assessment is not needed";
          return CalculatorResult.Fail;
        }
      }
      catch
      {
        this.ErrorMessage = "Profile readings contain multiple Tmm's - Currently unable to proceed with the assessment.";
        return CalculatorResult.Fail;
      }
      this.RSFa = 0.9;
      this.Rt = (this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.Tc;
      this.Rt = Math.Round(this.Rt, 4);
      this.RtGreaterThan0 = this.Rt > 0.0;
      if (!this.RtGreaterThan0)
      {
        this.ErrorMessage = "Remaining thickness ratio is less than or equal to zero. The assessment fails. ";
        return CalculatorResult.Fail;
      }
      this.Q = this.Rt >= this.RSFa ? 50.0 : 1.123 * Math.Pow(Math.Pow((1.0 - this.Rt) / (1.0 - this.Rt / this.RSFa), 2.0) - 1.0, 0.5);
      this.L = this.Q * Math.Pow(2.0 * this.Radius * this.Tc, 0.5);
      this._thicknessReadings.ProcessThicknessReadings(this.L, this.CircumferentialThicknessReadingSpacing, this.LongitudinalThicknessReadingSpacing, this.Trd);
      this.S = this._thicknessReadings.S;
      this.C = this._thicknessReadings.C;
      this.TCam = this._thicknessReadings.Tcam;
      this.TSam = this._thicknessReadings.Tsam;
      this.minTamsTamc = Math.Min(this.TCam, this.TSam);
      this.minTamsTamcMinusFCA = this.minTamsTamc - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
      this.Tlim = Math.Max(0.2 * this.NominalThickness, 2.5);
      this.TmmMinusFCA = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
      this.Max0_5TminTlim = Math.Max(0.5 * this.Tmin, this.Tlim);
      this.MAWPrTamMinusFCA = 4.0 * this.AllowableStrength * this.minTamsTamcMinusFCA / (this.diameter + 1.2 * this.minTamsTamcMinusFCA);
      this.AverageMeasuredThicknessL1 = this.minTamsTamcMinusFCA > this.Tmin;
      this.MawpL1 = this.MAWPrTamMinusFCA >= this.DesignPressure;
      this.MinimumMeasuredThicknessL1 = this.TmmMinusFCA >= this.Max0_5TminTlim;
      if (this.AverageMeasuredThicknessL1 && this.MawpL1 && this.MinimumMeasuredThicknessL1)
      {
        this.Level1 = "Level 1 Assessment ACCEPTABLE";
        this.Level1Passed = true;
        return CalculatorResult.Completed;
      }
      this.Level1 = "Level 1 Assessment UNACCEPTABLE";
      this.Level1Passed = false;
      if (!this.ToLevel2)
        return CalculatorResult.Completed;
      this.MinimumMeasuredThicknessL2 = this.MinimumMeasuredThicknessL1;
      this.MinTamsTamcMinusFCAOverRSFa = this.minTamsTamcMinusFCA / this.RSFa;
      this.MAWPrTamMinusFCAOverRSFa = 4.0 * this.AllowableStrength * this.MinTamsTamcMinusFCAOverRSFa / (this.diameter + 1.2 * this.MinTamsTamcMinusFCAOverRSFa);
      this.AverageMeasuredThicknessL2 = this.MinTamsTamcMinusFCAOverRSFa >= this.Tmin;
      this.MawpL2 = this.MAWPrTamMinusFCAOverRSFa >= this.DesignPressure;
      if (this.AverageMeasuredThicknessL2 && this.MawpL2 && this.MinimumMeasuredThicknessL2)
      {
        this.Level2 = "Level 2 Assessment ACCEPTABLE";
        this.Level2Passed = true;
      }
      else
      {
        this.Level2 = "Level 2 Assessment UNACCEPTABLE";
        this.Level2Passed = false;
      }
      return CalculatorResult.Completed;
    }
    this.ErrorMessage = "Design pressure must be less than  times the allowable stress.";
    return CalculatorResult.Fail;
  }

  private MaterialStrengthLookupResult getStrengthResult(double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialPD5500StressValueDTO pd5500StressValueDto = this._allowableStrengths.Where<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 <= tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialPD5500StressValueDTO, double?>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialPD5500StressValueDTO>();
    if (pd5500StressValueDto == null)
    {
      num1 = this._allowableStrengths.Max<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        pd5500StressValueDto = new MaterialPD5500StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (pd5500StressValueDto == null)
    {
      num1 = this._allowableStrengths.Min<MaterialPD5500StressValueDTO>((Func<MaterialPD5500StressValueDTO, double?>) (x => x.TLower)).Value;
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      pd5500StressValueDto = new MaterialPD5500StressValueDTO()
      {
        TLower = new double?(0.0),
        TUpper = new double?(0.0),
        SLower = new double?(0.0),
        SUpper = new double?(0.0)
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = pd5500StressValueDto.TLower.Value,
      TUpper = pd5500StressValueDto.TUpper.Value,
      SLower = pd5500StressValueDto.SLower.Value,
      SUpper = pd5500StressValueDto.SUpper.Value
    };
  }
}
