// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.GeneralMetalLossThicknessReading.GeneralMetalLossThicknessReading
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.GeneralMetalLossThicknessReading;

public class GeneralMetalLossThicknessReading : BaseCalculator
{
  private MaterialAPI650DTO _material;
  private ThicknessReadings _thicknessReadingsCalculator;

  public bool ToLevel2 { get; set; }

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double NominalHeight { private get; set; }

  public double SpecificGravity { private get; set; }

  public double LongitudinalWeldJointEfficiency { get; set; }

  public double CircumferentialWeldJointEfficiency { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double AllowableStrength { get; set; }

  public double TMin { get; set; }

  public double Tmm { get; set; }

  public double Tam { get; set; }

  public double Cov { get; set; }

  public double Tlim { get; set; }

  public bool AverageMeasuredThicknessL1 { get; set; }

  public bool MinimumMeasuredThicknessL1 { get; set; }

  public bool MFHL1 { get; set; }

  public bool Level1Passed { get; set; }

  public string Level1ResultMsg { get; set; }

  public bool AverageMeasuredThicknessL2 { get; set; }

  public bool MinimumMeasuredThicknessL2 { get; set; }

  public bool MFHL2 { get; set; }

  public bool Level2Passed { get; set; }

  public string Level2ResultMsg { get; set; }

  private double Height { get; set; }

  private double Diameter { get; set; }

  private double MFHr_m { get; set; }

  private double Max0_6TminTlim { get; set; }

  private string checkCOV { get; set; }

  private string MinimumTemperatureC { get; set; }

  private double MinimumAllowableTemperature { get; set; }

  private string TemperatureLowLimit { get; set; }

  private double TLower { get; set; }

  private double TUpper { get; set; }

  private double SLower { get; set; }

  private double SUpper { get; set; }

  private double Tlower2 { get; set; }

  private double Tupper2 { get; set; }

  private double Ylower { get; set; }

  private double Yupper { get; set; }

  private double YB31ASMEB31 { get; set; }

  private double OutsideDiameterDo { get; set; }

  private double tc { get; set; }

  private double tcmin1 { get; set; }

  private double tlmin1 { get; set; }

  private double tmin1 { get; set; }

  private double checkYB31 { get; set; }

  private double tcmin { get; set; }

  public double Tlmin { get; set; }

  private double ThicknessReadingsN { get; set; }

  private double S { get; set; }

  private string CheckCOV { get; set; }

  private double tamMinusFCA { get; set; }

  private double tmmMinusFCA { get; set; }

  private double Max0_5MminTlim { get; set; }

  private double MAWPrcTamMinusFCA { get; set; }

  private double MAWPrlTamMinusFCA { get; set; }

  private double RSFa { get; set; }

  public double TamMinusFCAOverRSFa { get; set; }

  private double tamMinusTslMinusFCAOverRSFa { get; set; }

  private double MAWPrcTamMinusFCAOverRSFa { get; set; }

  private double MAWPrlTamMinusTslMinusFCAOverRSFa { get; set; }

  private double Max0_6MminTlim { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private string TemperatureUpLimit { get; set; }

  private string StrengthTemperatureLimit { get; set; }

  private double ThicknessLower { get; set; }

  private double ThicknessUpper { get; set; }

  private double TminLower { get; set; }

  private double TminUpper { get; set; }

  public GeneralMetalLossThicknessReading(
    MaterialAPI650DTO material,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this.Warnings = new List<string>();
    this._thicknessReadingsCalculator = thicknessReadingsCalculator;
  }

  public CalculatorResult CalculateAssessment()
  {
    try
    {
      if (this.DesignTemperature > 90.0)
      {
        this.ErrorMessage = "The temperature is higher than the maximum permissible operating temperature of tanks designed to API 650";
        return CalculatorResult.Fail;
      }
      if (this._material.UserDefined)
      {
        this.AllowableStrength = Math.Min(2.0 * this._material.YieldStrengthNew.Value / 3.0, 2.0 * this._material.TensileStrengthNew.Value / 5.0);
        this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
      }
      else
      {
        this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
        this.AllowableStrength = this._material.ProductDesignStressSMPa.Value;
      }
      this.Height = this.NominalHeight / 1000.0;
      this.Diameter = this.NominalInsideDiameter * 0.001;
      this.TMin = 4.9 * (this.Diameter * (this.Height - 0.3) * this.SpecificGravity) / this.AllowableStrength + this.InternalFutureCorrosionAllowance + this.ExternalFutureCorrosionAllowance;
      this.ThicknessReadingsN = (double) this._thicknessReadingsCalculator.TotalNumThicknessPoints();
      this.S = this._thicknessReadingsCalculator.SumOfThicknessReadings();
      this.Tam = this.S / this.ThicknessReadingsN;
      this.S = this._thicknessReadingsCalculator.SumOfThicknessReadingsPow2();
      this.Tmm = this._thicknessReadingsCalculator.MinimumThicknessPointValue();
      this.Cov = 1.0 / this.Tam * Math.Pow(this.S / (this.ThicknessReadingsN - 1.0), 0.5) * 100.0;
      if (this.Cov > 10.0)
      {
        this.checkCOV = "The Coefficient of Variation (COV) of the thickness readings is greater than 10%, the general metal loss procedure can not be used in this case. Then thickness profiles shall be considered for use in the assessment (see paragraph 4.3.3.3 API 579)";
        this.ErrorMessage = this.checkCOV;
        return CalculatorResult.Fail;
      }
      this.tamMinusFCA = this.Tam - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
      this.Tlim = Math.Max(0.2 * this.NominalThickness, 2.5);
      this.tmmMinusFCA = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
      this.Max0_6TminTlim = Math.Max(0.6 * this.TMin, this.Tlim);
      this.MFHr_m = this.tamMinusFCA * this.AllowableStrength / (4.9 * this.Diameter * this.SpecificGravity) + 0.3;
      this.RSFa = 0.9;
      this.AverageMeasuredThicknessL1 = this.tamMinusFCA >= this.TMin;
      this.MFHL1 = this.MFHr_m >= this.Height;
      this.MinimumMeasuredThicknessL1 = this.tmmMinusFCA >= this.Max0_6TminTlim;
      if (this.AverageMeasuredThicknessL1 && this.MFHL1 && this.MinimumMeasuredThicknessL1)
      {
        this.Level1Passed = true;
        this.Level1ResultMsg = "The Level 1 Assessment is ACCEPTABLE";
        return CalculatorResult.Completed;
      }
      this.Level1Passed = false;
      this.Level1ResultMsg = "The Level 1 Assessment is UNACCEPTABLE";
      if (!this.ToLevel2)
        return CalculatorResult.Completed;
      this.MinimumMeasuredThicknessL2 = this.MinimumMeasuredThicknessL1;
      this.AverageMeasuredThicknessL2 = this.tamMinusFCA >= this.TMin;
      this.MFHL2 = this.MFHr_m >= this.Height;
      if (this.MinimumMeasuredThicknessL2 && this.AverageMeasuredThicknessL2 && this.MFHL2)
      {
        this.Level2Passed = true;
        this.Level2ResultMsg = "The Level 2 Assessment is ACCEPTABLE";
      }
      else
      {
        this.Level2Passed = false;
        this.Level2ResultMsg = "The Level 2 Assessment is UNACCEPTABLE";
      }
      return CalculatorResult.Completed;
    }
    catch (Exception ex)
    {
      this.ErrorMessage = $"GeneralMetalLossP4PointThicknessReadings failed: {ex.Message}";
      return CalculatorResult.Fail;
    }
  }
}
