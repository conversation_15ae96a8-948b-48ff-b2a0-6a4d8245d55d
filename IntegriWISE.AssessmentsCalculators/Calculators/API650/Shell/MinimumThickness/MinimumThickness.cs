// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.MinimumThickness.MinimumThickness
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.MinimumThickness;

public class MinimumThickness : BaseCalculator
{
  private MaterialAPI650DTO _material;

  public double DesignTemperature { private get; set; }

  public double DesignPressure { private get; set; }

  public double? YieldStrengthNew { private get; set; }

  public double? TensileStrengthNew { private get; set; }

  public double? LongitudinalWeldJointEfficiency { private get; set; }

  public double? CircumferentialWeldJointEfficiency { private get; set; }

  public double? NominalThickness { private get; set; }

  public double NominalHeight { private get; set; }

  public double NominalInsideDiameter { private get; set; }

  public double SpecificGravity { private get; set; }

  public double ExternalFutureCorrosionAllowance { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public double InternalFutureCorrosionAllowance { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public double AllowableStrength { get; private set; }

  public double TMin { get; private set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public MinimumThickness(MaterialAPI650DTO material)
  {
    this._material = material;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.UserDefined)
    {
      this.AllowableStrength = Math.Min(2.0 * this.YieldStrengthNew.Value / 3.0, 2.0 * this.TensileStrengthNew.Value / 5.0);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      this.AllowableStrength = Convert.ToDouble((object) this._material.ProductDesignStressSMPa);
    }
    this.TMin = 4.9 * (this.NominalInsideDiameter * 0.001 * (this.NominalHeight / 1000.0 - 0.3) * this.SpecificGravity) / this.AllowableStrength + this.InternalFutureCorrosionAllowance + this.ExternalFutureCorrosionAllowance;
    return CalculatorResult.Completed;
  }
}
