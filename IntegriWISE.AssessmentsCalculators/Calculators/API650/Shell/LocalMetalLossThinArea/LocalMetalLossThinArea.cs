// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.LocalMetalLossThinArea.LocalMetalLossThinArea
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.LocalMetalLossThinArea;

public class LocalMetalLossThinArea : BaseCalculator
{
  private bool RtGreater0;
  private IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.MFH.MFH _calculatorMFH;
  private ThicknessReadings _thicknessReadings;
  private MaterialAPI650DTO _material;
  private List<MaterialTSFCurveDTO> _api579Table5_4List;

  public bool ToLevel2 { get; set; }

  public bool MFH { get; set; }

  public double DesignTemperature { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double SpecificGravityFluid { get; set; }

  public double HighFlaw { get; set; }

  public double Lmsd { get; set; }

  public double LongitudinalThicknessReadingSpacingLc { get; set; }

  public double CircumferentialThicknessReadingSpacingLm { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double LongitudinalMetalLossExtentS { get; set; }

  public double CircumferentiallMetalLossExtentC { get; set; }

  public double AllowableStrength { get; set; }

  public double Tmm { get; set; }

  public double Trd { get; set; }

  public double Tc { get; set; }

  public double Lambda { get; set; }

  public double RSF { get; set; }

  public bool RtGreater0_2 { get; set; }

  public bool TmmMinusFCAGreater2_5 { get; set; }

  public bool LmsdGreater1_8DtcPower0_5 { get; set; }

  public bool ScreeningCriteriaFigure5_6 { get; set; }

  public bool RSFGreaterRSFa { get; set; }

  public bool Level1Passed { get; set; }

  public string ConditionLongitudinalExtentL1 { get; set; }

  public string Level1AssessmentConclusion { get; set; }

  public bool min_RSFiGreaterThanOrEqualToRSFa { get; set; }

  public double minRSFi { get; set; }

  public bool Level2Passed { get; set; }

  public string ConditionLongitudinalExtentL2 { get; set; }

  public string Level2AssessmentConclusion { get; set; }

  public double MFHValue { get; set; }

  public int iConditionLongitudinalExtentLevel1 { get; set; }

  public double MFHrL1 { get; set; }

  public int iConditionLongitudinalExtentLevel2 { get; set; }

  public double MFHrL2 { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private double Radius { get; set; }

  private double RSFa { get; set; }

  private double Rt { get; set; }

  private double LambdaTable5_2 { get; set; }

  private double MtTable5_2 { get; set; }

  private double RtFigure5_7 { get; set; }

  public LocalMetalLossThinArea(
    MaterialAPI650DTO material,
    List<MaterialTSFCurveDTO> api579Table5_4List,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this._api579Table5_4List = api579Table5_4List;
    this._thicknessReadings = thicknessReadingsCalculator;
    this.Warnings = new List<string>();
  }

  public virtual void CalculateTrd()
  {
    this.Trd = this.NominalThickness - this.ExternalUniformMetalLoss - this.InternalUniformMetalLoss;
  }

  public virtual CalculatorResult CalculateMFH()
  {
    this._calculatorMFH = new IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.MFH.MFH(this._material);
    this._calculatorMFH.NominalInsideDiameter = this.NominalInsideDiameter;
    this._calculatorMFH.NominalThickness = this.NominalThickness;
    this._calculatorMFH.SpecificGravityFluid = this.SpecificGravityFluid;
    this._calculatorMFH.ExternalFutureCorrosionAllowance = this.ExternalFutureCorrosionAllowance;
    this._calculatorMFH.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    this._calculatorMFH.InternalFutureCorrosionAllowance = this.InternalFutureCorrosionAllowance;
    this._calculatorMFH.InternalUniformMetalLoss = this.InternalUniformMetalLoss;
    this._calculatorMFH.YieldStrengthNew = this._material.YieldStrengthNew;
    this._calculatorMFH.TensileStrengthNew = this._material.TensileStrengthNew;
    return this._calculatorMFH.CalculateAssessment();
  }

  public CalculatorResult CalculateAssessment()
  {
    CalculatorResult calculatorResult = CalculatorResult.Fail;
    if (this.MFH)
    {
      calculatorResult = this.CalculateMFH();
      if (calculatorResult == CalculatorResult.Fail)
      {
        this.ErrorMessage = this._calculatorMFH.ErrorMessage;
        return CalculatorResult.Fail;
      }
      this.MFHValue = this._calculatorMFH.MFHValue;
      this.AllowableStrength = this._calculatorMFH.AllowableStrength;
      this.Warnings.AddRange((IEnumerable<string>) this._calculatorMFH.Warnings);
    }
    this.Radius = this.NominalInsideDiameter / 2.0 + this.InternalUniformMetalLoss + this.InternalFutureCorrosionAllowance;
    this.Tc = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.CalculateTrd();
    try
    {
      this.Tmm = this._thicknessReadings.GetMinimumValue();
      if (this.Tmm >= this.Trd)
      {
        this.ErrorMessage = "All thickness readings are greater than trd = tnom - LOSS. No metal loss has been found. This assessment is not needed";
        return CalculatorResult.Fail;
      }
    }
    catch
    {
      this.ErrorMessage = "Profile readings contain multiple Tmm's - Currently unable to proceed with the assessment.";
      return CalculatorResult.Fail;
    }
    this._thicknessReadings.ProcessP5ThicknessReadings(this.CircumferentialThicknessReadingSpacingLm, this.LongitudinalThicknessReadingSpacingLc, Math.Round(this.Trd, 4));
    this.LongitudinalMetalLossExtentS = this._thicknessReadings.S;
    this.CircumferentiallMetalLossExtentC = this._thicknessReadings.C;
    this.RSFa = 0.9;
    this.Rt = (this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.Tc;
    this.Rt = Math.Round(this.Rt, 4);
    this.RtGreater0 = this.Rt > 0.0;
    if (!this.RtGreater0)
    {
      this.ErrorMessage = "Rt must be greater than zero - unable to proceed with the assessment.";
      return CalculatorResult.Fail;
    }
    this.Lambda = 1.285 * this.LongitudinalMetalLossExtentS / Math.Pow(2.0 * this.Radius * this.Tc, 0.5);
    this.RtGreater0_2 = this.Rt > 0.2;
    this.TmmMinusFCAGreater2_5 = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance >= 2.5;
    this.LmsdGreater1_8DtcPower0_5 = this.Lmsd > 1.8 * Math.Pow(this.Radius * 2.0 * this.Tc, 0.5);
    this.LambdaTable5_2 = this.Lambda <= 20.0 ? this.Lambda : 20.0;
    this.MtTable5_2 = (1.0005 + 0.49001 * this.LambdaTable5_2 + 0.32409 * Math.Pow(this.LambdaTable5_2, 2.0)) / (1.0 + 0.50144 * this.LambdaTable5_2 - 0.011067 * Math.Pow(this.LambdaTable5_2, 2.0));
    this.RtFigure5_7 = this.Lambda > 0.33 ? (this.Lambda >= 20.0 ? 0.9 : (this.RSFa - this.RSFa / this.MtTable5_2) * Math.Pow(1.0 - this.RSFa / this.MtTable5_2, -1.0)) : 0.2;
    this.ScreeningCriteriaFigure5_6 = this.Rt > this.RtFigure5_7;
    this.RSF = this.Rt / (1.0 - 1.0 / this.MtTable5_2 * (1.0 - this.Rt));
    this.RSFGreaterRSFa = this.RSF >= this.RSFa;
    this.iConditionLongitudinalExtentLevel1 = 0;
    if (this.RtGreater0_2 && this.TmmMinusFCAGreater2_5 && this.LmsdGreater1_8DtcPower0_5)
    {
      if (this.ScreeningCriteriaFigure5_6 || this.RSFGreaterRSFa)
      {
        this.ConditionLongitudinalExtentL1 = "The extent of the flaw is ACCEPTABLE for operation at the MFH.";
        this.iConditionLongitudinalExtentLevel1 = 1;
        this.Level1AssessmentConclusion = "The Level 1 Assessment is ACCEPTABLE";
        this.Level1Passed = true;
        return CalculatorResult.Completed;
      }
      this.ConditionLongitudinalExtentL1 = "The extent of the flaw is UNACCEPTABLE for operation at MFH but it is acceptable for operation at MFHr";
      this.iConditionLongitudinalExtentLevel1 = 2;
      this.Level1AssessmentConclusion = "The Level 1 Assessment is UNACCEPTABLE. The component is unacceptable for operation at MFH but it is ACCEPTABLE for operation at MFHr";
      this.Level1Passed = false;
    }
    else
    {
      this.ConditionLongitudinalExtentL1 = "The extent of the flaw is UNACCEPTABLE";
      this.iConditionLongitudinalExtentLevel1 = 3;
      this.Level1AssessmentConclusion = "The Level 1 Assessment is UNACCEPTABLE";
      this.Level1Passed = false;
    }
    if (this.MFH && calculatorResult == CalculatorResult.Completed && this.iConditionLongitudinalExtentLevel1 == 2 && !this.RSFGreaterRSFa)
    {
      if (this.HighFlaw >= this.MFHValue)
      {
        this.Level1AssessmentConclusion = "";
        this.Level1Passed = false;
        this.ErrorMessage = "Distance between the bottom of the flaw and the tank bottom (Hf) should be less than the calculated Maximum Fill Height ";
        return CalculatorResult.Fail;
      }
      this.MFHrL1 = this.HighFlaw + (this.MFHValue - this.HighFlaw) * this.RSF / this.RSFa;
    }
    if (!this.ToLevel2)
      return CalculatorResult.Completed;
    this._thicknessReadings.ProcessP5Level2(this.Trd, this.LongitudinalThicknessReadingSpacingLc, this.NominalInsideDiameter, this.Tc);
    this.minRSFi = this._thicknessReadings.GetLevel2RSF();
    this.min_RSFiGreaterThanOrEqualToRSFa = this.minRSFi >= this.RSFa;
    this.iConditionLongitudinalExtentLevel2 = 0;
    if (this.RtGreater0_2 && this.TmmMinusFCAGreater2_5 && this.LmsdGreater1_8DtcPower0_5)
    {
      if (this.min_RSFiGreaterThanOrEqualToRSFa)
      {
        this.ConditionLongitudinalExtentL2 = "The extent of the flaw is ACCEPTABLE for operation at the MFH";
        this.iConditionLongitudinalExtentLevel2 = 1;
        this.Level2AssessmentConclusion = "The Level 2 Assessment is ACCEPTABLE.";
        this.Level2Passed = true;
      }
      else
      {
        this.ConditionLongitudinalExtentL2 = "The extent of the flaw is UNACCEPTABLE for operation at MFH but it is acceptable for operation at MFHr";
        this.Level2AssessmentConclusion = "The Level 2 Assessment is UNACCEPTABLE.The component is unacceptable for operation at MFH but it is ACCEPTABLE for operation at MFHr";
        this.iConditionLongitudinalExtentLevel2 = 2;
        this.Level2Passed = false;
      }
    }
    else
    {
      this.ConditionLongitudinalExtentL2 = "The extent of the flaw is UNACCEPTABLE";
      this.iConditionLongitudinalExtentLevel2 = 3;
      this.Level2AssessmentConclusion = "The Level 2 Assessment is UNACCEPTABLE";
      this.Level2Passed = false;
    }
    if (this.MFH && calculatorResult == CalculatorResult.Completed && this.iConditionLongitudinalExtentLevel2 == 2 && !this.min_RSFiGreaterThanOrEqualToRSFa)
      this.MFHrL2 = this.HighFlaw + (this.MFHValue - this.HighFlaw) * this.minRSFi / this.RSFa;
    return CalculatorResult.Completed;
  }
}
