// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.GeneralMetalLossThicknessProfile.GeneralMetalLossThicknessProfile
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.GeneralMetalLossThicknessProfile;

public class GeneralMetalLossThicknessProfile : BaseCalculator
{
  private MaterialAPI650DTO _material;
  private ThicknessReadings _thicknessReadings;

  public bool ToLevel2 { get; set; }

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double NominalHeight { private get; set; }

  public double SpecificGravity { private get; set; }

  public double LongitudinalWeldJointEfficiency { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double LongitudinalThicknessReadingSpacing { get; set; }

  public double S { get; set; }

  public double AllowableStrength { get; set; }

  public double TMin { get; set; }

  public double Tmm { get; set; }

  public double Trd { get; set; }

  public double Rt { get; set; }

  public double Q { get; set; }

  public double L { get; set; }

  public double TSam { get; set; }

  public double MFHr { get; set; }

  public bool AverageMeasuredThicknessL1 { get; set; }

  public bool MFHL1 { get; set; }

  public bool MinimumMeasuredThicknessL1 { get; set; }

  public bool Level1Passed { get; set; }

  public string Level1 { get; set; }

  public bool AverageMeasuredThicknessL2 { get; set; }

  public bool MFHL2 { get; set; }

  public bool MinimumMeasuredThicknessL2 { get; set; }

  public bool Level2Passed { get; set; }

  public string Level2 { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private double Height { get; set; }

  private double Diameter { get; set; }

  private double tc { get; set; }

  private double RSFa { get; set; }

  private double tlim { get; set; }

  private double tmmMinusFCA { get; set; }

  private bool RtGreater0 { get; set; }

  private double diameter { get; set; }

  private double Max0_6MminTlim { get; set; }

  private bool RtGreaterThan0 { get; set; }

  private double tsamMinusFCA { get; set; }

  private double Max0_6TminTlim { get; set; }

  public GeneralMetalLossThicknessProfile(
    MaterialAPI650DTO material,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this.Warnings = new List<string>();
    this._thicknessReadings = thicknessReadingsCalculator;
  }

  public virtual void CalculateTrd()
  {
    this.Trd = this.NominalThickness - this.ExternalUniformMetalLoss - this.InternalUniformMetalLoss;
  }

  public CalculatorResult CalculateAssessment()
  {
    try
    {
      if (this.DesignTemperature > 90.0)
      {
        this.ErrorMessage = "The temperature is higher than the maximum permissible operating temperature of tanks designed to API 650";
        return CalculatorResult.Fail;
      }
      if (this._material.UserDefined)
      {
        this.AllowableStrength = Math.Min(2.0 * this._material.YieldStrengthNew.Value / 3.0, 2.0 * this._material.TensileStrengthNew.Value / 5.0);
        this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
      }
      else
      {
        this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
        this.AllowableStrength = this._material.ProductDesignStressSMPa.Value;
      }
      this.Height = this.NominalHeight / 1000.0;
      this.Diameter = this.NominalInsideDiameter * 0.001;
      this.TMin = 4.9 * (this.Diameter * (this.Height - 0.3) * this.SpecificGravity) / this.AllowableStrength + this.InternalFutureCorrosionAllowance + this.ExternalFutureCorrosionAllowance;
      this.Trd = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss;
      this.tc = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance - this.InternalFutureCorrosionAllowance;
      this.RSFa = 0.9;
      try
      {
        this.Tmm = this._thicknessReadings.GetMinimumValue();
        if (this.Tmm >= this.Trd)
        {
          this.ErrorMessage = "All thickness readings are greater than trd = tnom - LOSS. No metal loss has been found. This assessment is not needed";
          return CalculatorResult.Fail;
        }
      }
      catch
      {
        this.ErrorMessage = "Profile readings contain multiple Tmm's - Currently unable to proceed with the assessment.";
        return CalculatorResult.Fail;
      }
      this.Rt = (this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.tc;
      this.Rt = Math.Round(this.Rt, 4);
      this.RtGreater0 = this.Rt > 0.0;
      if (!this.RtGreater0)
      {
        this.ErrorMessage = "Rt must be greater than zero - unable to proceed with the assessment.";
        return CalculatorResult.Fail;
      }
      this.Q = this.Rt >= this.RSFa ? 50.0 : 1.123 * Math.Pow(Math.Pow((1.0 - this.Rt) / (1.0 - this.Rt / this.RSFa), 2.0) - 1.0, 0.5);
      this.L = this.Q * Math.Pow(this.Diameter * 1000.0 * this.tc, 0.5);
      this._thicknessReadings.ProcessThicknessReadings(this.L, this.LongitudinalThicknessReadingSpacing, this.LongitudinalThicknessReadingSpacing, this.Trd);
      this.S = this._thicknessReadings.S;
      this.TSam = this._thicknessReadings.Tsam;
      this.tsamMinusFCA = this.TSam - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
      this.tlim = Math.Max(0.2 * this.NominalThickness, 2.5);
      this.tmmMinusFCA = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
      this.Max0_6TminTlim = Math.Max(0.6 * this.TMin, this.tlim);
      this.MFHr = this.tsamMinusFCA * this.AllowableStrength / (4.9 * this.Diameter * this.SpecificGravity) + 0.3;
      this.MFHr *= 1000.0;
      this.AverageMeasuredThicknessL1 = this.tsamMinusFCA >= this.TMin;
      this.MFHL1 = this.MFHr > this.Height;
      this.MinimumMeasuredThicknessL1 = this.tmmMinusFCA >= this.Max0_6TminTlim;
      if (this.AverageMeasuredThicknessL1 && this.MFHL1 && this.MinimumMeasuredThicknessL1)
      {
        this.Level1 = "The Level 1 Assessment is ACCEPTABLE";
        this.Level1Passed = true;
        return CalculatorResult.Completed;
      }
      this.Level1 = "The Level 1 Assessment is UNACCEPTABLE";
      this.Level1Passed = false;
      if (!this.ToLevel2)
        return CalculatorResult.Completed;
      this.AverageMeasuredThicknessL2 = this.tsamMinusFCA >= this.TMin;
      this.MFHL2 = this.MFHr > this.Height;
      this.MinimumMeasuredThicknessL2 = this.MinimumMeasuredThicknessL1;
      if (this.AverageMeasuredThicknessL2 && this.MFHL2 && this.MinimumMeasuredThicknessL2)
      {
        this.Level2 = "The Level 2 Assessment is ACCEPTABLE";
        this.Level2Passed = true;
      }
      else
      {
        this.Level2 = "The Level 2 Assessment is UNACCEPTABLE";
        this.Level2Passed = false;
      }
      return CalculatorResult.Completed;
    }
    catch (Exception ex)
    {
      this.ErrorMessage = $"GeneralMetalLossP4ThicknessProfiles failed: {ex.Message}";
      return CalculatorResult.Fail;
    }
  }
}
