// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.MFH.MFH
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.MFH;

public class MFH : BaseCalculator
{
  private MaterialAPI650DTO _allowableStress;

  public double DesignTemperature { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double SpecificGravityFluid { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double? YieldStrengthNew { get; set; }

  public double? TensileStrengthNew { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double MFHValue { get; private set; }

  public double AllowableStrength { get; private set; }

  public MFH()
  {
  }

  public MFH(MaterialAPI650DTO allowableStress)
  {
    this._allowableStress = allowableStress;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._allowableStress.UserDefined)
    {
      double? yieldStrengthNew = this.YieldStrengthNew;
      double? nullable1 = yieldStrengthNew.HasValue ? new double?(2.0 * yieldStrengthNew.GetValueOrDefault()) : new double?();
      double val1 = (nullable1.HasValue ? new double?(nullable1.GetValueOrDefault() / 3.0) : new double?()).Value;
      double? tensileStrengthNew = this.TensileStrengthNew;
      double? nullable2 = tensileStrengthNew.HasValue ? new double?(2.0 * tensileStrengthNew.GetValueOrDefault()) : new double?();
      double val2 = (nullable2.HasValue ? new double?(nullable2.GetValueOrDefault() / 5.0) : new double?()).Value;
      this.AllowableStrength = Math.Min(val1, val2);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      this.AllowableStrength = this._allowableStress.ProductDesignStressSMPa.Value;
    }
    this.MFHValue = (this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss) * this.AllowableStrength / (4.9 * (this.NominalInsideDiameter * 0.001) * this.SpecificGravityFluid) + 0.3;
    this.MFHValue *= 1000.0;
    return CalculatorResult.Completed;
  }
}
