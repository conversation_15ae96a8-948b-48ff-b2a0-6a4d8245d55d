// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.Blister.BlisterLevel2
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.AssessmentData;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.Blister;

public class BlisterLevel2 : BaseCalculator, IBlisterCalculator
{
  private List<MaterialTSFCurveDTO> _api579Table5_4List;

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double CircumferentialBlisterDimension { get; set; }

  public double LongitudinalBlisterDimension { private get; set; }

  public double BlisterSpacing { get; set; }

  public double BlisterBulgeProjection { get; set; }

  public double TMMBlister { private get; set; }

  public VentType BlisterCrownCrackingAndVentHoles { get; set; }

  public double SC { get; set; }

  public double LW { get; set; }

  public double LMSD { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  private double InsideDiameterD { get; set; }

  public double tc { get; set; }

  public double BdDiameter { get; set; }

  private double TMMBlisterModifiedByFCA { get; set; }

  private string ConditionBlistersOverHICProximity { get; set; }

  public bool BdLessThan50 { get; set; }

  public bool BlisterVented { get; set; }

  public bool ConditionS { get; set; }

  public bool ConditionC { get; set; }

  public bool MinimumMeasuredUndamagedThickness { get; set; }

  public bool BlisterProjection { get; private set; }

  public bool DistanceToWeldSeam { get; set; }

  public bool ConditionDistanceMajorDiscontinuity { get; set; }

  public string Level1Conclusion { get; set; }

  public bool Level1Passed { get; set; }

  public double LongitudinalWeldJointEfficiency { private get; set; }

  public double CircumferentialWeldJointEfficiency { private get; set; }

  public double HighFlaw { get; set; }

  public bool IsMFH { private get; set; }

  public double MFH { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public bool DistanceToWeldSeamLevel2 { get; private set; }

  public string LongitudinalExtent { get; private set; }

  public double RSF { get; private set; }

  public double DepthOfHicDamage { get; private set; }

  public double MFHr { get; private set; }

  public double rt { get; private set; }

  public bool RSFGreaterRSFa { get; private set; }

  public bool Level2Passed { get; private set; }

  public string FinalL2Conclusion { get; private set; }

  public double LambdaC { get; private set; }

  public string CircumferentialExtent { get; private set; }

  public bool RtGreater0_2 { get; private set; }

  public bool TmmMinusFCAGreater2_5 { get; private set; }

  public bool? LambdaCLessThan9 { get; private set; }

  public bool? DOverTcGreaterThan20 { get; private set; }

  public bool? RSFBetween0_7And1 { get; private set; }

  public bool? ElBetween0_7And1 { get; private set; }

  public bool? EcBetween0_7And2 { get; private set; }

  public bool? ScreeningCriteriaFigure5_8 { get; private set; }

  public double? TSF { get; private set; }

  public bool CrownCrackPart5 { set; get; }

  public BlisterLevel2(List<MaterialTSFCurveDTO> api579Table5_4List)
  {
    this._api579Table5_4List = api579Table5_4List;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.Blister.Blister blister = new IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.Blister.Blister();
    blister.NominalInsideDiameter = this.NominalInsideDiameter;
    blister.NominalThickness = this.NominalThickness;
    blister.ExternalFutureCorrosionAllowance = this.ExternalFutureCorrosionAllowance;
    blister.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    blister.InternalFutureCorrosionAllowance = this.InternalFutureCorrosionAllowance;
    blister.InternalUniformMetalLoss = this.InternalUniformMetalLoss;
    blister.CircumferentialBlisterDimension = this.CircumferentialBlisterDimension;
    blister.LongitudinalBlisterDimension = this.LongitudinalBlisterDimension;
    blister.BlisterSpacing = this.BlisterSpacing;
    blister.BlisterBulgeProjection = this.BlisterBulgeProjection;
    blister.TMMBlister = this.TMMBlister;
    blister.BlisterCrownCrackingAndVentHoles = this.BlisterCrownCrackingAndVentHoles;
    blister.SC = this.SC;
    blister.LW = this.LW;
    blister.LMSD = this.LMSD;
    CalculatorResult assessment = blister.CalculateAssessment();
    this.ErrorMessage = blister.ErrorMessage;
    this.Warnings.AddRange((IEnumerable<string>) blister.Warnings);
    this.tc = blister.tc;
    this.BdDiameter = blister.BdDiameter;
    this.ConditionS = blister.ConditionS;
    this.ConditionC = blister.ConditionC;
    this.BdLessThan50 = blister.BdLessThan50;
    this.MinimumMeasuredUndamagedThickness = blister.MinimumMeasuredUndamagedThickness;
    this.BlisterProjection = blister.BlisterProjection;
    this.DistanceToWeldSeam = blister.DistanceToWeldSeam;
    this.ConditionDistanceMajorDiscontinuity = blister.ConditionDistanceMajorDiscontinuity;
    this.Level1Passed = blister.Level1Passed;
    this.Level1Conclusion = blister.Level1Conclusion;
    if (assessment == CalculatorResult.Fail)
      return assessment;
    if (this.Level1Passed)
      return CalculatorResult.Completed;
    this.DistanceToWeldSeamLevel2 = this.BlisterCrownCrackingAndVentHoles == VentType.No || this.DistanceToWeldSeam;
    this.CrownCrackPart5 = this.BlisterCrownCrackingAndVentHoles == VentType.Crown || !this.BlisterProjection;
    if (this.CrownCrackPart5)
    {
      this.Warnings.Add("The blister should be evaluated as an equivalent local thin area using the methods in part 5. API 579.");
      double num1 = this.NominalInsideDiameter / 2.0 - this.ExternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss;
      double num2 = 1.285 * this.LongitudinalBlisterDimension / Math.Pow(2.0 * num1 * this.tc, 0.5);
      double x = num2 > 20.0 ? 20.0 : num2;
      double num3 = 1001.0 / 1000.0 - 0.014195 * x + 0.2909 * Math.Pow(x, 2.0) - 0.09642 * Math.Pow(x, 3.0) + 0.02089 * Math.Pow(x, 4.0) - 0.003054 * Math.Pow(x, 5.0) + 2.957 * Math.Pow(10.0, -4.0) * Math.Pow(x, 6.0) - 1.8462 * Math.Pow(10.0, -5.0) * Math.Pow(x, 7.0) + 7.1553 * Math.Pow(10.0, -7.0) * Math.Pow(x, 8.0) - 1.5631 * Math.Pow(10.0, -8.0) * Math.Pow(x, 9.0) + 916.0 / 625.0 * Math.Pow(10.0, -10.0) * Math.Pow(x, 10.0);
      this.rt = (this.TMMBlister - (this.InternalFutureCorrosionAllowance + this.ExternalFutureCorrosionAllowance)) / this.tc;
      this.RSF = this.rt / (1.0 - 1.0 / num3 * (1.0 - this.rt));
      this.RtGreater0_2 = this.rt > 0.2;
      this.TmmMinusFCAGreater2_5 = this.TMMBlister - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance > 2.5;
      this.RSFGreaterRSFa = this.RSF >= 0.9;
      this.LambdaC = 1.285 * this.CircumferentialBlisterDimension / Math.Pow(num1 * 2.0 * this.tc, 0.5);
      this.LambdaCLessThan9 = new bool?(this.LambdaC <= 9.0);
      this.DOverTcGreaterThan20 = new bool?(num1 * 2.0 / this.tc >= 20.0);
      this.RSFBetween0_7And1 = new bool?(this.RSF >= 0.7 && this.RSF <= 1.0);
      this.ElBetween0_7And1 = new bool?(this.LongitudinalWeldJointEfficiency >= 0.7 && this.LongitudinalWeldJointEfficiency <= 1.0);
      this.EcBetween0_7And2 = new bool?(this.CircumferentialWeldJointEfficiency >= 0.7 && this.CircumferentialWeldJointEfficiency <= 1.0);
      bool flag = this.LambdaCLessThan9.Value && this.DOverTcGreaterThan20.Value && this.RSFBetween0_7And1.Value && this.ElBetween0_7And1.Value && this.EcBetween0_7And2.Value;
      if (this.TMMBlister <= 0.0)
      {
        this.ErrorMessage = "Minimum equivalent thickness (tmm) must be greater than 0, cannot do level 2 assessment";
        return CalculatorResult.Fail;
      }
      if (this.rt <= 0.0)
      {
        this.ErrorMessage = "Remaining ration (rt) must be greater than 0, cannot do level 2 assessment";
        return CalculatorResult.Fail;
      }
      if (!flag)
      {
        this.CircumferentialExtent = "The Circumferential extent of the flaw is UNACCEPTABLE";
        this.Level2Passed = false;
        this.FinalL2Conclusion = "Level 2 Assessment UNACCEPTABLE";
      }
      else
      {
        this.TSF = new double?(this.CircumferentialWeldJointEfficiency / (2.0 * this.RSF) * (1.0 + Math.Pow(4.0 - 3.0 * Math.Pow(this.LongitudinalWeldJointEfficiency, 2.0), 0.5) / this.LongitudinalWeldJointEfficiency));
        double RtFigure5_8;
        string errorMessage;
        if (!this.GetRtFigure5_8(this._api579Table5_4List, this.TSF.Value, this.LambdaC, out RtFigure5_8, out errorMessage))
        {
          this.ErrorMessage = errorMessage;
          return CalculatorResult.Fail;
        }
        this.ScreeningCriteriaFigure5_8 = new bool?(this.rt >= RtFigure5_8);
      }
      BlisterLevel2.PassType passType1;
      if (this.ConditionDistanceMajorDiscontinuity && this.RtGreater0_2 && this.TmmMinusFCAGreater2_5)
      {
        if (this.RSFGreaterRSFa)
        {
          this.LongitudinalExtent = "The longitudinal extent of the blister damage is ACCEPTABLE for operation at MFH";
          passType1 = BlisterLevel2.PassType.pass;
        }
        else
        {
          this.LongitudinalExtent = "The longitudinal extent of the blister damage is UNACCEPTABLE for operation at MFH, but is acceptable for operation at the MFHr";
          passType1 = BlisterLevel2.PassType.failWithWarning;
          if (this.IsMFH)
            this.MFHr = this.HighFlaw + (this.MFH - this.HighFlaw) * this.RSF / 0.9;
        }
      }
      else
      {
        this.LongitudinalExtent = "The longitudinal extent of the blister damage is UNACCEPTABLE.";
        passType1 = BlisterLevel2.PassType.fail;
      }
      BlisterLevel2.PassType passType2;
      if (flag && this.ScreeningCriteriaFigure5_8.Value)
      {
        this.CircumferentialExtent = "The circumferential extent of the blister damage is ACCEPTABLE.";
        passType2 = BlisterLevel2.PassType.pass;
      }
      else
      {
        this.CircumferentialExtent = "The circumferential extent of the blister damage is UNACCEPTABLE.";
        passType2 = BlisterLevel2.PassType.fail;
      }
      if (this.ConditionDistanceMajorDiscontinuity && this.DistanceToWeldSeamLevel2)
      {
        this.Level2Passed = passType1 == BlisterLevel2.PassType.pass && passType2 == BlisterLevel2.PassType.pass;
        this.FinalL2Conclusion = !this.Level2Passed ? "Level 2 Assessment is UNACCEPTABLE" : "Level 2 Assessment is ACCEPTABLE";
        if (passType2 == BlisterLevel2.PassType.pass && passType1 == BlisterLevel2.PassType.failWithWarning)
          this.FinalL2Conclusion = "Level 2 Assessment is unacceptable for operation at MFH but the component can operate at MFHr";
      }
      else
        this.FinalL2Conclusion = "Level 2 Assessment is UNACCEPTABLE";
    }
    else if (this.ConditionDistanceMajorDiscontinuity && this.DistanceToWeldSeamLevel2)
    {
      this.FinalL2Conclusion = "Level 2 Assessment is ACCEPTABLE";
      this.Level2Passed = true;
    }
    else
    {
      this.FinalL2Conclusion = "Level 2 Assessment is UNACCEPTABLE";
      this.Level2Passed = false;
    }
    if (this.FinalL2Conclusion == "Level 2 Assessment is ACCEPTABLE")
      this.Warnings.Add("Monitoring blister growth while the component is in service is required.");
    return CalculatorResult.Completed;
  }

  private enum PassType
  {
    pass,
    fail,
    failWithWarning,
  }
}
