// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.BrittleFracture.BrittleFractureLevel2
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.BrittleFracture;

public class BrittleFractureLevel2 : BaseCalculator, IBrittleFractureCalculator
{
  public string Material { private get; set; }

  public double DesignTemperature { private get; set; }

  public double NominalInsideDiameter { private get; set; }

  public double NominalHeight { private get; set; }

  public double SpecificGravity { private get; set; }

  public bool HasTankBeenOperatedAtLODMT { get; set; }

  public double InternalFutureCorrosionAllowance { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public double ExternalFutureCorrosionAllowance { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public double TG { private get; set; }

  public bool ImpactTestResultAvailable { get; set; }

  public double? MaxImpactTestTemperature { private get; set; }

  public double? CET { private get; set; }

  public bool Level1Passed { get; private set; }

  public double Level1MAT { get; private set; }

  public string Level1Message { get; private set; }

  public string Level1FormattedMessage { get; private set; }

  public bool? CETGreaterThanMAT { get; private set; }

  public bool HydroTestPerformed { get; set; }

  public bool Level2Passed { get; private set; }

  public double Level2MAT { get; private set; }

  public string Level2Message { get; private set; }

  public string Level2FormattedMessage { get; private set; }

  public bool TGLessOrEqual12_7 { get; private set; }

  public bool TemperatureGreaterOrEqual16 { get; private set; }

  public bool MembraneStressLessOrEqual55_2 { get; private set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public BrittleFractureLevel2() => this.Warnings = new List<string>();

  public CalculatorResult CalculateAssessment()
  {
    IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.BrittleFracture.BrittleFracture brittleFracture = new IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.BrittleFracture.BrittleFracture();
    brittleFracture.TG = this.TG;
    brittleFracture.CET = this.CET;
    brittleFracture.Material = this.Material;
    CalculatorResult assessment = brittleFracture.CalculateAssessment();
    this.CETGreaterThanMAT = brittleFracture.CETGreaterThanMAT;
    this.Level1MAT = brittleFracture.Level1MAT;
    this.Level1FormattedMessage = brittleFracture.Level1FormattedMessage;
    this.Level1Message = brittleFracture.Level1Message;
    this.Level1Passed = brittleFracture.Level1Passed;
    this.ErrorMessage = brittleFracture.ErrorMessage;
    this.Warnings.AddRange((IEnumerable<string>) brittleFracture.Warnings);
    if (assessment == CalculatorResult.Fail)
      return assessment;
    if (!this.CET.HasValue || !this.Level1Passed)
    {
      this.Warnings.Add("If the tank is constructed from courses with different thicknesses, the membrane stress may be different at each course. This Level 2 assessment calculates the membrane stress based on the inputs thickness and height values, however the maximum membrane stress should be considered.");
      if (this.DesignTemperature > 90.0)
      {
        this.ErrorMessage = "The temperature is higher than the maximum permissible operating temperature of tanks designed to API 650";
        return CalculatorResult.Fail;
      }
      this.TGLessOrEqual12_7 = this.TG <= 12.7;
      this.TemperatureGreaterOrEqual16 = this.DesignTemperature >= 16.0;
      this.MembraneStressLessOrEqual55_2 = 2.6 * this.SpecificGravity * ((this.NominalInsideDiameter + 2.0 * (this.InternalUniformMetalLoss + this.InternalFutureCorrosionAllowance)) / 1000.0) * (this.NominalHeight / 1000.0) / (this.TG - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) <= 55.2;
      bool flag = this.HydroTestPerformed || this.TGLessOrEqual12_7 || this.TemperatureGreaterOrEqual16 || this.MembraneStressLessOrEqual55_2 || this.ImpactTestResultAvailable || this.HasTankBeenOperatedAtLODMT;
      this.Level2MAT = brittleFracture.Level1MAT;
      if (flag && !this.CET.HasValue)
      {
        this.Level2FormattedMessage = "Level 2 Assessment is ACCEPTABLE if CET is greater than {0}{1} otherwise Level 2 assessment fails.";
        this.Level2Passed = true;
      }
      else if (flag && this.CETGreaterThanMAT.Value)
      {
        this.Level2Message = "Level 2 Assessment ACCEPTABLE.";
        this.Level2Passed = true;
      }
      else
      {
        this.Level2Message = "Level 2 Assessment UNACCEPTABLE";
        this.Level2Passed = false;
      }
    }
    return CalculatorResult.Completed;
  }

  public double Convert(double quantity, string from, string to)
  {
    return from == to ? quantity : this.Convert(quantity, $"{from.Trim()} -> {to.Trim()}");
  }

  public double Convert(double quantity, string conversion)
  {
    switch (conversion)
    {
      case "DEG_C -> DEG_F":
        return quantity * 9.0 / 5.0 + 32.0;
      case "MPA -> PSI":
        return quantity * 145.0377;
      case "DEG_F -> DEG_C":
        return (quantity - 32.0) * (5.0 / 9.0);
      case "K -> DEG_C":
        return quantity - 273.15;
      case "DEG_C -> K":
        return quantity + 273.15;
      default:
        throw new NotImplementedException($"Unit conversion not implemented : {conversion}");
    }
  }
}
