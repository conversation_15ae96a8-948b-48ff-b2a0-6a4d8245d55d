// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.BrittleFracture.IBrittleFractureCalculator
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.BrittleFracture;

public interface IBrittleFractureCalculator
{
  CalculatorResult CalculateAssessment();

  double TG { set; }

  double? CET { set; }

  bool? CETGreaterThanMAT { get; }

  string ErrorMessage { get; }

  string Level1FormattedMessage { get; }

  double Level1MAT { get; }

  string Level1Message { get; }

  bool Level1Passed { get; }

  List<string> Warnings { get; }

  string Material { set; }
}
