// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.BrittleFracture.BrittleFracture
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.BrittleFracture;

public class BrittleFracture : BaseCalculator, IBrittleFractureCalculator
{
  public string Material { private get; set; }

  public double TG { private get; set; }

  public double? CET { private get; set; }

  public bool Level1Passed { get; private set; }

  public double Level1MAT { get; private set; }

  public string Level1Message { get; private set; }

  public string Level1FormattedMessage { get; private set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public bool? CETGreaterThanMAT { get; private set; }

  public BrittleFracture() => this.Warnings = new List<string>();

  private static Dictionary<string, Func<double, double>> groupValues()
  {
    return new Dictionary<string, Func<double, double>>()
    {
      {
        "GROUP I",
        (Func<double, double>) (TGin =>
        {
          if (TGin <= 0.25)
            return 10.0;
          return TGin <= 0.5 ? 40.0 * TGin : 60.0 * TGin - 10.0;
        })
      },
      {
        "GROUP II",
        (Func<double, double>) (TGin =>
        {
          if (TGin <= 0.25)
            return -18.0;
          return TGin <= 0.5 ? 30.0 * TGin - 25.0 : 60.0 * TGin - 40.0;
        })
      },
      {
        "GROUP IIA",
        (Func<double, double>) (TGin =>
        {
          if (TGin <= 0.43)
            return -20.0;
          if (TGin < 0.5)
            return 228.0 * TGin - 118.0;
          return TGin <= 0.7 ? 76.0 * TGin - 42.0 : 140.0 * TGin / 3.0 - 20.0;
        })
      },
      {
        "GROUP III",
        (Func<double, double>) (TGin => TGin <= 0.5 ? -40.0 : TGin * 60.0 - 70.0)
      },
      {
        "GROUP IIIA",
        (Func<double, double>) (TGin => -40.0)
      },
      {
        "GROUP IV",
        (Func<double, double>) (TGin => TGin <= 0.25 ? 7.0 : 34.4 * TGin - 1.6)
      },
      {
        "GROUP IVA",
        (Func<double, double>) (TGin => TGin <= 0.25 ? -3.0 : 36.0 * TGin - 12.0)
      },
      {
        "GROUP V",
        (Func<double, double>) (TGin => TGin <= 0.25 ? -18.0 : 30.4 * TGin - 25.6)
      },
      {
        "GROUP VI",
        (Func<double, double>) (TGin => TGin <= 0.25 ? -36.0 : 20.0 * TGin - 41.0)
      },
      {
        "GROUP VIA",
        (Func<double, double>) (TGin => TGin <= 0.25 ? -36.0 : 20.0 * TGin - 41.0)
      }
    };
  }

  public CalculatorResult CalculateAssessment()
  {
    double num = this.TG * 1.0 / 25.4;
    double quantity;
    try
    {
      quantity = IntegriWISE.AssessmentsCalculators.Calculators.API650.Shell.BrittleFracture.BrittleFracture.groupValues()[this.Material](num);
    }
    catch
    {
      this.ErrorMessage = "Invalid group meant the material temperature assignment failed";
      return CalculatorResult.Fail;
    }
    this.Level1MAT = this.Convert(quantity, "DEG_F", "DEG_C");
    if (this.CET.HasValue)
    {
      double? cet = this.CET;
      double level1Mat = this.Level1MAT;
      this.CETGreaterThanMAT = new bool?(cet.GetValueOrDefault() >= level1Mat && cet.HasValue);
      if (this.CETGreaterThanMAT.Value)
      {
        this.Level1Passed = true;
        this.Level1Message = "Level 1 Assessment ACCEPTABLE";
        this.Warnings.Add("It is assumed that the tank meets the Toughness requirements included in API 650 code otherwise Level 1 fails.");
      }
      else
      {
        this.Level1Passed = false;
        this.Level1Message = "Level 1 Assessment UNACCEPTABLE";
      }
    }
    else
    {
      this.Level1Passed = true;
      this.Level1FormattedMessage = "Level 1 Assessment is ACCEPTABLE if CET is greater than {0}{1} and the tank meets the Toughness requirements included in API 650 otherwise Level 1 fails.";
    }
    return CalculatorResult.Completed;
  }

  public double Convert(double quantity, string from, string to)
  {
    return from == to ? quantity : this.Convert(quantity, $"{from.Trim()} -> {to.Trim()}");
  }

  public double Convert(double quantity, string conversion)
  {
    switch (conversion)
    {
      case "DEG_C -> DEG_F":
        return quantity * 9.0 / 5.0 + 32.0;
      case "MPA -> PSI":
        return quantity * 145.0377;
      case "DEG_F -> DEG_C":
        return (quantity - 32.0) * (5.0 / 9.0);
      case "K -> DEG_C":
        return quantity - 273.15;
      case "DEG_C -> K":
        return quantity + 273.15;
      default:
        throw new NotImplementedException($"Unit conversion not implemented : {conversion}");
    }
  }
}
