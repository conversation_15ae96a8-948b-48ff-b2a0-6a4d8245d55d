// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.Thickness.CTP
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.Thickness;

internal class CTP
{
  public List<RSFiCTPResult> results;

  public double Index { get; set; }

  public double ctp { get; set; }

  public double rsf { get; set; }

  public double MeasureSe { get; set; }

  public double MeasureSs { get; set; }

  public CTP(double Ctp, double index)
  {
    this.ctp = Ctp;
    this.Index = index;
    this.results = new List<RSFiCTPResult>();
  }

  public double GetRSF()
  {
    return this.results.Min<RSFiCTPResult>((Func<RSFiCTPResult, double>) (result => result.RSFi));
  }
}
