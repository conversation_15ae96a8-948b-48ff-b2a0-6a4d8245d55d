// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.Thickness.ThicknessReadings
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.Thickness;

public class ThicknessReadings
{
  public double[,] readings;
  public double[,] readingsCorrected;
  public double[] CircumferentialCTPCorrected;
  public double[] LongitudinalCTPCorrected;
  public double[] CircumferentialCTP;
  public double[] LongitudinalCTP;
  private List<double> RSFs;
  public ThicknessReadingsDTO ThicknessReadingsDTO;

  public double S { get; set; }

  public double C { get; set; }

  public double Tcam { get; set; }

  public double Tsam { get; set; }

  public string ErrorMessage { get; private set; }

  public double[,] ProfilePoints { get; set; }

  public ThicknessReadings(ThicknessReadingsDTO thicknessReadings)
  {
    this.ThicknessReadingsDTO = thicknessReadings;
  }

  public bool InitialiseThicknessReadings()
  {
    if (this.ThicknessReadingsDTO.ReadingsType == "GeneralMetalLossThicknessProfile" || this.ThicknessReadingsDTO.ReadingsType == "LocalMetalLossThinArea" || this.ThicknessReadingsDTO.ReadingsType == "LocalMetalLossASMEB31G")
    {
      double?[,] nullableArray = new double?[500, 500];
      foreach (ThicknessPointReadingDTO thicknessPointReading in this.ThicknessReadingsDTO.ThicknessPointReadings)
        nullableArray[thicknessPointReading.RowNo, thicknessPointReading.ColNo] = new double?(thicknessPointReading.ThicknessValue);
      StringBuilder stringBuilder = new StringBuilder();
      for (int index1 = 0; index1 < nullableArray.GetLength(0); ++index1)
      {
        for (int index2 = 0; index2 < nullableArray.GetLength(1); ++index2)
        {
          if (nullableArray[index1, index2].HasValue)
            stringBuilder.Append(nullableArray[index1, index2].ToString()).Append('\t');
        }
        --stringBuilder.Length;
        stringBuilder.Append("\r\n");
      }
      this.GetMeasuredThicknessReadingsGrid(stringBuilder.ToString());
      return true;
    }
    return this.ThicknessReadingsDTO.ReadingsType == "GeneralMetalLossThicknessReading";
  }

  private void FillInEmptyReadings()
  {
    if (this.ThicknessReadingsDTO.ReadingsType == "GeneralMetalLossThicknessProfile" || this.ThicknessReadingsDTO.ReadingsType == "LocalMetalLossThinArea" || this.ThicknessReadingsDTO.ReadingsType == "LocalMetalLossASMEB31G")
    {
      int num1 = this.ThicknessReadingsDTO.ThicknessPointReadings.Max<ThicknessPointReadingDTO>((Func<ThicknessPointReadingDTO, int>) (p => p.ColNo));
      int num2 = this.ThicknessReadingsDTO.ThicknessPointReadings.Max<ThicknessPointReadingDTO>((Func<ThicknessPointReadingDTO, int>) (p => p.RowNo));
      if (this.ThicknessReadingsDTO.ThicknessPointReadings.Count == (num1 + 1) * (num2 + 1))
        return;
      double num3 = this.ThicknessReadingsDTO.Trd.Value;
      for (int r = 0; r <= num2; ++r)
      {
        for (int c = 0; c <= num1; ++c)
        {
          if (this.ThicknessReadingsDTO.ThicknessPointReadings.Where<ThicknessPointReadingDTO>((Func<ThicknessPointReadingDTO, bool>) (p => p.RowNo == r && p.ColNo == c)).FirstOrDefault<ThicknessPointReadingDTO>() == null)
            this.ThicknessReadingsDTO.ThicknessPointReadings.Add(new ThicknessPointReadingDTO()
            {
              AssessmentID = this.ThicknessReadingsDTO.AssessmentID,
              RowNo = r,
              ColNo = c,
              ThicknessValue = num3
            });
        }
      }
    }
    else
    {
      if (!(this.ThicknessReadingsDTO.ReadingsType == "GeneralMetalLossThicknessReading"))
        return;
      int num4 = this.ThicknessReadingsDTO.ThicknessPointReadings.Max<ThicknessPointReadingDTO>((Func<ThicknessPointReadingDTO, int>) (p => p.ColNo));
      if (this.ThicknessReadingsDTO.ThicknessPointReadings.Count == num4 + 1)
        return;
      double num5 = this.ThicknessReadingsDTO.Trd.Value;
      for (int c = 0; c <= num4; ++c)
      {
        if (this.ThicknessReadingsDTO.ThicknessPointReadings.Where<ThicknessPointReadingDTO>((Func<ThicknessPointReadingDTO, bool>) (p => p.RowNo == 0 && p.ColNo == c)).FirstOrDefault<ThicknessPointReadingDTO>() == null)
          this.ThicknessReadingsDTO.ThicknessPointReadings.Add(new ThicknessPointReadingDTO()
          {
            AssessmentID = this.ThicknessReadingsDTO.AssessmentID,
            RowNo = 0,
            ColNo = c,
            ThicknessValue = num5
          });
      }
    }
  }

  private void ThicknessReadingsForCandS()
  {
    if (!(this.ThicknessReadingsDTO.ReadingsType == "GeneralMetalLossThicknessProfile") && !(this.ThicknessReadingsDTO.ReadingsType == "LocalMetalLossThinArea") && !(this.ThicknessReadingsDTO.ReadingsType == "LocalMetalLossASMEB31G"))
      return;
    double?[,] nullableArray = new double?[500, 500];
    double? trd = this.ThicknessReadingsDTO.Trd;
    foreach (ThicknessPointReadingDTO thicknessPointReading in this.ThicknessReadingsDTO.ThicknessPointReadings)
    {
      double thicknessValue = thicknessPointReading.ThicknessValue;
      double? nullable = trd;
      if ((thicknessValue <= nullable.GetValueOrDefault() ? 0 : (nullable.HasValue ? 1 : 0)) != 0)
        nullableArray[thicknessPointReading.RowNo, thicknessPointReading.ColNo] = trd;
      else
        nullableArray[thicknessPointReading.RowNo, thicknessPointReading.ColNo] = new double?(thicknessPointReading.ThicknessValue);
    }
    StringBuilder stringBuilder = new StringBuilder();
    for (int index1 = 0; index1 < nullableArray.GetLength(0); ++index1)
    {
      for (int index2 = 0; index2 < nullableArray.GetLength(1); ++index2)
      {
        if (nullableArray[index1, index2].HasValue)
          stringBuilder.Append(nullableArray[index1, index2].ToString()).Append('\t');
      }
      --stringBuilder.Length;
      stringBuilder.Append("\r\n");
    }
    this.GetMeasuredThicknessReadingsGridCorrected(stringBuilder.ToString());
  }

  public bool ValidateThicknessReadingsIntegrity()
  {
    if (this.ThicknessReadingsDTO == null || !this.ThicknessReadingsDTO.Trd.HasValue || this.ThicknessReadingsDTO.ThicknessPointReadings == null || this.ThicknessReadingsDTO.ThicknessPointReadings.Count == 0)
    {
      this.ErrorMessage = "Please Enter the Thickness Readings";
      return false;
    }
    if (this.ThicknessReadingsDTO != null && this.ThicknessReadingsDTO.Trd.HasValue)
    {
      double? trd = this.ThicknessReadingsDTO.Trd;
      if ((trd.GetValueOrDefault() != 0.0 ? 0 : (trd.HasValue ? 1 : 0)) == 0)
      {
        if (this.ThicknessReadingsDTO.ReadingsType == "GeneralMetalLossThicknessProfile" || this.ThicknessReadingsDTO.ReadingsType == "LocalMetalLossThinArea" || this.ThicknessReadingsDTO.ReadingsType == "LocalMetalLossASMEB31G")
        {
          if (this.ThicknessReadingsDTO.ThicknessPointReadings.Where<ThicknessPointReadingDTO>((Func<ThicknessPointReadingDTO, bool>) (p => p.ThicknessValue > 0.0)).Count<ThicknessPointReadingDTO>() < 5)
          {
            this.ErrorMessage = "A minimum of 5 thickness readings is recommended for each inspection plane according to API 579";
            return false;
          }
        }
        else if (this.ThicknessReadingsDTO.ReadingsType == "GeneralMetalLossThicknessReading" && this.ThicknessReadingsDTO.ThicknessPointReadings.Where<ThicknessPointReadingDTO>((Func<ThicknessPointReadingDTO, bool>) (p => p.ThicknessValue > 0.0)).Count<ThicknessPointReadingDTO>() < 2)
        {
          this.ErrorMessage = "A minimum of two thickness readings is required for this assessment. However, Part 4 API 579 recommends a minimum of 15 thickness readings.";
          return false;
        }
        this.ThicknessReadingsDTO.ThicknessPointReadings.Where<ThicknessPointReadingDTO>((Func<ThicknessPointReadingDTO, bool>) (p => p.ThicknessValue <= 0.0)).ToList<ThicknessPointReadingDTO>();
        this.FillInEmptyReadings();
        this.ThicknessReadingsForCandS();
        this.InitialiseThicknessReadings();
        return true;
      }
    }
    this.ErrorMessage = "There was an error in calculating the Trd. The calculated Trd is 0";
    return false;
  }

  public bool ValidateB31GThicknessReadingsIntegrity()
  {
    this.FillInEmptyReadings();
    this.ThicknessReadingsForCandS();
    this.InitialiseThicknessReadings();
    return true;
  }

  public double SumOfThicknessReadings()
  {
    double num = 0.0;
    foreach (ThicknessPointReadingDTO thicknessPointReading in this.ThicknessReadingsDTO.ThicknessPointReadings)
      num += thicknessPointReading.ThicknessValue;
    return num;
  }

  public double SumOfThicknessReadingsPow2()
  {
    double num1 = 0.0;
    double num2 = this.SumOfThicknessReadings() / (double) this.ThicknessReadingsDTO.ThicknessPointReadings.Count<ThicknessPointReadingDTO>();
    foreach (ThicknessPointReadingDTO thicknessPointReading in this.ThicknessReadingsDTO.ThicknessPointReadings)
      num1 += Math.Pow(thicknessPointReading.ThicknessValue - num2, 2.0);
    return num1;
  }

  public double MinimumThicknessPointValue()
  {
    return this.ThicknessReadingsDTO.ThicknessPointReadings.Min<ThicknessPointReadingDTO>((Func<ThicknessPointReadingDTO, double>) (p => p.ThicknessValue));
  }

  public int TotalNumThicknessPoints() => this.ThicknessReadingsDTO.ThicknessPointReadings.Count;

  public void GetMeasuredThicknessReadings(string text)
  {
  }

  public void GetMeasuredThicknessReadingsGrid(string data_string)
  {
    char[] chArray;
    if (string.Compare(NumberFormatInfo.CurrentInfo.NumberDecimalSeparator, ",", false) != 0)
      chArray = new char[2]{ '\t', ',' };
    else
      chArray = new char[1]{ '\t' };
    string[] strArray1 = data_string.Split(new string[1]
    {
      Environment.NewLine
    }, StringSplitOptions.RemoveEmptyEntries);
    int length1 = strArray1.GetLength(0);
    if (length1 < 1)
      return;
    int length2 = strArray1[0].Split(chArray).GetLength(0);
    if (length2 < 1)
      return;
    this.ProfilePoints = new double[length1, length2];
    int index1 = 0;
    string str1 = data_string;
    string[] separator = new string[1]
    {
      Environment.NewLine
    };
    foreach (string str2 in str1.Split(separator, StringSplitOptions.RemoveEmptyEntries))
    {
      string[] strArray2 = str2.Split(chArray);
      if (strArray2.Length >= 2)
      {
        double[] numArray = new double[strArray2.Length];
        for (int index2 = 0; index2 < strArray2.Length; ++index2)
        {
          if (double.TryParse(strArray2[index2], out numArray[index2]))
            this.ProfilePoints[index1, index2] = numArray[index2];
        }
        ++index1;
      }
    }
    this.ProfilePoints.GetLength(0);
    this.ProfilePoints.GetLength(1);
    this.SetReadings(this.ProfilePoints);
  }

  public void GetMeasuredThicknessReadingsGridCorrected(string data_string)
  {
    char[] chArray;
    if (string.Compare(NumberFormatInfo.CurrentInfo.NumberDecimalSeparator, ",", false) != 0)
      chArray = new char[2]{ '\t', ',' };
    else
      chArray = new char[1]{ '\t' };
    string[] strArray1 = data_string.Split(new string[1]
    {
      Environment.NewLine
    }, StringSplitOptions.RemoveEmptyEntries);
    int length1 = strArray1.GetLength(0);
    if (length1 < 1)
      return;
    int length2 = strArray1[0].Split(chArray).GetLength(0);
    if (length2 < 1)
      return;
    this.ProfilePoints = new double[length1, length2];
    int index1 = 0;
    string str1 = data_string;
    string[] separator = new string[1]
    {
      Environment.NewLine
    };
    foreach (string str2 in str1.Split(separator, StringSplitOptions.RemoveEmptyEntries))
    {
      string[] strArray2 = str2.Split(chArray);
      if (strArray2.Length >= 2)
      {
        double[] numArray = new double[strArray2.Length];
        for (int index2 = 0; index2 < strArray2.Length; ++index2)
        {
          if (double.TryParse(strArray2[index2], out numArray[index2]))
            this.ProfilePoints[index1, index2] = numArray[index2];
        }
        ++index1;
      }
    }
    this.readingsCorrected = this.ProfilePoints;
    this.SetLongitudinalCTPsCorrected();
    this.SetCircumferentialCTPsCorrected();
  }

  public bool ProcessP5ThicknessReadings(double Lm2, double Lc2, double trd)
  {
    this.SetCircumferentialCTPs();
    this.SetLongitudinalCTPs();
    this.C = this.GetCCorrected(trd, Lm2);
    this.S = this.GetSCorrected(trd, Lc2);
    return true;
  }

  public void GetLongitudinalCTP() => this.SetLongitudinalCTPs();

  public void GetCircumferentialCTP() => this.SetCircumferentialCTPs();

  public bool ProcessThicknessReadings(double L, double Lm2, double Lc2, double trd)
  {
    bool flag = false;
    this.SetCircumferentialCTPs();
    this.SetLongitudinalCTPs();
    this.C = this.GetCCorrected(trd, Lm2);
    this.S = this.GetSCorrected(trd, Lc2);
    if (((IEnumerable) this.readingsCorrected).Cast<double>().Min() >= trd)
    {
      this.C = 0.0;
      this.S = 0.0;
      this.Tcam = trd;
      this.Tsam = trd;
      return true;
    }
    double smallestLongitudinalCtp = this.GetSmallestLongitudinalCTP();
    double circumferentialCtp = this.GetSmallestCircumferentialCTP();
    List<double> source1 = new List<double>();
    List<double> source2 = new List<double>();
    int[] allInstances1 = this.GetAllInstances(this.LongitudinalCTP, smallestLongitudinalCtp);
    for (int index = 0; index <= allInstances1.GetUpperBound(0); ++index)
    {
      double num = 0.0 + this.ProcessInstanceLeft(this.LongitudinalCTP, allInstances1[index], trd, L, Lc2) + this.ProcessInstanceRight(this.LongitudinalCTP, allInstances1[index], trd, L, Lc2);
      source1.Add(num);
    }
    int[] allInstances2 = this.GetAllInstances(this.CircumferentialCTP, circumferentialCtp);
    for (int index = 0; index <= allInstances2.GetUpperBound(0); ++index)
    {
      double num = 0.0 + this.ProcessInstanceLeft(this.CircumferentialCTP, allInstances2[index], trd, L, Lm2) + this.ProcessInstanceRight(this.CircumferentialCTP, allInstances2[index], trd, L, Lm2);
      source2.Add(num);
    }
    double num1 = source1.Min();
    this.Tcam = source2.Min() / L;
    this.Tsam = num1 / L;
    return flag;
  }

  public void SetReadings(double[,] NewReadings)
  {
    this.readings = NewReadings;
    this.SetLongitudinalCTPs();
    this.SetCircumferentialCTPs();
  }

  public double GetMinimumValue()
  {
    return this.ThicknessReadingsDTO.ThicknessPointReadings.Min<ThicknessPointReadingDTO>((Func<ThicknessPointReadingDTO, double>) (p => p.ThicknessValue));
  }

  public double GetMaximumValue()
  {
    return this.ThicknessReadingsDTO.ThicknessPointReadings.Max<ThicknessPointReadingDTO>((Func<ThicknessPointReadingDTO, double>) (p => p.ThicknessValue));
  }

  public bool ValidThickessProfiles(double Maxv)
  {
    bool flag = true;
    if (!(this.ThicknessReadingsDTO.ReadingsType == "GeneralMetalLossThicknessProfile"))
    {
      if (!(this.ThicknessReadingsDTO.ReadingsType == "LocalMetalLossThinArea"))
      {
        if (!(this.ThicknessReadingsDTO.ReadingsType == "LocalMetalLossASMEB31G"))
        {
          if (this.ThicknessReadingsDTO.ReadingsType == "GeneralMetalLossThicknessReading" && this.ThicknessReadingsDTO.ThicknessPointReadings.Max<ThicknessPointReadingDTO>((Func<ThicknessPointReadingDTO, double>) (p => p.ThicknessValue)) > Maxv)
            return false;
          goto label_8;
        }
      }
    }
    try
    {
      if (((IEnumerable) this.readings).Cast<double>().Max() > Maxv)
        return false;
    }
    catch (Exception ex)
    {
      throw;
    }
label_8:
    return flag;
  }

  private void SetLongitudinalCTPs()
  {
    int col = 0;
    this.LongitudinalCTP = new double[this.readings.GetUpperBound(1) + 1];
    for (col = 0; col <= this.readings.GetUpperBound(1); ++col)
    {
      double num = Enumerable.Range(0, this.readings.GetUpperBound(0) + 1).Select<int, double>((Func<int, double>) (i => this.readings[i, col])).Min();
      this.LongitudinalCTP[col] = Math.Round(num, 4);
    }
  }

  private void SetCircumferentialCTPs()
  {
    int row = 0;
    this.CircumferentialCTP = new double[this.readings.GetUpperBound(0) + 1];
    for (row = 0; row <= this.readings.GetUpperBound(0); ++row)
    {
      double num = Enumerable.Range(0, this.readings.GetUpperBound(1) + 1).Select<int, double>((Func<int, double>) (i => this.readings[row, i])).Min();
      this.CircumferentialCTP[row] = Math.Round(num, 4);
    }
  }

  private void SetLongitudinalCTPsCorrected()
  {
    int col = 0;
    this.LongitudinalCTPCorrected = new double[this.readingsCorrected.GetUpperBound(1) + 1];
    for (col = 0; col <= this.readingsCorrected.GetUpperBound(1); ++col)
    {
      double num = Enumerable.Range(0, this.readingsCorrected.GetUpperBound(0) + 1).Select<int, double>((Func<int, double>) (i => this.readingsCorrected[i, col])).Min();
      this.LongitudinalCTPCorrected[col] = Math.Round(num, 4);
    }
  }

  private void SetCircumferentialCTPsCorrected()
  {
    int row = 0;
    this.CircumferentialCTPCorrected = new double[this.readingsCorrected.GetUpperBound(0) + 1];
    for (row = 0; row <= this.readingsCorrected.GetUpperBound(0); ++row)
    {
      double num = Enumerable.Range(0, this.readingsCorrected.GetUpperBound(1) + 1).Select<int, double>((Func<int, double>) (i => this.readingsCorrected[row, i])).Min();
      this.CircumferentialCTPCorrected[row] = Math.Round(num, 4);
    }
  }

  private double GetSmallestCircumferentialCTP()
  {
    return ((IEnumerable<double>) this.CircumferentialCTP).Min();
  }

  private double GetSmallestLongitudinalCTP() => ((IEnumerable<double>) this.LongitudinalCTP).Min();

  private int[] GetAllInstances(double[] CTP, double seachValue)
  {
    int startIndex = 0;
    List<int> intList = new List<int>();
    for (int index = Array.FindIndex<double>(CTP, startIndex, (Predicate<double>) (x => x == seachValue)); index != -1; index = Array.FindIndex<double>(CTP, index + 1, (Predicate<double>) (x => x == seachValue)))
      intList.Add(index);
    return intList.ToArray();
  }

  private double GetFirstAtTRD(double[] ary, double trd, double Lc)
  {
    double firstAtTrd = 0.0;
    if (ary[0] < trd)
    {
      firstAtTrd = Lc * -1.0;
    }
    else
    {
      int startIndex = 0;
      int index = Array.FindIndex<double>(ary, startIndex, (Predicate<double>) (x => x < trd));
      if (index != -1)
        firstAtTrd = Lc * ((double) index - 1.0);
    }
    return firstAtTrd;
  }

  private double GetLastAtTRD(double[] ary, double trd, double Lc)
  {
    double lastAtTrd = 0.0;
    double[] array = (double[]) ary.Clone();
    Array.Reverse((Array) array);
    if (array[0] < trd)
    {
      lastAtTrd = Lc * (double) (ary.GetUpperBound(0) + 1);
    }
    else
    {
      int startIndex = 0;
      int upperBound = array.GetUpperBound(0);
      int index = Array.FindIndex<double>(array, startIndex, (Predicate<double>) (x => x < trd));
      if (index != -1)
        lastAtTrd = Lc * (double) (upperBound - (index - 1));
    }
    return lastAtTrd;
  }

  private double GetC(double trd, double Spacing)
  {
    double firstAtTrd = this.GetFirstAtTRD(this.CircumferentialCTP, trd, Spacing);
    return this.GetLastAtTRD(this.CircumferentialCTP, trd, Spacing) - firstAtTrd;
  }

  private double GetS(double trd, double Spacing)
  {
    double firstAtTrd = this.GetFirstAtTRD(this.LongitudinalCTP, trd, Spacing);
    return this.GetLastAtTRD(this.LongitudinalCTP, trd, Spacing) - firstAtTrd;
  }

  private double GetCCorrected(double trd, double Spacing)
  {
    double firstAtTrd = this.GetFirstAtTRD(this.CircumferentialCTPCorrected, trd, Spacing);
    return this.GetLastAtTRD(this.CircumferentialCTPCorrected, trd, Spacing) - firstAtTrd;
  }

  private double GetSCorrected(double trd, double Spacing)
  {
    double firstAtTrd = this.GetFirstAtTRD(this.LongitudinalCTPCorrected, trd, Spacing);
    return this.GetLastAtTRD(this.LongitudinalCTPCorrected, trd, Spacing) - firstAtTrd;
  }

  private double ProcessInstanceLeft(
    double[] CTPs,
    int Position,
    double trd,
    double L,
    double Lc)
  {
    double num1 = (double) Position * Lc - L / 2.0;
    $"From {(double) Position * Lc + L / 2.0} to {num1}";
    double num2 = 0.0;
    int index = Position;
    double num3 = (double) Position * Lc;
    while (num3 != num1)
    {
      double num4 = 0.0;
      double val1 = trd;
      if (index >= 0)
        val1 = CTPs[index];
      double val2 = trd;
      if (index - 1 >= 0)
        val2 = CTPs[index - 1];
      double num5 = Lc;
      double num6;
      if (num3 - Lc >= num1)
      {
        double num7 = Math.Abs(val1 - val2);
        double num8 = num7 * num5 / 2.0;
        $"TriangularArea({num8}) = (side({num7}) * width({num5})) / 2d ";
        double num9 = Math.Min(val1, val2);
        num6 = num4 + (num9 * num5 + num8);
        $"ThisArea += height({num9}) * width({num5}) + TriangularArea({num8})";
        num3 -= Lc;
      }
      else
      {
        num5 = Math.Abs(num3 - num1);
        num3 = num1;
        if (val1 != val2)
        {
          double num10 = Math.Abs(val1 - val2);
          double a = Math.Atan(num10 / Lc);
          $"theta({a}) = Math.Atan(opposite({num10}) / gridInterval({Lc}))";
          double num11 = Math.Tan(a) * num5;
          $"opposite({num11}) = Math.Tan(theta[{a}]) * width({num5})";
          double num12 = num11 * num5 / 2.0;
          $"TriangularArea({num12}) = (side({num11}) * width({num5})) / 2d ";
          double num13 = val1 <= val2 ? Math.Min(val1, val2) : val1 - num11;
          num6 = num4 + (num13 * num5 + num12);
          $"ThisArea += height({num13}) * width({num5}) + TriangularArea({num12})";
        }
        else
        {
          num6 = num4 + val1 * num5;
          $"ThisArea += height{val1}) * width({num5}";
        }
      }
      $"t1={val1}, t2={val2}, width={num5} Area = {num6}";
      num2 += num6;
      --index;
    }
    return num2;
  }

  private double ProcessInstanceRight(
    double[] CTPs,
    int Position,
    double trd,
    double L,
    double Lc)
  {
    double num1 = (double) Position * Lc - L / 2.0;
    double num2 = (double) Position * Lc + L / 2.0;
    $"From {num1} to {num2}";
    double num3 = 0.0;
    int index = Position;
    double num4 = (double) Position * Lc;
    while (num4 != num2)
    {
      double num5 = 0.0;
      double val1 = trd;
      if (index >= 0 && index <= CTPs.GetUpperBound(0))
        val1 = CTPs[index];
      double val2 = trd;
      if (index + 1 >= 0 && index + 1 <= CTPs.GetUpperBound(0))
        val2 = CTPs[index + 1];
      double num6 = Lc;
      double num7;
      if (num4 + Lc <= num2)
      {
        double num8 = Math.Abs(val1 - val2);
        double num9 = num8 * num6 / 2.0;
        $"TriangularArea({num9}) = (side({num8}) * width({num6})) / 2d ";
        num7 = num5 + (Math.Min(val1, val2) * Lc + num9);
        num4 += Lc;
      }
      else
      {
        double num10 = Math.Abs(num4 - num2);
        num4 = num2;
        if (val1 != val2)
        {
          double num11 = Math.Abs(val1 - val2);
          double a = Math.Atan(num11 / Lc);
          $"theta({a}) = Math.Atan(opposite({num11}) / gridInterval({Lc}))";
          double num12 = Math.Tan(a) * num10;
          $"opposite({num12}) = Math.Tan(theta[{a}]) * width({num10})";
          double num13 = num12 * num10 / 2.0;
          $"TriangularArea({num13}) = (side({num12}) * width({num10})) / 2d ";
          double num14 = val1 <= val2 ? Math.Min(val1, val2) : val1 - num12;
          num7 = num5 + (num14 * num10 + num13);
          $"ThisArea += height({num14}) * width({num10}) + TriangularArea({num13})";
        }
        else
        {
          num7 = num5 + val1 * num10;
          $"ThisArea += height{val1}) * width({num10}";
        }
      }
      num3 += num7;
      ++index;
    }
    return num3;
  }

  public void ProcessB31GOriginal(double trd, double Lc, double Diameter, double Tc)
  {
    bool flag1 = true;
    this.SetCircumferentialCTPs();
    this.SetLongitudinalCTPs();
    double firstAtTrd = this.GetFirstAtTRD(this.LongitudinalCTP, trd, Lc);
    double lastAtTrd = this.GetLastAtTRD(this.LongitudinalCTP, trd, Lc);
    this.RSFs = new List<double>();
    List<CTP> ctpList = new List<CTP>();
    int num1 = (int) (firstAtTrd / Lc);
    int num2 = num1 >= 0 ? num1 + 1 : 0;
    int num3 = (int) (lastAtTrd / Lc);
    int num4 = num3 <= this.LongitudinalCTP.GetUpperBound(0) ? num3 - 1 : this.LongitudinalCTP.GetUpperBound(0);
    for (int index = num2; index <= num4; ++index)
    {
      CTP ctp = index <= this.LongitudinalCTP.GetUpperBound(0) ? new CTP(this.LongitudinalCTP[index], (double) index) : new CTP(trd, (double) index);
      ctp.MeasureSe = index <= 0 ? trd : this.LongitudinalCTP[index - 1];
      ctp.MeasureSs = index >= this.LongitudinalCTP.GetUpperBound(0) ? trd : this.LongitudinalCTP[index + 1];
      ctpList.Add(ctp);
    }
    ctpList.Sort((IComparer<CTP>) new CTPComparer());
    int num5 = 1;
    int num6 = flag1 ? 1 : 0;
    foreach (CTP ctp in ctpList)
    {
      double num7 = 1.0;
      double index1 = ctp.Index - 1.0;
      double index2 = ctp.Index + 1.0;
      double index3 = ctp.Index;
      bool flag2 = false;
      bool flag3 = false;
      bool flag4 = false;
      int num8 = flag1 ? 1 : 0;
      RSFiCTPResult rsFiCtpResult1 = (RSFiCTPResult) null;
      double num9 = 0.0;
      for (; !flag4; flag4 = flag3 && flag2)
      {
        RSFiCTPResult rsFiCtpResult2 = new RSFiCTPResult();
        rsFiCtpResult2.ss = Lc * index1;
        rsFiCtpResult2.se = Lc * index2;
        if (rsFiCtpResult2.se > lastAtTrd)
          rsFiCtpResult2.se = lastAtTrd;
        rsFiCtpResult2.si = Math.Abs(rsFiCtpResult2.se - rsFiCtpResult2.ss);
        rsFiCtpResult2.Lambdai = Math.Pow(rsFiCtpResult2.si, 2.0) / (Diameter * Tc);
        rsFiCtpResult2.Aoi = trd * rsFiCtpResult2.si;
        rsFiCtpResult2.MeasureSe = trd;
        rsFiCtpResult2.MeasureSs = trd;
        if (index1 >= 0.0)
          rsFiCtpResult2.MeasureSe = this.LongitudinalCTP[(int) index1];
        if (index2 <= (double) this.LongitudinalCTP.GetUpperBound(0))
          rsFiCtpResult2.MeasureSs = this.LongitudinalCTP[(int) index2];
        if (ctp.results.Count == 0)
        {
          rsFiCtpResult2.TrapezeRuleUnderALine = Math.Abs(0.5 * (rsFiCtpResult2.MeasureSe + ctp.ctp) * (rsFiCtpResult2.si / 2.0)) + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSs + ctp.ctp) * (rsFiCtpResult2.si / 2.0));
          num9 = rsFiCtpResult2.si;
        }
        else
          rsFiCtpResult2.TrapezeRuleUnderALine = rsFiCtpResult2.ss != rsFiCtpResult1.ss || rsFiCtpResult2.se != rsFiCtpResult1.se ? (rsFiCtpResult2.ss != rsFiCtpResult1.ss ? (rsFiCtpResult2.se != rsFiCtpResult1.se ? rsFiCtpResult1.TrapezeRuleUnderALine + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSs + rsFiCtpResult1.MeasureSs) * (num9 / 2.0)) + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSe + rsFiCtpResult1.MeasureSe) * (num9 / 2.0)) : rsFiCtpResult1.TrapezeRuleUnderALine + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSe + rsFiCtpResult1.MeasureSe) * (num9 / 2.0))) : rsFiCtpResult1.TrapezeRuleUnderALine + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSs + rsFiCtpResult1.MeasureSs) * (num9 / 2.0))) : rsFiCtpResult1.TrapezeRuleUnderALine;
        rsFiCtpResult2.Ai = rsFiCtpResult2.Aoi - rsFiCtpResult2.TrapezeRuleUnderALine;
        rsFiCtpResult2.Mti = Math.Pow(1.0 + 0.8 * rsFiCtpResult2.Lambdai, 0.5);
        rsFiCtpResult2.RSFi = (1.0 - rsFiCtpResult2.Ai / rsFiCtpResult2.Aoi) / (1.0 - rsFiCtpResult2.Ai / rsFiCtpResult2.Aoi / rsFiCtpResult2.Mti);
        int num10 = flag1 ? 1 : 0;
        rsFiCtpResult1 = rsFiCtpResult2;
        ctp.results.Add(rsFiCtpResult2);
        ++num7;
        if (index1 >= (double) num2)
          --index1;
        else
          flag2 = true;
        if (index2 <= (double) num4)
          ++index2;
        else
          flag3 = true;
      }
      ++num5;
      this.RSFs.Add(ctp.GetRSF());
    }
  }

  public void ProcessB31GModified(double trd, double Lc, double Diameter, double Tc)
  {
    bool flag1 = true;
    this.SetCircumferentialCTPs();
    this.SetLongitudinalCTPs();
    double firstAtTrd = this.GetFirstAtTRD(this.LongitudinalCTP, trd, Lc);
    double lastAtTrd = this.GetLastAtTRD(this.LongitudinalCTP, trd, Lc);
    this.RSFs = new List<double>();
    List<CTP> ctpList = new List<CTP>();
    int num1 = (int) (firstAtTrd / Lc);
    int num2 = num1 >= 0 ? num1 + 1 : 0;
    int num3 = (int) (lastAtTrd / Lc);
    int num4 = num3 <= this.LongitudinalCTP.GetUpperBound(0) ? num3 - 1 : this.LongitudinalCTP.GetUpperBound(0);
    for (int index = num2; index <= num4; ++index)
    {
      CTP ctp = index <= this.LongitudinalCTP.GetUpperBound(0) ? new CTP(this.LongitudinalCTP[index], (double) index) : new CTP(trd, (double) index);
      ctp.MeasureSe = index <= 0 ? trd : this.LongitudinalCTP[index - 1];
      ctp.MeasureSs = index >= this.LongitudinalCTP.GetUpperBound(0) ? trd : this.LongitudinalCTP[index + 1];
      ctpList.Add(ctp);
    }
    ctpList.Sort((IComparer<CTP>) new CTPComparer());
    int num5 = 1;
    int num6 = flag1 ? 1 : 0;
    foreach (CTP ctp in ctpList)
    {
      double num7 = 1.0;
      double index1 = ctp.Index - 1.0;
      double index2 = ctp.Index + 1.0;
      double index3 = ctp.Index;
      bool flag2 = false;
      bool flag3 = false;
      bool flag4 = false;
      int num8 = flag1 ? 1 : 0;
      RSFiCTPResult rsFiCtpResult1 = (RSFiCTPResult) null;
      double num9 = 0.0;
      for (; !flag4; flag4 = flag3 && flag2)
      {
        RSFiCTPResult rsFiCtpResult2 = new RSFiCTPResult();
        rsFiCtpResult2.ss = Lc * index1;
        rsFiCtpResult2.se = Lc * index2;
        if (rsFiCtpResult2.se > lastAtTrd)
          rsFiCtpResult2.se = lastAtTrd;
        rsFiCtpResult2.si = Math.Abs(rsFiCtpResult2.se - rsFiCtpResult2.ss);
        rsFiCtpResult2.Lambdai = Math.Pow(rsFiCtpResult2.si, 2.0) / (Diameter * Tc);
        rsFiCtpResult2.Aoi = trd * rsFiCtpResult2.si;
        rsFiCtpResult2.MeasureSe = trd;
        rsFiCtpResult2.MeasureSs = trd;
        if (index1 >= 0.0)
          rsFiCtpResult2.MeasureSe = this.LongitudinalCTP[(int) index1];
        if (index2 <= (double) this.LongitudinalCTP.GetUpperBound(0))
          rsFiCtpResult2.MeasureSs = this.LongitudinalCTP[(int) index2];
        if (ctp.results.Count == 0)
        {
          rsFiCtpResult2.TrapezeRuleUnderALine = Math.Abs(0.5 * (rsFiCtpResult2.MeasureSe + ctp.ctp) * (rsFiCtpResult2.si / 2.0)) + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSs + ctp.ctp) * (rsFiCtpResult2.si / 2.0));
          num9 = rsFiCtpResult2.si;
        }
        else
          rsFiCtpResult2.TrapezeRuleUnderALine = rsFiCtpResult2.ss != rsFiCtpResult1.ss || rsFiCtpResult2.se != rsFiCtpResult1.se ? (rsFiCtpResult2.ss != rsFiCtpResult1.ss ? (rsFiCtpResult2.se != rsFiCtpResult1.se ? rsFiCtpResult1.TrapezeRuleUnderALine + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSs + rsFiCtpResult1.MeasureSs) * (num9 / 2.0)) + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSe + rsFiCtpResult1.MeasureSe) * (num9 / 2.0)) : rsFiCtpResult1.TrapezeRuleUnderALine + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSe + rsFiCtpResult1.MeasureSe) * (num9 / 2.0))) : rsFiCtpResult1.TrapezeRuleUnderALine + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSs + rsFiCtpResult1.MeasureSs) * (num9 / 2.0))) : rsFiCtpResult1.TrapezeRuleUnderALine;
        rsFiCtpResult2.Ai = rsFiCtpResult2.Aoi - rsFiCtpResult2.TrapezeRuleUnderALine;
        rsFiCtpResult2.Mti = rsFiCtpResult2.Lambdai > 50.0 ? 0.032 * rsFiCtpResult2.Lambdai + 3.3 : Math.Pow(1.0 + 251.0 / 400.0 * rsFiCtpResult2.Lambdai - 0.003375 * Math.Pow(rsFiCtpResult2.Lambdai, 2.0), 0.5);
        rsFiCtpResult2.RSFi = (1.0 - rsFiCtpResult2.Ai / rsFiCtpResult2.Aoi) / (1.0 - rsFiCtpResult2.Ai / rsFiCtpResult2.Aoi / rsFiCtpResult2.Mti);
        int num10 = flag1 ? 1 : 0;
        rsFiCtpResult1 = rsFiCtpResult2;
        ctp.results.Add(rsFiCtpResult2);
        ++num7;
        if (index1 >= (double) num2)
          --index1;
        else
          flag2 = true;
        if (index2 <= (double) num4)
          ++index2;
        else
          flag3 = true;
      }
      ++num5;
      this.RSFs.Add(ctp.GetRSF());
    }
  }

  public void ProcessP5Level2(double trd, double Lc, double Diameter, double Tc)
  {
    bool flag1 = true;
    this.SetCircumferentialCTPs();
    this.SetLongitudinalCTPs();
    double firstAtTrd = this.GetFirstAtTRD(this.LongitudinalCTP, trd, Lc);
    double lastAtTrd = this.GetLastAtTRD(this.LongitudinalCTP, trd, Lc);
    this.RSFs = new List<double>();
    List<CTP> ctpList = new List<CTP>();
    int num1 = (int) (firstAtTrd / Lc);
    int num2 = num1 >= 0 ? num1 + 1 : 0;
    int num3 = (int) (lastAtTrd / Lc);
    int num4 = num3 <= this.LongitudinalCTP.GetUpperBound(0) ? num3 - 1 : this.LongitudinalCTP.GetUpperBound(0);
    for (int index = num2; index <= num4; ++index)
    {
      CTP ctp = index <= this.LongitudinalCTP.GetUpperBound(0) ? new CTP(this.LongitudinalCTP[index], (double) index) : new CTP(trd, (double) index);
      ctp.MeasureSe = index <= 0 ? trd : this.LongitudinalCTP[index - 1];
      ctp.MeasureSs = index >= this.LongitudinalCTP.GetUpperBound(0) ? trd : this.LongitudinalCTP[index + 1];
      ctpList.Add(ctp);
    }
    ctpList.Sort((IComparer<CTP>) new CTPComparer());
    int num5 = 1;
    int num6 = flag1 ? 1 : 0;
    foreach (CTP ctp in ctpList)
    {
      double num7 = 1.0;
      double index1 = ctp.Index - 1.0;
      double index2 = ctp.Index + 1.0;
      double index3 = ctp.Index;
      bool flag2 = false;
      bool flag3 = false;
      bool flag4 = false;
      int num8 = flag1 ? 1 : 0;
      RSFiCTPResult rsFiCtpResult1 = (RSFiCTPResult) null;
      double num9 = 0.0;
      for (; !flag4; flag4 = flag3 && flag2)
      {
        RSFiCTPResult rsFiCtpResult2 = new RSFiCTPResult();
        rsFiCtpResult2.ss = Lc * index1;
        rsFiCtpResult2.se = Lc * index2;
        if (rsFiCtpResult2.se > lastAtTrd)
          rsFiCtpResult2.se = lastAtTrd;
        rsFiCtpResult2.si = Math.Abs(rsFiCtpResult2.se - rsFiCtpResult2.ss);
        rsFiCtpResult2.Lambdai = 1.285 * rsFiCtpResult2.si / Math.Pow(Diameter * Tc, 0.5);
        rsFiCtpResult2.Aoi = trd * rsFiCtpResult2.si;
        rsFiCtpResult2.MeasureSe = trd;
        rsFiCtpResult2.MeasureSs = trd;
        if (index1 >= 0.0)
          rsFiCtpResult2.MeasureSe = this.LongitudinalCTP[(int) index1];
        if (index2 <= (double) this.LongitudinalCTP.GetUpperBound(0))
          rsFiCtpResult2.MeasureSs = this.LongitudinalCTP[(int) index2];
        if (ctp.results.Count == 0)
        {
          rsFiCtpResult2.TrapezeRuleUnderALine = Math.Abs(0.5 * (rsFiCtpResult2.MeasureSe + ctp.ctp) * (rsFiCtpResult2.si / 2.0)) + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSs + ctp.ctp) * (rsFiCtpResult2.si / 2.0));
          num9 = rsFiCtpResult2.si;
        }
        else
          rsFiCtpResult2.TrapezeRuleUnderALine = rsFiCtpResult2.ss != rsFiCtpResult1.ss || rsFiCtpResult2.se != rsFiCtpResult1.se ? (rsFiCtpResult2.ss != rsFiCtpResult1.ss ? (rsFiCtpResult2.se != rsFiCtpResult1.se ? rsFiCtpResult1.TrapezeRuleUnderALine + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSs + rsFiCtpResult1.MeasureSs) * (num9 / 2.0)) + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSe + rsFiCtpResult1.MeasureSe) * (num9 / 2.0)) : rsFiCtpResult1.TrapezeRuleUnderALine + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSe + rsFiCtpResult1.MeasureSe) * (num9 / 2.0))) : rsFiCtpResult1.TrapezeRuleUnderALine + Math.Abs(0.5 * (rsFiCtpResult2.MeasureSs + rsFiCtpResult1.MeasureSs) * (num9 / 2.0))) : rsFiCtpResult1.TrapezeRuleUnderALine;
        rsFiCtpResult2.Ai = rsFiCtpResult2.Aoi - rsFiCtpResult2.TrapezeRuleUnderALine;
        rsFiCtpResult2.Mti = rsFiCtpResult2.Lambdai > 20.0 ? 0.71709999999999985 + 0.2909 * Math.Pow(20.0, 2.0) - 0.09642 * Math.Pow(20.0, 3.0) + 0.02089 * Math.Pow(20.0, 4.0) - 0.003054 * Math.Pow(20.0, 5.0) + 2.957 * Math.Pow(10.0, -4.0) * Math.Pow(20.0, 6.0) - 1.8462 * Math.Pow(10.0, -5.0) * Math.Pow(20.0, 7.0) + 7.1553 * Math.Pow(10.0, -7.0) * Math.Pow(20.0, 8.0) - 1.5631 * Math.Pow(10.0, -8.0) * Math.Pow(20.0, 9.0) + 916.0 / 625.0 * Math.Pow(10.0, -10.0) * Math.Pow(20.0, 10.0) : 1001.0 / 1000.0 - 0.014195 * rsFiCtpResult2.Lambdai + 0.2909 * Math.Pow(rsFiCtpResult2.Lambdai, 2.0) - 0.09642 * Math.Pow(rsFiCtpResult2.Lambdai, 3.0) + 0.02089 * Math.Pow(rsFiCtpResult2.Lambdai, 4.0) - 0.003054 * Math.Pow(rsFiCtpResult2.Lambdai, 5.0) + 2.957 * Math.Pow(10.0, -4.0) * Math.Pow(rsFiCtpResult2.Lambdai, 6.0) - 1.8462 * Math.Pow(10.0, -5.0) * Math.Pow(rsFiCtpResult2.Lambdai, 7.0) + 7.1553 * Math.Pow(10.0, -7.0) * Math.Pow(rsFiCtpResult2.Lambdai, 8.0) - 1.5631 * Math.Pow(10.0, -8.0) * Math.Pow(rsFiCtpResult2.Lambdai, 9.0) + 916.0 / 625.0 * Math.Pow(10.0, -10.0) * Math.Pow(rsFiCtpResult2.Lambdai, 10.0);
        rsFiCtpResult2.RSFi = (1.0 - rsFiCtpResult2.Ai / rsFiCtpResult2.Aoi) / (1.0 - rsFiCtpResult2.Ai / rsFiCtpResult2.Aoi / rsFiCtpResult2.Mti);
        int num10 = flag1 ? 1 : 0;
        rsFiCtpResult1 = rsFiCtpResult2;
        ctp.results.Add(rsFiCtpResult2);
        ++num7;
        if (index1 >= (double) num2)
          --index1;
        else
          flag2 = true;
        if (index2 <= (double) num4)
          ++index2;
        else
          flag3 = true;
      }
      ++num5;
      this.RSFs.Add(ctp.GetRSF());
    }
  }

  public double GetLevel2RSF() => this.RSFs.Count > 0 ? this.RSFs.Min() : 1.0;
}
