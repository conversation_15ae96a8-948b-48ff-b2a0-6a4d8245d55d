// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.Thickness.RSFiCTPResult
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.Thickness;

internal class RSFiCTPResult
{
  public double ss { get; set; }

  public double se { get; set; }

  public double si { get; set; }

  public double Lambdai { get; set; }

  public double Aoi { get; set; }

  public double MeasureSe { get; set; }

  public double MeasureSs { get; set; }

  public double TrapezeRuleUnderALine { get; set; }

  public double Ai { get; set; }

  public double Mti { get; set; }

  public double RSFi { get; set; }
}
