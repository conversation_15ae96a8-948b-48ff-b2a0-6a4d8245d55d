// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.Thickness.CTPComparer
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.Thickness;

internal class CTPComparer : IComparer<CTP>
{
  public int Compare(CTP x, CTP y)
  {
    if (x.ctp > y.ctp)
      return 1;
    return x.ctp == y.ctp ? (x.Index < y.Index ? 1 : 0) : (x.ctp < y.ctp ? -1 : 0);
  }
}
