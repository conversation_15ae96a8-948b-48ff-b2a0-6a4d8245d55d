// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.Thickness.TRDCalculation
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.Thickness;

public class TRDCalculation
{
  public double? NominalThickness { get; set; }

  public double? ExternalUniformMetalLoss { get; set; }

  public double? InternalUniformMetalLoss { get; set; }

  public double? CalculateTRD()
  {
    double? trd = new double?();
    if (this.NominalThickness.HasValue && this.ExternalUniformMetalLoss.HasValue && this.InternalUniformMetalLoss.HasValue)
    {
      double? nominalThickness = this.NominalThickness;
      double? uniformMetalLoss1 = this.ExternalUniformMetalLoss;
      double? nullable = nominalThickness.HasValue & uniformMetalLoss1.HasValue ? new double?(nominalThickness.GetValueOrDefault() - uniformMetalLoss1.GetValueOrDefault()) : new double?();
      double? uniformMetalLoss2 = this.InternalUniformMetalLoss;
      trd = nullable.HasValue & uniformMetalLoss2.HasValue ? new double?(nullable.GetValueOrDefault() - uniformMetalLoss2.GetValueOrDefault()) : new double?();
    }
    return trd;
  }
}
