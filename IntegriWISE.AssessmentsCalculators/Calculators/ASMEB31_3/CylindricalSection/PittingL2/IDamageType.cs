// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.PittingL2.IDamageType
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.PittingL2;

public interface IDamageType
{
  double tc { get; }

  double Rt { get; }

  double Lambda { get; }

  double LambdaTable5_2 { get; }

  double Mt { get; }

  double RSFlta { get; }

  bool RationCondition { get; }

  bool TmmCondition { get; }

  bool LmsdCondition { get; }

  double? RSFcomb { get; }

  double LambdaC { get; }

  bool LambdaCLessThan9 { get; }

  bool DOverTcGreaterThan20 { get; }

  bool RSFBetween0_7And1 { get; }

  bool ElBetween0_7And1 { get; }

  bool EcBetween0_7And2 { get; }

  bool ConditionCircumferentialExtent { get; }

  double? TSF { get; }
}
