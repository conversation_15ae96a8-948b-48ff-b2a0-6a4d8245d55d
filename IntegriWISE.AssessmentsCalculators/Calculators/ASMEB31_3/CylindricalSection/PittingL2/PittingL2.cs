// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.PittingL2.PittingL2
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.AssessmentData;
using IntegriWISE.DataTransferObjects.Lookup;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.PittingL2;

public class PittingL2 : BaseCalculator
{
  private const double RSFa = 0.9;
  private List<MaterialASMEB31_3StressValueDTO> _allowableStrengths;
  private List<MaterialASMEB31_3_Coefficient_YDTO> _allowableStrenghtsByType;
  private TemperatureRangeDTO _minDesignTemperature;
  private MaterialASMEB31_3DTO _material;
  private List<MaterialTSFCurveDTO> _api579Table5_4List;

  public double NominalThickness { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public bool MAWPLevel { get; set; }

  public double MAWP { get; set; }

  public double NominalOutsideDiameter { private get; set; }

  public List<PittingDetailDTO> OutsideCouples { private get; set; }

  public List<PittingDetailDTO> InsideCouples { private get; set; }

  public PittingDamageSide DamageSide { private get; set; }

  public PittingDamageType DamageType { private get; set; }

  public double? Lmsd { get; set; }

  public double? Tmm { get; set; }

  public double? LTAs { get; set; }

  public double? LTAc { get; set; }

  public double? EqLTAs { get; set; }

  public double? EqLTAc { get; set; }

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double? YieldStrengthNew { get; set; }

  public double? TensileStrengthNew { get; set; }

  public double SupplementalThickness { get; set; }

  public double LongitudinalWeldJointEfficiency { get; set; }

  public double CircumferentialWeldJointEfficiency { get; set; }

  public double MechanicalAllowances { get; set; }

  public double RSFpit { get; private set; }

  public double Rt { get; private set; }

  public double RSFlta { get; private set; }

  public double Lambda { get; private set; }

  public double LambdaTable5_2 { get; private set; }

  public double Mt { get; private set; }

  public bool RationCondition { get; private set; }

  public bool TmmCondition { get; private set; }

  public bool LmsdCondition { get; private set; }

  public double Diameter { get; private set; }

  public double FCA { get; private set; }

  public double? RSFcomb { get; private set; }

  public List<PittingCoupleRanked> InsideRankedCouples { get; private set; }

  public List<PittingCoupleRanked> OutsideRankedCouples { get; private set; }

  public bool Level2Passed { get; private set; }

  public double LambdaC { get; private set; }

  public bool? LambdaCLessThan9 { get; private set; }

  public bool? DOverTcGreaterThan20 { get; private set; }

  public bool? RSFBetween0_7And1 { get; private set; }

  public bool? ElBetween0_7And1 { get; private set; }

  public bool? EcBetween0_7And2 { get; private set; }

  public bool? ConditionCircumferentialExtent { get; private set; }

  public double? TSF { get; private set; }

  public double LambdaCMinus0_2 { get; private set; }

  public double C1 { get; private set; }

  public double C2 { get; private set; }

  public double C3 { get; private set; }

  public double C4 { get; private set; }

  public double C5 { get; private set; }

  public double C6 { get; private set; }

  public double C7 { get; private set; }

  public double RtFigure5_8 { get; private set; }

  public bool? ScreeningCriteriaFigure5_8 { get; private set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public PittingL2(
    MaterialASMEB31_3DTO material,
    TemperatureRangeDTO minDesignTemperature,
    List<MaterialASMEB31_3StressValueDTO> allowableStrengths,
    List<MaterialASMEB31_3_Coefficient_YDTO> allowableStrenghtsByType,
    List<MaterialTSFCurveDTO> api579Table5_4List)
  {
    this.Warnings = new List<string>();
    this._material = material;
    this._minDesignTemperature = minDesignTemperature;
    this._allowableStrengths = allowableStrengths;
    this._allowableStrenghtsByType = allowableStrenghtsByType;
    this._api579Table5_4List = api579Table5_4List;
  }

  public CalculatorResult CalculateAssessment()
  {
    IDamageType damageCalculator = this.createDamageCalculator();
    double num = this.InternalUniformMetalLoss + this.InternalFutureCorrosionAllowance + this.ExternalUniformMetalLoss + this.ExternalFutureCorrosionAllowance;
    double tc = damageCalculator != null ? damageCalculator.tc : this.NominalThickness - num;
    this.FCA = this.ExternalFutureCorrosionAllowance + this.InternalFutureCorrosionAllowance;
    this.Diameter = this.NominalOutsideDiameter;
    if (this.DamageSide == PittingDamageSide.Both && this.getMaxDepth(this.InsideCouples) + this.getMaxDepth(this.OutsideCouples) > this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss - this.FCA)
    {
      this.ErrorMessage = "Pitting damage is overlapped from both surfaces. It is UNACCEPTABLE for Level 2. Level 3 Assessment is required.";
      return CalculatorResult.Fail;
    }
    if (!this.checkInputsForAllPits(tc, this.FCA, this.Diameter, this.InsideCouples) || !this.checkInputsForAllPits(tc, this.FCA, this.Diameter, this.OutsideCouples))
      return CalculatorResult.Fail;
    IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.MembraneStress.MembraneStress membraneCalculator = this.getMembraneCalculator();
    if (membraneCalculator.CalculateAssessment() == CalculatorResult.Fail)
    {
      this.ErrorMessage = membraneCalculator.ErrorMessage;
      return CalculatorResult.Fail;
    }
    this.Warnings.AddRange((IEnumerable<string>) membraneCalculator.Warnings);
    double sigmaCm = membraneCalculator.SigmaCm;
    double sigmaLm = membraneCalculator.SigmaLm;
    this.InsideRankedCouples = this.buildRankableCouples(this.InsideCouples, false);
    this.OutsideRankedCouples = this.buildRankableCouples(this.OutsideCouples, true);
    this.calculateEavg(this.InsideRankedCouples, sigmaCm, sigmaLm);
    this.calculateEavg(this.OutsideRankedCouples, sigmaCm, sigmaLm);
    if (this.DamageSide == PittingDamageSide.Both)
      this.calculateRSF(this.InsideRankedCouples, this.OutsideRankedCouples, this.calculateLayers(this.InsideRankedCouples, this.OutsideRankedCouples), tc);
    else if (this.DamageSide == PittingDamageSide.Inside)
      this.calculateRSF(this.InsideRankedCouples, tc);
    else
      this.calculateRSF(this.OutsideRankedCouples, tc);
    this.RSFpit = (this.InsideRankedCouples.Sum<PittingCoupleRanked>((Func<PittingCoupleRanked, double>) (x => x.RSF)) + this.OutsideRankedCouples.Sum<PittingCoupleRanked>((Func<PittingCoupleRanked, double>) (x => x.RSF))) / (double) (this.InsideRankedCouples.Count + this.OutsideRankedCouples.Count);
    if (damageCalculator != null)
    {
      this.Rt = damageCalculator.Rt;
      this.Lambda = damageCalculator.Lambda;
      this.LambdaTable5_2 = damageCalculator.LambdaTable5_2;
      this.Mt = damageCalculator.Mt;
      this.RSFlta = damageCalculator.RSFlta;
      this.RationCondition = damageCalculator.RationCondition;
      this.TmmCondition = damageCalculator.TmmCondition;
      this.LmsdCondition = damageCalculator.LmsdCondition;
      this.RSFcomb = damageCalculator.RSFcomb;
      this.LambdaC = damageCalculator.LambdaC;
      this.LambdaCLessThan9 = new bool?(damageCalculator.LambdaCLessThan9);
      this.DOverTcGreaterThan20 = new bool?(damageCalculator.DOverTcGreaterThan20);
      this.RSFBetween0_7And1 = new bool?(damageCalculator.RSFBetween0_7And1);
      this.ElBetween0_7And1 = new bool?(damageCalculator.ElBetween0_7And1);
      this.EcBetween0_7And2 = new bool?(damageCalculator.EcBetween0_7And2);
      this.ConditionCircumferentialExtent = new bool?(damageCalculator.ConditionCircumferentialExtent);
      this.TSF = damageCalculator.TSF;
      RtFigure5_8Result RtFigure5_8 = new RtFigure5_8Result();
      string errorMessage;
      if (!this.GetRtFigure5_8(this._api579Table5_4List, this.TSF.Value, this.LambdaC, RtFigure5_8, out errorMessage))
      {
        this.ErrorMessage = errorMessage;
        return CalculatorResult.Fail;
      }
      this.LambdaCMinus0_2 = RtFigure5_8.LambdaCMinus0_2;
      this.C1 = RtFigure5_8.C1;
      this.C2 = RtFigure5_8.C2;
      this.C3 = RtFigure5_8.C3;
      this.C4 = RtFigure5_8.C4;
      this.C5 = RtFigure5_8.C5;
      this.C6 = RtFigure5_8.C6;
      this.RtFigure5_8 = RtFigure5_8.RtFigure5_8;
      this.ScreeningCriteriaFigure5_8 = new bool?(this.Rt >= this.RtFigure5_8);
    }
    return CalculatorResult.Completed;
  }

  private IDamageType createDamageCalculator()
  {
    switch (this.DamageType)
    {
      case PittingDamageType.Localized:
        return (IDamageType) new DamageTypeLocalized(this);
      case PittingDamageType.WidespreadLOSS:
        return (IDamageType) new DamageTypeWidespreadLOSS(this);
      case PittingDamageType.LocalizedLOSS:
        return (IDamageType) new DamageTypeLocalizedLOSS(this);
      default:
        return (IDamageType) null;
    }
  }

  private void calculateRSF(List<PittingCoupleRanked> rankedCouples, double tc)
  {
    foreach (PittingCoupleRanked rankedCouple in rankedCouples)
      rankedCouple.RSF = 1.0 - rankedCouple.wAvg.Value / tc * (1.0 - rankedCouple.Eavg.Value);
  }

  private void calculateRSF(
    List<PittingCoupleRanked> insideRankedCouples,
    List<PittingCoupleRanked> outsideRankedCouples,
    List<Layer> layers,
    double tc)
  {
    using (List<PittingCoupleRanked>.Enumerator enumerator = insideRankedCouples.GetEnumerator())
    {
      while (enumerator.MoveNext())
      {
        PittingCoupleRanked couple = enumerator.Current;
        double num = layers.Sum<Layer>((Func<Layer, double>) (x => x.Thickness / tc * (1.0 - couple.EavgLayer)));
        couple.RSF = 1.0 - num;
      }
    }
    using (List<PittingCoupleRanked>.Enumerator enumerator = outsideRankedCouples.GetEnumerator())
    {
      while (enumerator.MoveNext())
      {
        PittingCoupleRanked couple = enumerator.Current;
        layers.Sum<Layer>((Func<Layer, double>) (x => x.Thickness / tc * (1.0 - couple.EavgLayer)));
        couple.RSF = 1.0 - layers.Sum<Layer>((Func<Layer, double>) (x => x.Thickness / tc * (1.0 - couple.EavgLayer)));
      }
    }
  }

  private List<Layer> calculateLayers(
    List<PittingCoupleRanked> insideRankedCouples,
    List<PittingCoupleRanked> outsideRankedCouples)
  {
    int num = insideRankedCouples.Count + outsideRankedCouples.Count + 1;
    List<Layer> layers = this.buildThicknessLayers(insideRankedCouples);
    layers.Add(new Layer()
    {
      Thickness = insideRankedCouples[insideRankedCouples.Count - 1].MaxDepth - outsideRankedCouples[0].MaxDepth
    });
    layers.AddRange((IEnumerable<Layer>) this.buildThicknessLayers(outsideRankedCouples));
    if (num != layers.Count)
      throw new SystemException("Unable to create thickness layers");
    this.calculateEavgLayer(insideRankedCouples, outsideRankedCouples, layers);
    return layers;
  }

  private void calculateEavgLayer(
    List<PittingCoupleRanked> insideRankedCouples,
    List<PittingCoupleRanked> outsideRankedCouples,
    List<Layer> layers)
  {
    double num = 0.0;
    int index1 = 0;
    for (int index2 = 0; index2 < insideRankedCouples.Count; ++index2)
    {
      PittingCoupleRanked insideRankedCouple = insideRankedCouples[index2];
      num += layers[index1].Thickness;
      insideRankedCouple.EavgLayer = insideRankedCouple.MaxDepth >= num ? insideRankedCouple.Eavg.Value : 1.0;
      ++index1;
    }
    for (int index3 = 0; index3 < outsideRankedCouples.Count; ++index3)
    {
      PittingCoupleRanked outsideRankedCouple = outsideRankedCouples[index3];
      num += layers[index1].Thickness;
      outsideRankedCouple.EavgLayer = this.NominalThickness - outsideRankedCouple.MaxDepth < num ? outsideRankedCouple.Eavg.Value : 1.0;
      ++index1;
    }
  }

  private List<Layer> buildThicknessLayers(List<PittingCoupleRanked> rankedCouples)
  {
    List<Layer> layerList = new List<Layer>();
    layerList.Add(new Layer()
    {
      Thickness = rankedCouples[0].MaxDepth
    });
    for (int index = 0; index < rankedCouples.Count - 1; ++index)
    {
      PittingCoupleRanked rankedCouple1 = rankedCouples[index];
      PittingCoupleRanked rankedCouple2 = rankedCouples[index + 1];
      layerList.Add(new Layer()
      {
        Thickness = rankedCouple2.MaxDepth - rankedCouple1.MaxDepth
      });
    }
    return layerList;
  }

  private List<PittingCoupleRanked> buildRankableCouples(
    List<PittingDetailDTO> couples,
    bool isDesc)
  {
    List<PittingCoupleRanked> source = new List<PittingCoupleRanked>();
    foreach (PittingDetailDTO couple in couples)
    {
      double num = Math.Max(couple.Wi, couple.Wj);
      source.Add(new PittingCoupleRanked(couple)
      {
        MaxDepth = num
      });
    }
    return isDesc ? source.OrderByDescending<PittingCoupleRanked, double>((Func<PittingCoupleRanked, double>) (x => x.MaxDepth)).ToList<PittingCoupleRanked>() : source.OrderBy<PittingCoupleRanked, double>((Func<PittingCoupleRanked, double>) (x => x.MaxDepth)).ToList<PittingCoupleRanked>();
  }

  private void calculateEavg(List<PittingCoupleRanked> couples, double sigmaCm, double sigmaLm)
  {
    foreach (PittingCoupleRanked couple in couples)
    {
      double num1 = (couple.Wi + couple.Wj) / 2.0;
      double num2 = (couple.Di + couple.Dj) / 2.0;
      double num3 = (couple.DistanceP - num2) / couple.DistanceP;
      double x1 = sigmaCm / num3;
      double x2 = sigmaLm / num3;
      double num4 = Math.Pow(Math.Sin(2.0 * couple.AngleTeta * Math.PI / 180.0), 2.0);
      double num5 = Math.Pow(x1, 2.0);
      double num6 = Math.Pow(x2, 2.0);
      double d = (Math.Pow(Math.Cos(couple.AngleTeta * Math.PI / 180.0), 4.0) + num4) * num5 - 3.0 * num4 * x1 * x2 / 2.0 + (Math.Pow(Math.Sin(couple.AngleTeta * Math.PI / 180.0), 4.0) + num4) * num6;
      double num7 = num3 * Math.Max(Math.Max(Math.Abs(x1), Math.Abs(x2)), Math.Abs(x1 - x2));
      couple.Eavg = new double?(Math.Min(num7 / Math.Sqrt(d), 1.0));
      couple.wAvg = new double?(num1);
    }
  }

  private IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.MembraneStress.MembraneStress getMembraneCalculator()
  {
    return new IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.MembraneStress.MembraneStress(this._material, this._minDesignTemperature, this._allowableStrengths, this._allowableStrenghtsByType)
    {
      YieldStrengthNew = this.YieldStrengthNew,
      TensileStrengthNew = this.TensileStrengthNew,
      DesignTemperature = this.DesignTemperature,
      DesignPressure = this.DesignPressure,
      InternalFutureCorrosionAllowance = this.InternalFutureCorrosionAllowance,
      ExternalFutureCorrosionAllowance = this.ExternalFutureCorrosionAllowance,
      InternalUniformMetalLoss = this.InternalUniformMetalLoss,
      ExternalUniformMetalLoss = this.ExternalUniformMetalLoss,
      NominalThickness = this.NominalThickness,
      LongitudinalWeldJointEfficiency = this.LongitudinalWeldJointEfficiency,
      CircumferentialWeldJointEfficiency = this.CircumferentialWeldJointEfficiency,
      NominalOutsideDiameter = this.NominalOutsideDiameter,
      MechanicalAllowances = this.MechanicalAllowances,
      SupplementalThickness = this.SupplementalThickness
    };
  }

  private double getMaxDepth(List<PittingDetailDTO> couples)
  {
    return couples.Max<PittingDetailDTO>((Func<PittingDetailDTO, double>) (x => Math.Max(x.Wi, x.Wj)));
  }

  private bool checkInputsForAllPits(
    double tc,
    double FCA,
    double diameter,
    List<PittingDetailDTO> couples)
  {
    int coupleIndex = 0;
    foreach (PittingDetailDTO couple in couples)
    {
      if (!this.checkPit(tc, FCA, diameter, couple.Di, couple.Wi, ++coupleIndex, "i") || !this.checkPit(tc, FCA, diameter, couple.Dj, couple.Wj, coupleIndex, "j"))
        return false;
    }
    return true;
  }

  private bool checkPit(
    double tc,
    double FCA,
    double diameter,
    double D,
    double W,
    int coupleIndex,
    string pitName)
  {
    double num1 = (tc + FCA - W) / tc;
    double num2 = 50.0;
    if (num1 < 0.9)
      num2 = 1.123 * Math.Pow(Math.Pow((1.0 - num1) / (1.0 - num1 / 0.9), 2.0) - 1.0, 0.5);
    if (D > num2 * Math.Sqrt(diameter * tc))
    {
      this.ErrorMessage = $"Pit couple's {coupleIndex} pit '{pitName}' is too large and it should be evaluated as a LTA using methods of Part 5 to ensure that a local ligament failure at the base of the pit does not occur. The recommended limitations on the individual pit dimensions are not satisfied, then the assessment fails.";
      return false;
    }
    if (num1 >= 0.2)
      return true;
    this.ErrorMessage = $"Rt>=0.20 limit is recommended to prevent a local failure characterized by pinhole type leakage. Pit couple's {coupleIndex} pit '{pitName}'  doesn’t meet this limit. The recommended limitations on the individual pit dimensions are not satisfied, then the assessment fails.";
    return false;
  }

  public bool RSFPitMoreEqualRSFa => this.RSFpit >= 0.9;

  public bool RSFltaGreaterRSFa => this.RSFlta >= 0.9;

  public bool RSFcombMoreEqualRSFa => this.RSFcomb.Value >= 0.9;

  public string LongitudinalExtentDamageType2
  {
    get
    {
      if (!this.RationCondition || !this.TmmCondition || !this.LmsdCondition)
        return "The longitudinal extent of the pitting damage is UNACCEPTABLE";
      return this.RSFltaGreaterRSFa ? "The longitudinal extent of the pitting damage is ACCEPTABLE for operation at MAWP" : "The longitudinal extent of the pitting damage is unacceptable for operation at MAWP but it is acceptable for operation at the MAWPr";
    }
  }

  public string CircumferentialExtentDamageType2
  {
    get
    {
      return this.ConditionCircumferentialExtent.Value && this.ScreeningCriteriaFigure5_8.Value ? "The circumferential extent of the pitting damage is ACCEPTABLE" : "The circumferential extent of the pitting damage is UNACCEPTABLE";
    }
  }

  public string LongitudinalExtentDamageType3And4
  {
    get
    {
      return this.RationCondition && this.TmmCondition && this.LmsdCondition ? "The longitudinal extent of the LTA is ACCEPTABLE" : "The longitudinal extent of the LTA is UNACCEPTABLE";
    }
  }

  public string CircumferentialExtentDamageType3And4
  {
    get
    {
      return this.ConditionCircumferentialExtent.Value && this.ScreeningCriteriaFigure5_8.Value ? "The circumferential extent of the LTA is ACCEPTABLE" : "The circumferential extent of the LTA is UNACCEPTABLE";
    }
  }

  public string Level2ConclusionType1
  {
    get
    {
      return !this.RSFPitMoreEqualRSFa ? "Level 2 Assessment is UNACCEPTABLE" : "Level 2 Assessment is ACCEPTABLE";
    }
  }

  public string Level2ConclusionType2
  {
    get
    {
      if (this.ConditionCircumferentialExtent.Value && this.ScreeningCriteriaFigure5_8.Value)
      {
        if (this.RationCondition && this.TmmCondition && this.LmsdCondition)
        {
          if (this.RSFltaGreaterRSFa)
          {
            this.Level2Passed = true;
            return "Level 2 Assessment is ACCEPTABLE";
          }
          this.Level2Passed = false;
          return "Level 2 Assessment is unacceptable for operation at MAWP but the component can operate at MAWPr";
        }
        this.Level2Passed = false;
        return "Level 2 Assessment is UNACCEPTABLE";
      }
      this.Level2Passed = false;
      return "Level 2 Assessment is UNACCEPTABLE";
    }
  }

  public string Level2ConclusionType3And4
  {
    get
    {
      if (this.ConditionCircumferentialExtent.Value && this.ScreeningCriteriaFigure5_8.Value)
      {
        if (this.RationCondition && this.TmmCondition && this.LmsdCondition)
        {
          if (this.RSFcombMoreEqualRSFa)
          {
            this.Level2Passed = true;
            return "Level 2 Assessment is ACCEPTABLE";
          }
          this.Level2Passed = false;
          return "Level 2 Assessment is unacceptable for operation at MAWP but the component can operate at MAWPr";
        }
        this.Level2Passed = false;
        return "Level 2 Assessment is UNACCEPTABLE";
      }
      this.Level2Passed = false;
      return "Level 2 Assessment is UNACCEPTABLE";
    }
  }

  public double MAWPr
  {
    get
    {
      switch (this.DamageType)
      {
        case PittingDamageType.Widespread:
          return this.MAWP * (this.RSFpit / 0.9);
        case PittingDamageType.Localized:
          return this.MAWP * (this.RSFlta / 0.9);
        default:
          return this.MAWP * (this.RSFcomb.Value / 0.9);
      }
    }
  }
}
