// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.PittingL2.DamageTypeLocalizedLOSS
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.PittingL2;

public class DamageTypeLocalizedLOSS : IDamageType
{
  private IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.PittingL2.PittingL2 level2Values;

  public DamageTypeLocalizedLOSS(IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.PittingL2.PittingL2 level2)
  {
    this.level2Values = level2;
  }

  public double Rt => (this.level2Values.Tmm.Value - this.level2Values.FCA) / this.tc;

  public double Lambda
  {
    get => 1.285 * this.level2Values.EqLTAs.Value / Math.Sqrt(this.level2Values.Diameter * this.tc);
  }

  public double LambdaTable5_2
  {
    get
    {
      double lambda = this.Lambda;
      return lambda <= 20.0 ? lambda : 20.0;
    }
  }

  public double Mt
  {
    get
    {
      double lambdaTable52 = this.LambdaTable5_2;
      return 1001.0 / 1000.0 - 0.014195 * lambdaTable52 + 0.2909 * Math.Pow(lambdaTable52, 2.0) - 0.09642 * Math.Pow(lambdaTable52, 3.0) + 0.02089 * Math.Pow(lambdaTable52, 4.0) - 0.003054 * Math.Pow(lambdaTable52, 5.0) + 2.957 * Math.Pow(10.0, -4.0) * Math.Pow(lambdaTable52, 6.0) - 1.8462 * Math.Pow(10.0, -5.0) * Math.Pow(lambdaTable52, 7.0) + 7.1553 * Math.Pow(10.0, -7.0) * Math.Pow(lambdaTable52, 8.0) - 1.5631 * Math.Pow(10.0, -8.0) * Math.Pow(lambdaTable52, 9.0) + 916.0 / 625.0 * Math.Pow(10.0, -10.0) * Math.Pow(lambdaTable52, 10.0);
    }
  }

  public double RSFlta
  {
    get
    {
      double mt = this.Mt;
      double rt = this.Rt;
      return rt / (1.0 - 1.0 / mt * (1.0 - rt));
    }
  }

  public bool RationCondition => this.Rt >= 0.2;

  public bool TmmCondition => this.level2Values.Tmm.Value - this.level2Values.FCA >= 2.5;

  public bool LmsdCondition
  {
    get
    {
      double? lmsd = this.level2Values.Lmsd;
      double num = 1.8 * Math.Sqrt(this.level2Values.Diameter * this.tc);
      return lmsd.GetValueOrDefault() >= num && lmsd.HasValue;
    }
  }

  public double tc
  {
    get
    {
      return (this.level2Values.NominalThickness - this.level2Values.InternalUniformMetalLoss - this.level2Values.ExternalUniformMetalLoss) * this.RSFlta;
    }
  }

  public double? RSFcomb => new double?(this.level2Values.RSFpit * this.RSFlta);

  public double LambdaC
  {
    get => 1.285 * this.level2Values.EqLTAc.Value / Math.Sqrt(this.level2Values.Diameter * this.tc);
  }

  public bool LambdaCLessThan9 => this.LambdaC <= 9.0;

  public bool DOverTcGreaterThan20 => this.level2Values.Diameter / this.tc >= 20.0;

  public bool RSFBetween0_7And1
  {
    get
    {
      double? rsFcomb1 = this.RSFcomb;
      if ((rsFcomb1.GetValueOrDefault() < 0.7 ? 0 : (rsFcomb1.HasValue ? 1 : 0)) == 0)
        return false;
      double? rsFcomb2 = this.RSFcomb;
      return rsFcomb2.GetValueOrDefault() <= 1.0 && rsFcomb2.HasValue;
    }
  }

  public bool ElBetween0_7And1
  {
    get
    {
      return this.level2Values.LongitudinalWeldJointEfficiency >= 0.7 && this.level2Values.LongitudinalWeldJointEfficiency <= 1.0;
    }
  }

  public bool EcBetween0_7And2
  {
    get
    {
      return this.level2Values.CircumferentialWeldJointEfficiency >= 0.7 && this.level2Values.CircumferentialWeldJointEfficiency <= 1.0;
    }
  }

  public bool ConditionCircumferentialExtent
  {
    get
    {
      return this.LambdaCLessThan9 && this.DOverTcGreaterThan20 && this.RSFBetween0_7And1 && this.ElBetween0_7And1 && this.EcBetween0_7And2;
    }
  }

  public double? TSF
  {
    get
    {
      return new double?(this.level2Values.CircumferentialWeldJointEfficiency / (2.0 * this.RSFlta) * (1.0 + Math.Sqrt(4.0 - 3.0 * Math.Pow(this.level2Values.LongitudinalWeldJointEfficiency, 2.0)) / this.level2Values.LongitudinalWeldJointEfficiency));
    }
  }
}
