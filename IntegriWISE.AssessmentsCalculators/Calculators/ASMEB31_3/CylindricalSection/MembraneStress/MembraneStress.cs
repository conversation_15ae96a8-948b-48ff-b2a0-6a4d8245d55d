// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.MembraneStress.MembraneStress
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.MembraneStress;

public class MembraneStress : BaseCalculator
{
  private List<MaterialASMEB31_3StressValueDTO> _allowableStrengths;
  private List<MaterialASMEB31_3_Coefficient_YDTO> _allowableStrenghtsByType;
  private TemperatureRangeDTO _minDesignTemperature;
  private MaterialASMEB31_3DTO _material;

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double? YieldStrengthNew { get; set; }

  public double? TensileStrengthNew { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double NominalThickness { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double SupplementalThickness { get; set; }

  public double LongitudinalWeldJointEfficiency { get; set; }

  public double CircumferentialWeldJointEfficiency { get; set; }

  public double MechanicalAllowances { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double SigmaCm { get; private set; }

  public double SigmaLm { get; private set; }

  public double SigmaM { get; private set; }

  public MembraneStress(
    MaterialASMEB31_3DTO material,
    TemperatureRangeDTO minDesignTemperature,
    List<MaterialASMEB31_3StressValueDTO> allowableStrengths,
    List<MaterialASMEB31_3_Coefficient_YDTO> allowableStrenghtsByType)
  {
    this._material = material;
    this._minDesignTemperature = minDesignTemperature;
    this._allowableStrengths = allowableStrengths;
    this._allowableStrenghtsByType = allowableStrenghtsByType;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    double tMin = 0.0;
    double tMax = 0.0;
    double? yUpper = new double?(0.0);
    double? yLower = new double?(0.0);
    if (this._material.StressTemperatureLimit.HasValue)
    {
      double designTemperature = this.DesignTemperature;
      double? temperatureLimit = this._material.StressTemperatureLimit;
      if ((designTemperature < temperatureLimit.GetValueOrDefault() ? 0 : (temperatureLimit.HasValue ? 1 : 0)) != 0)
        this.Warnings.Add("Strength value exceeds two-thirds of the expected yield strength at this temperature.See paragraph 302.3.2(d)(3) and ( e) ASME B31.3");
    }
    string minimumTemperatureC = this._material.MinimumTemperatureC;
    double num1;
    if (!this.IsNumeric(minimumTemperatureC))
    {
      double num2 = this._minDesignTemperature.TLower.Value;
      double num3 = this._minDesignTemperature.TUpper.Value;
      double num4 = this._minDesignTemperature.SUpper.Value;
      double num5 = this._minDesignTemperature.SLower.Value;
      num1 = (this.NominalThickness - num2) / (num3 - num2) * (num4 - num5) + num5;
    }
    else
      num1 = double.Parse(minimumTemperatureC);
    if (this.DesignTemperature < num1)
      this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress value at the lowest listed temperature of this material (ASME B31.3) to calculate tmin.");
    MaterialStrengthLookupResult strengthResult = this.getStrengthResult(this.DesignTemperature, num1);
    if (strengthResult.Status == "Above Range")
    {
      this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material.";
      return CalculatorResult.Fail;
    }
    double tupper = strengthResult.TUpper;
    double tlower = strengthResult.TLower;
    double slower = strengthResult.SLower;
    double supper = strengthResult.SUpper;
    if (this.DesignTemperature < num1)
      Utilities.GetB_36_1Coefficient(this._material.Material, num1, out tMin, out tMax, out yLower, out yUpper);
    else
      Utilities.GetB_36_1Coefficient(this._material.Material, this.DesignTemperature, out tMin, out tMax, out yLower, out yUpper);
    double num6 = (this.DesignTemperature - tMin) / (tMax - tMin) * (yUpper.Value - yLower.Value) + yLower.Value;
    double num7 = this.NominalOutsideDiameter - 2.0 * (this.ExternalFutureCorrosionAllowance + this.ExternalUniformMetalLoss);
    double num8 = this.NominalThickness - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance - this.InternalFutureCorrosionAllowance - this.InternalUniformMetalLoss;
    double num9 = num8 < num7 / 6.0 ? num6 : (this.NominalOutsideDiameter - 2.0 * this.NominalThickness + 2.0 * this.MechanicalAllowances) / (this.NominalOutsideDiameter + this.NominalOutsideDiameter - 2.0 * this.NominalThickness + 2.0 * this.MechanicalAllowances);
    this.SigmaCm = this.DesignPressure / this.LongitudinalWeldJointEfficiency * (num7 / (2.0 * (num8 - this.MechanicalAllowances)) - num9);
    this.SigmaLm = this.DesignPressure / this.CircumferentialWeldJointEfficiency * (num7 / (4.0 * (num8 - this.SupplementalThickness - this.MechanicalAllowances)) - num9);
    this.SigmaM = Math.Max(this.SigmaCm, this.SigmaLm);
    return CalculatorResult.Completed;
  }

  private MaterialTypeStrengthLookupResult getStrengthByTypeResult(double temperature)
  {
    double num1 = 0.0;
    string str = "In Range";
    MaterialASMEB31_3_Coefficient_YDTO obj = this._allowableStrenghtsByType.Where<MaterialASMEB31_3_Coefficient_YDTO>((Func<MaterialASMEB31_3_Coefficient_YDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.Tlower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.Tupper;
      return num3 < tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEB31_3_Coefficient_YDTO, double?>((Func<MaterialASMEB31_3_Coefficient_YDTO, double?>) (x => x.Tlower)).FirstOrDefault<MaterialASMEB31_3_Coefficient_YDTO>() ?? this._allowableStrenghtsByType.Where<MaterialASMEB31_3_Coefficient_YDTO>((Func<MaterialASMEB31_3_Coefficient_YDTO, bool>) (x =>
    {
      double num4 = temperature;
      double? tupper = x.Tupper;
      return num4 == tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEB31_3_Coefficient_YDTO, double?>((Func<MaterialASMEB31_3_Coefficient_YDTO, double?>) (x => x.Tlower)).FirstOrDefault<MaterialASMEB31_3_Coefficient_YDTO>();
    if (obj == null)
    {
      num1 = this._allowableStrengths.Max<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TUpper)).Value;
      str = "Above Range";
      if (num1 < temperature)
        obj = new MaterialASMEB31_3_Coefficient_YDTO()
        {
          Tlower = new double?(0.0),
          Tupper = new double?(0.0),
          Tlower_SI = new double?(0.0),
          Tupper_SI = new double?(0.0),
          Ylower = new double?(0.0),
          Yupper = new double?(0.0)
        };
    }
    if (obj == null)
    {
      num1 = this._allowableStrengths.Min<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TLower)).Value;
      str = "Below Range";
      if (num1 > temperature)
        obj = new MaterialASMEB31_3_Coefficient_YDTO()
        {
          Tlower = new double?(0.0),
          Tupper = new double?(0.0),
          Tlower_SI = new double?(0.0),
          Tupper_SI = new double?(0.0),
          Ylower = new double?(0.0),
          Yupper = new double?(0.0)
        };
    }
    return new MaterialTypeStrengthLookupResult()
    {
      Temperature = num1,
      Status = str,
      Tlower = obj.Tlower.Value,
      Tupper = obj.Tupper.Value,
      Tlower_SI = obj.Tlower_SI.Value,
      Tupper_SI = obj.Tupper_SI.Value,
      Ylower = obj.Ylower.Value,
      Yupper = obj.Yupper.Value
    };
  }

  private MaterialStrengthLookupResult getStrengthResult(
    double designTemperature,
    double minimumAllowableTemperature)
  {
    double num1 = 0.0;
    string str = "In Range";
    MaterialASMEB31_3StressValueDTO b313StressValueDto;
    if (designTemperature < minimumAllowableTemperature)
    {
      b313StressValueDto = this._allowableStrengths.OrderBy<MaterialASMEB31_3StressValueDTO, double?>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEB31_3StressValueDTO>();
    }
    else
    {
      b313StressValueDto = this._allowableStrengths.Where<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, bool>) (x =>
      {
        double num2 = designTemperature;
        double? tlower = x.TLower;
        if ((num2 <= tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
          return false;
        double num3 = designTemperature;
        double? tupper = x.TUpper;
        return num3 <= tupper.GetValueOrDefault() && tupper.HasValue;
      })).OrderBy<MaterialASMEB31_3StressValueDTO, double?>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEB31_3StressValueDTO>() ?? this._allowableStrengths.Where<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, bool>) (x =>
      {
        double num4 = designTemperature;
        double? tlower = x.TLower;
        return num4 <= tlower.GetValueOrDefault() && tlower.HasValue;
      })).OrderBy<MaterialASMEB31_3StressValueDTO, double?>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEB31_3StressValueDTO>();
      if (b313StressValueDto == null)
      {
        num1 = this._allowableStrengths.Max<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TUpper)).Value;
        str = "Above Range";
        b313StressValueDto = new MaterialASMEB31_3StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      Status = str,
      TLower = b313StressValueDto.TLower.Value,
      TUpper = b313StressValueDto.TUpper.Value,
      SLower = b313StressValueDto.SLower.Value,
      SUpper = b313StressValueDto.SUpper.Value
    };
  }
}
