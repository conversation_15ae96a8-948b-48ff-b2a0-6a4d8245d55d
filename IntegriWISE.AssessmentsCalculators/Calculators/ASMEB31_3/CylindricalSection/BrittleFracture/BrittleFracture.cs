// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.BrittleFracture.BrittleFracture
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.CylindricalSection.BrittleFracture;

public class BrittleFracture : BaseCalculator, IBrittleFractureCalculator
{
  private List<MaterialASMEB31_3StressValueDTO> _allowableStrengths;
  private List<MaterialASMEB31_3_Coefficient_YDTO> _allowableStrenghtsByType;
  private TemperatureRangeDTO _minDesignTemperature;
  private MaterialASMEB31_3DTO _material;

  public double DesignTemperature { private get; set; }

  public double DesignPressure { private get; set; }

  public double? YieldStrengthNew { private get; set; }

  public double? TensileStrengthNew { private get; set; }

  public double LongitudinalWeldJointEfficiency { private get; set; }

  public double CircumferentialWeldJointEfficiency { private get; set; }

  public double ExternalFutureCorrosionAllowance { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public double NominalThickness { private get; set; }

  public double NominalOutsideDiameter { private get; set; }

  public double SupplementalThickness { private get; set; }

  public double MechanicalAllowances { private get; set; }

  public bool PWHT { private get; set; }

  public double TG { private get; set; }

  public bool ImpactTestResultAvailable { private get; set; }

  public double? MaxImpactTestTemperature { private get; set; }

  public double? CET { private get; set; }

  public bool Level1Passed { get; private set; }

  public double Level1MAT { get; private set; }

  public string Level1Message { get; private set; }

  public string Level1FormattedMessage { get; private set; }

  public double MinimumThickness { get; private set; }

  public double AllowableStrength { get; private set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double MAT_figure_323_2_2A_B31_3 { get; private set; }

  public double MAT_OptionA { get; private set; }

  public double? MAT_OptionB { get; private set; }

  public bool? CETGreaterThanMAT { get; private set; }

  public BrittleFracture(
    MaterialASMEB31_3DTO material,
    TemperatureRangeDTO minDesignTemperature,
    List<MaterialASMEB31_3StressValueDTO> allowableStrengths,
    List<MaterialASMEB31_3_Coefficient_YDTO> allowableStrenghtsByType)
  {
    this._material = material;
    this._minDesignTemperature = minDesignTemperature;
    this._allowableStrengths = allowableStrengths;
    this._allowableStrenghtsByType = allowableStrenghtsByType;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_3.MinimumThickness.MinimumThickness minimumThickness = new IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_3.MinimumThickness.MinimumThickness(this._material, this._minDesignTemperature, this._allowableStrengths, this._allowableStrenghtsByType);
    minimumThickness.YieldStrengthNew = this.YieldStrengthNew;
    minimumThickness.TensileStrengthNew = this.TensileStrengthNew;
    minimumThickness.DesignTemperature = this.DesignTemperature;
    minimumThickness.DesignPressure = this.DesignPressure;
    minimumThickness.ExternalFutureCorrosionAllowance = this.ExternalFutureCorrosionAllowance;
    minimumThickness.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    minimumThickness.NominalThickness = this.NominalThickness;
    minimumThickness.LongitudinalWeldJointEfficiency = this.LongitudinalWeldJointEfficiency;
    minimumThickness.CircumferentialWeldJointEfficiency = this.CircumferentialWeldJointEfficiency;
    minimumThickness.NominalOutsideDiameter = this.NominalOutsideDiameter;
    minimumThickness.MechanicalAllowances = this.MechanicalAllowances;
    minimumThickness.SupplementalThickness = this.SupplementalThickness;
    if (minimumThickness.CalculateAssessment() == CalculatorResult.Fail)
    {
      this.ErrorMessage = minimumThickness.ErrorMessage;
      return CalculatorResult.Fail;
    }
    this.Warnings.AddRange((IEnumerable<string>) minimumThickness.Warnings);
    this.AllowableStrength = minimumThickness.AllowableStrength;
    this.MinimumThickness = minimumThickness.TMin;
    if (this.AllowableStrength > 172.5)
    {
      this.ErrorMessage = "Level 1 and Level 2 assessments fail due to the design allowable stress is greater than 172.5 Mpa (25 ksi)";
      return CalculatorResult.Fail;
    }
    string pnoOrSno = this._material.PNoOrSNo;
    string minimumTemperatureC = this._material.MinimumTemperatureC;
    bool flag1 = this.TG <= 38.0;
    bool pwht = this.PWHT;
    bool flag2 = (this._material.PNoOrSNo == "1" || this._material.PNoOrSNo == "2") && flag1 && pwht;
    if (this.TG >= 2.5)
    {
      if (minimumTemperatureC == "A" || minimumTemperatureC == "B" || minimumTemperatureC == "C" || minimumTemperatureC == "D")
      {
        double num1 = this._minDesignTemperature.TLower.Value;
        double num2 = this._minDesignTemperature.TUpper.Value;
        double num3 = this._minDesignTemperature.SUpper.Value;
        double num4 = this._minDesignTemperature.SLower.Value;
        this.MAT_figure_323_2_2A_B31_3 = (this.TG - num1) / (num2 - num1) * (num3 - num4) + num4;
      }
      else if (!string.IsNullOrEmpty(minimumTemperatureC) && this.IsNumeric(minimumTemperatureC))
        this.MAT_figure_323_2_2A_B31_3 = double.Parse(minimumTemperatureC);
      this.MAT_OptionA = !flag2 ? this.MAT_figure_323_2_2A_B31_3 : this.MAT_figure_323_2_2A_B31_3 - 17.0;
    }
    else
      this.MAT_OptionA = -48.0;
    if (this.ImpactTestResultAvailable)
    {
      this.MAT_OptionB = new double?(this.MaxImpactTestTemperature.Value);
      double? matOptionB = this.MAT_OptionB;
      double matOptionA = this.MAT_OptionA;
      this.Level1MAT = (matOptionB.GetValueOrDefault() >= matOptionA ? 0 : (matOptionB.HasValue ? 1 : 0)) == 0 ? this.MAT_OptionA : this.MAT_OptionB.Value;
    }
    else
      this.Level1MAT = this.MAT_OptionA;
    if (!this.CET.HasValue)
    {
      this.Level1Passed = true;
      this.Level1FormattedMessage = "Level 1 Assessment is ACCEPTABLE if CET is greater than {0}{1} otherwise Level 1 assessment fails.";
    }
    else
    {
      this.CETGreaterThanMAT = new bool?(this.CET.Value > this.Level1MAT);
      if (this.CETGreaterThanMAT.Value)
      {
        this.Level1Passed = true;
        this.Level1Message = "Level 1 Assessment is ACCEPTABLE.";
      }
      else
      {
        this.Level1Passed = false;
        this.Level1Message = "Level 1 Assessment is UNACCEPTABLE.";
      }
    }
    return CalculatorResult.Completed;
  }
}
