// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.Utilities
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3;

public class Utilities
{
  public static bool GetB_36_1Coefficient(
    string Material,
    double temperature,
    out double tMin,
    out double tMax,
    out double? yLower,
    out double? yUpper)
  {
    double[] numArray = new double[8]
    {
      -1000.0,
      482.0,
      510.0,
      538.0,
      566.0,
      593.0,
      621.0,
      100000.0
    };
    double?[,] nullableArray = new double?[4, 8]
    {
      {
        new double?(0.4),
        new double?(0.4),
        new double?(0.5),
        new double?(0.7),
        new double?(0.7),
        new double?(0.7),
        new double?(0.7),
        new double?(0.7)
      },
      {
        new double?(0.4),
        new double?(0.4),
        new double?(0.4),
        new double?(0.4),
        new double?(0.4),
        new double?(0.5),
        new double?(0.7),
        new double?(0.7)
      },
      {
        new double?(0.4),
        new double?(0.4),
        new double?(0.4),
        new double?(0.4),
        new double?(0.4),
        new double?(0.4),
        new double?(0.4),
        new double?(0.4)
      },
      {
        new double?(0.0),
        new double?(0.0),
        new double?(),
        new double?(),
        new double?(),
        new double?(),
        new double?(),
        new double?()
      }
    };
    int index1 = -1;
    int index2 = 0;
    switch (Material.ToLower())
    {
      case "ferritic steel":
        index2 = 0;
        break;
      case "austenitic steel":
        index2 = 1;
        break;
      case "other ductile material":
        index2 = 2;
        break;
      case "cast iron":
        index2 = 3;
        break;
    }
    tMin = 0.0;
    tMax = 0.0;
    for (int index3 = 0; index3 < numArray.GetUpperBound(0); ++index3)
    {
      if (numArray[index3 + 1] > temperature)
      {
        index1 = index3;
        tMin = numArray[index3];
        tMax = numArray[index3 + 1];
        break;
      }
    }
    yLower = new double?();
    yUpper = new double?();
    if (index1 != -1)
    {
      yLower = nullableArray[index2, index1];
      yUpper = nullableArray[index2, index1 < 7 ? index1 + 1 : index1];
    }
    return index1 != -1;
  }

  public static MaterialTypeStrengthLookupResult GetStrengthByTypeResult(
    List<MaterialASMEB31_3_Coefficient_YDTO> allowableStrenghtsByType,
    double temperature)
  {
    double num1 = 0.0;
    string str = "In Range";
    MaterialASMEB31_3_Coefficient_YDTO obj = allowableStrenghtsByType.Where<MaterialASMEB31_3_Coefficient_YDTO>((Func<MaterialASMEB31_3_Coefficient_YDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.Tlower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.Tupper;
      return num3 < tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEB31_3_Coefficient_YDTO, double?>((Func<MaterialASMEB31_3_Coefficient_YDTO, double?>) (x => x.Tlower)).FirstOrDefault<MaterialASMEB31_3_Coefficient_YDTO>() ?? allowableStrenghtsByType.Where<MaterialASMEB31_3_Coefficient_YDTO>((Func<MaterialASMEB31_3_Coefficient_YDTO, bool>) (x =>
    {
      double num4 = temperature;
      double? tupper = x.Tupper;
      return num4 == tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEB31_3_Coefficient_YDTO, double?>((Func<MaterialASMEB31_3_Coefficient_YDTO, double?>) (x => x.Tlower)).FirstOrDefault<MaterialASMEB31_3_Coefficient_YDTO>();
    if (obj == null)
    {
      num1 = allowableStrenghtsByType.Max<MaterialASMEB31_3_Coefficient_YDTO>((Func<MaterialASMEB31_3_Coefficient_YDTO, double?>) (x => x.Tupper)).Value;
      str = "Above Range";
      if (num1 < temperature)
        obj = new MaterialASMEB31_3_Coefficient_YDTO()
        {
          Tlower = new double?(0.0),
          Tupper = new double?(0.0),
          Tlower_SI = new double?(0.0),
          Tupper_SI = new double?(0.0),
          Ylower = new double?(0.0),
          Yupper = new double?(0.0)
        };
    }
    if (obj == null)
    {
      num1 = allowableStrenghtsByType.Min<MaterialASMEB31_3_Coefficient_YDTO>((Func<MaterialASMEB31_3_Coefficient_YDTO, double?>) (x => x.Tlower)).Value;
      str = "Below Range";
      if (num1 > temperature)
        obj = new MaterialASMEB31_3_Coefficient_YDTO()
        {
          Tlower = new double?(0.0),
          Tupper = new double?(0.0),
          Tlower_SI = new double?(0.0),
          Tupper_SI = new double?(0.0),
          Ylower = new double?(0.0),
          Yupper = new double?(0.0)
        };
    }
    return new MaterialTypeStrengthLookupResult()
    {
      Temperature = num1,
      Status = str,
      Tlower = obj.Tlower.Value,
      Tupper = obj.Tupper.Value,
      Tlower_SI = obj.Tlower_SI.Value,
      Tupper_SI = obj.Tupper_SI.Value,
      Ylower = obj.Ylower.Value,
      Yupper = obj.Yupper.Value
    };
  }

  public static MaterialStrengthLookupResult GetStrengthResult(
    List<MaterialASMEB31_3StressValueDTO> allowableStrengths,
    double designTemperature,
    double minimumAllowableTemperature)
  {
    double num1 = 0.0;
    string str = "In Range";
    MaterialASMEB31_3StressValueDTO b313StressValueDto;
    if (designTemperature < minimumAllowableTemperature)
    {
      b313StressValueDto = allowableStrengths.OrderBy<MaterialASMEB31_3StressValueDTO, double?>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEB31_3StressValueDTO>();
    }
    else
    {
      b313StressValueDto = allowableStrengths.Where<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, bool>) (x =>
      {
        double num2 = designTemperature;
        double? tlower = x.TLower;
        return num2 <= tlower.GetValueOrDefault() && tlower.HasValue;
      })).OrderBy<MaterialASMEB31_3StressValueDTO, double?>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEB31_3StressValueDTO>() ?? allowableStrengths.Where<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, bool>) (x =>
      {
        double num3 = designTemperature;
        double? tlower = x.TLower;
        if ((num3 <= tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
          return false;
        double num4 = designTemperature;
        double? tupper = x.TUpper;
        return num4 <= tupper.GetValueOrDefault() && tupper.HasValue;
      })).FirstOrDefault<MaterialASMEB31_3StressValueDTO>();
      if (b313StressValueDto == null)
      {
        num1 = allowableStrengths.Max<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TUpper)).Value;
        str = "Above Range";
        b313StressValueDto = new MaterialASMEB31_3StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      Status = str,
      TLower = b313StressValueDto.TLower.Value,
      TUpper = b313StressValueDto.TUpper.Value,
      SLower = b313StressValueDto.SLower.Value,
      SUpper = b313StressValueDto.SUpper.Value
    };
  }
}
