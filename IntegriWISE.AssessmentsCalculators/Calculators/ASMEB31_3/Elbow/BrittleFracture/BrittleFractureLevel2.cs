// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.Elbow.BrittleFracture.BrittleFractureLevel2
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.Elbow.BrittleFracture;

public class BrittleFractureLevel2 : BaseCalculator, IBrittleFractureCalculator
{
  private List<MaterialASMEB31_3StressValueDTO> _allowableStrengths;
  private List<MaterialASMEB31_3_Coefficient_YDTO> _allowableStrenghtsByType;
  private TemperatureRangeDTO _minDesignTemperature;
  private MaterialASMEB31_3DTO _material;

  public double DesignTemperature { private get; set; }

  public double DesignPressure { private get; set; }

  public double? YieldStrengthNew { private get; set; }

  public double? TensileStrengthNew { private get; set; }

  public double LongitudinalWeldJointEfficiency { private get; set; }

  public double CircumferentialWeldJointEfficiency { private get; set; }

  public double ExternalFutureCorrosionAllowance { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public double NominalThickness { private get; set; }

  public double NominalOutsideDiameter { private get; set; }

  public double SupplementalThickness { private get; set; }

  public double MechanicalAllowances { private get; set; }

  public bool PWHT { private get; set; }

  public double TG { private get; set; }

  public bool ImpactTestResultAvailable { private get; set; }

  public double? MaxImpactTestTemperature { private get; set; }

  public double? CET { private get; set; }

  public bool Level1Passed { get; private set; }

  public double Level1MAT { get; private set; }

  public string Level1Message { get; private set; }

  public string Level1FormattedMessage { get; private set; }

  public double MAT_OptionA { get; private set; }

  public double? MAT_OptionB { get; private set; }

  public bool? CETGreaterThanMAT { get; private set; }

  public double InternalFutureCorrosionAllowance { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public bool HydroTestPerformed { private get; set; }

  public double? HydroTestPressure { private get; set; }

  public double? HydroTestTemperature { private get; set; }

  public double MAT_Level2_MethodA { get; private set; }

  public double? MAT_Level2_MethodB { get; private set; }

  public bool Level2Passed { get; private set; }

  public double Level2MAT { get; private set; }

  public string Level2Message { get; private set; }

  public string Level2FormattedMessage { get; private set; }

  public bool? CETGreaterThanMATLevel2 { get; private set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public BrittleFractureLevel2(
    MaterialASMEB31_3DTO material,
    TemperatureRangeDTO minDesignTemperature,
    List<MaterialASMEB31_3StressValueDTO> allowableStrengths,
    List<MaterialASMEB31_3_Coefficient_YDTO> allowableStrenghtsByType)
  {
    this._material = material;
    this._minDesignTemperature = minDesignTemperature;
    this._allowableStrengths = allowableStrengths;
    this._allowableStrenghtsByType = allowableStrenghtsByType;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.Elbow.BrittleFracture.BrittleFracture brittleFracture = new IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3.Elbow.BrittleFracture.BrittleFracture(this._material, this._minDesignTemperature, this._allowableStrengths, this._allowableStrenghtsByType);
    brittleFracture.YieldStrengthNew = this.YieldStrengthNew;
    brittleFracture.TensileStrengthNew = this.TensileStrengthNew;
    brittleFracture.DesignTemperature = this.DesignTemperature;
    brittleFracture.DesignPressure = this.DesignPressure;
    brittleFracture.ExternalFutureCorrosionAllowance = this.ExternalFutureCorrosionAllowance;
    brittleFracture.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    brittleFracture.NominalThickness = this.NominalThickness;
    brittleFracture.LongitudinalWeldJointEfficiency = this.LongitudinalWeldJointEfficiency;
    brittleFracture.CircumferentialWeldJointEfficiency = this.CircumferentialWeldJointEfficiency;
    brittleFracture.NominalOutsideDiameter = this.NominalOutsideDiameter;
    brittleFracture.MechanicalAllowances = this.MechanicalAllowances;
    brittleFracture.SupplementalThickness = this.SupplementalThickness;
    brittleFracture.TG = this.TG;
    brittleFracture.PWHT = this.PWHT;
    brittleFracture.ImpactTestResultAvailable = this.ImpactTestResultAvailable;
    brittleFracture.MaxImpactTestTemperature = this.MaxImpactTestTemperature;
    brittleFracture.CET = this.CET;
    CalculatorResult assessment = brittleFracture.CalculateAssessment();
    double minimumThickness = brittleFracture.MinimumThickness;
    double allowableStrength = brittleFracture.AllowableStrength;
    double figure32322AB313 = brittleFracture.MAT_figure_323_2_2A_B31_3;
    this.MAT_OptionA = brittleFracture.MAT_OptionA;
    this.MAT_OptionB = brittleFracture.MAT_OptionB;
    this.CETGreaterThanMAT = brittleFracture.CETGreaterThanMAT;
    this.Level1MAT = brittleFracture.Level1MAT;
    this.Level1FormattedMessage = brittleFracture.Level1FormattedMessage;
    this.Level1Message = brittleFracture.Level1Message;
    this.Level1Passed = brittleFracture.Level1Passed;
    this.ErrorMessage = brittleFracture.ErrorMessage;
    this.Warnings.AddRange((IEnumerable<string>) brittleFracture.Warnings);
    if (assessment == CalculatorResult.Fail)
      return assessment;
    bool flag1 = this.TG < 2.5;
    bool flag2 = this._material.PNoOrSNo == "1" || this._material.PNoOrSNo == "2";
    if (!this.CET.HasValue || !this.Level1Passed)
    {
      double num1 = Math.Max(Math.Min(this.LongitudinalWeldJointEfficiency, this.CircumferentialWeldJointEfficiency), 0.8);
      double num2 = this.TG - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
      if (num2 != 0.0)
      {
        double num3 = minimumThickness * num1 / num2;
        if (num3 > 1.0)
          num3 = 1.0;
        double num4 = num3 < 0.6 ? (allowableStrength > 120.8 || num3 > 0.4 ? (allowableStrength <= 120.8 || allowableStrength > 137.8 || num3 > 0.35 ? (allowableStrength <= 137.8 || allowableStrength > 172.5 || num3 > 0.3 ? -9979.57 - 14125.0 * Math.Pow(num3, 1.5) + 9088.11 * Math.Exp(num3) - 17.3893 * Math.Log(num3) / Math.Pow(num3, 2.0) : 200.0) : 140.0) : 105.0) : 100.0 * (1.0 - num3);
        double num5 = !flag1 ? this.Convert(figure32322AB313, "DEG_C", "DEG_F") : -55.0;
        double num6 = this.Convert(allowableStrength <= 120.8 && num3 <= 0.4 || allowableStrength <= 137.8 && allowableStrength > 120.8 && num3 <= 0.35 || allowableStrength <= 172.5 && allowableStrength > 137.8 && num3 <= 0.3 ? -155.0 : Math.Max(num5 - num4, -55.0), "DEG_F", "DEG_C");
        bool flag3 = this.TG <= 38.0;
        if (this.ImpactTestResultAvailable)
        {
          double num7 = this.Convert(Math.Max(this.Convert(this.MaxImpactTestTemperature.Value, "DEG_C", "DEG_F") - num4, -155.0), "DEG_F", "DEG_C");
          this.MAT_Level2_MethodA = this.MAT_OptionA <= this.MAT_OptionB.Value && flag2 && flag3 && this.PWHT ? num6 - 17.0 : (num7 >= num6 ? num6 : num7);
        }
        else
          this.MAT_Level2_MethodA = flag2 && flag3 && this.PWHT ? num6 - 17.0 : num6;
        if (this.HydroTestPerformed)
          this.Warnings.Add("Method B (for Level 2 Assessment) is limited to Hydrotest Pressures of 125%, 130% and 150 % of the Design Pressure corrected by the ratio of the Allowable Stress at Test Temperature to the Allowable Stress at the Design Temperature.");
        if (this.HydroTestPerformed && this.HydroTestPressure.Value >= 1.25 * this.DesignPressure)
        {
          double x = this.DesignPressure / this.HydroTestPressure.Value;
          double num8 = x <= 0.25 ? 105.0 : 52.1971 - 53.3079 * x - 9814.0 / 625.0 * Math.Pow(x, 2.0) + 16.7548 / x;
          this.MAT_Level2_MethodB = new double?(this.Convert(this.Convert(this.HydroTestTemperature.Value, "DEG_C", "DEG_F") - num8 >= -155.0 ? this.Convert(this.HydroTestTemperature.Value, "DEG_C", "DEG_F") - num8 : -155.0, "DEG_F", "DEG_C"));
          double? matLevel2MethodB = this.MAT_Level2_MethodB;
          double matLevel2MethodA = this.MAT_Level2_MethodA;
          this.Level2MAT = (matLevel2MethodB.GetValueOrDefault() >= matLevel2MethodA ? 0 : (matLevel2MethodB.HasValue ? 1 : 0)) == 0 ? this.MAT_Level2_MethodA : this.MAT_Level2_MethodB.Value;
          if (this.HydroTestTemperature.Value < this.Level1MAT + 17.0)
            this.Warnings.Add("The hydrotest is performed at a temperature colder than the MAT as determined by Level 1 assessment plus 17 C degrees (30 F degrees), it should be noted that a brittle fracture may occur during the hydrotest.");
        }
        else
          this.Level2MAT = this.MAT_Level2_MethodA;
        if (this.CET.HasValue)
        {
          if (this.CET.Value > this.Level2MAT)
          {
            this.CETGreaterThanMATLevel2 = new bool?(true);
            this.Level2Message = "Level 2 Assessment is ACCEPTABLE.";
            this.Level2Passed = true;
          }
          else
          {
            this.CETGreaterThanMATLevel2 = new bool?(false);
            this.Level2Message = "Level 2 Assessment is UNACCEPTABLE.";
            this.Level2Passed = false;
          }
        }
        else
        {
          this.Level2FormattedMessage = "Level 2 Assessment is ACCEPTABLE if CET is greater than {0}{1} otherwise Level 2 assessment fails.";
          this.Level2Passed = true;
        }
      }
      else
      {
        this.ErrorMessage = "The calculated tc is 0";
        return CalculatorResult.Fail;
      }
    }
    return CalculatorResult.Completed;
  }

  public double Convert(double quantity, string from, string to)
  {
    return from == to ? quantity : this.Convert(quantity, $"{from.Trim()} -> {to.Trim()}");
  }

  public double Convert(double quantity, string conversion)
  {
    switch (conversion)
    {
      case "DEG_C -> DEG_F":
        return quantity * 9.0 / 5.0 + 32.0;
      case "MPA -> PSI":
        return quantity * 145.0377;
      case "DEG_F -> DEG_C":
        return (quantity - 32.0) * (5.0 / 9.0);
      case "K -> DEG_C":
        return quantity - 273.15;
      case "DEG_C -> K":
        return quantity + 273.15;
      default:
        throw new NotImplementedException($"Unit conversion not implemented : {conversion}");
    }
  }
}
