// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.SphericalShell.Lamination.LaminationLevel2
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.SphericalShell.Lamination;

public class LaminationLevel2 : BaseCalculator, ILaminationCalculator
{
  private List<MaterialTSFCurveDTO> _api579Table5_4List;
  private bool RtGreater0;

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public string MultipleLaminations { get; set; }

  public double LongitudinalLaminationDimension { get; set; }

  public double CircumferentialLaminationDimension { get; set; }

  public double LaminationHeight { get; set; }

  public double LaminationSpacing { get; set; }

  public double TMM { get; set; }

  public double LW { get; set; }

  public double LMSD { get; set; }

  public bool InHydrogenChargingService { get; set; }

  public bool MAWPLevel { get; set; }

  public double AllowableStrength { get; set; }

  public double tc { get; set; }

  public bool CrackLikeFlaw { get; set; }

  public bool LaminationIsNotSurfaceBreaking { get; set; }

  public bool DistanceToTheNearestDiscontinuity { get; set; }

  public bool DistanceToTheNearestWeldSeam { get; set; }

  public bool HydrogenChargingAndDimensions { get; set; }

  public bool HydrogenChargingAndDimensions2 { get; set; }

  public double MAWP { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public string Level1 { get; set; }

  public bool Level1Passed { get; set; }

  public double Rt { get; set; }

  public double RSF { get; set; }

  public double TmmPart5 { get; set; }

  public bool RtGreater0_2 { get; set; }

  public bool TmmMinusFCAGreater2_5 { get; set; }

  public bool LmsdGreater1_8DtcPower0_5 { get; set; }

  public bool ScreeningCriteriaFigure5_6 { get; set; }

  public bool RSFGreaterRSFa { get; set; }

  public int iConditionLongitudinalExtentLevel2 { get; set; }

  public bool Level2Passed { get; set; }

  public string ConditionLongitudinalExtentL2 { get; set; }

  public string Level2AssessmentConclusion { get; set; }

  private double Lambda { get; set; }

  private double RSFa { get; set; }

  private double LambdaTable5_2 { get; set; }

  private double MtTable5_2 { get; set; }

  private double RtFigure5_6 { get; set; }

  private double Radius { get; set; }

  public double MAWPrL2 { get; set; }

  public LaminationLevel2(List<MaterialTSFCurveDTO> api579Table5_4List)
  {
    this._api579Table5_4List = api579Table5_4List;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.SphericalShell.Lamination.Lamination lamination = new IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.SphericalShell.Lamination.Lamination();
    lamination.NominalInsideDiameter = this.NominalInsideDiameter;
    lamination.NominalThickness = this.NominalThickness;
    lamination.InternalUniformMetalLoss = this.InternalUniformMetalLoss;
    lamination.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    lamination.InternalFutureCorrosionAllowance = this.InternalFutureCorrosionAllowance;
    lamination.ExternalFutureCorrosionAllowance = this.ExternalFutureCorrosionAllowance;
    lamination.MultipleLaminations = this.MultipleLaminations;
    lamination.LongitudinalLaminationDimension = this.LongitudinalLaminationDimension;
    lamination.CircumferentialLaminationDimension = this.CircumferentialLaminationDimension;
    lamination.LaminationHeight = this.LaminationHeight;
    lamination.LaminationSpacing = this.LaminationSpacing;
    lamination.TMM = this.TMM;
    lamination.LW = this.LW;
    lamination.LMSD = this.LMSD;
    lamination.InHydrogenChargingService = this.InHydrogenChargingService;
    lamination.MAWPLevel = this.MAWPLevel;
    lamination.MAWP = this.MAWP;
    CalculatorResult assessment = lamination.CalculateAssessment();
    this.ErrorMessage = lamination.ErrorMessage;
    this.Warnings.AddRange((IEnumerable<string>) lamination.Warnings);
    this.tc = lamination.tc;
    this.CrackLikeFlaw = lamination.CrackLikeFlaw;
    this.LaminationIsNotSurfaceBreaking = lamination.LaminationIsNotSurfaceBreaking;
    this.DistanceToTheNearestWeldSeam = lamination.DistanceToTheNearestWeldSeam;
    this.DistanceToTheNearestDiscontinuity = lamination.DistanceToTheNearestDiscontinuity;
    this.HydrogenChargingAndDimensions2 = lamination.HydrogenChargingAndDimensions2;
    this.HydrogenChargingAndDimensions = lamination.HydrogenChargingAndDimensions;
    this.Level1Passed = lamination.Level1Passed;
    this.Level1 = lamination.Level1;
    if (assessment == CalculatorResult.Fail)
      return assessment;
    if (this.Level1Passed)
      return CalculatorResult.Completed;
    if (this.InHydrogenChargingService)
    {
      this.Warnings.Add("The lamination should be evaluated as an equivalent LTA using methods in Part 5");
      this.Radius = this.NominalInsideDiameter / 2.0 - this.ExternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss;
      this.RSFa = 0.9;
      this.TmmPart5 = Math.Max(this.tc - this.LaminationHeight - this.TMM, this.TMM);
      this.Rt = (this.TmmPart5 - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.tc;
      this.Rt = Math.Round(this.Rt, 4);
      this.RtGreater0 = this.Rt > 0.0;
      if (!this.RtGreater0)
      {
        this.ErrorMessage = "Remaining thickness ratio is less than or equal to zero. The assessment fails. ";
        return CalculatorResult.Fail;
      }
      this.Lambda = 1.285 * this.LongitudinalLaminationDimension / Math.Pow(2.0 * this.Radius * this.tc, 0.5);
      this.RtGreater0_2 = this.Rt > 0.2;
      this.TmmMinusFCAGreater2_5 = this.TmmPart5 - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance > 2.5;
      this.LmsdGreater1_8DtcPower0_5 = this.LMSD > 1.8 * Math.Pow(this.Radius * 2.0 * this.tc, 0.5);
      this.LambdaTable5_2 = this.Lambda <= 20.0 ? this.Lambda : 20.0;
      this.MtTable5_2 = (1.0005 + 0.49001 * this.LambdaTable5_2 + 0.32409 * Math.Pow(this.LambdaTable5_2, 2.0)) / (1.0 + 0.50144 * this.LambdaTable5_2 - 0.011067 * Math.Pow(this.LambdaTable5_2, 2.0));
      this.RtFigure5_6 = this.Lambda > 0.354 ? (this.Lambda >= 20.0 ? 0.9 : (this.RSFa - this.RSFa / this.MtTable5_2) * Math.Pow(1.0 - this.RSFa / this.MtTable5_2, -1.0)) : 0.2;
      this.ScreeningCriteriaFigure5_6 = this.Rt > this.RtFigure5_6;
      this.RSF = this.Rt / (1.0 - 1.0 / this.MtTable5_2 * (1.0 - this.Rt));
      this.RSFGreaterRSFa = this.RSF >= this.RSFa;
      if (this.RtGreater0_2 && this.TmmMinusFCAGreater2_5 && this.LmsdGreater1_8DtcPower0_5)
      {
        if (this.ScreeningCriteriaFigure5_6 || this.RSFGreaterRSFa)
        {
          this.ConditionLongitudinalExtentL2 = "The longitudinal extent of the lamination is ACCEPTABLE for operation at the MAWP.";
          this.iConditionLongitudinalExtentLevel2 = 1;
        }
        else
        {
          this.ConditionLongitudinalExtentL2 = "The longitudinal extent of the lamination is ACCEPTABLE for operation at the MAWPr.";
          this.iConditionLongitudinalExtentLevel2 = 2;
        }
      }
      else
      {
        this.ConditionLongitudinalExtentL2 = "The longitudinal extent of the lamination is UNACCEPTABLE";
        this.iConditionLongitudinalExtentLevel2 = 3;
      }
      if (this.CrackLikeFlaw && this.DistanceToTheNearestDiscontinuity && this.LaminationIsNotSurfaceBreaking)
      {
        if (this.iConditionLongitudinalExtentLevel2 == 1)
        {
          this.Level2Passed = true;
          this.Level2AssessmentConclusion = "The Level 2 Assessment is ACCEPTABLE";
          return CalculatorResult.Completed;
        }
        if (this.iConditionLongitudinalExtentLevel2 == 2)
        {
          this.Level2Passed = false;
          this.Level2AssessmentConclusion = "The Level 2 Assessment is UNACCEPTABLE.The component is unacceptable for operation at MAWP but it is ACCEPTABLE for operation at MAWPr";
        }
      }
      else
      {
        this.Level2Passed = false;
        this.Level2AssessmentConclusion = "The Level 2 Assessment is UNACCEPTABLE";
      }
    }
    else
    {
      if (this.CrackLikeFlaw && this.DistanceToTheNearestDiscontinuity && this.LaminationIsNotSurfaceBreaking)
      {
        this.Level2Passed = true;
        this.Level2AssessmentConclusion = "The Level 2 Assessment is ACCEPTABLE";
        return CalculatorResult.Completed;
      }
      this.Level2Passed = false;
      this.Level2AssessmentConclusion = "Level 2 Assessment UNACCEPTABLE";
    }
    if (this.MAWPLevel && this.iConditionLongitudinalExtentLevel2 == 2)
      this.MAWPrL2 = this.MAWP * this.RSF / this.RSFa;
    return CalculatorResult.Completed;
  }
}
