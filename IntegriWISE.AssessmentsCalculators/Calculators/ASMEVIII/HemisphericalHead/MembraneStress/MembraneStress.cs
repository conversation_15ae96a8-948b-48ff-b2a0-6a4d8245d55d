// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.HemisphericalHead.MembraneStress.MembraneStress
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.HemisphericalHead.MembraneStress;

public class MembraneStress : BaseCalculator
{
  public double DesignPressure { private get; set; }

  public double NominalThickness { private get; set; }

  public double NominalInsideDiameter { private get; set; }

  public double WeldJointEfficiency { private get; set; }

  public double InternalFutureCorrosionAllowance { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public double ExternalFutureCorrosionAllowance { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double SigmaM { get; private set; }

  public MembraneStress() => this.Warnings = new List<string>();

  public CalculatorResult CalculateAssessment()
  {
    double num1 = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    double num2 = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss;
    double num3 = this.DesignPressure / (2.0 * this.WeldJointEfficiency) * (num1 / num2 + 0.2);
    this.SigmaM = this.DesignPressure >= 0.665 * num3 * this.WeldJointEfficiency || this.NominalThickness > 0.356 * num1 ? this.DesignPressure / (2.0 * this.WeldJointEfficiency) * (Math.Pow((num1 + num2) / num1, 3.0) + 2.0) * Math.Pow(Math.Pow((num1 + num2) / num1, 3.0) - 1.0, -1.0) : num3;
    return CalculatorResult.Completed;
  }
}
