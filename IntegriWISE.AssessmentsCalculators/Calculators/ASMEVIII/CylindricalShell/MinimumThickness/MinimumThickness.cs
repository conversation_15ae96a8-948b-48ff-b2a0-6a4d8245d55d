// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.CylindricalShell.MinimumThickness.MinimumThickness
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.CylindricalShell.MinimumThickness;

public class MinimumThickness : BaseCalculator
{
  public bool ASMEVIIIBefore1999;
  private double tupper;
  private double tlower;
  private double supper;
  private double slower;
  private bool PLessThanSE;
  private double checkTcMin;
  private double checkTlMin;
  private MaterialASMEVIIIDiv1DTO _material;
  private List<MaterialASMEVIIIDiv1StressValueDTO> _allowableStrengths;

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double? YieldStrengthNew { get; set; }

  public double? TensileStrengthNew { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double SupplementalThickness { get; set; }

  public double LongitudinalWeldJointEfficiency { get; set; }

  public double CircumferentialWeldJointEfficiency { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double AllowableStrength { get; private set; }

  public double TMin { get; private set; }

  public double Radius { get; set; }

  public double TcMin { get; set; }

  public double TlMin { get; set; }

  public List<string> Warnings { get; set; }

  public string ErrorMessage { get; set; }

  public MinimumThickness(
    MaterialASMEVIIIDiv1DTO material,
    List<MaterialASMEVIIIDiv1StressValueDTO> allowableStrengths)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.UserDefined)
    {
      this.AllowableStrength = !this.ASMEVIIIBefore1999 ? Math.Min(this.YieldStrengthNew.Value / 1.5, this.TensileStrengthNew.Value / 3.5) : Math.Min(this.YieldStrengthNew.Value / 1.5, this.TensileStrengthNew.Value / 4.0);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      MaterialStrengthLookupResult strengthResult = this.getStrengthResult(this.DesignTemperature);
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.AboveRange)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material.";
        return CalculatorResult.Fail;
      }
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.BelowRange)
      {
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).");
        strengthResult = this.getStrengthResult(-30.0);
      }
      if (this._material.CreepTemperature.HasValue)
      {
        double designTemperature = this.DesignTemperature;
        double? creepTemperature = this._material.CreepTemperature;
        if ((designTemperature <= creepTemperature.GetValueOrDefault() ? 0 : (creepTemperature.HasValue ? 1 : 0)) != 0)
          this.Warnings.Add("Creep Assessment is required due to the high temperature.");
      }
      this.tlower = strengthResult.TLower;
      this.tupper = strengthResult.TUpper;
      this.slower = strengthResult.SLower;
      this.supper = strengthResult.SUpper;
      this.AllowableStrength = (this.DesignTemperature - this.tlower) / (this.tupper - this.tlower) * (this.supper - this.slower) + this.slower;
    }
    this.Radius = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    this.PLessThanSE = this.DesignPressure < this.AllowableStrength * this.LongitudinalWeldJointEfficiency;
    if (!this.PLessThanSE)
    {
      this.ErrorMessage = "Design Pressure is greater than Allowable Stress * Longitudinal Weld Joint Efficiency";
      return CalculatorResult.Fail;
    }
    this.TcMin = this.DesignPressure > 0.385 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency ? this.Radius * (Math.Pow((this.AllowableStrength * this.LongitudinalWeldJointEfficiency + this.DesignPressure) / (this.AllowableStrength * this.LongitudinalWeldJointEfficiency - this.DesignPressure), 0.5) - 1.0) : this.DesignPressure * this.Radius / (this.AllowableStrength * this.LongitudinalWeldJointEfficiency - 0.6 * this.DesignPressure);
    this.checkTcMin = this.DesignPressure > 0.385 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency || this.TcMin > 0.5 * this.Radius ? this.Radius * (Math.Pow((this.AllowableStrength * this.LongitudinalWeldJointEfficiency + this.DesignPressure) / (this.AllowableStrength * this.LongitudinalWeldJointEfficiency - this.DesignPressure), 0.5) - 1.0) : this.DesignPressure * this.Radius / (this.AllowableStrength * this.LongitudinalWeldJointEfficiency - 0.6 * this.DesignPressure);
    this.TlMin = this.DesignPressure > 1.25 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency ? this.Radius * (Math.Pow(this.DesignPressure / (this.CircumferentialWeldJointEfficiency * this.AllowableStrength) + 1.0, 0.5) - 1.0) + this.SupplementalThickness : this.DesignPressure * this.Radius / (2.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency + 0.4 * this.DesignPressure) + this.SupplementalThickness;
    this.checkTlMin = this.DesignPressure > 1.25 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency || this.TlMin > 0.5 * this.Radius ? this.Radius * (Math.Pow(this.DesignPressure / (this.CircumferentialWeldJointEfficiency * this.AllowableStrength) + 1.0, 0.5) - 1.0) + this.SupplementalThickness : this.DesignPressure * this.Radius / (2.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency + 0.4 * this.DesignPressure) + this.SupplementalThickness;
    this.TMin = Math.Max(this.checkTlMin, this.checkTcMin);
    return CalculatorResult.Completed;
  }

  private MaterialStrengthLookupResult getStrengthResult(double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialASMEVIIIDiv1StressValueDTO div1StressValueDto = this._allowableStrengths.Where<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 <= tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEVIIIDiv1StressValueDTO, double?>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEVIIIDiv1StressValueDTO>();
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Max<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Min<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).Value;
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
      {
        TLower = new double?(0.0),
        TUpper = new double?(0.0),
        SLower = new double?(0.0),
        SUpper = new double?(0.0)
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = div1StressValueDto.TLower.Value,
      TUpper = div1StressValueDto.TUpper.Value,
      SLower = div1StressValueDto.SLower.Value,
      SUpper = div1StressValueDto.SUpper.Value
    };
  }
}
