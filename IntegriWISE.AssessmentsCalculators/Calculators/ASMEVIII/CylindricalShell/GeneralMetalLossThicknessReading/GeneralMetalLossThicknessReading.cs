// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.CylindricalShell.GeneralMetalLossThicknessReading.GeneralMetalLossThicknessReading
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.CylindricalShell.GeneralMetalLossThicknessReading;

public class GeneralMetalLossThicknessReading : BaseCalculator
{
  public bool Div1B41999;
  private bool PLessThanSE;
  private double Max05TminTlim;
  private double Radius;
  private double checkTcMin;
  private double checkTlMin;
  private string TemperatureLowerLimit;
  private double WorkingTemperature;
  private List<MaterialASMEVIIIDiv1StressValueDTO> _allowableStrengths;
  private MaterialASMEVIIIDiv1DTO _material;
  private ThicknessReadings _thicknessReadings;

  public bool ToLevel2 { get; set; }

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double SupplementalThickness { get; set; }

  public double LongitudinalWeldJointEfficiency { get; set; }

  public double CircumferentialWeldJointEfficiency { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double AllowableStrength { get; set; }

  public double TMin { get; set; }

  public double Tmm { get; set; }

  public double Tam { get; set; }

  public double Cov { get; set; }

  public double Tlim { get; set; }

  public double MAWPrTamMinusFCA { get; set; }

  public double MAWPrTamMinusFCAOverRSFa { get; set; }

  public bool AverageMeasuredThicknessL1 { get; set; }

  public bool MawpL1 { get; set; }

  public bool MinimumMeasuredThicknessL1 { get; set; }

  public bool Level1Passed { get; set; }

  public string Level1ResultMsg { get; set; }

  public bool MinimumMeasuredThicknessL2 { get; set; }

  public bool AverageMeasuredThicknessL2 { get; set; }

  public bool MawpL2 { get; set; }

  public bool Level2Passed { get; set; }

  public string Level2ResultMsg { get; set; }

  private string MinimumTemperatureC { get; set; }

  private double MinimumAllowableTemperature { get; set; }

  private string TemperatureLowLimit { get; set; }

  private double TLower { get; set; }

  private double TUpper { get; set; }

  private double SLower { get; set; }

  private double SUpper { get; set; }

  private double Tlower2 { get; set; }

  private double Tupper2 { get; set; }

  private double Ylower { get; set; }

  private double Yupper { get; set; }

  private double YB31ASMEB31 { get; set; }

  private double OutsideDiameterDo { get; set; }

  private double tc { get; set; }

  private double tcmin1 { get; set; }

  private double tlmin1 { get; set; }

  private double tmin1 { get; set; }

  private double checkYB31 { get; set; }

  private double tcmin { get; set; }

  private double tlMin { get; set; }

  private double ThicknessReadingsN { get; set; }

  private double S { get; set; }

  private string CheckCOV { get; set; }

  private double tamMinusFCA { get; set; }

  private double tmmMinusFCA { get; set; }

  private double Max0_5MminTlim { get; set; }

  private double MAWPrcTamMinusFCA { get; set; }

  private double MAWPrlTamMinusFCA { get; set; }

  private double RSFa { get; set; }

  public double TamMinusFCAOverRSFa { get; set; }

  private double tamMinusTslMinusFCAOverRSFa { get; set; }

  private double MAWPrcTamMinusFCAOverRSFa { get; set; }

  private double MAWPrlTamMinusTslMinusFCAOverRSFa { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private string TemperatureUpLimit { get; set; }

  private string StrengthTemperatureLimit { get; set; }

  private double ThicknessLower { get; set; }

  private double ThicknessUpper { get; set; }

  private double TminLower { get; set; }

  private double TminUpper { get; set; }

  public GeneralMetalLossThicknessReading(
    MaterialASMEVIIIDiv1DTO material,
    List<MaterialASMEVIIIDiv1StressValueDTO> allowableStrengths,
    ThicknessReadings thicknessReadings)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
    this._thicknessReadings = thicknessReadings;
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.UserDefined)
    {
      double num1 = this._material.YieldStrengthNew.Value;
      double num2 = this._material.TensileStrengthNew.Value;
      this.AllowableStrength = !this.Div1B41999 ? Math.Min(num1 / 1.5, num2 / 3.5) : Math.Min(num1 / 1.5, num2 / 4.0);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      this.WorkingTemperature = this.DesignTemperature;
      if (this.DesignTemperature < -30.0)
      {
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).");
        this.WorkingTemperature = -30.0;
      }
      MaterialStrengthLookupResult strengthResult = this.getStrengthResult(this.WorkingTemperature);
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.AboveRange)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material.";
        return CalculatorResult.Fail;
      }
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.BelowRange)
      {
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).");
        this.TemperatureLowerLimit = "Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).";
        this.Warnings.Add(this.TemperatureLowerLimit);
        strengthResult = this.getStrengthResult(-30.0);
      }
      if (this._material.CreepTemperature.HasValue)
      {
        double designTemperature = this.DesignTemperature;
        double? creepTemperature = this._material.CreepTemperature;
        if ((designTemperature <= creepTemperature.GetValueOrDefault() ? 0 : (creepTemperature.HasValue ? 1 : 0)) != 0)
          this.Warnings.Add("Creep Assessment is required due to the high temperature.");
      }
      this.TLower = strengthResult.TLower;
      this.TUpper = strengthResult.TUpper;
      this.SLower = strengthResult.SLower;
      this.SUpper = strengthResult.SUpper;
      this.AllowableStrength = (this.DesignTemperature - this.TLower) / (this.TUpper - this.TLower) * (this.SUpper - this.SLower) + this.SLower;
    }
    this.PLessThanSE = this.DesignPressure > this.AllowableStrength * this.LongitudinalWeldJointEfficiency;
    if (this.PLessThanSE)
    {
      this.ErrorMessage = "Design Pressure should be less than Allowable Stress * Weld Joint Efficiency";
      this.Level1Passed = false;
      return CalculatorResult.Fail;
    }
    this.Radius = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    this.tc = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance;
    this.tcmin = this.DesignPressure > 0.385 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency ? this.Radius * (Math.Pow((this.AllowableStrength * this.LongitudinalWeldJointEfficiency + this.DesignPressure) / (this.AllowableStrength * this.LongitudinalWeldJointEfficiency - this.DesignPressure), 0.5) - 1.0) : this.DesignPressure * this.Radius / (this.AllowableStrength * this.LongitudinalWeldJointEfficiency - 0.6 * this.DesignPressure);
    this.checkTcMin = this.DesignPressure > 0.385 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency || this.tcmin > 0.5 * this.Radius ? this.Radius * (Math.Pow((this.AllowableStrength * this.LongitudinalWeldJointEfficiency + this.DesignPressure) / (this.AllowableStrength * this.LongitudinalWeldJointEfficiency - this.DesignPressure), 0.5) - 1.0) : this.DesignPressure * this.Radius / (this.AllowableStrength * this.LongitudinalWeldJointEfficiency - 0.6 * this.DesignPressure);
    this.tlMin = this.DesignPressure > 1.25 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency ? this.Radius * (Math.Pow(this.DesignPressure / (this.CircumferentialWeldJointEfficiency * this.AllowableStrength) + 1.0, 0.5) - 1.0) + this.SupplementalThickness : this.DesignPressure * this.Radius / (2.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency + 0.4 * this.DesignPressure) + this.SupplementalThickness;
    this.checkTlMin = this.DesignPressure > 1.25 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency || this.tlMin > 0.5 * this.Radius ? this.Radius * (Math.Pow(this.DesignPressure / (this.CircumferentialWeldJointEfficiency * this.AllowableStrength) + 1.0, 0.5) - 1.0) + this.SupplementalThickness : this.DesignPressure * this.Radius / (2.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency + 0.4 * this.DesignPressure) + this.SupplementalThickness;
    this.TMin = Math.Max(this.checkTcMin, this.checkTlMin);
    this.ThicknessReadingsN = (double) this._thicknessReadings.TotalNumThicknessPoints();
    this.S = this._thicknessReadings.SumOfThicknessReadings();
    this.Tam = this.S / this.ThicknessReadingsN;
    this.Tmm = this._thicknessReadings.MinimumThicknessPointValue();
    this.S = this._thicknessReadings.SumOfThicknessReadingsPow2();
    this.Cov = 1.0 / this.Tam * Math.Pow(this.S / (this.ThicknessReadingsN - 1.0), 0.5) * 100.0;
    if (this.Cov > 10.0)
    {
      this.ErrorMessage = "The Coefficient of Variation (COV) of the thickness readings is greater than 10%, the general metal loss procedure can not be used in this case. Then thickness profiles shall be considered for use in the assessment (see paragraph 4.3.3.3 API 579)";
      return CalculatorResult.Fail;
    }
    this.tamMinusFCA = this.Tam - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.Tlim = Math.Max(0.2 * this.NominalThickness, 2.5);
    this.tmmMinusFCA = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.Max05TminTlim = Math.Max(0.5 * this.TMin, this.Tlim);
    this.MAWPrcTamMinusFCA = this.DesignPressure > 0.385 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency || this.checkTcMin > 0.5 * this.Radius ? this.AllowableStrength * this.LongitudinalWeldJointEfficiency * (Math.Pow((this.Radius + this.tamMinusFCA) / this.Radius, 2.0) - 1.0) * Math.Pow(Math.Pow((this.Radius + this.tamMinusFCA) / this.Radius, 2.0) + 1.0, -1.0) : this.AllowableStrength * this.LongitudinalWeldJointEfficiency * this.tamMinusFCA / (this.Radius + 0.6 * this.tamMinusFCA);
    this.MAWPrlTamMinusFCA = 0.0;
    this.MAWPrlTamMinusFCA = this.DesignPressure > 1.25 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency || this.checkTlMin > 0.5 * this.Radius ? this.AllowableStrength * this.CircumferentialWeldJointEfficiency * (Math.Pow((this.Radius + this.tamMinusFCA - this.SupplementalThickness) / this.Radius, 2.0) - 1.0) : 2.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency * (this.tamMinusFCA - this.SupplementalThickness) / (this.Radius - 0.4 * (this.tamMinusFCA - this.SupplementalThickness));
    this.RSFa = 0.9;
    this.AverageMeasuredThicknessL1 = this.tamMinusFCA >= this.TMin;
    this.MAWPrTamMinusFCA = Math.Min(this.MAWPrcTamMinusFCA, this.MAWPrlTamMinusFCA);
    this.MawpL1 = this.MAWPrTamMinusFCA >= this.DesignPressure;
    this.MinimumMeasuredThicknessL1 = this.tmmMinusFCA >= this.Max05TminTlim;
    if (this.Cov > 10.0)
    {
      this.Level1Passed = false;
      this.Level1ResultMsg = "The Level 1 Assessment is UNACCEPTABLE";
    }
    else
    {
      if (this.AverageMeasuredThicknessL1 && this.MawpL1 && this.MinimumMeasuredThicknessL1)
      {
        this.Level1Passed = true;
        this.Level1ResultMsg = "The Level 1 Assessment is ACCEPTABLE";
        return CalculatorResult.Completed;
      }
      this.Level1ResultMsg = "The Level 1 Assessment is UNACCEPTABLE";
      this.Level1Passed = false;
    }
    if (!this.ToLevel2)
      return CalculatorResult.Completed;
    this.MinimumMeasuredThicknessL2 = this.MinimumMeasuredThicknessL1;
    this.TamMinusFCAOverRSFa = this.tamMinusFCA / this.RSFa;
    this.tamMinusTslMinusFCAOverRSFa = (this.tamMinusFCA - this.SupplementalThickness) / this.RSFa;
    this.MAWPrcTamMinusFCAOverRSFa = this.DesignPressure > 0.385 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency || this.checkTcMin > 0.5 * this.Radius ? this.AllowableStrength * this.LongitudinalWeldJointEfficiency * (Math.Pow((this.Radius + this.TamMinusFCAOverRSFa) / this.Radius, 2.0) - 1.0) * Math.Pow(Math.Pow((this.Radius + this.TamMinusFCAOverRSFa) / this.Radius, 2.0) + 1.0, -1.0) : this.AllowableStrength * this.LongitudinalWeldJointEfficiency * (this.TamMinusFCAOverRSFa / (this.Radius + 0.6 * this.TamMinusFCAOverRSFa));
    this.MAWPrlTamMinusTslMinusFCAOverRSFa = this.DesignPressure > 1.25 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency || this.checkTlMin > 0.5 * this.Radius ? this.AllowableStrength * this.CircumferentialWeldJointEfficiency * (Math.Pow((this.Radius + this.tamMinusTslMinusFCAOverRSFa - this.SupplementalThickness) / this.Radius, 2.0) - 1.0) : 2.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency * (this.tamMinusTslMinusFCAOverRSFa - this.SupplementalThickness) / (this.Radius - 0.4 * (this.tamMinusTslMinusFCAOverRSFa - this.SupplementalThickness));
    this.MAWPrTamMinusFCAOverRSFa = Math.Min(this.MAWPrcTamMinusFCAOverRSFa, this.MAWPrlTamMinusTslMinusFCAOverRSFa);
    this.AverageMeasuredThicknessL2 = this.tamMinusFCA >= this.TMin * this.RSFa;
    this.MawpL2 = this.MAWPrTamMinusFCAOverRSFa >= this.DesignPressure;
    if (this.Cov > 10.0)
    {
      this.Level2ResultMsg = "The Level 2 Assessment is UNACCEPTABLE";
      this.Level2Passed = false;
      return CalculatorResult.Completed;
    }
    if (this.MinimumMeasuredThicknessL2 && this.AverageMeasuredThicknessL2 && this.MawpL2)
    {
      this.Level2ResultMsg = "The Level 2 Assessment is ACCEPTABLE";
      this.Level2Passed = true;
      return CalculatorResult.Completed;
    }
    this.Level2ResultMsg = "The Level 2 Assessment is UNACCEPTABLE";
    this.Level2Passed = false;
    return CalculatorResult.Completed;
  }

  private MaterialStrengthLookupResult getStrengthResult(double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialASMEVIIIDiv1StressValueDTO div1StressValueDto = this._allowableStrengths.Where<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 < tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEVIIIDiv1StressValueDTO, double?>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEVIIIDiv1StressValueDTO>();
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Max<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Min<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).Value;
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
      {
        TLower = new double?(0.0),
        TUpper = new double?(0.0),
        SLower = new double?(0.0),
        SUpper = new double?(0.0)
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = div1StressValueDto.TLower.Value,
      TUpper = div1StressValueDto.TUpper.Value,
      SLower = div1StressValueDto.SLower.Value,
      SUpper = div1StressValueDto.SUpper.Value
    };
  }
}
