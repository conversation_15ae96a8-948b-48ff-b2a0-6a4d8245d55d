// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.CylindricalShell.GeneralMetalLossThicknessProfile.GeneralMetalLossThicknessProfile
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.CylindricalShell.GeneralMetalLossThicknessProfile;

public class GeneralMetalLossThicknessProfile : BaseCalculator
{
  public bool Div1B41999;
  private bool CheckPLessThanSE;
  private double checkTcMin;
  private double CheckTlMin;
  private bool CheckRtGreaterThanZero;
  private double WorkingTemperature;
  private string TemperatureLowerLimit;
  private List<MaterialASMEVIIIDiv1StressValueDTO> _allowableStrengths;
  private MaterialASMEVIIIDiv1DTO _material;
  private ThicknessReadings _thicknessReadings;

  public bool ToLevel2 { get; set; }

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double SupplementalThickness { get; set; }

  public double LongitudinalWeldJointEfficiency { get; set; }

  public double CircumferentialWeldJointEfficiency { get; set; }

  public double MechanicalAllowances { get; set; }

  public double LongitudinalThicknessReadingSpacing { get; set; }

  public double CircumferentialThicknessReadingSpacing { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double S { get; set; }

  public double C { get; set; }

  public double AllowableStrength { get; set; }

  public double Tcmin { get; set; }

  public double Tlmin { get; set; }

  public double Tmm { get; set; }

  public double Trd { get; set; }

  public double Rt { get; set; }

  public double Q { get; set; }

  public double L { get; set; }

  public double TSam { get; set; }

  public double TCam { get; set; }

  public double MAWPrcTSamMinusFCA { get; set; }

  public double MAWPrlTCamMinusFCA { get; set; }

  public double MAWPrcTsamMinusFCAOverRSFa { get; set; }

  public double MAWPrlTcamMinusTslMinusFCAOverRSFa { get; set; }

  public bool AverageMeasuredThicknessL1 { get; set; }

  public bool MawpL1 { get; set; }

  public bool MinimumMeasuredThicknessL1 { get; set; }

  public bool Level1Passed { get; set; }

  public string Level1 { get; set; }

  public bool AverageMeasuredThicknessL2 { get; set; }

  public bool MawpL2 { get; set; }

  public bool MinimumMeasuredThicknessL2 { get; set; }

  public bool Level2Passed { get; set; }

  public string Level2 { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private bool LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa { get; set; }

  private double TLower { get; set; }

  private double TUpper { get; set; }

  private double SLower { get; set; }

  private double SUpper { get; set; }

  private double Tc { get; set; }

  private double Radius { get; set; }

  private double TMin { get; set; }

  private double RSFa { get; set; }

  private double TamCMinusFCA { get; set; }

  private double TamSMinusFCA { get; set; }

  private double Tlim { get; set; }

  private double TmmMinusFCA { get; set; }

  private double Max0_5TminTlim { get; set; }

  private double MAWPrTamMinusFCA { get; set; }

  private bool LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin { get; set; }

  private bool LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin { get; set; }

  private double TamSMinusFCAOverRSFa { get; set; }

  private double TamCMinusTslMinusFCAOverRSFa { get; set; }

  private double MAWPrTamMinusFCAOverRSFa { get; set; }

  private bool LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa { get; set; }

  public GeneralMetalLossThicknessProfile(
    MaterialASMEVIIIDiv1DTO material,
    List<MaterialASMEVIIIDiv1StressValueDTO> allowableStrengths,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
    this._thicknessReadings = thicknessReadingsCalculator;
  }

  public virtual void CalculateTrd()
  {
    this.Trd = this.NominalThickness - this.ExternalUniformMetalLoss - this.InternalUniformMetalLoss;
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.UserDefined)
    {
      double num1 = this._material.YieldStrengthNew.Value;
      double num2 = this._material.TensileStrengthNew.Value;
      this.AllowableStrength = !this.Div1B41999 ? Math.Min(num1 / 1.5, num2 / 3.5) : Math.Min(num1 / 1.5, num2 / 4.0);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      this.WorkingTemperature = this.DesignTemperature;
      if (this.DesignTemperature < -30.0)
      {
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).");
        this.WorkingTemperature = -30.0;
      }
      if (this._material.CreepTemperature.HasValue)
      {
        double designTemperature = this.DesignTemperature;
        double? creepTemperature = this._material.CreepTemperature;
        if ((designTemperature <= creepTemperature.GetValueOrDefault() ? 0 : (creepTemperature.HasValue ? 1 : 0)) != 0)
          this.Warnings.Add("Creep Assessment is required due to the high temperature.");
      }
      MaterialStrengthLookupResult strengthResult = this.getStrengthResult(this.WorkingTemperature);
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.AboveRange)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material.";
        return CalculatorResult.Fail;
      }
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.BelowRange)
      {
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).");
        this.TemperatureLowerLimit = "Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).";
        strengthResult = this.getStrengthResult(-30.0);
      }
      this.TLower = strengthResult.TLower;
      this.TUpper = strengthResult.TUpper;
      this.SLower = strengthResult.SLower;
      this.SUpper = strengthResult.SUpper;
      this.AllowableStrength = (this.DesignTemperature - this.TLower) / (this.TUpper - this.TLower) * (this.SUpper - this.SLower) + this.SLower;
    }
    this.CheckPLessThanSE = this.DesignPressure >= this.AllowableStrength * this.LongitudinalWeldJointEfficiency;
    if (this.CheckPLessThanSE)
    {
      this.ErrorMessage = "Design pressure must be less than Allowable Stress * Longitudinal Weld Joint Efficiency";
      return CalculatorResult.Fail;
    }
    this.Radius = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    this.Tc = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance;
    this.Tcmin = this.DesignPressure > 0.385 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency ? this.Radius * (Math.Pow((this.AllowableStrength * this.LongitudinalWeldJointEfficiency + this.DesignPressure) / (this.AllowableStrength * this.LongitudinalWeldJointEfficiency - this.DesignPressure), 0.5) - 1.0) : this.DesignPressure * this.Radius / (this.AllowableStrength * this.LongitudinalWeldJointEfficiency - 0.6 * this.DesignPressure);
    this.checkTcMin = this.DesignPressure > 0.385 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency || this.Tcmin > 0.5 * this.Radius ? this.Radius * (Math.Pow((this.AllowableStrength * this.LongitudinalWeldJointEfficiency + this.DesignPressure) / (this.AllowableStrength * this.LongitudinalWeldJointEfficiency - this.DesignPressure), 0.5) - 1.0) : this.DesignPressure * this.Radius / (this.AllowableStrength * this.LongitudinalWeldJointEfficiency - 0.6 * this.DesignPressure);
    this.Tlmin = this.DesignPressure > 1.25 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency ? this.Radius * (Math.Pow(this.DesignPressure / (this.CircumferentialWeldJointEfficiency * this.AllowableStrength) + 1.0, 0.5) - 1.0) + this.SupplementalThickness : this.DesignPressure * this.Radius / (2.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency + 0.4 * this.DesignPressure) + this.SupplementalThickness;
    this.CheckTlMin = this.DesignPressure > 1.25 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency || this.Tlmin > 0.5 * this.Radius ? this.Radius * (Math.Pow(this.DesignPressure / (this.CircumferentialWeldJointEfficiency * this.AllowableStrength) + 1.0, 0.5) - 1.0) + this.SupplementalThickness : this.DesignPressure * this.Radius / (2.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency + 0.4 * this.DesignPressure) + this.SupplementalThickness;
    this.TMin = Math.Max(this.checkTcMin, this.CheckTlMin);
    this.Trd = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss;
    try
    {
      this.Tmm = this._thicknessReadings.GetMinimumValue();
      if (this.Tmm >= this.Trd)
      {
        this.ErrorMessage = "All thickness readings are greater than trd = tnom - LOSS. No metal loss has been found. This assessment is not needed";
        return CalculatorResult.Fail;
      }
    }
    catch
    {
      this.ErrorMessage = "Profile readings contain multiple Tmm's - Currently unable to proceed with the assessment.";
      return CalculatorResult.Fail;
    }
    this.RSFa = 0.9;
    this.Rt = (this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.Tc;
    this.Rt = Math.Round(this.Rt, 4);
    if (this.Rt <= 0.0)
    {
      this.CheckRtGreaterThanZero = true;
      this.ErrorMessage = "Remaining thickness ratio is less than or equal to zero. The assessment fails. ";
      return CalculatorResult.Fail;
    }
    this.Q = this.Rt >= this.RSFa ? 50.0 : 1.123 * Math.Pow(Math.Pow((1.0 - this.Rt) / (1.0 - this.Rt / this.RSFa), 2.0) - 1.0, 0.5);
    this.L = this.Q * Math.Pow(2.0 * this.Radius * this.Tc, 0.5);
    this._thicknessReadings.ProcessThicknessReadings(this.L, this.CircumferentialThicknessReadingSpacing, this.LongitudinalThicknessReadingSpacing, this.Trd);
    this.S = this._thicknessReadings.S;
    this.C = this._thicknessReadings.C;
    this.TCam = this._thicknessReadings.Tcam;
    this.TSam = this._thicknessReadings.Tsam;
    this.TamCMinusFCA = this.TCam - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.TamSMinusFCA = this.TSam - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.Tlim = Math.Max(0.2 * this.NominalThickness, 2.5);
    this.TmmMinusFCA = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.Max0_5TminTlim = Math.Max(0.5 * this.TMin, this.Tlim);
    this.MAWPrcTSamMinusFCA = this.DesignPressure > 0.385 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency || this.checkTcMin > 0.5 * this.Radius ? this.AllowableStrength * this.LongitudinalWeldJointEfficiency * (Math.Pow((this.Radius + this.TamSMinusFCA) / this.Radius, 2.0) - 1.0) * Math.Pow(Math.Pow((this.Radius + this.TamSMinusFCA) / this.Radius, 2.0) + 1.0, -1.0) : this.AllowableStrength * this.LongitudinalWeldJointEfficiency * this.TamSMinusFCA / (this.Radius + 0.6 * this.TamSMinusFCA);
    this.MAWPrlTCamMinusFCA = this.DesignPressure > 1.25 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency || this.CheckTlMin > 0.5 * this.Radius ? this.AllowableStrength * this.CircumferentialWeldJointEfficiency * (Math.Pow((this.Radius + this.TamCMinusFCA - this.SupplementalThickness) / this.Radius, 2.0) - 1.0) : 2.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency * (this.TamCMinusFCA - this.SupplementalThickness) / (this.Radius - 0.4 * (this.TamCMinusFCA - this.SupplementalThickness));
    this.MAWPrTamMinusFCA = Math.Min(this.MAWPrcTSamMinusFCA, this.MAWPrlTCamMinusFCA);
    this.LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin = this.TamCMinusFCA >= this.CheckTlMin;
    this.LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin = this.TamSMinusFCA >= this.checkTcMin;
    this.AverageMeasuredThicknessL1 = this.LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin && this.LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin;
    this.MawpL1 = this.MAWPrTamMinusFCA >= this.DesignPressure;
    this.MinimumMeasuredThicknessL1 = this.TmmMinusFCA >= this.Max0_5TminTlim;
    if (this.LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin && this.LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin && this.MawpL1 && this.MinimumMeasuredThicknessL1)
    {
      this.Level1 = "The Level 1 Assessment is ACCEPTABLE";
      this.Level1Passed = true;
      return CalculatorResult.Completed;
    }
    this.Level1 = "The Level 1 Assessment is UNACCEPTABLE";
    this.Level1Passed = false;
    if (!this.ToLevel2)
      return CalculatorResult.Completed;
    this.MinimumMeasuredThicknessL2 = this.MinimumMeasuredThicknessL1;
    this.TamSMinusFCAOverRSFa = this.TamSMinusFCA / this.RSFa;
    this.TamCMinusTslMinusFCAOverRSFa = (this.TamCMinusFCA - this.SupplementalThickness) / this.RSFa;
    this.MAWPrcTsamMinusFCAOverRSFa = this.DesignPressure > 0.385 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency || this.checkTcMin > 0.5 * this.Radius ? this.AllowableStrength * this.LongitudinalWeldJointEfficiency * (Math.Pow((this.Radius + this.TamSMinusFCA / this.RSFa) / this.Radius, 2.0) - 1.0) * Math.Pow(Math.Pow((this.Radius + this.TamSMinusFCA / this.RSFa) / this.Radius, 2.0) + 1.0, -1.0) : this.AllowableStrength * this.LongitudinalWeldJointEfficiency * this.TamSMinusFCA / this.RSFa / (this.Radius + 0.6 * this.TamSMinusFCAOverRSFa);
    this.MAWPrlTcamMinusTslMinusFCAOverRSFa = this.DesignPressure > 1.25 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency || this.CheckTlMin > 0.5 * this.Radius ? this.AllowableStrength * this.CircumferentialWeldJointEfficiency * (Math.Pow((this.Radius + this.TamCMinusTslMinusFCAOverRSFa - this.SupplementalThickness) / this.Radius, 2.0) - 1.0) : 2.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency * ((this.TamCMinusTslMinusFCAOverRSFa - this.SupplementalThickness) / (this.Radius - 0.4 * (this.TamCMinusTslMinusFCAOverRSFa - this.SupplementalThickness)));
    this.MAWPrTamMinusFCAOverRSFa = Math.Min(this.MAWPrcTsamMinusFCAOverRSFa, this.MAWPrlTcamMinusTslMinusFCAOverRSFa);
    this.LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa = this.TamCMinusFCA >= this.RSFa * this.CheckTlMin;
    this.LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa = this.TamSMinusFCA >= this.checkTcMin * this.RSFa;
    this.MawpL2 = this.MAWPrTamMinusFCAOverRSFa >= this.DesignPressure;
    this.AverageMeasuredThicknessL2 = this.LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa && this.LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa;
    if (this.LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa && this.LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa && this.MawpL2 && this.MinimumMeasuredThicknessL2)
    {
      this.Level2 = "The Level 2 Assessment is ACCEPTABLE";
      this.Level2Passed = true;
    }
    else
    {
      this.Level2 = "The Level 2 Assessment is UNACCEPTABLE";
      this.Level2Passed = false;
    }
    return CalculatorResult.Completed;
  }

  private MaterialStrengthLookupResult getStrengthResult(double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialASMEVIIIDiv1StressValueDTO div1StressValueDto = this._allowableStrengths.Where<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 < tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEVIIIDiv1StressValueDTO, double?>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEVIIIDiv1StressValueDTO>();
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Max<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Min<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).Value;
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
      {
        TLower = new double?(0.0),
        TUpper = new double?(0.0),
        SLower = new double?(0.0),
        SUpper = new double?(0.0)
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = div1StressValueDto.TLower.Value,
      TUpper = div1StressValueDto.TUpper.Value,
      SLower = div1StressValueDto.SLower.Value,
      SUpper = div1StressValueDto.SUpper.Value
    };
  }
}
