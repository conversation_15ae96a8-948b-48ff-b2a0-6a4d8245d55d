// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.CylindricalShell.MembraneStress.MembraneStress
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.CylindricalShell.MembraneStress;

public class MembraneStress : BaseCalculator
{
  public double DesignPressure { private get; set; }

  public double NominalThickness { private get; set; }

  public double NominalInsideDiameter { private get; set; }

  public double InternalFutureCorrosionAllowance { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public double ExternalFutureCorrosionAllowance { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public double LongitudinalWeldJointEfficiency { private get; set; }

  public double CircumferentialWeldJointEfficiency { private get; set; }

  public double SupplementalThickness { private get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double SigmaCm { get; private set; }

  public double SigmaLm { get; private set; }

  public double SigmaM { get; private set; }

  public MembraneStress() => this.Warnings = new List<string>();

  public CalculatorResult CalculateAssessment()
  {
    double num1 = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    double num2 = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance;
    double num3 = Math.Max(this.DesignPressure / this.LongitudinalWeldJointEfficiency * (num1 / num2 + 0.6), this.DesignPressure / (2.0 * this.CircumferentialWeldJointEfficiency) * (num1 / (num2 - this.SupplementalThickness) - 0.4));
    this.SigmaCm = this.DesignPressure > 0.385 * num3 * this.LongitudinalWeldJointEfficiency || this.NominalThickness > 0.5 * num1 ? this.DesignPressure / this.LongitudinalWeldJointEfficiency * (Math.Pow((num1 + num2) / num1, 2.0) + 1.0) * Math.Pow(Math.Pow((num1 + num2) / num1, 2.0) - 1.0, -1.0) : this.DesignPressure / this.LongitudinalWeldJointEfficiency * (num1 / num2 + 0.6);
    this.SigmaLm = this.DesignPressure > 1.25 * num3 * this.CircumferentialWeldJointEfficiency || this.NominalThickness > 0.5 * num1 ? this.DesignPressure / this.CircumferentialWeldJointEfficiency * Math.Pow(Math.Pow((num1 + num2 - this.SupplementalThickness) / num1, 2.0) - 1.0, -1.0) : this.DesignPressure / (2.0 * this.CircumferentialWeldJointEfficiency) * (num1 / (num2 - this.SupplementalThickness) - 0.4);
    this.SigmaM = Math.Max(this.SigmaCm, this.SigmaLm);
    return CalculatorResult.Completed;
  }
}
