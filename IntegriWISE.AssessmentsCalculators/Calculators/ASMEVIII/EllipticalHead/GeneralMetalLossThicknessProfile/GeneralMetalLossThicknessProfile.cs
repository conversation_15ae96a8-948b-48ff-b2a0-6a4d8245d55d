// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.GeneralMetalLossThicknessProfile.GeneralMetalLossThicknessProfile
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.GeneralMetalLossThicknessProfile;

public class GeneralMetalLossThicknessProfile : BaseCalculator
{
  public bool Div1B41999;
  private double tmin1;
  private bool CheckPLessThanSE;
  private double checkTcMin;
  private double CheckTlMin;
  private bool CheckRtGreaterThanZero;
  private double WorkingTemperature;
  private string TemperatureLowerLimit;
  private List<MaterialASMEVIIIDiv1StressValueDTO> _allowableStrengths;
  private MaterialASMEVIIIDiv1DTO _material;
  private ThicknessReadings _thicknessReadings;

  public bool ToLevel2 { get; set; }

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double WeldJointEfficiency { get; set; }

  public double HeightHead { get; set; }

  public bool CenteredCorroded { get; set; }

  public double LongitudinalThicknessReadingSpacing { get; set; }

  public double CircumferentialThicknessReadingSpacing { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double S { get; set; }

  public double C { get; set; }

  public double AllowableStrength { get; set; }

  public double Tmin { get; set; }

  public double Tlmin { get; set; }

  public double Tmm { get; set; }

  public double Trd { get; set; }

  public double Rt { get; set; }

  public double Q { get; set; }

  public double L { get; set; }

  public double TSam { get; set; }

  public double TCam { get; set; }

  public double MAWPrcTSamMinusFCA { get; set; }

  public double MAWPrcTsamMinusFCAOverRSFa { get; set; }

  public bool AverageMeasuredThicknessL1 { get; set; }

  public bool MawpL1 { get; set; }

  public bool MinimumMeasuredThicknessL1 { get; set; }

  public bool Level1Passed { get; set; }

  public string Level1 { get; set; }

  public bool AverageMeasuredThicknessL2 { get; set; }

  public bool MawpL2 { get; set; }

  public bool MinimumMeasuredThicknessL2 { get; set; }

  public bool Level2Passed { get; set; }

  public string Level2 { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private bool ConditionP { get; set; }

  private bool RtGreaterThan0 { get; set; }

  private double MinTamsTamc { get; set; }

  private double MinTamsTamcMinusFCA { get; set; }

  private double MinTamsTamcMinusFCAOverRSFa { get; set; }

  private double Rell { get; set; }

  private double K { get; set; }

  private bool LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa { get; set; }

  private double TLower { get; set; }

  private double TUpper { get; set; }

  private double SLower { get; set; }

  private double SUpper { get; set; }

  private double Tc { get; set; }

  private double Radius { get; set; }

  private double RSFa { get; set; }

  private double TamCMinusFCA { get; set; }

  private double TamSMinusFCA { get; set; }

  private double Tlim { get; set; }

  private double TmmMinusFCA { get; set; }

  private double Max0_5TminTlim { get; set; }

  private bool LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin { get; set; }

  private bool LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin { get; set; }

  private double TamSMinusFCAOverRSFa { get; set; }

  private double TamCMinusTslMinusFCAOverRSFa { get; set; }

  private bool LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa { get; set; }

  public GeneralMetalLossThicknessProfile(
    MaterialASMEVIIIDiv1DTO material,
    List<MaterialASMEVIIIDiv1StressValueDTO> allowableStrengths,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
    this._thicknessReadings = thicknessReadingsCalculator;
  }

  public virtual void CalculateTrd()
  {
    this.Trd = this.NominalThickness - this.ExternalUniformMetalLoss - this.InternalUniformMetalLoss;
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.UserDefined)
    {
      double num1 = this._material.YieldStrengthNew.Value;
      double num2 = this._material.TensileStrengthNew.Value;
      this.AllowableStrength = !this.Div1B41999 ? Math.Min(num1 / 1.5, num2 / 3.5) : Math.Min(num1 / 1.5, num2 / 4.0);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      this.WorkingTemperature = this.DesignTemperature;
      if (this.DesignTemperature < -30.0)
      {
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).");
        this.WorkingTemperature = -30.0;
      }
      if (this._material.CreepTemperature.HasValue)
      {
        double designTemperature = this.DesignTemperature;
        double? creepTemperature = this._material.CreepTemperature;
        if ((designTemperature <= creepTemperature.GetValueOrDefault() ? 0 : (creepTemperature.HasValue ? 1 : 0)) != 0)
          this.Warnings.Add("Creep Assessment is required due to the high temperature.");
      }
      MaterialStrengthLookupResult strengthResult = this.getStrengthResult(this.WorkingTemperature);
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.AboveRange)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material.";
        return CalculatorResult.Fail;
      }
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.BelowRange)
      {
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).");
        this.TemperatureLowerLimit = "Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).";
        strengthResult = this.getStrengthResult(-30.0);
      }
      this.TLower = strengthResult.TLower;
      this.TUpper = strengthResult.TUpper;
      this.SLower = strengthResult.SLower;
      this.SUpper = strengthResult.SUpper;
      this.AllowableStrength = (this.DesignTemperature - this.TLower) / (this.TUpper - this.TLower) * (this.SUpper - this.SLower) + this.SLower;
    }
    this.Rell = this.NominalInsideDiameter / (2.0 * this.HeightHead);
    this.K = this.CenteredCorroded ? 0.25346 + 0.13995 * this.Rell + 0.12238 * Math.Pow(this.Rell, 2.0) - 0.015297 * Math.Pow(this.Rell, 3.0) : 1.0 / 6.0 * (2.0 + Math.Pow(this.Rell, 2.0));
    if (this.Rell < 1.7 || this.Rell > 2.2)
    {
      this.ErrorMessage = "Minimum thickness can not be computed because the component geometry does not meet the condition 1.7 ≤ Rell ≤ 2.2. (Annex A API 579, Clause A.3.6)";
      return CalculatorResult.Fail;
    }
    this.Radius = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    this.Tc = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance;
    if (2.0 * this.Radius * (0.44 * this.Rell + 0.02) / this.Tc > 500.0)
    {
      this.ErrorMessage = "Minimum thickness; MAWP and membrane stress can not be computed because the component geometry does not meet the condition D(0.44Rell+0.02)/tmin ≤ 500 (Annex A API 579, Clause A.3.6)";
      return CalculatorResult.Fail;
    }
    this.ConditionP = 2.0 * this.AllowableStrength * this.WeldJointEfficiency > this.DesignPressure;
    if (!this.ConditionP)
    {
      this.ErrorMessage = "Design Pressure should be less than 2 * Allowable Strength * Weld Joint Efficiency";
      this.Level1 = "Level1 failed";
      this.Level1Passed = false;
      return CalculatorResult.Fail;
    }
    if (this.ConditionP)
      this.Tmin = this.DesignPressure * 2.0 * this.Radius * this.K / (2.0 * this.AllowableStrength * this.WeldJointEfficiency - 0.2 * this.DesignPressure);
    this.Trd = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss;
    try
    {
      this.Tmm = this._thicknessReadings.GetMinimumValue();
      if (this.Tmm >= this.Trd)
      {
        this.ErrorMessage = "All thickness readings are greater than trd = tnom - LOSS. No metal loss has been found. This assessment is not needed";
        return CalculatorResult.Fail;
      }
    }
    catch
    {
      this.ErrorMessage = "Profile readings contain multiple Tmm's - Currently unable to proceed with the assessment.";
      return CalculatorResult.Fail;
    }
    this.RSFa = 0.9;
    this.Rt = (this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.Tc;
    this.Rt = Math.Round(this.Rt, 4);
    if (this.Rt <= 0.0)
    {
      this.ErrorMessage = "Remaining thickness ratio is less than or equal to zero. The assessment fails. ";
      return CalculatorResult.Fail;
    }
    if (this.Rt < this.RSFa)
      this.Q = 1.123 * Math.Pow(Math.Pow((1.0 - this.Rt) / (1.0 - this.Rt / this.RSFa), 2.0) - 1.0, 0.5);
    this.L = this.Q * Math.Pow(2.0 * this.Radius * this.Tc, 0.5);
    this._thicknessReadings.ProcessThicknessReadings(this.L, this.CircumferentialThicknessReadingSpacing, this.LongitudinalThicknessReadingSpacing, this.Trd);
    this.S = this._thicknessReadings.S;
    this.C = this._thicknessReadings.C;
    this.TCam = this._thicknessReadings.Tcam;
    this.TSam = this._thicknessReadings.Tsam;
    this.MinTamsTamc = Math.Min(this.TCam, this.TSam);
    this.MinTamsTamcMinusFCA = this.MinTamsTamc - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.Tlim = Math.Max(0.2 * this.NominalThickness, 2.5);
    this.TmmMinusFCA = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.Max0_5TminTlim = Math.Max(0.5 * this.Tmin, this.Tlim);
    this.MAWPrcTSamMinusFCA = 2.0 * this.AllowableStrength * this.WeldJointEfficiency * this.MinTamsTamcMinusFCA / (2.0 * this.Radius * this.K + 0.2 * this.MinTamsTamcMinusFCA);
    this.AverageMeasuredThicknessL1 = this.MinTamsTamcMinusFCA >= this.Tmin;
    this.MawpL1 = this.MAWPrcTSamMinusFCA >= this.DesignPressure;
    this.MinimumMeasuredThicknessL1 = this.TmmMinusFCA >= this.Max0_5TminTlim;
    if (this.AverageMeasuredThicknessL1 && this.MawpL1 && this.MinimumMeasuredThicknessL1)
    {
      this.Level1 = "The Level 1 Assessment is ACCEPTABLE";
      this.Level1Passed = true;
      return CalculatorResult.Completed;
    }
    this.Level1 = "The Level 1 Assessment is UNACCEPTABLE";
    this.Level1Passed = false;
    if (!this.ToLevel2)
      return CalculatorResult.Completed;
    this.MinimumMeasuredThicknessL2 = this.MinimumMeasuredThicknessL1;
    this.MinTamsTamcMinusFCAOverRSFa = this.MinTamsTamcMinusFCA / this.RSFa;
    this.MAWPrcTsamMinusFCAOverRSFa = 2.0 * this.AllowableStrength * this.WeldJointEfficiency * this.MinTamsTamcMinusFCAOverRSFa / (2.0 * this.Radius * this.K + 0.2 * this.MinTamsTamcMinusFCAOverRSFa);
    this.AverageMeasuredThicknessL2 = this.MinTamsTamcMinusFCAOverRSFa >= this.Tmin;
    this.MawpL2 = this.MAWPrcTsamMinusFCAOverRSFa >= this.DesignPressure;
    if (this.AverageMeasuredThicknessL2 && this.MawpL2 && this.MinimumMeasuredThicknessL2)
    {
      this.Level2 = "the Level 2 Assessment is ACCEPTABLE";
      this.Level2Passed = true;
    }
    else
    {
      this.Level2 = "The Level 2 Assessment is UNACCEPTABLE";
      this.Level2Passed = false;
    }
    return CalculatorResult.Completed;
  }

  private MaterialStrengthLookupResult getStrengthResult(double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialASMEVIIIDiv1StressValueDTO div1StressValueDto = this._allowableStrengths.Where<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 < tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEVIIIDiv1StressValueDTO, double?>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEVIIIDiv1StressValueDTO>();
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Max<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Min<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).Value;
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
      {
        TLower = new double?(0.0),
        TUpper = new double?(0.0),
        SLower = new double?(0.0),
        SUpper = new double?(0.0)
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = div1StressValueDto.TLower.Value,
      TUpper = div1StressValueDto.TUpper.Value,
      SLower = div1StressValueDto.SLower.Value,
      SUpper = div1StressValueDto.SUpper.Value
    };
  }
}
