// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.MembraneStress.MembraneStress
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.MembraneStress;

public class MembraneStress : BaseCalculator
{
  public double DesignPressure { private get; set; }

  public double NominalThickness { private get; set; }

  public double NominalInsideDiameter { private get; set; }

  public double HeightEllipticalHead { private get; set; }

  public bool CorrodedRegionWithin0_8D { private get; set; }

  public double WeldJointEfficiency { private get; set; }

  public double InternalFutureCorrosionAllowance { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public double ExternalFutureCorrosionAllowance { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double SigmaM { get; private set; }

  public MembraneStress() => this.Warnings = new List<string>();

  public CalculatorResult CalculateAssessment()
  {
    if (this.NominalInsideDiameter / 2.0 / this.HeightEllipticalHead < 1.7 || this.NominalInsideDiameter / 2.0 / this.HeightEllipticalHead > 2.2)
    {
      this.ErrorMessage = "Membrane stress can not be computed because the component geometry does not meet the condition 1.7 ≤ Rell ≤ 2.2. (Annex A API 579, Clause A.3.6)";
      return CalculatorResult.Fail;
    }
    double num1 = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    double num2 = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance;
    double x = this.NominalInsideDiameter / (2.0 * this.HeightEllipticalHead);
    double num3 = this.CorrodedRegionWithin0_8D ? 0.25346 + 0.13995 * x + 0.12238 * Math.Pow(x, 2.0) - 0.015297 * Math.Pow(x, 3.0) : 1.0 / 6.0 * (2.0 + Math.Pow(x, 2.0));
    if (2.0 * num1 * (0.44 * x + 0.02) / num2 > 500.0)
    {
      this.ErrorMessage = "Membrane stress can not be computed because the component geometry does not meet the condition D(0.44Rell+0.02)/tc ≤ 500(Annex A API 579, Clause A.3.6)";
      return CalculatorResult.Fail;
    }
    this.SigmaM = this.DesignPressure / (2.0 * this.WeldJointEfficiency) * (2.0 * num1 * num3 / num2 + 0.2);
    return CalculatorResult.Completed;
  }
}
