// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.LocalMetalLossGrooveLike.LocalMetalLossGrooveLike
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.LocalMetalLossGrooveLike;

public class LocalMetalLossGrooveLike : BaseCalculator
{
  public CalculatorResult MAWPResult;
  private double MtTable5_2;
  private double RtfIGURE5_6;
  private double lambdaTable5_2;

  public LocalMetalLossGrooveLike() => this.Warnings = new List<string>();

  public List<MaterialTSFCurveDTO> TSFLookup { get; set; }

  public List<string> Warnings { get; private set; }

  public double NominalInsideDiameter { get; set; }

  private bool RtGreater0 { get; set; }

  private double Radius { get; set; }

  private double RSFa { get; set; }

  private double Rt { get; set; }

  public bool BdLessThan50 { get; set; }

  public bool BlisterCrownCrackingAndVentHoles { get; set; }

  public bool BlisterProjection { get; set; }

  public bool BlisterVented { get; set; }

  public bool ConditionC { get; set; }

  public bool ConditionDistanceMajorDiscontinuity { get; set; }

  public bool ConditionS { get; set; }

  public bool ConditionsCircumferentialExtent { get; set; }

  public bool DDivTcGreaterThanEqual20 { get; set; }

  public bool DistanceToWeldSeam { get; set; }

  public bool EcBetween07And1 { get; set; }

  public bool ElBetween07And1 { get; set; }

  public bool grGreaterOrEqual1MinusRttc { get; set; }

  public bool lambdaCLessThanEqual9 { get; set; }

  public bool Level1Passed { get; set; }

  public bool LmsdGreaterOrEqual1_8Dtc { get; set; }

  public bool MAWPLevel { get; set; }

  public bool MinimumMeasuredUndamagedThickness { get; set; }

  public bool RSFBetween07And1 { get; set; }

  public bool RSFGreaterEqualRSFa { get; set; }

  public bool RtGreaterOrEqual0_20 { get; set; }

  public bool ScreeningCriteriaFigure5_6 { get; set; }

  public bool ScreeningCriteriaFigure5_8 { get; set; }

  public bool tmmMinusFCAGreaterOrEqual2_5 { get; set; }

  public double BdDiameter { get; set; }

  public double BlisterBulgeProjection { get; set; }

  public double BlisterSpacing { get; set; }

  public double C { get; set; }

  public double CircumferentialBlisterDimension { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double GrooveLikeFlawAngle { get; set; }

  public double GrooveLikeFlawDepth { get; set; }

  public double GrooveLikeFlawLength { get; set; }

  public double GrooveLikeFlawRadius { get; set; }

  public double GrooveLikeFlawWidth { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double lambda { get; set; }

  public double lambdaC { get; set; }

  public double Lmsd { get; set; }

  public double LongitudinalBlisterDimension { get; set; }

  public double LW { get; set; }

  public double MAWP { get; set; }

  public double MAWPr { get; set; }

  public double NominalThickness { get; set; }

  public double RSF { get; set; }

  public double S { get; set; }

  public double SC { get; set; }

  public double tc { get; set; }

  public double tmm { get; set; }

  public double TMMBlister { get; set; }

  public double trd { get; set; }

  public double TSF { get; set; }

  public double WeldJointEfficiency { get; set; }

  public int iConditionLongitudinalExtentLevel1 { get; set; }

  public string ConditionCircumferentialExtentLevel1 { get; set; }

  public string ConditionLongitudinalExtentLevel1 { get; set; }

  public string ErrorMessage { get; private set; }

  public string Level1 { get; set; }

  public string WarningFailureLevel1 { get; set; }

  public CalculatorResult CalculateAssessment()
  {
    this.Level1 = "Level 1 Assessment UNACCEPTABLE";
    this.Level1Passed = false;
    this.Radius = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    this.tc = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance;
    this.trd = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss;
    this.tmm = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss - this.GrooveLikeFlawDepth;
    this.RSFa = 0.9;
    this.Rt = (this.tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.tc;
    this.Rt = Math.Round(this.Rt, 4);
    this.RtGreater0 = this.Rt > 0.0;
    if (!this.RtGreater0)
    {
      this.ErrorMessage = "Remaining thickness ratio is less than or equal to zero. The assessment fails. ";
      return CalculatorResult.Fail;
    }
    this.S = this.GrooveLikeFlawLength * Math.Cos(this.GetRadians(this.GrooveLikeFlawAngle)) + this.GrooveLikeFlawWidth * Math.Sin(this.GetRadians(this.GrooveLikeFlawAngle));
    this.C = this.GrooveLikeFlawLength * Math.Sin(this.GetRadians(this.GrooveLikeFlawAngle)) + this.GrooveLikeFlawWidth * Math.Cos(this.GetRadians(this.GrooveLikeFlawAngle));
    this.lambda = 1.285 * this.S / Math.Pow(2.0 * this.Radius * this.tc, 0.5);
    this.RtGreaterOrEqual0_20 = this.Rt >= 0.2;
    this.tmmMinusFCAGreaterOrEqual2_5 = this.tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance >= 2.5;
    this.LmsdGreaterOrEqual1_8Dtc = this.Lmsd >= 1.7999999523162842 * Math.Pow(this.Radius * 2.0 * this.tc, 0.5);
    this.grGreaterOrEqual1MinusRttc = this.GrooveLikeFlawRadius >= (1.0 - this.Rt) * this.tc;
    this.lambdaTable5_2 = this.lambda > 20.0 ? 20.0 : this.lambda;
    this.MtTable5_2 = 1001.0 / 1000.0 - 0.014195 * this.lambdaTable5_2 + 0.2909 * Math.Pow(this.lambdaTable5_2, 2.0) - 0.09642 * Math.Pow(this.lambdaTable5_2, 3.0) + 0.02089 * Math.Pow(this.lambdaTable5_2, 4.0) - 0.003054 * Math.Pow(this.lambdaTable5_2, 5.0) + 2.957 * Math.Pow(10.0, -4.0) * Math.Pow(this.lambdaTable5_2, 6.0) - 1.8462 * Math.Pow(10.0, -5.0) * Math.Pow(this.lambdaTable5_2, 7.0) + 7.1553 * Math.Pow(10.0, -7.0) * Math.Pow(this.lambdaTable5_2, 8.0) - 1.5631 * Math.Pow(10.0, -8.0) * Math.Pow(this.lambdaTable5_2, 9.0) + 916.0 / 625.0 * Math.Pow(10.0, -10.0) * Math.Pow(this.lambdaTable5_2, 10.0);
    this.RtfIGURE5_6 = this.lambda > 0.354 ? (this.lambda >= 20.0 ? 0.9 : (this.RSFa - this.RSFa / this.MtTable5_2) * Math.Pow(1.0 - this.RSFa / this.MtTable5_2, -1.0)) : 0.2;
    this.ScreeningCriteriaFigure5_6 = this.Rt >= this.RtfIGURE5_6;
    this.RSF = this.Rt / (1.0 - 1.0 / this.MtTable5_2 * (1.0 - this.Rt));
    this.RSFGreaterEqualRSFa = this.RSF >= this.RSFa;
    this.iConditionLongitudinalExtentLevel1 = 0;
    if (this.RtGreaterOrEqual0_20 && this.tmmMinusFCAGreaterOrEqual2_5 && this.LmsdGreaterOrEqual1_8Dtc && this.grGreaterOrEqual1MinusRttc)
    {
      if (this.ScreeningCriteriaFigure5_6 || this.RSFGreaterEqualRSFa)
      {
        this.ConditionLongitudinalExtentLevel1 = "The longitudinal extent of the flaw is ACCEPTABLE for operation at the MAWP.";
        this.Level1 = "Level 1 Assessment ACCEPTABLE";
        this.iConditionLongitudinalExtentLevel1 = 1;
        this.Level1Passed = true;
      }
      else
      {
        this.ConditionLongitudinalExtentLevel1 = "The longitudinal extent of the flaw is ACCEPTABLE for operation at the MAWPr.";
        this.Level1 = "Level 1 Assessment UNACCEPTABLE. The component is unacceptable for operation at MAWP but it is ACCEPTABLE for operation at MAWPr";
        this.iConditionLongitudinalExtentLevel1 = 2;
        this.Level1Passed = false;
      }
    }
    else
    {
      this.ConditionLongitudinalExtentLevel1 = "The longitudinal extent of the flaw is UNACCEPTABLE";
      this.Level1 = "Level 1 Assessment UNACCEPTABLE";
      this.iConditionLongitudinalExtentLevel1 = 3;
      this.Level1Passed = false;
    }
    if (!this.Level1Passed)
    {
      this.WarningFailureLevel1 = "As Level 1 assessment fails, the region of metal loss categorised as a groove can be evaluated using Level 2 assessment procedure for a Local Thin Area if the groove radius meets the condition gr >= (1-Rt)tc. Otherwise the groove shall be evaluated as an equivalent crack-like flaw using the assessment procedures in Part 9 API 579.";
      this.Warnings.Add(this.WarningFailureLevel1);
    }
    if (this.MAWPLevel)
    {
      if (this.MAWPResult == CalculatorResult.Completed)
      {
        if (this.RSF < this.RSFa)
          this.MAWPr = this.MAWP * this.RSF / this.RSFa;
      }
      else
      {
        this.ErrorMessage = "Unknow error in MAWP Calculator. Please Contact TWI Support to report this error";
        return CalculatorResult.Fail;
      }
    }
    return CalculatorResult.Completed;
  }

  private double GetRadians(double degrees) => Math.PI * degrees / 180.0;
}
