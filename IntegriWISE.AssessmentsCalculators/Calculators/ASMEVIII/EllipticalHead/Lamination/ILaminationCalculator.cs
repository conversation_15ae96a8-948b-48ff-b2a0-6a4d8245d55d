// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.Lamination.ILaminationCalculator
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.Lamination;

public interface ILaminationCalculator
{
  double NominalInsideDiameter { get; set; }

  double NominalThickness { get; set; }

  double InternalUniformMetalLoss { get; set; }

  double ExternalUniformMetalLoss { get; set; }

  double InternalFutureCorrosionAllowance { get; set; }

  double ExternalFutureCorrosionAllowance { get; set; }

  string MultipleLaminations { get; set; }

  double LongitudinalLaminationDimension { get; set; }

  double CircumferentialLaminationDimension { get; set; }

  double LaminationHeight { get; set; }

  double LaminationSpacing { get; set; }

  double TMM { get; set; }

  double LW { get; set; }

  double LMSD { get; set; }

  bool InHydrogenChargingService { get; set; }

  bool MAWPLevel { get; set; }

  double tc { get; set; }

  bool CrackLikeFlaw { get; set; }

  bool LaminationIsNotSurfaceBreaking { get; set; }

  bool DistanceToTheNearestDiscontinuity { get; set; }

  bool DistanceToTheNearestWeldSeam { get; set; }

  bool HydrogenChargingAndDimensions { get; set; }

  bool HydrogenChargingAndDimensions2 { get; set; }

  bool Level1Passed { get; }

  string Level1 { get; }

  List<string> Warnings { get; }

  string ErrorMessage { get; }

  CalculatorResult CalculateAssessment();

  double MAWP { get; set; }

  double AllowableStrength { get; set; }
}
