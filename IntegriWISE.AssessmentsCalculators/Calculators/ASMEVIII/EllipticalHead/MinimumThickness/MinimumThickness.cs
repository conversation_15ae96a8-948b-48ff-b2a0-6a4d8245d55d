// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.MinimumThickness.MinimumThickness
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.MinimumThickness;

public class MinimumThickness : BaseCalculator
{
  public bool ASMEVIIIBefore1999;
  private double tupper;
  private double tlower;
  private double supper;
  private double slower;
  private MaterialASMEVIIIDiv1DTO _material;
  private List<MaterialASMEVIIIDiv1StressValueDTO> _allowableStrengths;

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double? YieldStrengthNew { get; set; }

  public double? TensileStrengthNew { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalInsideRadius { get; set; }

  public double NominalHeight { get; set; }

  public double WeldJointEfficiency { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public bool CenteredCorroded { get; set; }

  public double AllowableStrength { get; private set; }

  public double TMin { get; private set; }

  public double Radius { get; set; }

  public double TcMin { get; set; }

  public double TlMin { get; set; }

  public List<string> Warnings { get; set; }

  public string ErrorMessage { get; set; }

  public MinimumThickness(
    MaterialASMEVIIIDiv1DTO material,
    List<MaterialASMEVIIIDiv1StressValueDTO> allowableStrengths)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    MaterialStrengthLookupResult strengthLookupResult = new MaterialStrengthLookupResult();
    if (this._material.UserDefined)
    {
      this.AllowableStrength = !this.ASMEVIIIBefore1999 ? Math.Min(this.YieldStrengthNew.Value / 1.5, this.TensileStrengthNew.Value / 3.5) : Math.Min(this.YieldStrengthNew.Value / 1.5, this.TensileStrengthNew.Value / 4.0);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
      strengthLookupResult.TemperatureRangeStatus = MaterialStrengthRangeStatus.InRange;
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      strengthLookupResult = this.getStrengthResult(this.DesignTemperature);
      if (strengthLookupResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.AboveRange)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material.";
        return CalculatorResult.Fail;
      }
      if (strengthLookupResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.BelowRange)
      {
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).");
        strengthLookupResult = this.getStrengthResult(-30.0);
      }
      if (this._material.CreepTemperature.HasValue)
      {
        double designTemperature = this.DesignTemperature;
        double? creepTemperature = this._material.CreepTemperature;
        if ((designTemperature <= creepTemperature.GetValueOrDefault() ? 0 : (creepTemperature.HasValue ? 1 : 0)) != 0)
          this.Warnings.Add("Creep Assessment is required due to the high temperature.");
      }
      this.tlower = strengthLookupResult.TLower;
      this.tupper = strengthLookupResult.TUpper;
      this.slower = strengthLookupResult.SLower;
      this.supper = strengthLookupResult.SUpper;
      this.AllowableStrength = (this.DesignTemperature - this.tlower) / (this.tupper - this.tlower) * (this.supper - this.slower) + this.slower;
    }
    if (0.2 * this.DesignPressure >= 2.0 * this.AllowableStrength * this.WeldJointEfficiency)
    {
      this.Warnings.Add("(0.2 * Design Pressure) is greater than (2 * AllowableStrength * LongitudinalWeldJointEfficiency)");
      return CalculatorResult.Fail;
    }
    this.Radius = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    double x = this.NominalInsideDiameter / (2.0 * this.NominalHeight);
    double num = !this.CenteredCorroded ? 1.0 / 6.0 * (2.0 + Math.Pow(x, 2.0)) : 0.25346 + 0.13995 * x + 0.12238 * Math.Pow(x, 2.0) - 0.015297 * Math.Pow(x, 3.0);
    this.TlMin = strengthLookupResult.TemperatureRangeStatus != MaterialStrengthRangeStatus.InRange ? 0.0 : this.DesignPressure * 2.0 * this.Radius * num / (2.0 * this.AllowableStrength * this.WeldJointEfficiency - 0.2 * this.DesignPressure);
    if (2.0 * this.Radius * (0.44 * x + 0.02) / this.TlMin > 500.0)
    {
      this.ErrorMessage = "Minimum thickness MAWP and membrane stress can not be computed because the component geometry does not meet the condition D(0.44Rell+0.02)/tmin ≤ 500 (Annex A API 579, Clause A.3.6)";
      return CalculatorResult.Fail;
    }
    this.TMin = this.TlMin;
    return CalculatorResult.Completed;
  }

  private MaterialStrengthLookupResult getStrengthResult(double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialASMEVIIIDiv1StressValueDTO div1StressValueDto = this._allowableStrengths.Where<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 <= tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEVIIIDiv1StressValueDTO, double?>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEVIIIDiv1StressValueDTO>();
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Max<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Min<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).Value;
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
      {
        TLower = new double?(0.0),
        TUpper = new double?(0.0),
        SLower = new double?(0.0),
        SUpper = new double?(0.0)
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = div1StressValueDto.TLower.Value,
      TUpper = div1StressValueDto.TUpper.Value,
      SLower = div1StressValueDto.SLower.Value,
      SUpper = div1StressValueDto.SUpper.Value
    };
  }
}
