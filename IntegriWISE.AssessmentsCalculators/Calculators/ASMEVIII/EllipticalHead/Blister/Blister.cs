// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.Blister.Blister
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.AssessmentData;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.Blister;

public class Blister : BaseCalculator, IBlisterCalculator
{
  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double CircumferentialBlisterDimension { get; set; }

  public double LongitudinalBlisterDimension { get; set; }

  public double BlisterSpacing { get; set; }

  public double BlisterBulgeProjection { get; set; }

  public double TMMBlister { get; set; }

  public VentType BlisterCrownCrackingAndVentHoles { get; set; }

  public double SC { get; set; }

  public double LW { get; set; }

  public double LMSD { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  private double InsideDiameterD { get; set; }

  public double tc { get; set; }

  public double BdDiameter { get; set; }

  private double TMMBlisterModifiedByFCA { get; set; }

  private string ConditionBlistersOverHICProximity { get; set; }

  public bool BdLessThan50 { get; set; }

  public bool BlisterVented { get; set; }

  public bool ConditionS { get; set; }

  public bool ConditionC { get; set; }

  public bool MinimumMeasuredUndamagedThickness { get; set; }

  public bool BlisterProjection { get; set; }

  public bool DistanceToWeldSeam { get; set; }

  public bool ConditionDistanceMajorDiscontinuity { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public string Level1 { get; set; }

  public bool Level1Passed { get; set; }

  public bool CrownCrackPart5 { get; set; }

  public string Level1Conclusion { get; private set; }

  public double MAWP { get; set; }

  public Blister() => this.Warnings = new List<string>();

  public CalculatorResult CalculateAssessment()
  {
    this.InsideDiameterD = this.NominalInsideDiameter + 2.0 * (this.InternalUniformMetalLoss + this.InternalFutureCorrosionAllowance);
    this.tc = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss;
    this.BdDiameter = Math.Max(this.CircumferentialBlisterDimension, this.LongitudinalBlisterDimension);
    this.TMMBlisterModifiedByFCA = this.TMMBlister - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    if (this.BlisterSpacing <= 2.0 * this.tc)
    {
      this.ErrorMessage = "The distance between two adjacent blister zones (measured edge-to-edge) is less than or equal to two times the corroded thickness, tc. The blisters should be combined and evaluated as a single blister.";
      return CalculatorResult.Fail;
    }
    this.BdLessThan50 = this.BdDiameter <= 50.0;
    this.BlisterVented = this.BlisterCrownCrackingAndVentHoles != VentType.No;
    this.ConditionS = this.LongitudinalBlisterDimension <= 0.6 * Math.Pow(this.InsideDiameterD * this.tc, 0.5);
    this.ConditionC = this.CircumferentialBlisterDimension <= 0.6 * Math.Pow(this.tc * this.InsideDiameterD, 0.5);
    this.MinimumMeasuredUndamagedThickness = this.TMMBlisterModifiedByFCA >= 0.5 * this.tc;
    this.BlisterProjection = this.BlisterBulgeProjection <= 0.1 * Math.Min(this.CircumferentialBlisterDimension, this.LongitudinalBlisterDimension);
    this.DistanceToWeldSeam = this.LW > Math.Max(2.0 * this.tc, 25.0);
    this.ConditionDistanceMajorDiscontinuity = this.LMSD >= 1.8 * Math.Pow(this.InsideDiameterD * this.tc, 0.5);
    if (this.ConditionDistanceMajorDiscontinuity && this.DistanceToWeldSeam && this.BlisterProjection && this.MinimumMeasuredUndamagedThickness && (this.BlisterVented && this.ConditionC && this.ConditionS || this.BdLessThan50))
    {
      this.Level1Conclusion = "Level 1 Assessment is ACCEPTABLE";
      this.Level1Passed = true;
    }
    else
    {
      this.Level1Conclusion = "Level 1 Assessment is UNACCEPTABLE";
      this.Level1Passed = false;
    }
    return CalculatorResult.Completed;
  }
}
