// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.MAWP.MAWP
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.MAWP;

public class MAWP : BaseCalculator
{
  private bool WithinGeometricLimitation;
  private double K;
  private double Rell;
  private double tupper;
  private double tlower;
  private double supper;
  private double slower;
  private MaterialASMEVIIIDiv1DTO _material;
  private List<MaterialASMEVIIIDiv1StressValueDTO> _allowableStrengths;

  public bool ASMEVIIIDiv1Before1999 { get; set; }

  public double DesignTemperature { get; set; }

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double WeldJointEfficiency { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double HeightEllipticalHead { get; set; }

  public bool CorrodedRegionWithin0_8D { get; set; }

  public double? YieldStrengthNew { get; set; }

  public double? TensileStrengthNew { get; set; }

  public double Radius { get; set; }

  public double Tc { get; set; }

  public double MAWPc { get; set; }

  public double MAWPl { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double MAWPValue { get; private set; }

  public double AllowableStrength { get; private set; }

  public MAWP()
  {
  }

  public MAWP(
    MaterialASMEVIIIDiv1DTO material,
    List<MaterialASMEVIIIDiv1StressValueDTO> allowableStrengths)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.UserDefined)
    {
      if (this.ASMEVIIIDiv1Before1999)
      {
        double? yieldStrengthNew = this.YieldStrengthNew;
        double val1 = (yieldStrengthNew.HasValue ? new double?(yieldStrengthNew.GetValueOrDefault() / 1.5) : new double?()).Value;
        double? tensileStrengthNew = this.TensileStrengthNew;
        double val2 = (tensileStrengthNew.HasValue ? new double?(tensileStrengthNew.GetValueOrDefault() / 4.0) : new double?()).Value;
        this.AllowableStrength = Math.Min(val1, val2);
      }
      else
      {
        double? yieldStrengthNew = this.YieldStrengthNew;
        double val1 = (yieldStrengthNew.HasValue ? new double?(yieldStrengthNew.GetValueOrDefault() / 1.5) : new double?()).Value;
        double? tensileStrengthNew = this.TensileStrengthNew;
        double val2 = (tensileStrengthNew.HasValue ? new double?(tensileStrengthNew.GetValueOrDefault() / 3.5) : new double?()).Value;
        this.AllowableStrength = Math.Min(val1, val2);
      }
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      MaterialStrengthLookupResult strengthResult = this.getStrengthResult(this.DesignTemperature);
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.AboveRange)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material.";
        return CalculatorResult.Fail;
      }
      if (strengthResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.BelowRange)
      {
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).");
        strengthResult = this.getStrengthResult(-30.0);
      }
      if (this._material.CreepTemperature.HasValue)
      {
        double designTemperature = this.DesignTemperature;
        double? creepTemperature = this._material.CreepTemperature;
        if ((designTemperature <= creepTemperature.GetValueOrDefault() ? 0 : (creepTemperature.HasValue ? 1 : 0)) != 0)
          this.Warnings.Add("Creep Assessment is required due to the high temperature.");
      }
      this.tlower = strengthResult.TLower;
      this.tupper = strengthResult.TUpper;
      this.slower = strengthResult.SLower;
      this.supper = strengthResult.SUpper;
      this.AllowableStrength = (this.DesignTemperature - this.tlower) / (this.tupper - this.tlower) * (this.supper - this.slower) + this.slower;
    }
    this.Radius = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    this.WithinGeometricLimitation = this.Radius / this.HeightEllipticalHead >= 1.7 && this.Radius / this.HeightEllipticalHead <= 2.2;
    if (!this.WithinGeometricLimitation)
    {
      this.ErrorMessage = "Minimum thickness, MAWP and membrane stress can not be computed because the component geometry does not meet the condition 1.7 ≤ Rell ≤ 2.2. (Annex A API 579, Clause A.3.6).";
      return CalculatorResult.Fail;
    }
    this.Tc = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance;
    this.Rell = this.NominalInsideDiameter / (2.0 * this.HeightEllipticalHead);
    this.K = !this.CorrodedRegionWithin0_8D ? 1.0 / 6.0 * (2.0 + Math.Pow(this.Rell, 2.0)) : 0.25346 + 0.13995 * this.Rell + 0.12238 * Math.Pow(this.Rell, 2.0) - 0.015297 * Math.Pow(this.Rell, 3.0);
    this.WithinGeometricLimitation = 2.0 * this.Radius * (0.44 * this.Rell + 0.02) / this.Tc > 500.0;
    if (this.WithinGeometricLimitation)
    {
      this.ErrorMessage = "MAWP can not be computed because the component geometry does not meet the condition D(0.44Rell+0.02)/tc ≤ 500 (Annex A API 579, Clause A.3.6)";
      return CalculatorResult.Fail;
    }
    double radius = this.Radius;
    double tc1 = this.Tc;
    double allowableStrength = this.AllowableStrength;
    double weldJointEfficiency = this.WeldJointEfficiency;
    double tc2 = this.Tc;
    this.MAWPValue = 2.0 * this.AllowableStrength * this.WeldJointEfficiency * this.Tc / (2.0 * this.Radius * this.K + 0.2 * this.Tc);
    return CalculatorResult.Completed;
  }

  private MaterialStrengthLookupResult getStrengthResult(double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialASMEVIIIDiv1StressValueDTO div1StressValueDto = this._allowableStrengths.Where<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 <= tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEVIIIDiv1StressValueDTO, double?>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEVIIIDiv1StressValueDTO>();
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Max<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Min<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).Value;
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
      {
        TLower = new double?(0.0),
        TUpper = new double?(0.0),
        SLower = new double?(0.0),
        SUpper = new double?(0.0)
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = div1StressValueDto.TLower.Value,
      TUpper = div1StressValueDto.TUpper.Value,
      SLower = div1StressValueDto.SLower.Value,
      SUpper = div1StressValueDto.SUpper.Value
    };
  }
}
