// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.BrittleFracture.IBrittleFractureCalculator
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.EllipticalHead.BrittleFracture;

public interface IBrittleFractureCalculator
{
  bool ASMEVIIIBefore1999 { set; }

  CalculatorResult CalculateAssessment();

  double? CET { set; }

  bool? CETGreaterThanMAT { get; }

  double DesignPressure { set; }

  double DesignTemperature { set; }

  string ErrorMessage { get; }

  bool ImpactTestResultAvailable { set; }

  bool HydroTestPerformed { set; }

  double? HydroTestPressure { set; }

  bool PWHT { set; }

  string Level1FormattedMessage { get; }

  double? Level1MAT { get; }

  string Level1Message { get; }

  bool Level1Passed { get; }

  double? MAT_OptionA { get; }

  double? MAT_OptionB { get; }

  double? MaxImpactTestTemperature { set; }

  double NominalInsideDiameter { set; }

  double InternalFutureCorrosionAllowance { set; }

  double InternalUniformMetalLoss { set; }

  char ToughnessCurve_dependOnMATERIAL { set; }

  double TG { set; }

  List<string> Warnings { get; }

  double? YieldStrengthNew { set; }

  double? TensileStrengthNew { set; }

  double WeldJointEfficiency { set; }

  double NominalHeight { set; }

  bool CenteredCorroded { set; }
}
