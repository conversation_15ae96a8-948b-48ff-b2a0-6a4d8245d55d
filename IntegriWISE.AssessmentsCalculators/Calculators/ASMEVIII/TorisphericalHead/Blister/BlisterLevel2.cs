// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.Blister.BlisterLevel2
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.AssessmentData;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.Blister;

public class BlisterLevel2 : BaseCalculator, IBlisterCalculator
{
  private List<MaterialTSFCurveDTO> _api579Table5_4List;

  public double NominalInsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double CircumferentialBlisterDimension { get; set; }

  public double LongitudinalBlisterDimension { private get; set; }

  public double BlisterSpacing { get; set; }

  public double BlisterBulgeProjection { get; set; }

  public double TMMBlister { private get; set; }

  public VentType BlisterCrownCrackingAndVentHoles { get; set; }

  public double SC { get; set; }

  public double LW { get; set; }

  public double LMSD { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  private double InsideDiameterD { get; set; }

  public double tc { get; set; }

  public double BdDiameter { get; set; }

  private double TMMBlisterModifiedByFCA { get; set; }

  public bool BdLessThan50 { get; set; }

  public bool BlisterVented { get; set; }

  public bool ConditionS { get; set; }

  public bool ConditionC { get; set; }

  public bool MinimumMeasuredUndamagedThickness { get; set; }

  public bool BlisterProjection { get; private set; }

  public bool DistanceToWeldSeam { get; set; }

  public bool ConditionDistanceMajorDiscontinuity { get; set; }

  public string Level1Conclusion { get; set; }

  public bool Level1Passed { get; set; }

  public bool IsMAWP { private get; set; }

  public double MAWP { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public bool DistanceToWeldSeamLevel2 { get; private set; }

  public string LongitudinalExtent { get; private set; }

  public double RSF { get; private set; }

  public double DepthOfHicDamage { get; private set; }

  public double MAWPr { get; private set; }

  public double rt { get; private set; }

  public bool RSFGreaterRSFa { get; private set; }

  public bool Level2Passed { get; private set; }

  public string FinalL2Conclusion { get; private set; }

  public double LambdaC { get; private set; }

  public string CircumferentialExtent { get; private set; }

  public bool RtGreater0_2 { get; private set; }

  public bool TmmMinusFCAGreater2_5 { get; private set; }

  public bool? LambdaCLessThan9 { get; private set; }

  public bool? DOverTcGreaterThan20 { get; private set; }

  public bool? RSFBetween0_7And1 { get; private set; }

  public bool? ElBetween0_7And1 { get; private set; }

  public bool? EcBetween0_7And2 { get; private set; }

  public bool? ScreeningCriteriaFigure5_8 { get; private set; }

  public double? TSF { get; private set; }

  public bool CrownCrackPart5 { get; set; }

  public BlisterLevel2(List<MaterialTSFCurveDTO> api579Table5_4List)
  {
    this._api579Table5_4List = api579Table5_4List;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.Blister.Blister blister = new IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.Blister.Blister();
    blister.NominalInsideDiameter = this.NominalInsideDiameter;
    blister.NominalThickness = this.NominalThickness;
    blister.ExternalFutureCorrosionAllowance = this.ExternalFutureCorrosionAllowance;
    blister.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    blister.InternalFutureCorrosionAllowance = this.InternalFutureCorrosionAllowance;
    blister.InternalUniformMetalLoss = this.InternalUniformMetalLoss;
    blister.CircumferentialBlisterDimension = this.CircumferentialBlisterDimension;
    blister.LongitudinalBlisterDimension = this.LongitudinalBlisterDimension;
    blister.BlisterSpacing = this.BlisterSpacing;
    blister.BlisterBulgeProjection = this.BlisterBulgeProjection;
    blister.TMMBlister = this.TMMBlister;
    blister.BlisterCrownCrackingAndVentHoles = this.BlisterCrownCrackingAndVentHoles;
    blister.SC = this.SC;
    blister.LW = this.LW;
    blister.LMSD = this.LMSD;
    CalculatorResult assessment = blister.CalculateAssessment();
    this.ErrorMessage = blister.ErrorMessage;
    this.Warnings.AddRange((IEnumerable<string>) blister.Warnings);
    this.tc = blister.tc;
    this.BdDiameter = blister.BdDiameter;
    this.ConditionS = blister.ConditionS;
    this.ConditionC = blister.ConditionC;
    this.BdLessThan50 = blister.BdLessThan50;
    this.MinimumMeasuredUndamagedThickness = blister.MinimumMeasuredUndamagedThickness;
    this.BlisterProjection = blister.BlisterProjection;
    this.DistanceToWeldSeam = blister.DistanceToWeldSeam;
    this.ConditionDistanceMajorDiscontinuity = blister.ConditionDistanceMajorDiscontinuity;
    this.Level1Passed = blister.Level1Passed;
    this.Level1Conclusion = blister.Level1Conclusion;
    if (assessment == CalculatorResult.Fail)
      return assessment;
    if (this.Level1Passed)
      return CalculatorResult.Completed;
    this.DistanceToWeldSeamLevel2 = this.BlisterCrownCrackingAndVentHoles == VentType.No || this.DistanceToWeldSeam;
    this.CrownCrackPart5 = this.BlisterCrownCrackingAndVentHoles == VentType.Crown || !this.BlisterProjection;
    if (this.CrownCrackPart5)
    {
      this.Warnings.Add("The blister should be evaluated as an equivalent local thin area using the methods in part 5. API 579.");
      double num1 = 1.285 * this.LongitudinalBlisterDimension / Math.Pow(2.0 * (this.NominalInsideDiameter / 2.0 - this.ExternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss) * this.tc, 0.5);
      double x = num1 > 20.0 ? 20.0 : num1;
      double num2 = 1001.0 / 1000.0 - 0.014195 * x + 0.2909 * Math.Pow(x, 2.0) - 0.09642 * Math.Pow(x, 3.0) + 0.02089 * Math.Pow(x, 4.0) - 0.003054 * Math.Pow(x, 5.0) + 2.957 * Math.Pow(10.0, -4.0) * Math.Pow(x, 6.0) - 1.8462 * Math.Pow(10.0, -5.0) * Math.Pow(x, 7.0) + 7.1553 * Math.Pow(10.0, -7.0) * Math.Pow(x, 8.0) - 1.5631 * Math.Pow(10.0, -8.0) * Math.Pow(x, 9.0) + 916.0 / 625.0 * Math.Pow(10.0, -10.0) * Math.Pow(x, 10.0);
      this.rt = (this.TMMBlister - (this.InternalFutureCorrosionAllowance + this.ExternalFutureCorrosionAllowance)) / this.tc;
      this.RSF = this.rt / (1.0 - 1.0 / num2 * (1.0 - this.rt));
      this.RtGreater0_2 = this.rt > 0.2;
      this.TmmMinusFCAGreater2_5 = this.TMMBlister - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance > 2.5;
      this.RSFGreaterRSFa = this.RSF >= 0.9;
      BlisterLevel2.PassType passType;
      if (this.ConditionDistanceMajorDiscontinuity && this.RtGreater0_2 && this.TmmMinusFCAGreater2_5)
      {
        if (this.RSFGreaterRSFa)
        {
          this.LongitudinalExtent = "The longitudinal extent of the blister damage is ACCEPTABLE for operation at MAWP";
          passType = BlisterLevel2.PassType.pass;
        }
        else
        {
          this.LongitudinalExtent = "The longitudinal extent of the blister damage is UNACCEPTABLE for operation at MAWP, but is acceptable for operation at the MAWPr";
          passType = BlisterLevel2.PassType.failWithWarning;
          if (this.IsMAWP)
            this.MAWPr = this.MAWP * this.RSF / 0.9;
        }
      }
      else
      {
        this.LongitudinalExtent = "The longitudinal extent of the blister damage is UNACCEPTABLE.";
        passType = BlisterLevel2.PassType.fail;
      }
      if (this.ConditionDistanceMajorDiscontinuity && this.DistanceToWeldSeamLevel2)
      {
        switch (passType)
        {
          case BlisterLevel2.PassType.pass:
            this.FinalL2Conclusion = "Level 2 Assessment is ACCEPTABLE";
            break;
          case BlisterLevel2.PassType.fail:
            this.FinalL2Conclusion = "Level 2 Assessment is UNACCEPTABLE";
            break;
          case BlisterLevel2.PassType.failWithWarning:
            this.FinalL2Conclusion = "Level 2 Assessment is unacceptable for operation at MAWP but the component can operate at MAWPr";
            break;
        }
      }
      else
        this.FinalL2Conclusion = "Level 2 Assessment is UNACCEPTABLE";
    }
    else if (this.ConditionDistanceMajorDiscontinuity && this.DistanceToWeldSeamLevel2)
    {
      this.FinalL2Conclusion = "Level 2 Assessment is ACCEPTABLE";
      this.Level2Passed = true;
    }
    else
    {
      this.FinalL2Conclusion = "Level 2 Assessment is UNACCEPTABLE";
      this.Level2Passed = false;
    }
    if (this.FinalL2Conclusion == "Level 2 Assessment is ACCEPTABLE")
      this.Warnings.Add("Monitoring blister growth while the component is in service is required.");
    return CalculatorResult.Completed;
  }

  private enum PassType
  {
    pass,
    fail,
    failWithWarning,
  }
}
