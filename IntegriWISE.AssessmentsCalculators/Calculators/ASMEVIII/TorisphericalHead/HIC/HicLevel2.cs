// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.HIC.HicLevel2
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.HIC;

public class HicLevel2 : BaseCalculator, IHicCalculator
{
  private List<MaterialTSFCurveDTO> _api579Table5_4List;

  public double NominalInsideDiameter { private get; set; }

  public double NominalThickness { private get; set; }

  public double LongitudinalHICDimension { private get; set; }

  public double CircumferentialHICDimension { private get; set; }

  public double LH { private get; set; }

  public double LW { private get; set; }

  public double LMSD { private get; set; }

  public double TMMID { private get; set; }

  public double TMMOD { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public double InternalFutureCorrosionAllowance { private get; set; }

  public double ExternalFutureCorrosionAllowance { private get; set; }

  public double HiC2HiCLongSpacing { private get; set; }

  public bool IsMAWP { private get; set; }

  public double MAWP { get; set; }

  public double tc { get; private set; }

  public double wHModifiedByFCA { get; private set; }

  public bool ConditionS { get; private set; }

  public bool ConditionC { get; private set; }

  public bool ConditionThroughThicknessExtend { get; private set; }

  public bool ConditionDistanceToWeldSeam { get; private set; }

  public bool ConditionDistanceMajorDiscontinuity { get; private set; }

  public bool ConditionSubsurfaceHICTmmID { get; private set; }

  public bool ConditionSubsurfaceHICTmmOD { get; private set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public string Level1Conclusion { get; private set; }

  public bool Level1Passed { get; private set; }

  public string LongitudinalExtent { get; private set; }

  public double RSF { get; private set; }

  public SurfaceSubsurface IsSurfaceSubsurface { get; private set; }

  public double MAWPr { get; private set; }

  public bool RSFGreaterRSFa { get; private set; }

  public bool Level2Passed { get; private set; }

  public string FinalL2Conclusion { get; private set; }

  public HicLevel2(List<MaterialTSFCurveDTO> api579Table5_4List)
  {
    this._api579Table5_4List = api579Table5_4List;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.HIC.HIC hic = new IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.HIC.HIC();
    hic.NominalInsideDiameter = this.NominalInsideDiameter;
    hic.NominalThickness = this.NominalThickness;
    hic.ExternalFutureCorrosionAllowance = this.ExternalFutureCorrosionAllowance;
    hic.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    hic.InternalUniformMetalLoss = this.InternalUniformMetalLoss;
    hic.InternalFutureCorrosionAllowance = this.InternalFutureCorrosionAllowance;
    hic.LW = this.LW;
    hic.LMSD = this.LMSD;
    hic.TMMID = this.TMMID;
    hic.TMMOD = this.TMMOD;
    hic.LongitudinalHICDimension = this.LongitudinalHICDimension;
    hic.CircumferentialHICDimension = this.CircumferentialHICDimension;
    hic.LH = this.LH;
    CalculatorResult assessment = hic.CalculateAssessment();
    this.ErrorMessage = hic.ErrorMessage;
    this.Warnings.AddRange((IEnumerable<string>) hic.Warnings);
    this.tc = hic.tc;
    this.wHModifiedByFCA = hic.wHModifiedByFCA;
    this.ConditionS = hic.ConditionS;
    this.ConditionC = hic.ConditionC;
    this.ConditionThroughThicknessExtend = hic.ConditionThroughThicknessExtend;
    this.ConditionDistanceToWeldSeam = hic.ConditionDistanceToWeldSeam;
    this.ConditionDistanceMajorDiscontinuity = hic.ConditionDistanceMajorDiscontinuity;
    this.ConditionSubsurfaceHICTmmID = hic.ConditionSubsurfaceHICTmmID;
    this.ConditionSubsurfaceHICTmmOD = hic.ConditionSubsurfaceHICTmmOD;
    this.Level1Passed = hic.Level1Passed;
    this.Level1Conclusion = hic.Level1Conclusion;
    if (assessment == CalculatorResult.Fail)
      return assessment;
    if (this.Level1Passed)
      return CalculatorResult.Completed;
    this.IsSurfaceSubsurface = !this.ConditionSubsurfaceHICTmmID || !this.ConditionSubsurfaceHICTmmOD ? SurfaceSubsurface.Surface : SurfaceSubsurface.Subsurface;
    double num1 = this.IsSurfaceSubsurface == SurfaceSubsurface.Surface ? this.wHModifiedByFCA + Math.Min(this.TMMID, this.TMMOD) : this.wHModifiedByFCA;
    double num2 = this.NominalInsideDiameter / 2.0 - this.ExternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss;
    double num3 = 0.8;
    double num4 = 1.285 * this.LongitudinalHICDimension / Math.Pow(2.0 * num2 * this.tc, 0.5);
    double x = num4 > 20.0 ? 20.0 : num4;
    double num5 = (1.0005 + 0.49001 * x + 0.32409 * Math.Pow(x, 2.0)) / (1.0 + 0.50144 * x - 0.011067 * Math.Pow(x, 2.0));
    double num6 = Math.Min(this.HiC2HiCLongSpacing / 2.0, 8.0 * this.tc);
    double num7 = num1 * num3 / this.tc;
    this.RSF = this.IsSurfaceSubsurface != SurfaceSubsurface.Surface ? (2.0 * num6 + this.LongitudinalHICDimension * (1.0 - num7)) / (2.0 * num6 + this.LongitudinalHICDimension) : (1.0 - num7) / (1.0 - num7 / num5);
    this.RSFGreaterRSFa = this.RSF >= 0.9;
    HicLevel2.PassType passType;
    if (this.ConditionDistanceMajorDiscontinuity && this.ConditionDistanceToWeldSeam)
    {
      if (this.RSFGreaterRSFa)
      {
        this.LongitudinalExtent = "The longitudinal extent of the HIC damage is ACCEPTABLE for operation at MAWP";
        passType = HicLevel2.PassType.pass;
      }
      else
      {
        this.LongitudinalExtent = "The longitudinal extent of the HIC damage is UNACCEPTABLE for operation at MAWP, but is acceptable for operation at the MAWPr";
        passType = HicLevel2.PassType.failWithWarning;
        if (this.IsMAWP)
          this.MAWPr = this.MAWP * this.RSF / 0.9;
      }
    }
    else
    {
      this.LongitudinalExtent = "The longitudinal extent of the HIC damage is UNACCEPTABLE.";
      passType = HicLevel2.PassType.fail;
    }
    if (passType == HicLevel2.PassType.pass || passType == HicLevel2.PassType.failWithWarning)
    {
      this.Warnings.Add("Confirmation that further HIC damage has been prevented or is limited is required. Otherwise, Level 2 Assessment is not satisfied.");
      this.Warnings.Add("If the HIC damage remains in hydrogen charging or it is classified as surface breaking (tmmID < 0.20tc or tmmOD < 0.20tc) or the depth of the HIC does not meet the criterion wH<=min(tc/3, 13mm (0.5in)), then fracture assessment is required. Two crack-like flaw assessments shall be performed, one for the longitudinal and one for the circumferential extent of the HIC damage.");
    }
    this.Level2Passed = passType == HicLevel2.PassType.pass;
    this.FinalL2Conclusion = !this.Level2Passed ? "Level 2 Assessment is UNACCEPTABLE" : "Level 2 Assessment is ACCEPTABLE";
    if (passType == HicLevel2.PassType.failWithWarning)
      this.FinalL2Conclusion = "Level 2 Assessment is unacceptable for operation at MAWP but the component can operate at MAWPr";
    return CalculatorResult.Completed;
  }

  private enum PassType
  {
    pass,
    fail,
    failWithWarning,
  }
}
