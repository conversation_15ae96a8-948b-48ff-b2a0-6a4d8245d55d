// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.MinimumThickness.MinimumThickness
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.MinimumThickness;

public class MinimumThickness : BaseCalculator
{
  private double tupper;
  private double tlower;
  private double supper;
  private double slower;
  private MaterialASMEVIIIDiv1DTO _material;
  private List<MaterialASMEVIIIDiv1StressValueDTO> _allowableStrengths;

  public bool ASMEVIIIBefore1999 { private get; set; }

  public double DesignTemperature { private get; set; }

  public double DesignPressure { private get; set; }

  public double? YieldStrengthNew { private get; set; }

  public double? TensileStrengthNew { private get; set; }

  public double NominalInsideDiameter { private get; set; }

  public double WeldJointEfficiency { private get; set; }

  public double HeightEllipticalHead { private get; set; }

  public double InsideCrownRadius { private get; set; }

  public double InsideKnuckleRadius { private get; set; }

  public double InsideThickness { private get; set; }

  public double InternalFutureCorrosionAllowance { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public bool CenterPortionOfHead { private get; set; }

  public double AllowableStrength { get; private set; }

  public double TMin { get; private set; }

  public double TcMin { get; set; }

  public double TlMin { get; set; }

  public List<string> Warnings { get; set; }

  public string ErrorMessage { get; set; }

  public MinimumThickness(
    MaterialASMEVIIIDiv1DTO material,
    List<MaterialASMEVIIIDiv1StressValueDTO> allowableStrengths)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    MaterialStrengthLookupResult strengthLookupResult = new MaterialStrengthLookupResult();
    if (this._material.UserDefined)
    {
      this.AllowableStrength = !this.ASMEVIIIBefore1999 ? Math.Min(this.YieldStrengthNew.Value / 1.5, this.TensileStrengthNew.Value / 3.5) : Math.Min(this.YieldStrengthNew.Value / 1.5, this.TensileStrengthNew.Value / 4.0);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
      strengthLookupResult.TemperatureRangeStatus = MaterialStrengthRangeStatus.InRange;
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      strengthLookupResult = this.getStrengthResult(this.DesignTemperature);
      if (strengthLookupResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.AboveRange)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material.";
        return CalculatorResult.Fail;
      }
      if (strengthLookupResult.TemperatureRangeStatus == MaterialStrengthRangeStatus.BelowRange)
      {
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress at -30 degrees to calculate tmin (lowest listed temperature for carbon steel materials designed to ASME VIII).");
        strengthLookupResult = this.getStrengthResult(-30.0);
      }
      if (this._material.CreepTemperature.HasValue)
      {
        double designTemperature = this.DesignTemperature;
        double? creepTemperature = this._material.CreepTemperature;
        if ((designTemperature <= creepTemperature.GetValueOrDefault() ? 0 : (creepTemperature.HasValue ? 1 : 0)) != 0)
          this.Warnings.Add("Creep Assessment is required due to the high temperature.");
      }
      this.tlower = strengthLookupResult.TLower;
      this.tupper = strengthLookupResult.TUpper;
      this.slower = strengthLookupResult.SLower;
      this.supper = strengthLookupResult.SUpper;
      this.AllowableStrength = (this.DesignTemperature - this.tlower) / (this.tupper - this.tlower) * (this.supper - this.slower) + this.slower;
    }
    if (this.DesignPressure * 0.2 >= 2.0 * this.AllowableStrength * this.WeldJointEfficiency)
    {
      this.ErrorMessage = "Design Pressure * 0.2 is less than or equal to 2 * AllowableStrength * LongitudinalWeldJointEfficiency";
      return CalculatorResult.Fail;
    }
    double num1 = this.CenterPortionOfHead ? 1.0 : 0.25 * (3.0 + Math.Pow((this.InsideCrownRadius + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss) / (this.InsideKnuckleRadius + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss), 0.5));
    double num2 = strengthLookupResult.TemperatureRangeStatus != MaterialStrengthRangeStatus.InRange ? 0.0 : this.DesignPressure * (this.InsideCrownRadius + this.InternalUniformMetalLoss + this.InternalFutureCorrosionAllowance) * (num1 / (2.0 * this.AllowableStrength * this.WeldJointEfficiency - 0.2 * this.DesignPressure));
    if ((this.InsideCrownRadius + this.InsideKnuckleRadius + this.InternalUniformMetalLoss) / num2 > 500.0)
    {
      this.ErrorMessage = "Minimum thickness can not be computed because the component geometry does not meet the condition Cr/tmin ≤ 500 (Annex A API 579, Clause A.3.7).";
      return CalculatorResult.Fail;
    }
    this.TMin = num2;
    return CalculatorResult.Completed;
  }

  private MaterialStrengthLookupResult getStrengthResult(double temperature)
  {
    double num1 = 0.0;
    MaterialStrengthRangeStatus strengthRangeStatus = MaterialStrengthRangeStatus.InRange;
    MaterialASMEVIIIDiv1StressValueDTO div1StressValueDto = this._allowableStrengths.Where<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.TLower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.TUpper;
      return num3 <= tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEVIIIDiv1StressValueDTO, double?>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEVIIIDiv1StressValueDTO>();
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Max<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TUpper)).Value;
      if (temperature >= num1)
      {
        strengthRangeStatus = MaterialStrengthRangeStatus.AboveRange;
        div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    if (div1StressValueDto == null)
    {
      num1 = this._allowableStrengths.Min<MaterialASMEVIIIDiv1StressValueDTO>((Func<MaterialASMEVIIIDiv1StressValueDTO, double?>) (x => x.TLower)).Value;
      strengthRangeStatus = MaterialStrengthRangeStatus.BelowRange;
      div1StressValueDto = new MaterialASMEVIIIDiv1StressValueDTO()
      {
        TLower = new double?(0.0),
        TUpper = new double?(0.0),
        SLower = new double?(0.0),
        SUpper = new double?(0.0)
      };
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      TemperatureRangeStatus = strengthRangeStatus,
      TLower = div1StressValueDto.TLower.Value,
      TUpper = div1StressValueDto.TUpper.Value,
      SLower = div1StressValueDto.SLower.Value,
      SUpper = div1StressValueDto.SUpper.Value
    };
  }
}
