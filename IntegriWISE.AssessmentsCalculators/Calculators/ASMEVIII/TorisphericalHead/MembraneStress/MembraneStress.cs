// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.MembraneStress.MembraneStress
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.MembraneStress;

public class MembraneStress : BaseCalculator
{
  public double DesignPressure { private get; set; }

  public double NominalThickness { private get; set; }

  public double NominalInsideDiameter { private get; set; }

  public double WeldJointEfficiency { private get; set; }

  public double InsideCrownRadius { private get; set; }

  public double InsideKnuckleRadius { private get; set; }

  public bool CenterPortionOfHead { private get; set; }

  public double InternalFutureCorrosionAllowance { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public double ExternalFutureCorrosionAllowance { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double SigmaM { get; private set; }

  public MembraneStress() => this.Warnings = new List<string>();

  public CalculatorResult CalculateAssessment()
  {
    double num1 = this.NominalInsideDiameter / 2.0 + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
    double num2 = this.NominalThickness - this.InternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance;
    double num3 = this.CenterPortionOfHead ? 1.0 : 0.25 * (3.0 + Math.Pow((this.InsideCrownRadius + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss) / (this.InsideKnuckleRadius + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss), 0.5));
    if ((this.InsideCrownRadius + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss) / (2.0 * num1) < 0.7 || (this.InsideCrownRadius + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss) / (2.0 * num1) > 1.2)
    {
      this.ErrorMessage = "MAWP can not be computed because the component geometry does not meet the condition 0.7 ≤ Cr/D ≤ 1.2. (Annex A API 579, Clause A.3.7)";
      return CalculatorResult.Fail;
    }
    if ((this.InsideKnuckleRadius + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss) / (2.0 * num1) < 0.06)
    {
      this.ErrorMessage = "MAWP can not be computed because the component geometry does not meet the condition rk/D ≥ 0.06. (Annex A API 579, Clause A.3.7)";
      return CalculatorResult.Fail;
    }
    if ((this.InsideCrownRadius + this.InternalUniformMetalLoss + this.InternalFutureCorrosionAllowance) / num2 > 500.0)
    {
      this.ErrorMessage = "MAWP can not be computed because the component geometry does not meet the condition Cr/tc ≤ 500(Annex A API 579, Clause A.3.7)";
      return CalculatorResult.Fail;
    }
    this.SigmaM = this.DesignPressure / (2.0 * this.WeldJointEfficiency / ((this.InsideCrownRadius + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss) * num3 / num2 + 0.2));
    return CalculatorResult.Completed;
  }
}
