// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.BrittleFracture.BrittleFracture
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.BrittleFracture;

public class BrittleFracture : BaseCalculator, IBrittleFractureCalculator
{
  private MaterialASMEVIIIDiv1DTO _material;
  private List<MaterialASMEVIIIDiv1StressValueDTO> _allowableStrengths;

  public bool ASMEVIIIBefore1999 { private get; set; }

  public double DesignTemperature { private get; set; }

  public double DesignPressure { private get; set; }

  public double? YieldStrengthNew { private get; set; }

  public double? TensileStrengthNew { private get; set; }

  public double NominalInsideDiameter { private get; set; }

  public double InternalFutureCorrosionAllowance { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public char ToughnessCurve_dependOnMATERIAL { private get; set; }

  public double WeldJointEfficiency { private get; set; }

  public double InsideCrownRadius { private get; set; }

  public double InsideKnuckleRadius { private get; set; }

  public bool PWHT { private get; set; }

  public double TG { private get; set; }

  public bool ImpactTestResultAvailable { private get; set; }

  public double? MaxImpactTestTemperature { private get; set; }

  public double? CET { private get; set; }

  public bool HydroTestPerformed { private get; set; }

  public double? HydroTestPressure { private get; set; }

  public bool Level1Passed { get; private set; }

  public double? Level1MAT { get; private set; }

  public string Level1Message { get; private set; }

  public string Level1FormattedMessage { get; private set; }

  public double MinimumThickness { get; private set; }

  public double AllowableStrength { get; private set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double? MAT_OptionA { get; private set; }

  public double? MAT_OptionB { get; private set; }

  public bool? CETGreaterThanMAT { get; private set; }

  public double MAT_degreesF_figure3_4_API579 { get; private set; }

  public BrittleFracture(
    MaterialASMEVIIIDiv1DTO material,
    List<MaterialASMEVIIIDiv1StressValueDTO> allowableStrengths)
  {
    this._material = material;
    this._allowableStrengths = allowableStrengths;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.MinimumThickness.MinimumThickness minimumThickness = new IntegriWISE.AssessmentsCalculators.Calculators.ASMEVIII.TorisphericalHead.MinimumThickness.MinimumThickness(this._material, this._allowableStrengths)
    {
      YieldStrengthNew = this.YieldStrengthNew,
      DesignTemperature = this.DesignTemperature,
      DesignPressure = this.DesignPressure,
      InternalFutureCorrosionAllowance = this.InternalFutureCorrosionAllowance,
      InternalUniformMetalLoss = this.InternalUniformMetalLoss,
      NominalInsideDiameter = this.NominalInsideDiameter,
      TensileStrengthNew = this.TensileStrengthNew
    };
    minimumThickness.YieldStrengthNew = this.YieldStrengthNew;
    minimumThickness.ASMEVIIIBefore1999 = this.ASMEVIIIBefore1999;
    minimumThickness.TensileStrengthNew = this.TensileStrengthNew;
    minimumThickness.WeldJointEfficiency = this.WeldJointEfficiency;
    minimumThickness.InsideCrownRadius = this.InsideCrownRadius;
    minimumThickness.InsideKnuckleRadius = this.InsideKnuckleRadius;
    if (minimumThickness.CalculateAssessment() == CalculatorResult.Fail)
    {
      this.ErrorMessage = minimumThickness.ErrorMessage;
      return CalculatorResult.Fail;
    }
    this.Warnings.AddRange((IEnumerable<string>) minimumThickness.Warnings);
    this.AllowableStrength = minimumThickness.AllowableStrength;
    this.MinimumThickness = minimumThickness.TMin;
    if (this.AllowableStrength > 172.5)
    {
      this.ErrorMessage = "Level 1 and Level 2 assessments fail due to the design allowable stress is greater than 172.5 Mpa (25 ksi)";
      return CalculatorResult.Fail;
    }
    int? groupNo1 = this._material.GroupNo;
    int num1;
    if ((groupNo1.GetValueOrDefault() != 1 ? 0 : (groupNo1.HasValue ? 1 : 0)) == 0)
    {
      int? groupNo2 = this._material.GroupNo;
      num1 = groupNo2.GetValueOrDefault() != 2 ? 0 : (groupNo2.HasValue ? 1 : 0);
    }
    else
      num1 = 1;
    bool flag1 = num1 != 0;
    double x = this.TG * 1.0 / 25.4;
    bool flag2 = this.ToughnessCurve_dependOnMATERIAL == 'A' && this.TG <= 12.7 || (this.ToughnessCurve_dependOnMATERIAL == 'B' || this.ToughnessCurve_dependOnMATERIAL == 'C' || this.ToughnessCurve_dependOnMATERIAL == 'D') && this.TG <= 25.4;
    bool flag3 = false;
    if (this.HydroTestPerformed)
    {
      if (this.ASMEVIIIBefore1999)
      {
        double? hydroTestPressure = this.HydroTestPressure;
        double num2 = this.DesignPressure * 1.5;
        if ((hydroTestPressure.GetValueOrDefault() < num2 ? 0 : (hydroTestPressure.HasValue ? 1 : 0)) != 0)
        {
          flag3 = true;
          goto label_15;
        }
      }
      if (!this.ASMEVIIIBefore1999)
      {
        double? hydroTestPressure = this.HydroTestPressure;
        double num3 = this.DesignPressure * 1.3;
        if ((hydroTestPressure.GetValueOrDefault() < num3 ? 0 : (hydroTestPressure.HasValue ? 1 : 0)) != 0)
        {
          flag3 = true;
          goto label_15;
        }
      }
      flag3 = false;
    }
label_15:
    bool flag4 = false;
    if (this.CET.HasValue)
    {
      int num4;
      if (this.DesignTemperature <= 343.0)
      {
        double? cet = this.CET;
        num4 = cet.GetValueOrDefault() < -29.0 ? 0 : (cet.HasValue ? 1 : 0);
      }
      else
        num4 = 0;
      flag4 = num4 != 0;
    }
    if (flag1 && flag2 && flag3 && flag4)
    {
      this.Level1Message = "Vessels constructed to the ASME Code, Section VIII, Div 1 that meet all the requirements in paragraph 3.4.2.1.e API 579 satisfy the Level 1 Assessment.It is not neccesary to compute the MAT on a component basis to complete the assessment.";
      this.Level1Passed = true;
      return CalculatorResult.Completed;
    }
    bool flag5 = this.TG <= 38.0;
    bool pwht = this.PWHT;
    bool flag6 = flag1 && flag5 && pwht;
    bool flag7 = this.TG < 2.5;
    double num5 = x <= 0.394 ? 18.0 : (284.85 * x - 76.911 - 27.56 * Math.Pow(x, 2.0)) / (1.0 + 1.7971 * x - 0.17887 * Math.Pow(x, 2.0));
    double num6 = x <= 0.394 ? -20.0 : 171.56 * Math.Pow(x, 0.5) - 135.79 + 103.63 * x - 172.0 * Math.Pow(x, 1.5) + 73.737 * Math.Pow(x, 2.0) - 10.535 * Math.Pow(x, 2.5);
    double num7 = x <= 0.394 ? -55.0 : 101.29 - 255.5 / x + 287.86 / Math.Pow(x, 2.0) - 196.42 / Math.Pow(x, 3.0) + 69.457 / Math.Pow(x, 4.0) - 9.8082 / Math.Pow(x, 5.0);
    double num8 = x <= 0.5 ? -55.0 : 94.065 * x - 92.965 - 39.812 * Math.Pow(x, 2.0) + 9.6838 * Math.Pow(x, 3.0) - 1.1698 * Math.Pow(x, 4.0) + 0.054687 * Math.Pow(x, 5.0);
    this.MAT_degreesF_figure3_4_API579 = 0.0;
    switch (this.ToughnessCurve_dependOnMATERIAL)
    {
      case 'A':
        this.MAT_degreesF_figure3_4_API579 = num5;
        break;
      case 'B':
        this.MAT_degreesF_figure3_4_API579 = num6;
        break;
      case 'C':
        this.MAT_degreesF_figure3_4_API579 = num7;
        break;
      case 'D':
        this.MAT_degreesF_figure3_4_API579 = num8;
        break;
    }
    if (!flag7)
    {
      this.MAT_OptionA = new double?(this.Convert(this.MAT_degreesF_figure3_4_API579, "DEG_F", "DEG_C"));
      if (flag6)
      {
        double? matOptionA = this.MAT_OptionA;
        this.MAT_OptionA = matOptionA.HasValue ? new double?(matOptionA.GetValueOrDefault() - 17.0) : new double?();
      }
    }
    else
      this.MAT_OptionA = new double?(-48.0);
    if (this.ImpactTestResultAvailable)
    {
      if (this.MaxImpactTestTemperature.HasValue)
      {
        this.MAT_OptionB = new double?(this.MaxImpactTestTemperature.Value);
        double? matOptionB = this.MAT_OptionB;
        double? matOptionA = this.MAT_OptionA;
        this.Level1MAT = (matOptionB.GetValueOrDefault() >= matOptionA.GetValueOrDefault() ? 0 : (matOptionB.HasValue & matOptionA.HasValue ? 1 : 0)) == 0 ? this.MAT_OptionA : new double?(this.MAT_OptionB.Value);
      }
    }
    else
      this.Level1MAT = this.MAT_OptionA;
    if (this.CET.HasValue)
    {
      double? cet = this.CET;
      double? level1Mat = this.Level1MAT;
      this.CETGreaterThanMAT = new bool?(cet.GetValueOrDefault() >= level1Mat.GetValueOrDefault() && cet.HasValue & level1Mat.HasValue);
      if (this.CETGreaterThanMAT.Value)
      {
        this.Level1Passed = true;
        this.Level1Message = "Level 1 Assessment ACCEPTABLE";
      }
      else
      {
        this.Level1Passed = false;
        this.Level1Message = "Level 1 Assessment UNACCEPTABLE";
      }
    }
    else
    {
      this.Level1Passed = true;
      this.Level1FormattedMessage = "Level 1 Assessment is ACCEPTABLE if CET is greater than {0}{1} otherwise Level 1 assessment fails.";
    }
    return CalculatorResult.Completed;
  }

  public double Convert(double quantity, string from, string to)
  {
    return from == to ? quantity : this.Convert(quantity, $"{from.Trim()} -> {to.Trim()}");
  }

  public double Convert(double quantity, string conversion)
  {
    switch (conversion)
    {
      case "DEG_C -> DEG_F":
        return quantity * 9.0 / 5.0 + 32.0;
      case "MPA -> PSI":
        return quantity * 145.0377;
      case "DEG_F -> DEG_C":
        return (quantity - 32.0) * (5.0 / 9.0);
      case "K -> DEG_C":
        return quantity - 273.15;
      case "DEG_C -> K":
        return quantity + 273.15;
      default:
        throw new NotImplementedException($"Unit conversion not implemented : {conversion}");
    }
  }
}
