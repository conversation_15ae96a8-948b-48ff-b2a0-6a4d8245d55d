// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_8.Utilities
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_8;

public class Utilities
{
  public static bool Table841_116A(
    double temperature,
    out double TempLowerFactor,
    out double TempUpperFactorT,
    out double TLower,
    out double TUpper)
  {
    double[] numArray1 = new double[6]
    {
      -400.0,
      121.1111,
      148.8888889,
      176.6666667,
      204.4444444,
      232.2222222
    };
    double[] numArray2 = new double[6]
    {
      1.0,
      1.0,
      0.967,
      0.933,
      0.9,
      0.867
    };
    TLower = -1.0;
    TUpper = -1.0;
    TempLowerFactor = 0.0;
    TempUpperFactorT = 0.0;
    for (int index = 0; index < numArray1.GetUpperBound(0); ++index)
    {
      if (numArray1[index + 1] >= temperature)
      {
        TempLowerFactor = numArray1[index];
        TempUpperFactorT = numArray1[index + 1];
        TLower = numArray2[index];
        TUpper = numArray2[index + 1];
        return true;
      }
    }
    return false;
  }

  public static double Table841_114A(string LocationClass)
  {
    switch (LocationClass.ToLower())
    {
      case "location class 1, division 1":
        return 0.8;
      case "location class 1, division 2":
        return 0.72;
      case "location class 2":
        return 0.6;
      case "location class 3":
        return 0.5;
      case "location class 4":
        return 0.4;
      default:
        throw new Exception("Invalid (out of range) value used in call to Table841_114A");
    }
  }

  public static double Table841_115A(string SpecPipeClass)
  {
    return new double[20]
    {
      1.0,
      1.0,
      0.6,
      1.0,
      0.8,
      1.0,
      0.8,
      0.8,
      1.0,
      1.0,
      1.0,
      0.8,
      1.0,
      0.8,
      1.0,
      1.0,
      1.0,
      1.0,
      1.0,
      0.6
    }[Array.IndexOf<string>(new string[20]
    {
      "ASTM A 53Seamless",
      "ASTM A 53Electric Resistance Welded",
      "ASTM A 53Furnace Butt Welded: Continuous Weld",
      "ASTM A 106Seamless",
      "ASTM A 134Electric Fusion Arc Welded",
      "ASTM A 135Electric Resistance Welded",
      "ASTM A 139Electric Fusion Welded",
      "ASTM A 211Spiral Welded Steel Pipe",
      "ASTM A 333Seamless",
      "ASTM A 333Electric Resistance Welded",
      "ASTM A 381Double Submerged-Arc-Welded",
      "ASTM A 671Electric Fusion Welded Classes 13, 23, 33, 43, 53",
      "ASTM A 671Electric Fusion Welded Classes 12, 22, 32, 42, 52",
      "ASTM A 672Electric Fusion Welded Classes 13, 23, 33, 43, 53",
      "ASTM A 672Electric Fusion Welded Classes 12, 22, 32, 42, 52",
      "API 5LSeamless",
      "API 5LElectric Resistance Welded",
      "API 5LElectric Flash Welded",
      "API 5LSubmerged Arc Welded",
      "API 5LFurnace Butt Welded"
    }, SpecPipeClass)];
  }
}
