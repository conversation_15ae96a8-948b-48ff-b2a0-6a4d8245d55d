// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_8.CylindricalSection.Blister.IBlisterCalculator
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.AssessmentData;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_8.CylindricalSection.Blister;

public interface IBlisterCalculator
{
  double NominalOutsideDiameter { set; }

  double NominalThickness { set; }

  double LongitudinalBlisterDimension { set; }

  double CircumferentialBlisterDimension { set; }

  double BlisterSpacing { set; }

  double BlisterBulgeProjection { set; }

  double TMMBlister { set; }

  VentType BlisterCrownCrackingAndVentHoles { set; }

  double SC { set; }

  double LW { set; }

  double LMSD { set; }

  double InternalUniformMetalLoss { set; }

  double ExternalUniformMetalLoss { set; }

  double InternalFutureCorrosionAllowance { set; }

  double ExternalFutureCorrosionAllowance { set; }

  bool CrownCrackPart5 { set; get; }

  double tc { get; }

  double BdDiameter { get; }

  bool BdLessThan50 { get; }

  bool ConditionS { get; }

  bool ConditionC { get; }

  bool MinimumMeasuredUndamagedThickness { get; }

  bool BlisterProjection { get; }

  bool DistanceToWeldSeam { get; }

  bool ConditionDistanceMajorDiscontinuity { get; }

  bool Level1Passed { get; }

  string Level1Conclusion { get; }

  List<string> Warnings { get; }

  string ErrorMessage { get; }

  CalculatorResult CalculateAssessment();

  double MAWP { get; set; }
}
