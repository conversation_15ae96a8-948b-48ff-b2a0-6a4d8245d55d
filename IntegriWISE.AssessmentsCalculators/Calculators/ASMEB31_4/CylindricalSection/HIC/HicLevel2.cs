// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.CylindricalSection.HIC.HicLevel2
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.CylindricalSection.HIC;

public class HicLevel2 : BaseCalculator, IHicCalculator
{
  private List<MaterialTSFCurveDTO> _api579Table5_4List;

  public double NominalOutsideDiameter { private get; set; }

  public double NominalThickness { private get; set; }

  public double LongitudinalHICDimension { private get; set; }

  public double CircumferentialHICDimension { private get; set; }

  public double LH { private get; set; }

  public double LW { private get; set; }

  public double LMSD { private get; set; }

  public double TMMID { private get; set; }

  public double TMMOD { private get; set; }

  public double InternalUniformMetalLoss { private get; set; }

  public double ExternalUniformMetalLoss { private get; set; }

  public double InternalFutureCorrosionAllowance { private get; set; }

  public double ExternalFutureCorrosionAllowance { private get; set; }

  public double HiC2HiCLongSpacing { private get; set; }

  public double WeldJointEfficiency { private get; set; }

  public bool IsMAWP { private get; set; }

  public double MAWP { get; set; }

  public double tc { get; private set; }

  public double wHModifiedByFCA { get; private set; }

  public bool ConditionS { get; private set; }

  public bool ConditionC { get; private set; }

  public bool ConditionThroughThicknessExtend { get; private set; }

  public bool ConditionDistanceToWeldSeam { get; private set; }

  public bool ConditionDistanceMajorDiscontinuity { get; private set; }

  public bool ConditionSubsurfaceHICTmmID { get; private set; }

  public bool ConditionSubsurfaceHICTmmOD { get; private set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public string Level1Conclusion { get; private set; }

  public bool Level1Passed { get; private set; }

  public string LongitudinalExtent { get; private set; }

  public double RSF { get; private set; }

  public SurfaceSubsurface IsSurfaceSubsurface { get; private set; }

  public double DepthOfHicDamage { get; private set; }

  public double MAWPr { get; private set; }

  public bool RSFGreaterRSFa { get; private set; }

  public bool Level2Passed { get; private set; }

  public string FinalL2Conclusion { get; private set; }

  public double LambdaC { get; private set; }

  public string CircumferentialExtent { get; private set; }

  public bool? LambdaCLessThan9 { get; private set; }

  public bool? DOverTcGreaterThan20 { get; private set; }

  public bool? RSFBetween0_7And1 { get; private set; }

  public bool? ElBetween0_7And1 { get; private set; }

  public bool? EcBetween0_7And2 { get; private set; }

  public bool? ScreeningCriteriaFigure5_8 { get; private set; }

  public double? TSF { get; private set; }

  public HicLevel2(List<MaterialTSFCurveDTO> api579Table5_4List)
  {
    this._api579Table5_4List = api579Table5_4List;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.CylindricalSection.HIC.HIC hic = new IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.CylindricalSection.HIC.HIC();
    hic.NominalOutsideDiameter = this.NominalOutsideDiameter;
    hic.NominalThickness = this.NominalThickness;
    hic.ExternalFutureCorrosionAllowance = this.ExternalFutureCorrosionAllowance;
    hic.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    hic.InternalUniformMetalLoss = this.InternalUniformMetalLoss;
    hic.InternalFutureCorrosionAllowance = this.InternalFutureCorrosionAllowance;
    hic.LW = this.LW;
    hic.LMSD = this.LMSD;
    hic.TMMID = this.TMMID;
    hic.TMMOD = this.TMMOD;
    hic.LongitudinalHICDimension = this.LongitudinalHICDimension;
    hic.CircumferentialHICDimension = this.CircumferentialHICDimension;
    hic.LH = this.LH;
    CalculatorResult assessment = hic.CalculateAssessment();
    this.ErrorMessage = hic.ErrorMessage;
    this.Warnings.AddRange((IEnumerable<string>) hic.Warnings);
    this.tc = hic.tc;
    this.wHModifiedByFCA = hic.wHModifiedByFCA;
    this.ConditionS = hic.ConditionS;
    this.ConditionC = hic.ConditionC;
    this.ConditionThroughThicknessExtend = hic.ConditionThroughThicknessExtend;
    this.ConditionDistanceToWeldSeam = hic.ConditionDistanceToWeldSeam;
    this.ConditionDistanceMajorDiscontinuity = hic.ConditionDistanceMajorDiscontinuity;
    this.ConditionSubsurfaceHICTmmID = hic.ConditionSubsurfaceHICTmmID;
    this.ConditionSubsurfaceHICTmmOD = hic.ConditionSubsurfaceHICTmmOD;
    this.Level1Passed = hic.Level1Passed;
    this.Level1Conclusion = hic.Level1Conclusion;
    if (assessment == CalculatorResult.Fail)
      return assessment;
    if (this.Level1Passed)
      return CalculatorResult.Completed;
    this.IsSurfaceSubsurface = !this.ConditionSubsurfaceHICTmmID || !this.ConditionSubsurfaceHICTmmOD ? SurfaceSubsurface.Surface : SurfaceSubsurface.Subsurface;
    double num1 = this.IsSurfaceSubsurface == SurfaceSubsurface.Surface ? this.wHModifiedByFCA + Math.Min(this.TMMID, this.TMMOD) : this.wHModifiedByFCA;
    double num2 = this.NominalOutsideDiameter / 2.0 - this.ExternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss;
    double num3 = 0.8;
    double num4 = 1.285 * this.LongitudinalHICDimension / Math.Pow(2.0 * num2 * this.tc, 0.5);
    double x = num4 > 20.0 ? 20.0 : num4;
    double num5 = 1001.0 / 1000.0 - 0.014195 * x + 0.2909 * Math.Pow(x, 2.0) - 0.09642 * Math.Pow(x, 3.0) + 0.02089 * Math.Pow(x, 4.0) - 0.003054 * Math.Pow(x, 5.0) + 2.957 * Math.Pow(10.0, -4.0) * Math.Pow(x, 6.0) - 1.8462 * Math.Pow(10.0, -5.0) * Math.Pow(x, 7.0) + 7.1553 * Math.Pow(10.0, -7.0) * Math.Pow(x, 8.0) - 1.5631 * Math.Pow(10.0, -8.0) * Math.Pow(x, 9.0) + 916.0 / 625.0 * Math.Pow(10.0, -10.0) * Math.Pow(x, 10.0);
    double num6 = Math.Min(this.HiC2HiCLongSpacing / 2.0, 8.0 * this.tc);
    double num7 = num1 * num3 / this.tc;
    this.RSF = this.IsSurfaceSubsurface != SurfaceSubsurface.Surface ? (2.0 * num6 + this.LongitudinalHICDimension * (1.0 - num7)) / (2.0 * num6 + this.LongitudinalHICDimension) : (1.0 - num7) / (1.0 - num7 / num5);
    this.RSFGreaterRSFa = this.RSF >= 0.9;
    this.LambdaC = 1.285 * this.CircumferentialHICDimension / Math.Pow(num2 * 2.0 * this.tc, 0.5);
    this.LambdaCLessThan9 = new bool?(this.LambdaC <= 9.0);
    this.DOverTcGreaterThan20 = new bool?(num2 * 2.0 / this.tc >= 20.0);
    this.RSFBetween0_7And1 = new bool?(this.RSF >= 0.7 && this.RSF <= 1.0);
    this.ElBetween0_7And1 = new bool?(this.WeldJointEfficiency >= 0.7 && this.WeldJointEfficiency <= 1.0);
    this.EcBetween0_7And2 = new bool?(this.WeldJointEfficiency >= 0.7 && this.WeldJointEfficiency <= 1.0);
    bool flag = this.LambdaCLessThan9.Value && this.DOverTcGreaterThan20.Value && this.RSFBetween0_7And1.Value && this.ElBetween0_7And1.Value && this.EcBetween0_7And2.Value;
    double num8 = this.InternalFutureCorrosionAllowance + this.ExternalFutureCorrosionAllowance;
    this.DepthOfHicDamage = num1 * num3;
    double num9 = this.tc + num8 - this.DepthOfHicDamage;
    double num10 = (num9 - num8) / this.tc;
    if (num9 <= 0.0)
    {
      this.ErrorMessage = "Minimum equivalent thickness (tmm) must be greater than 0, cannot do level 2 assessment";
      return CalculatorResult.Fail;
    }
    if (num10 <= 0.0)
    {
      this.ErrorMessage = "Remaining ration (rt) must be greater than 0, cannot do level 2 assessment";
      return CalculatorResult.Fail;
    }
    if (!flag)
    {
      this.CircumferentialExtent = "The Circumferential extent of the flaw is UNACCEPTABLE";
      this.Level2Passed = false;
      this.FinalL2Conclusion = "Level 2 Assessment UNACCEPTABLE";
    }
    else
    {
      this.TSF = new double?(this.WeldJointEfficiency / (2.0 * this.RSF) * (1.0 + Math.Pow(4.0 - 3.0 * Math.Pow(this.WeldJointEfficiency, 2.0), 0.5) / this.WeldJointEfficiency));
      double RtFigure5_8;
      string errorMessage;
      if (!this.GetRtFigure5_8(this._api579Table5_4List, this.TSF.Value, this.LambdaC, out RtFigure5_8, out errorMessage))
      {
        this.ErrorMessage = errorMessage;
        return CalculatorResult.Fail;
      }
      this.ScreeningCriteriaFigure5_8 = new bool?(num10 >= RtFigure5_8);
    }
    HicLevel2.PassType passType1;
    if (this.ConditionDistanceMajorDiscontinuity && this.ConditionDistanceToWeldSeam)
    {
      if (this.RSFGreaterRSFa)
      {
        this.LongitudinalExtent = "The longitudinal extent of the HIC damage is ACCEPTABLE for operation at MAWP";
        passType1 = HicLevel2.PassType.pass;
      }
      else
      {
        this.LongitudinalExtent = "The longitudinal extent of the HIC damage is UNACCEPTABLE for operation at MAWP, but is acceptable for operation at the MAWPr";
        passType1 = HicLevel2.PassType.failWithWarning;
        if (this.IsMAWP)
          this.MAWPr = this.MAWP * this.RSF / 0.9;
      }
    }
    else
    {
      this.LongitudinalExtent = "The longitudinal extent of the HIC damage is UNACCEPTABLE.";
      passType1 = HicLevel2.PassType.fail;
    }
    HicLevel2.PassType passType2;
    if (flag && this.ScreeningCriteriaFigure5_8.Value)
    {
      this.CircumferentialExtent = "The circumferential extent of the HIC damage is ACCEPTABLE.";
      passType2 = HicLevel2.PassType.pass;
    }
    else
    {
      this.CircumferentialExtent = "The circumferential extent of the HIC damage is UNACCEPTABLE.";
      passType2 = HicLevel2.PassType.fail;
    }
    if (passType2 == HicLevel2.PassType.pass && (passType1 == HicLevel2.PassType.pass || passType1 == HicLevel2.PassType.failWithWarning))
    {
      this.Warnings.Add("Confirmation that further HIC damage has been prevented or is limited is required. Otherwise, Level 2 Assessment is not satisfied.");
      this.Warnings.Add("If the HIC damage remains in hydrogen charging or it is classified as surface breaking (tmmID < 0.20tc or tmmOD < 0.20tc) or the depth of the HIC does not meet the criterion wH<=min(tc/3, 13mm (0.5in)), then fracture assessment is required. Two crack-like flaw assessments shall be performed, one for the longitudinal and one for the circumferential extent of the HIC damage.");
    }
    this.Level2Passed = passType1 == HicLevel2.PassType.pass && passType2 == HicLevel2.PassType.pass;
    this.FinalL2Conclusion = !this.Level2Passed ? "Level 2 Assessment is UNACCEPTABLE" : "Level 2 Assessment is ACCEPTABLE";
    if (passType2 == HicLevel2.PassType.pass && passType1 == HicLevel2.PassType.failWithWarning)
      this.FinalL2Conclusion = "Level 2 Assessment is unacceptable for operation at MAWP but the component can operate at MAWPr";
    return CalculatorResult.Completed;
  }

  private enum PassType
  {
    pass,
    fail,
    failWithWarning,
  }
}
