// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.Reducer.LocalMetalLossASMEB31G.LocalMetalLossASMEB31G
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.Reducer.LocalMetalLossASMEB31G;

public class LocalMetalLossASMEB31G : BaseCalculator
{
  private MaterialASMEB31_4DTO _material;
  private ThicknessReadings _thicknessReadings;

  public bool ToLevel2 { get; set; }

  public double DesignPressure { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double MaxCorrodedDepth { get; set; }

  public double LongitudinalExtent { get; set; }

  public double SafetyFactor { get; set; }

  public string SflowLookup { get; set; }

  public double LongitudinalThicknessReadingSpacing { get; set; }

  public string Material_B31G { get; set; }

  public double Z { get; set; }

  public double So { get; set; }

  public double SflowOriginal { get; set; }

  public double MOriginal { get; set; }

  public double SflowModified { get; set; }

  public double MModified { get; set; }

  public double SFailureOriginalL1 { get; set; }

  public double SFailureModifiedL1 { get; set; }

  public double PressureOriginalL1 { get; set; }

  public double PressureModifiedL1 { get; set; }

  public bool SFailureCriteriaOriginalL1 { get; set; }

  public bool SFailureCriteriaModifiedL1 { get; set; }

  public bool PressureCriteriaOriginalL1 { get; set; }

  public bool PressureCriteriaModifiedL1 { get; set; }

  public bool Level1Passed { get; set; }

  public bool Level1OriginalPassed { get; set; }

  public string Level1OriginalAssessmentConclusion { get; set; }

  public bool Level1ModifiedPassed { get; set; }

  public string Level1ModifiedAssessmentConclusion { get; set; }

  public double minRSFiOriginal { get; set; }

  public double minRSFiModified { get; set; }

  public double SFailureOriginalL2 { get; set; }

  public double SFailureModifiedL2 { get; set; }

  public double PressureOriginalL2 { get; set; }

  public double PressureModifiedL2 { get; set; }

  public bool SFailureCriteriaOriginalL2 { get; set; }

  public bool SFailureCriteriaModifiedL2 { get; set; }

  public bool PressureCriteriaOriginalL2 { get; set; }

  public bool PressureCriteriaModifiedL2 { get; set; }

  public bool Level2OriginalPassed { get; set; }

  public string Level2OriginalAssessmentConclusion { get; set; }

  public bool Level2ModifiedPassed { get; set; }

  public string Level2ModifiedAssessmentConclusion { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private double DoverNominalThickness { get; set; }

  private double Radius { get; set; }

  public LocalMetalLossASMEB31G(
    MaterialASMEB31_4DTO material,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this._thicknessReadings = thicknessReadingsCalculator;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    this.Warnings.Add("The length (L) and depth (d) of the area of metal loss should be consistent with the matrix readings. Be aware that tnom should be equal to tmm + d.");
    if (this.SflowLookup == "Sflow=1.1*SMYS")
      this.Material_B31G = "Plain Carbon Steel";
    else if (this.SflowLookup == "Sflow=SMYS+69")
      this.Material_B31G = "Plain carbon steel having SMYS<483MPa";
    else if (this.SflowLookup == "Sflow=(SYT+SUT)/2")
      this.Material_B31G = "Plain carbon steel having SMYS<551MPa";
    this.Z = Math.Pow(this.LongitudinalExtent, 2.0) / (this.NominalOutsideDiameter * this.NominalThickness);
    this.So = this.DesignPressure * this.NominalOutsideDiameter / (2.0 * this.NominalThickness);
    double num = Convert.ToDouble(this._material.SMYS_MPA.Value);
    this.SflowOriginal = 1.1 * num;
    this.MOriginal = Math.Pow(1.0 + 0.8 * this.Z, 0.5);
    if (!this._material.UserDefined && this.SflowLookup == "Sflow=(SYT+SUT)/2" && (!this._material.YieldStrengthNew.HasValue || !this._material.TensileStrengthNew.HasValue))
    {
      this.ErrorMessage = "Please Enter your Material's Yield and Ultimate Tensile Strength at the assessment temperature";
      return CalculatorResult.Fail;
    }
    if (this.SflowLookup == "Sflow=1.1*SMYS")
      this.SflowModified = this.SflowOriginal;
    else if (this.SflowLookup == "Sflow=SMYS+69")
      this.SflowModified = num + 69.0;
    else if (this.SflowLookup == "Sflow=(SYT+SUT)/2")
      this.SflowModified = (this._material.YieldStrengthNew.Value + this._material.TensileStrengthNew.Value) / 2.0;
    this.MModified = this.Z > 50.0 ? 0.032 * this.Z + 3.3 : Math.Pow(1.0 + 251.0 / 400.0 * this.Z - 0.003375 * Math.Pow(this.Z, 2.0), 0.5);
    this.DoverNominalThickness = this.MaxCorrodedDepth / this.NominalThickness;
    this.SFailureOriginalL1 = this.Z > 20.0 ? this.SflowOriginal * (1.0 - this.DoverNominalThickness) : this.SflowOriginal * (1.0 - 2.0 / 3.0 * this.DoverNominalThickness) / (1.0 - 2.0 / 3.0 * this.DoverNominalThickness / this.MOriginal);
    this.SFailureModifiedL1 = this.SflowModified * (1.0 - 0.85 * this.DoverNominalThickness) / (1.0 - 0.85 * this.DoverNominalThickness / this.MModified);
    this.PressureOriginalL1 = 2.0 * this.SFailureOriginalL1 * this.NominalThickness / this.NominalOutsideDiameter;
    this.PressureModifiedL1 = 2.0 * this.SFailureModifiedL1 * this.NominalThickness / this.NominalOutsideDiameter;
    this.SFailureCriteriaOriginalL1 = this.SFailureOriginalL1 >= this.So * this.SafetyFactor;
    this.SFailureCriteriaModifiedL1 = this.SFailureModifiedL1 >= this.So * this.SafetyFactor;
    this.PressureCriteriaOriginalL1 = this.PressureOriginalL1 >= this.DesignPressure * this.SafetyFactor;
    this.PressureCriteriaModifiedL1 = this.PressureModifiedL1 >= this.DesignPressure * this.SafetyFactor;
    if (this.SFailureCriteriaOriginalL1 && this.PressureCriteriaOriginalL1)
    {
      this.Level1OriginalPassed = true;
      this.Level1OriginalAssessmentConclusion = "The Original B31.G Level 1 Assessment is ACCEPTABLE";
    }
    else
    {
      this.Level1OriginalPassed = false;
      this.Level1OriginalAssessmentConclusion = "The Original B31.G Level 1 Assessment is UNACCEPTABLE";
    }
    if (this.SFailureCriteriaModifiedL1 && this.PressureCriteriaModifiedL1)
    {
      this.Level1ModifiedPassed = true;
      this.Level1ModifiedAssessmentConclusion = "The Modified B31.G Level 1 Assessment is ACCEPTABLE";
    }
    else
    {
      this.Level1ModifiedPassed = false;
      this.Level1ModifiedAssessmentConclusion = "The Modified B31.G Level 1 Assessment is UNACCEPTABLE";
    }
    if (this.Level1OriginalPassed && this.Level1ModifiedPassed)
    {
      this.Level1Passed = true;
      return CalculatorResult.Completed;
    }
    this.Level1Passed = false;
    if (!this.ToLevel2)
      return CalculatorResult.Completed;
    try
    {
      if (this._thicknessReadings.GetMinimumValue() + this.MaxCorrodedDepth != this.NominalThickness)
      {
        this.ErrorMessage = "Incorrect Inputs. Please make sure you have defined the maximum corroded depth correctly ( NominalThickness = Tmm + MaxCorrodedDepth) ";
        return CalculatorResult.Fail;
      }
    }
    catch
    {
      this.ErrorMessage = "Profile readings contain multiple Tmm's - Currently unable to proceed with the assessment.";
      return CalculatorResult.Fail;
    }
    this.Radius = this.NominalOutsideDiameter / 2.0;
    this._thicknessReadings.ProcessB31GOriginal(this.NominalThickness, this.LongitudinalThicknessReadingSpacing, 2.0 * this.Radius, this.NominalThickness);
    this.minRSFiOriginal = this._thicknessReadings.GetLevel2RSF();
    this._thicknessReadings.ProcessB31GModified(this.NominalThickness, this.LongitudinalThicknessReadingSpacing, 2.0 * this.Radius, this.NominalThickness);
    this.minRSFiModified = this._thicknessReadings.GetLevel2RSF();
    this.SFailureOriginalL2 = this.SflowOriginal * this.minRSFiOriginal;
    this.SFailureModifiedL2 = this.SflowModified * this.minRSFiModified;
    this.PressureOriginalL2 = 2.0 * this.SFailureOriginalL2 * this.NominalThickness / this.NominalOutsideDiameter;
    this.PressureModifiedL2 = 2.0 * this.SFailureModifiedL2 * this.NominalThickness / this.NominalOutsideDiameter;
    this.SFailureCriteriaOriginalL2 = this.SFailureOriginalL2 >= this.So * this.SafetyFactor;
    this.SFailureCriteriaModifiedL2 = this.SFailureModifiedL2 >= this.So * this.SafetyFactor;
    this.PressureCriteriaOriginalL2 = this.PressureOriginalL2 >= this.DesignPressure * this.SafetyFactor;
    this.PressureCriteriaModifiedL2 = this.PressureModifiedL2 >= this.DesignPressure * this.SafetyFactor;
    if (this.SFailureCriteriaOriginalL2 && this.PressureCriteriaOriginalL2)
    {
      this.Level2OriginalPassed = true;
      this.Level2OriginalAssessmentConclusion = "The Original B31.G Level 2 Assessment is ACCEPTABLE";
    }
    else
    {
      this.Level2OriginalPassed = false;
      this.Level2OriginalAssessmentConclusion = "The Original B31.G Level 2 Assessment is UNACCEPTABLE";
    }
    if (this.SFailureCriteriaModifiedL2 && this.PressureCriteriaModifiedL2)
    {
      this.Level2ModifiedPassed = true;
      this.Level2ModifiedAssessmentConclusion = "The Modified B31.G Level 2 Assessment is ACCEPTABLE";
    }
    else
    {
      this.Level2ModifiedPassed = false;
      this.Level2ModifiedAssessmentConclusion = "The Modified B31.G Level 2 Assessment is UNACCEPTABLE";
    }
    return CalculatorResult.Completed;
  }
}
