// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.Reducer.MembraneStress.MembraneStress
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.Reducer.MembraneStress;

public class MembraneStress : BaseCalculator
{
  public double DesignPressure { get; set; }

  public double NominalThickness { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double SigmaM { get; private set; }

  public MembraneStress() => this.Warnings = new List<string>();

  public CalculatorResult CalculateAssessment()
  {
    this.SigmaM = this.DesignPressure * this.NominalOutsideDiameter / (2.0 * this.NominalThickness);
    return CalculatorResult.Completed;
  }
}
