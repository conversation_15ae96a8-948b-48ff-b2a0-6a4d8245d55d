// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.Elbow.Lamination.LaminationLevel2
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.Elbow.Lamination;

public class LaminationLevel2 : BaseCalculator, ILaminationCalculator
{
  private List<MaterialTSFCurveDTO> _api579Table5_4List;
  private bool RtGreater0;

  public double NominalOutsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public string MultipleLaminations { get; set; }

  public double LongitudinalLaminationDimension { get; set; }

  public double CircumferentialLaminationDimension { get; set; }

  public double LaminationHeight { get; set; }

  public double LaminationSpacing { get; set; }

  public double TMM { get; set; }

  public double LW { get; set; }

  public double LMSD { get; set; }

  public bool InHydrogenChargingService { get; set; }

  public bool MAWPLevel { get; set; }

  public double AllowableStrength { get; set; }

  public double tc { get; set; }

  public bool CrackLikeFlaw { get; set; }

  public bool LaminationIsNotSurfaceBreaking { get; set; }

  public bool DistanceToTheNearestDiscontinuity { get; set; }

  public bool DistanceToTheNearestWeldSeam { get; set; }

  public bool HydrogenChargingAndDimensions { get; set; }

  public bool HydrogenChargingAndDimensions2 { get; set; }

  public double MAWP { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public string Level1 { get; set; }

  public bool Level1Passed { get; set; }

  public double WeldJointEfficiency { get; set; }

  public double Rt { get; set; }

  public double LambdaC { get; set; }

  public double RSF { get; set; }

  public bool ConditionsCircumferentialExtent { get; set; }

  public double TSF { get; set; }

  public double TmmPart5 { get; set; }

  public bool RtGreater0_2 { get; set; }

  public bool TmmMinusFCAGreater2_5 { get; set; }

  public bool LmsdGreater1_8DtcPower0_5 { get; set; }

  public bool LambdaCLessThan9 { get; set; }

  public bool DOverTcGreaterThan20 { get; set; }

  public bool RSFBetween0_7And1 { get; set; }

  public bool ElBetween0_7And1 { get; set; }

  public bool EcBetween0_7And2 { get; set; }

  public bool ScreeningCriteriaFigure5_6 { get; set; }

  public bool RSFGreaterRSFa { get; set; }

  public bool ScreeningCriteriaFigure5_8 { get; set; }

  public int iConditionLongitudinalExtentLevel2 { get; set; }

  public string ConditionCircumferentialExtentL2 { get; set; }

  public bool Level2Passed { get; set; }

  public string ConditionLongitudinalExtentL2 { get; set; }

  public string Level2AssessmentConclusion { get; set; }

  private double Lambda { get; set; }

  private double RSFa { get; set; }

  private double LambdaTable5_2 { get; set; }

  private double MtTable5_2 { get; set; }

  private double RtFigure5_6 { get; set; }

  private double Radius { get; set; }

  public double MAWPrL2 { get; set; }

  public LaminationLevel2(List<MaterialTSFCurveDTO> api579Table5_4List)
  {
    this._api579Table5_4List = api579Table5_4List;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.Elbow.Lamination.Lamination lamination = new IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.Elbow.Lamination.Lamination();
    lamination.NominalOutsideDiameter = this.NominalOutsideDiameter;
    lamination.NominalThickness = this.NominalThickness;
    lamination.InternalUniformMetalLoss = this.InternalUniformMetalLoss;
    lamination.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    lamination.InternalFutureCorrosionAllowance = this.InternalFutureCorrosionAllowance;
    lamination.ExternalFutureCorrosionAllowance = this.ExternalFutureCorrosionAllowance;
    lamination.MultipleLaminations = this.MultipleLaminations;
    lamination.LongitudinalLaminationDimension = this.LongitudinalLaminationDimension;
    lamination.CircumferentialLaminationDimension = this.CircumferentialLaminationDimension;
    lamination.LaminationHeight = this.LaminationHeight;
    lamination.LaminationSpacing = this.LaminationSpacing;
    lamination.TMM = this.TMM;
    lamination.LW = this.LW;
    lamination.LMSD = this.LMSD;
    lamination.InHydrogenChargingService = this.InHydrogenChargingService;
    lamination.MAWPLevel = this.MAWPLevel;
    lamination.MAWP = this.MAWP;
    CalculatorResult assessment = lamination.CalculateAssessment();
    this.ErrorMessage = lamination.ErrorMessage;
    this.Warnings.AddRange((IEnumerable<string>) lamination.Warnings);
    this.tc = lamination.tc;
    this.CrackLikeFlaw = lamination.CrackLikeFlaw;
    this.LaminationIsNotSurfaceBreaking = lamination.LaminationIsNotSurfaceBreaking;
    this.DistanceToTheNearestWeldSeam = lamination.DistanceToTheNearestWeldSeam;
    this.DistanceToTheNearestDiscontinuity = lamination.DistanceToTheNearestDiscontinuity;
    this.HydrogenChargingAndDimensions2 = lamination.HydrogenChargingAndDimensions2;
    this.HydrogenChargingAndDimensions = lamination.HydrogenChargingAndDimensions;
    this.Level1Passed = lamination.Level1Passed;
    this.Level1 = lamination.Level1;
    if (assessment == CalculatorResult.Fail)
      return assessment;
    if (this.Level1Passed)
      return CalculatorResult.Completed;
    if (this.InHydrogenChargingService)
    {
      this.Warnings.Add("The lamination should be evaluated as an equivalent LTA using methods in Part 5");
      this.Radius = this.NominalOutsideDiameter / 2.0 - this.ExternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss;
      this.RSFa = 0.9;
      this.TmmPart5 = Math.Max(this.tc - this.LaminationHeight - this.TMM, this.TMM);
      this.Rt = (this.TmmPart5 - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.tc;
      this.Rt = Math.Round(this.Rt, 4);
      this.RtGreater0 = this.Rt > 0.0;
      if (!this.RtGreater0)
      {
        this.ErrorMessage = "Remaining thickness ratio is less than or equal to zero. The assessment fails. ";
        return CalculatorResult.Fail;
      }
      this.Lambda = 1.285 * this.LongitudinalLaminationDimension / Math.Pow(2.0 * this.Radius * this.tc, 0.5);
      this.RtGreater0_2 = this.Rt > 0.2;
      this.TmmMinusFCAGreater2_5 = this.TmmPart5 - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance > 2.5;
      this.LmsdGreater1_8DtcPower0_5 = this.LMSD > 1.8 * Math.Pow(this.Radius * 2.0 * this.tc, 0.5);
      this.LambdaTable5_2 = this.Lambda <= 20.0 ? this.Lambda : 20.0;
      this.MtTable5_2 = 1001.0 / 1000.0 - 0.014195 * this.LambdaTable5_2 + 0.2909 * Math.Pow(this.LambdaTable5_2, 2.0) - 0.09642 * Math.Pow(this.LambdaTable5_2, 3.0) + 0.02089 * Math.Pow(this.LambdaTable5_2, 4.0) - 0.003054 * Math.Pow(this.LambdaTable5_2, 5.0) + 2.957 * Math.Pow(10.0, -4.0) * Math.Pow(this.LambdaTable5_2, 6.0) - 1.8462 * Math.Pow(10.0, -5.0) * Math.Pow(this.LambdaTable5_2, 7.0) + 7.1553 * Math.Pow(10.0, -7.0) * Math.Pow(this.LambdaTable5_2, 8.0) - 1.5631 * Math.Pow(10.0, -8.0) * Math.Pow(this.LambdaTable5_2, 9.0) + 916.0 / 625.0 * Math.Pow(10.0, -10.0) * Math.Pow(this.LambdaTable5_2, 10.0);
      this.RtFigure5_6 = this.Lambda > 0.354 ? (this.Lambda >= 20.0 ? 0.9 : (this.RSFa - this.RSFa / this.MtTable5_2) * Math.Pow(1.0 - this.RSFa / this.MtTable5_2, -1.0)) : 0.2;
      this.ScreeningCriteriaFigure5_6 = this.Rt > this.RtFigure5_6;
      this.RSF = this.Rt / (1.0 - 1.0 / this.MtTable5_2 * (1.0 - this.Rt));
      this.RSFGreaterRSFa = this.RSF >= this.RSFa;
      if (this.RtGreater0_2 && this.TmmMinusFCAGreater2_5 && this.LmsdGreater1_8DtcPower0_5)
      {
        if (this.ScreeningCriteriaFigure5_6 || this.RSFGreaterRSFa)
        {
          this.ConditionLongitudinalExtentL2 = "The longitudinal extent of the lamination is ACCEPTABLE for operation at the MAWP.";
          this.iConditionLongitudinalExtentLevel2 = 1;
        }
        else
        {
          this.ConditionLongitudinalExtentL2 = "The longitudinal extent of the lamination is ACCEPTABLE for operation at the MAWPr.";
          this.iConditionLongitudinalExtentLevel2 = 2;
        }
      }
      else
      {
        this.ConditionLongitudinalExtentL2 = "The longitudinal extent of the lamination is UNACCEPTABLE";
        this.iConditionLongitudinalExtentLevel2 = 3;
      }
      this.LambdaC = 1.285 * this.CircumferentialLaminationDimension / Math.Pow(this.Radius * 2.0 * this.tc, 0.5);
      this.LambdaCLessThan9 = this.LambdaC <= 9.0;
      this.DOverTcGreaterThan20 = this.Radius * 2.0 / this.tc >= 20.0;
      this.RSFBetween0_7And1 = this.RSF >= 0.7 && this.RSF <= 1.0;
      this.ElBetween0_7And1 = this.WeldJointEfficiency >= 0.7 && this.WeldJointEfficiency <= 1.0;
      this.EcBetween0_7And2 = this.WeldJointEfficiency >= 0.7 && this.WeldJointEfficiency <= 1.0;
      this.ConditionsCircumferentialExtent = this.LambdaCLessThan9 && this.DOverTcGreaterThan20 && this.RSFBetween0_7And1 && this.ElBetween0_7And1 && this.EcBetween0_7And2;
      if (!this.ConditionsCircumferentialExtent)
      {
        this.ConditionCircumferentialExtentL2 = "The Circumferential extent of the lamination is UNACCEPTABLE";
        this.Level2Passed = false;
        this.Level2AssessmentConclusion = "The Level 2 Assessment is UNACCEPTABLE";
      }
      else
      {
        this.TSF = this.WeldJointEfficiency / (2.0 * this.RSF) * (1.0 + Math.Pow(Math.Pow(4.0 - 3.0 * this.WeldJointEfficiency, 2.0), 0.5) / this.WeldJointEfficiency);
        double RtFigure5_8;
        if (!this.GetRtFigure5_8(this.TSF, this.LambdaC, out RtFigure5_8))
          return CalculatorResult.Fail;
        this.ScreeningCriteriaFigure5_8 = this.Rt >= RtFigure5_8;
        if (this.ScreeningCriteriaFigure5_8)
        {
          this.ConditionCircumferentialExtentL2 = "The Circumferential extent of the lamination is ACCEPTABLE";
          if (this.CrackLikeFlaw && this.DistanceToTheNearestDiscontinuity && this.LaminationIsNotSurfaceBreaking)
          {
            if (this.iConditionLongitudinalExtentLevel2 == 1)
            {
              this.Level2Passed = true;
              this.Level2AssessmentConclusion = "The Level 2 Assessment is ACCEPTABLE";
              return CalculatorResult.Completed;
            }
            if (this.iConditionLongitudinalExtentLevel2 == 2)
            {
              this.Level2Passed = false;
              this.Level2AssessmentConclusion = "The Level 2 Assessment is UNACCEPTABLE.The component is unacceptable for operation at MAWP but it is ACCEPTABLE for operation at MAWPr";
            }
          }
          else
          {
            this.Level2Passed = false;
            this.Level2AssessmentConclusion = "The Level 2 Assessment is UNACCEPTABLE";
          }
        }
        else
        {
          this.ConditionCircumferentialExtentL2 = "The Circumferential extent of the lamination is UNACCEPTABLE";
          this.Level2Passed = false;
          this.Level2AssessmentConclusion = "Level 2 Assessment UNACCEPTABLE";
        }
      }
    }
    else
    {
      if (this.CrackLikeFlaw && this.DistanceToTheNearestDiscontinuity && this.LaminationIsNotSurfaceBreaking)
      {
        this.Level2Passed = true;
        this.Level2AssessmentConclusion = "The Level 2 Assessment is ACCEPTABLE";
        return CalculatorResult.Completed;
      }
      this.Level2Passed = false;
      this.Level2AssessmentConclusion = "Level 2 Assessment UNACCEPTABLE";
    }
    if (this.MAWPLevel && this.iConditionLongitudinalExtentLevel2 == 2)
      this.MAWPrL2 = this.MAWP * this.RSF / this.RSFa;
    return CalculatorResult.Completed;
  }

  private bool GetRtFigure5_8(double TSF, double LambdaC, out double RtFigure5_8)
  {
    MaterialTSFCurveDTO materialTsfCurveDto1 = this._api579Table5_4List.Where<MaterialTSFCurveDTO>((Func<MaterialTSFCurveDTO, bool>) (x =>
    {
      double? tsf = x.TSF;
      double num = TSF;
      return tsf.GetValueOrDefault() > num && tsf.HasValue;
    })).OrderBy<MaterialTSFCurveDTO, double?>((Func<MaterialTSFCurveDTO, double?>) (x => x.TSF)).FirstOrDefault<MaterialTSFCurveDTO>();
    if (materialTsfCurveDto1 == null)
    {
      this.ErrorMessage = "TSF Outside normal operating conditions";
      RtFigure5_8 = -1.0;
      return false;
    }
    MaterialTSFCurveDTO materialTsfCurveDto2 = this._api579Table5_4List.Where<MaterialTSFCurveDTO>((Func<MaterialTSFCurveDTO, bool>) (x =>
    {
      double? tsf = x.TSF;
      double num = TSF;
      return tsf.GetValueOrDefault() < num && tsf.HasValue;
    })).OrderByDescending<MaterialTSFCurveDTO, double?>((Func<MaterialTSFCurveDTO, double?>) (x => x.TSF)).FirstOrDefault<MaterialTSFCurveDTO>() ?? materialTsfCurveDto1;
    double num1 = materialTsfCurveDto2.TSF.Value;
    double num2 = materialTsfCurveDto1.TSF.Value;
    double num3 = materialTsfCurveDto2.LambdaCMinus02.Value;
    double num4 = materialTsfCurveDto1.LambdaCMinus02.Value;
    double num5 = num1 != 2.3 ? (TSF - num1) / (num2 - num1) * (num4 - num3) + num3 : num3;
    double num6 = materialTsfCurveDto2.C1.Value;
    double num7 = materialTsfCurveDto2.C2.Value;
    double num8 = materialTsfCurveDto2.C3.Value;
    double num9 = materialTsfCurveDto2.C4.Value;
    double num10 = materialTsfCurveDto2.C5.Value;
    double num11 = materialTsfCurveDto2.C6.Value;
    double num12 = materialTsfCurveDto1.C1.Value;
    double num13 = materialTsfCurveDto1.C2.Value;
    double num14 = materialTsfCurveDto1.C3.Value;
    double num15 = materialTsfCurveDto1.C4.Value;
    double num16 = materialTsfCurveDto1.C5.Value;
    double num17 = materialTsfCurveDto1.C6.Value;
    double num18;
    double num19;
    double num20;
    double num21;
    double num22;
    double num23;
    if (num1 == 2.3)
    {
      num18 = num6;
      num19 = num7;
      num20 = num8;
      num21 = num9;
      num22 = num10;
      num23 = num11;
    }
    else
    {
      num18 = (TSF - num1) / (num2 - num1) * (num12 - num6) + num6;
      num19 = (TSF - num1) / (num2 - num1) * (num13 - num7) + num7;
      num20 = (TSF - num1) / (num2 - num1) * (num14 - num8) + num8;
      num21 = (TSF - num1) / (num2 - num1) * (num15 - num9) + num9;
      num22 = (TSF - num1) / (num2 - num1) * (num16 - num10) + num10;
      num23 = (TSF - num1) / (num2 - num1) * (num17 - num11) + num11;
    }
    RtFigure5_8 = LambdaC > num5 ? num18 + num19 / LambdaC + num20 / Math.Pow(LambdaC, 2.0) + num21 / Math.Pow(LambdaC, 3.0) + num22 / Math.Pow(LambdaC, 4.0) + num23 / Math.Pow(LambdaC, 5.0) : 0.2;
    return true;
  }
}
