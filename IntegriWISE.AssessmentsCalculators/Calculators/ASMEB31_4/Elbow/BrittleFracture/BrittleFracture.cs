// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.Elbow.BrittleFracture.BrittleFracture
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.Elbow.BrittleFracture;

public class BrittleFracture : BaseCalculator, IBrittleFractureCalculator
{
  private MaterialASMEB31_4DTO _material;
  private MaterialASMEB31_4DTO _allowableStressForMaterial;

  public double DesignTemperature { private get; set; }

  public double DesignPressure { private get; set; }

  public double? YieldStrengthNew { private get; set; }

  public double NominalOutsideDiameter { private get; set; }

  public double MechanicalAllowances { private get; set; }

  public double WeldJointEfficiency { private get; set; }

  public char ToughnessCurve_dependOnMATERIAL { private get; set; }

  public double TG { private get; set; }

  public bool ImpactTestResultAvailable { private get; set; }

  public double? MaxImpactTestTemperature { private get; set; }

  public double? CET { private get; set; }

  public bool Level1Passed { get; private set; }

  public double Level1MAT { get; private set; }

  public string Level1Message { get; private set; }

  public string Level1FormattedMessage { get; private set; }

  public double MinimumThickness { get; private set; }

  public double AllowableStrength { get; private set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double MAT_OptionA { get; private set; }

  public double? MAT_OptionB { get; private set; }

  public bool? CETGreaterThanMAT { get; private set; }

  public BrittleFracture(
    MaterialASMEB31_4DTO material,
    MaterialASMEB31_4DTO allowableStressForMaterial)
  {
    this._material = material;
    this._allowableStressForMaterial = allowableStressForMaterial;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_4.MinimumThickness.MinimumThickness minimumThickness = new IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_4.MinimumThickness.MinimumThickness(this._material, this._allowableStressForMaterial);
    minimumThickness.YieldStrengthNew = this.YieldStrengthNew;
    minimumThickness.DesignTemperature = this.DesignTemperature;
    minimumThickness.DesignPressure = this.DesignPressure;
    minimumThickness.NominalOutsideDiameter = this.NominalOutsideDiameter;
    minimumThickness.MechanicalAllowances = this.MechanicalAllowances;
    minimumThickness.WeldJointEfficiency = this.WeldJointEfficiency;
    if (minimumThickness.CalculateAssessment() == CalculatorResult.Fail)
    {
      this.ErrorMessage = minimumThickness.ErrorMessage;
      return CalculatorResult.Fail;
    }
    this.Warnings.AddRange((IEnumerable<string>) minimumThickness.Warnings);
    this.AllowableStrength = minimumThickness.AllowableStrength;
    this.MinimumThickness = minimumThickness.TMin;
    if (this.AllowableStrength > 172.5)
    {
      this.ErrorMessage = "Level 1 and Level 2 assessments fail due to the design allowable stress is greater than 172.5 Mpa (25 ksi)";
      return CalculatorResult.Fail;
    }
    double x = this.TG * 1.0 / 25.4;
    bool flag = this.TG < 2.5;
    double num1 = x <= 0.394 ? 18.0 : (284.85 * x - 76.911 - 27.56 * Math.Pow(x, 2.0)) / (1.0 + 1.7971 * x - 0.17887 * Math.Pow(x, 2.0));
    double num2 = x <= 0.394 ? -20.0 : 171.56 * Math.Pow(x, 0.5) - 135.79 + 103.63 * x - 172.0 * Math.Pow(x, 1.5) + 73.737 * Math.Pow(x, 2.0) - 10.535 * Math.Pow(x, 2.5);
    double num3 = x <= 0.394 ? -55.0 : 101.29 - 255.5 / x + 287.86 / Math.Pow(x, 2.0) - 196.42 / Math.Pow(x, 3.0) + 69.457 / Math.Pow(x, 4.0) - 9.8082 / Math.Pow(x, 5.0);
    double num4 = x <= 0.5 ? -55.0 : 94.065 * x - 92.965 - 39.812 * Math.Pow(x, 2.0) + 9.6838 * Math.Pow(x, 3.0) - 1.1698 * Math.Pow(x, 4.0) + 0.054687 * Math.Pow(x, 5.0);
    double quantity = 0.0;
    switch (this.ToughnessCurve_dependOnMATERIAL)
    {
      case 'A':
        quantity = num1;
        break;
      case 'B':
        quantity = num2;
        break;
      case 'C':
        quantity = num3;
        break;
      case 'D':
        quantity = num4;
        break;
    }
    this.MAT_OptionA = flag ? -48.0 : this.Convert(quantity, "DEG_F", "DEG_C");
    if (this.ImpactTestResultAvailable)
    {
      if (this.MaxImpactTestTemperature.HasValue)
      {
        this.MAT_OptionB = new double?(this.MaxImpactTestTemperature.Value);
        double? matOptionB = this.MAT_OptionB;
        double matOptionA = this.MAT_OptionA;
        this.Level1MAT = (matOptionB.GetValueOrDefault() >= matOptionA ? 0 : (matOptionB.HasValue ? 1 : 0)) == 0 ? this.MAT_OptionA : this.MAT_OptionB.Value;
      }
    }
    else
      this.Level1MAT = this.MAT_OptionA;
    if (this.CET.HasValue)
    {
      double? cet = this.CET;
      double level1Mat = this.Level1MAT;
      this.CETGreaterThanMAT = new bool?(cet.GetValueOrDefault() >= level1Mat && cet.HasValue);
      if (this.CETGreaterThanMAT.Value)
      {
        this.Level1Passed = true;
        this.Level1Message = "Level 1 Assessment ACCEPTABLE";
      }
      else
      {
        this.Level1Passed = false;
        this.Level1Message = "Level 1 Assessment UNACCEPTABLE";
      }
    }
    else
    {
      this.Level1Passed = true;
      this.Level1FormattedMessage = "Level 1 Assessment is ACCEPTABLE if CET is greater than {0}{1} otherwise Level 1 assessment fails.";
    }
    return CalculatorResult.Completed;
  }

  public double Convert(double quantity, string from, string to)
  {
    return from == to ? quantity : this.Convert(quantity, $"{from.Trim()} -> {to.Trim()}");
  }

  public double Convert(double quantity, string conversion)
  {
    switch (conversion)
    {
      case "DEG_C -> DEG_F":
        return quantity * 9.0 / 5.0 + 32.0;
      case "MPA -> PSI":
        return quantity * 145.0377;
      case "DEG_F -> DEG_C":
        return (quantity - 32.0) * (5.0 / 9.0);
      case "K -> DEG_C":
        return quantity - 273.15;
      case "DEG_C -> K":
        return quantity + 273.15;
      default:
        throw new NotImplementedException($"Unit conversion not implemented : {conversion}");
    }
  }
}
