// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.Elbow.BrittleFracture.IBrittleFractureCalculator
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_4.Elbow.BrittleFracture;

public interface IBrittleFractureCalculator
{
  CalculatorResult CalculateAssessment();

  double? CET { set; }

  bool? CETGreaterThanMAT { get; }

  double DesignPressure { set; }

  double DesignTemperature { set; }

  string ErrorMessage { get; }

  bool ImpactTestResultAvailable { set; }

  string Level1FormattedMessage { get; }

  double Level1MAT { get; }

  string Level1Message { get; }

  bool Level1Passed { get; }

  double MAT_OptionA { get; }

  double? MAT_OptionB { get; }

  double? MaxImpactTestTemperature { set; }

  double MechanicalAllowances { set; }

  double NominalOutsideDiameter { set; }

  char ToughnessCurve_dependOnMATERIAL { set; }

  double WeldJointEfficiency { set; }

  double TG { set; }

  List<string> Warnings { get; }

  double? YieldStrengthNew { set; }
}
