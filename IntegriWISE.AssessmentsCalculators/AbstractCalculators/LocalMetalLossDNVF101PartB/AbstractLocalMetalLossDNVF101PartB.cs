// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.AbstractCalculators.LocalMetalLossDNVF101PartB.AbstractLocalMetalLossDNVF101PartB
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.AbstractCalculators.LocalMetalLossDNVF101PartB;

public abstract class AbstractLocalMetalLossDNVF101PartB : BaseCalculator
{
  public const double MODELLING_FACTOR = 0.9;

  public bool IntPressureAndCombinedCompLoading { get; set; }

  public bool AdjacentDefectPresent { get; set; }

  public double DesignPressure { get; set; }

  public double NominalThickness { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double DesignFactor { get; set; }

  public double DepthOfCorrodedArea { get; set; }

  public double LongitudinalExtentOfCorrodedArea { get; set; }

  public double CircumferentialExtentOfCorrodedArea { get; set; }

  public double LongitudinalSpacingBetweenDefects { get; set; }

  public double CircAngularSpacingBetweenAdjacentDefects { get; set; }

  public double ExternalLongitudinalForce { get; set; }

  public double ExternalBendingMoment { get; set; }

  public double MinYieldStrength { get; set; }

  public double MinTensileStrength { get; set; }

  public bool CircAngularSpacingBetweenAdjacentDefectsCriteria { get; private set; }

  public bool AxialSpacingBetweenAdjacentDefectsCriteria { get; private set; }

  public bool DefectsInteractionCriteria { get; private set; }

  public bool DefectDepthCriteria { get; private set; }

  public double ModellingFactor => 0.9;

  public double LengthCorrectionFactor { get; private set; }

  public double TotalUsageFactor { get; private set; }

  public double LongitudinalStressAxialForceWallThickness { get; private set; }

  public double LongitudinalStressBendingForceWallThickness { get; private set; }

  public double CombinedLongitudinalStress { get; private set; }

  public double LowerLimitExternalLoads { get; private set; }

  public bool ExternalLoadWithinLimit { get; private set; }

  public double FactorForCompressiveLongitudinalStresses { get; private set; }

  public double FailurePressureUnderInternalPressure { get; private set; }

  public double FailurePressureIntPressureCompLongitudinalStress { get; private set; }

  public double FailurePressureOfCorrodedPipe { get; private set; }

  public double SafeWorkingPressure { get; private set; }

  public List<string> Warnings { get; set; }

  public string ErrorMessage { get; private set; }

  public string Level1Conclusion { get; private set; }

  public bool Level1Passed { get; private set; }

  public CalculatorResult CalculateAssessment()
  {
    if (this.AdjacentDefectPresent)
    {
      this.CircAngularSpacingBetweenAdjacentDefectsCriteria = this.CircAngularSpacingBetweenAdjacentDefects > 360.0 * Math.Sqrt(this.NominalThickness / this.NominalOutsideDiameter);
      this.AxialSpacingBetweenAdjacentDefectsCriteria = this.LongitudinalSpacingBetweenDefects > 2.0 * Math.Sqrt(this.NominalThickness * this.NominalOutsideDiameter);
      this.DefectsInteractionCriteria = this.CircAngularSpacingBetweenAdjacentDefectsCriteria || this.AxialSpacingBetweenAdjacentDefectsCriteria;
      if (!this.DefectsInteractionCriteria)
      {
        this.ErrorMessage = "The defects are interacting hence the assessment cannot be continued.";
        return CalculatorResult.Fail;
      }
    }
    this.DefectDepthCriteria = this.DepthOfCorrodedArea <= 0.85 * this.NominalThickness;
    if (!this.DefectDepthCriteria)
    {
      this.ErrorMessage = "The defect depth criteria is not satisfied hence assessment cannot be continued.";
      return CalculatorResult.Fail;
    }
    this.LengthCorrectionFactor = Math.Sqrt(1.0 + 0.31 * Math.Pow(this.LongitudinalExtentOfCorrodedArea / Math.Sqrt(this.NominalOutsideDiameter * this.NominalThickness), 2.0));
    double num1 = (1.0 - this.DepthOfCorrodedArea / this.NominalThickness) / (1.0 - this.DepthOfCorrodedArea / this.NominalThickness * (1.0 / this.LengthCorrectionFactor));
    double num2 = 2.0 * this.NominalThickness * this.MinTensileStrength / (this.NominalOutsideDiameter - this.NominalThickness);
    this.FailurePressureUnderInternalPressure = num2 * num1;
    this.TotalUsageFactor = 0.9 * this.DesignFactor;
    if (this.IntPressureAndCombinedCompLoading)
    {
      this.LongitudinalStressAxialForceWallThickness = this.ExternalLongitudinalForce / (Math.PI * (this.NominalOutsideDiameter - this.NominalThickness) * this.NominalThickness);
      this.LongitudinalStressBendingForceWallThickness = 4.0 * this.ExternalBendingMoment * 1000.0 / (Math.PI * Math.Pow(this.NominalOutsideDiameter - this.NominalThickness, 2.0) * this.NominalThickness);
      this.CombinedLongitudinalStress = this.LongitudinalStressAxialForceWallThickness + this.LongitudinalStressBendingForceWallThickness;
      this.LowerLimitExternalLoads = -0.5 * this.MinTensileStrength * num1;
      this.ExternalLoadWithinLimit = this.CombinedLongitudinalStress > this.LowerLimitExternalLoads;
      double num3 = 1.0 - this.DepthOfCorrodedArea / this.NominalThickness * (this.CircumferentialExtentOfCorrodedArea / (Math.PI * this.NominalOutsideDiameter));
      this.FactorForCompressiveLongitudinalStresses = (1.0 + this.CombinedLongitudinalStress / this.MinTensileStrength * (1.0 / num3)) / (1.0 - 1.0 / (2.0 * num3) * num1);
      this.FailurePressureIntPressureCompLongitudinalStress = num2 * num1 * this.FactorForCompressiveLongitudinalStresses;
      this.FailurePressureOfCorrodedPipe = Math.Min(this.FailurePressureUnderInternalPressure, this.FailurePressureIntPressureCompLongitudinalStress);
    }
    else
      this.FailurePressureOfCorrodedPipe = this.FailurePressureUnderInternalPressure;
    this.SafeWorkingPressure = this.TotalUsageFactor * this.FailurePressureOfCorrodedPipe;
    this.Level1Passed = this.DefectDepthCriteria && this.SafeWorkingPressure > this.DesignPressure;
    return CalculatorResult.Completed;
  }
}
