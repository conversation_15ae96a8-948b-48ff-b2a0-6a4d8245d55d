// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.AbstractCalculators.Dent.AbstractDent
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.AbstractCalculators.Dent;

public abstract class AbstractDent : BaseCalculator
{
  public bool Level2 { get; set; }

  public double DesignPressure { get; set; }

  public double NominalDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double WeldJointEfficiency { get; set; }

  public double MinYieldStrength { get; set; }

  public double MinTensileStrength { get; set; }

  public double OperatingPressure { get; set; }

  public double DentDepthInPressurised { get; set; }

  public double DentDepthInUnpressurised { get; set; }

  public double RadiusBaseDent { get; set; }

  public double SpacingToWeldJoint { get; set; }

  public double SpacingToDiscontinuity { get; set; }

  public double FCAi { get; set; }

  public double FCAe { get; set; }

  public double LOSSi { get; set; }

  public double LOSSe { get; set; }

  public double MAWP { get; set; }

  public bool CyclicLoading { get; set; }

  public double MaxPressureCyclicLoading { get; set; }

  public double MinPressureCyclicLoading { get; set; }

  public int TotalDesignPressureCycles { get; set; }

  public bool DiameterCriteria { get; private set; }

  public bool SMYSCriteria { get; private set; }

  public bool UTSCriteria { get; private set; }

  public bool ThicknessCriteria { get; private set; }

  public bool SpacingToDiscontinuityCriteria { get; private set; }

  public bool SpacingToWeldJointCriteria { get; private set; }

  public bool MAWPCriteria { get; private set; }

  public bool DentDepthCriteria { get; private set; }

  public bool CyclicLoadingCriteria { get; private set; }

  public double CorrodedWallThickness { get; private set; }

  public double DentDepth { get; private set; }

  public double MinCircStressPressureCycle { get; set; }

  public double MaxCircStressPressureCycle { get; set; }

  public double AmplitudeCyclicCircMembraneStress { get; private set; }

  public double AmplitudeAdjustedCyclicCircMembraneStress { get; private set; }

  public double StressConcentrationDent { get; private set; }

  public int AllowableNumberPressureCycles { get; private set; }

  public List<string> Warnings { get; set; }

  public string ErrorMessage { get; private set; }

  public string Level1Conclusion { get; private set; }

  public bool Level1Passed { get; private set; }

  public bool Level2Passed { get; private set; }

  public CalculatorResult CalculateAssessment()
  {
    this.DiameterCriteria = this.NominalDiameter >= 168.0 && this.NominalDiameter <= 1050.0;
    this.SMYSCriteria = this.MinYieldStrength <= 482.0;
    this.CorrodedWallThickness = Math.Max(this.NominalThickness - this.FCAi - this.FCAe - this.LOSSi - this.LOSSe, 0.0);
    this.ThicknessCriteria = this.CorrodedWallThickness >= 5.0 && this.CorrodedWallThickness <= 19.0;
    this.SpacingToDiscontinuityCriteria = this.SpacingToDiscontinuity >= 1.8 * Math.Sqrt(this.NominalDiameter * this.CorrodedWallThickness);
    this.SpacingToWeldJointCriteria = this.SpacingToWeldJoint >= Math.Max(2.0 * this.CorrodedWallThickness, 25.0);
    if (!this.DiameterCriteria || !this.ThicknessCriteria || !this.SpacingToDiscontinuityCriteria || !this.SpacingToWeldJointCriteria)
    {
      this.Level1Passed = false;
      return CalculatorResult.Completed;
    }
    this.MAWPCriteria = this.MAWP >= this.DesignPressure;
    this.DentDepth = this.DentDepthInPressurised <= 0.0 ? (0.7 * this.MAWP > this.OperatingPressure ? this.DentDepthInUnpressurised : 0.7 * this.DentDepthInUnpressurised) : this.DentDepthInPressurised;
    this.DentDepthCriteria = 0.07 * this.NominalDiameter >= this.DentDepth;
    this.Level1Passed = !this.CyclicLoading && this.MAWPCriteria && this.DentDepthCriteria && this.SMYSCriteria;
    if (this.Level2)
    {
      if (this.Level1Passed)
        this.Level2Passed = true;
      else if (!this.CyclicLoading)
      {
        this.Level2Passed = true;
      }
      else
      {
        this.UTSCriteria = this.MinTensileStrength <= 711.0;
        if (!this.UTSCriteria)
        {
          this.Level2Passed = false;
          return CalculatorResult.CriteriaNotPassed;
        }
        this.AmplitudeCyclicCircMembraneStress = (this.MaxCircStressPressureCycle - this.MinCircStressPressureCycle) / 2.0;
        this.AmplitudeAdjustedCyclicCircMembraneStress = this.AmplitudeCyclicCircMembraneStress / (1.0 - Math.Pow((this.MaxCircStressPressureCycle - this.AmplitudeCyclicCircMembraneStress) / this.MinTensileStrength, 2.0));
        this.StressConcentrationDent = 1.0 + (this.RadiusBaseDent / this.CorrodedWallThickness >= 5.0 ? 2.0 : 1.0) * Math.Sqrt(this.CorrodedWallThickness / this.NominalDiameter * Math.Pow(this.DentDepthInUnpressurised, 1.5));
        this.AllowableNumberPressureCycles = Convert.ToInt32(562.2 * Math.Pow(this.MinTensileStrength / (2.0 * this.AmplitudeAdjustedCyclicCircMembraneStress * this.StressConcentrationDent), 5.26));
        this.CyclicLoadingCriteria = this.AllowableNumberPressureCycles >= this.TotalDesignPressureCycles;
        this.Level2Passed = this.CyclicLoadingCriteria;
      }
    }
    return CalculatorResult.Completed;
  }
}
