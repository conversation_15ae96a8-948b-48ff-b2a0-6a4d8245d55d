// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.AbstractCalculators.DentGouge.AbstractDentGouge
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.AbstractCalculators.DentGouge;

public class AbstractDentGouge : BaseCalculator
{
  public bool Level2 { get; set; }

  public double DesignPressure { get; set; }

  public double NominalDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double WeldJointEfficiency { get; set; }

  public double MinYieldStrength { get; set; }

  public double MinTensileStrength { get; set; }

  public double OperatingPressure { get; set; }

  public double OperatingTemperature { get; set; }

  public double GougeDepth { get; set; }

  public double DentDepthInPressurised { get; set; }

  public double DentDepthInUnpressurised { get; set; }

  public double RadiusBaseDent { get; set; }

  public double SpacingToWeldJoint { get; set; }

  public double SpacingToDiscontinuity { get; set; }

  public double FCAi { get; set; }

  public double FCAe { get; set; }

  public double LOSSi { get; set; }

  public double LOSSe { get; set; }

  public bool CyclicLoading { get; set; }

  public double MaxPressureCyclicLoading { get; set; }

  public double MinPressureCyclicLoading { get; set; }

  public int TotalDesignPressureCycles { get; set; }

  public double ModulusElasticity { get; set; }

  public double CharpyVNotch { get; set; }

  public double MAWP { get; set; }

  public bool DiameterCriteria { get; private set; }

  public bool SMYSCriteria { get; private set; }

  public bool UTSCriteria { get; private set; }

  public bool MinReqThicknessCriteria { get; private set; }

  public bool ThicknessCriteria { get; private set; }

  public bool SpacingToDiscontinuityCriteria { get; private set; }

  public bool SpacingToWeldJointCriteria { get; private set; }

  public bool MAWPCriteria { get; private set; }

  public bool DentDepthCriteria { get; private set; }

  public bool CyclicLoadingCriteria { get; private set; }

  public bool CurveCriteria { get; private set; }

  public bool GougeDepthCriteria { get; private set; }

  public bool RSFCriteria { get; private set; }

  public double CorrodedWallThickness { get; private set; }

  public double MinimumThickness { get; private set; }

  public double MAWPr { get; private set; }

  public double DentDepth { get; private set; }

  public double Ratio_a { get; private set; }

  public double Ratio_b { get; private set; }

  public double Ratio_A { get; private set; }

  public double MinCircStressPressureCycle { get; set; }

  public double MaxCircStressPressureCycle { get; set; }

  public double AmplitudeCyclicCircMembraneStress { get; private set; }

  public double AmplitudeAdjustedCyclicCircMembraneStress { get; private set; }

  public double StressConcentrationDent { get; private set; }

  public double StressConcentrationGouge { get; private set; }

  public int AllowableNumberPressureCycles { get; private set; }

  public double SigmaCM { get; set; }

  public double Fa1 { get; private set; }

  public double Fa2 { get; private set; }

  public double Fa3 { get; private set; }

  public double RSF { get; private set; }

  public double Sigma_gfs { get; private set; }

  public double Y1 { get; private set; }

  public double Y2 { get; private set; }

  public double C1 { get; private set; }

  public double C2 { get; private set; }

  public double C3 { get; private set; }

  public List<string> Warnings { get; set; }

  public string ErrorMessage { get; private set; }

  public string Level1Conclusion { get; private set; }

  public bool Level1Passed { get; private set; }

  public bool Level2Passed { get; private set; }

  public CalculatorResult CalculateAssessment()
  {
    this.MinimumThickness = this.NominalThickness - this.LOSSi - this.LOSSe - this.GougeDepth;
    this.DiameterCriteria = this.NominalDiameter >= 168.0 && this.NominalDiameter <= 1050.0;
    this.SMYSCriteria = this.MinYieldStrength <= 482.0;
    this.CorrodedWallThickness = Math.Max(this.NominalThickness - this.FCAi - this.FCAe - this.LOSSi - this.LOSSe, 0.0);
    this.MinReqThicknessCriteria = this.MinimumThickness - this.FCAi - this.FCAe > 2.5;
    this.ThicknessCriteria = this.CorrodedWallThickness >= 5.0 && this.CorrodedWallThickness <= 19.0;
    this.SpacingToDiscontinuityCriteria = this.SpacingToDiscontinuity >= 1.8 * Math.Sqrt(this.NominalDiameter * this.CorrodedWallThickness);
    this.SpacingToWeldJointCriteria = this.SpacingToWeldJoint >= Math.Max(2.0 * this.CorrodedWallThickness, 25.0);
    if (!this.DiameterCriteria || !this.ThicknessCriteria || !this.SpacingToDiscontinuityCriteria || !this.SpacingToWeldJointCriteria)
      return CalculatorResult.CriteriaNotPassed;
    this.MAWPCriteria = this.MAWP >= this.DesignPressure;
    this.DentDepth = this.DentDepthInPressurised <= 0.0 ? (0.7 * this.MAWP > this.DesignPressure ? this.DentDepthInUnpressurised : 0.7 * this.DentDepthInUnpressurised) : this.DentDepthInPressurised;
    this.DentDepthCriteria = 0.07 * this.NominalDiameter >= this.DentDepth;
    this.Ratio_a = this.DentDepth / this.NominalDiameter;
    this.Ratio_b = this.GougeDepth / this.CorrodedWallThickness;
    this.Ratio_A = this.SigmaCM / this.MinYieldStrength;
    this.Fa1 = this.Ratio_a < 0.0 || this.Ratio_a >= 0.005 ? (this.Ratio_a < 0.005 || this.Ratio_a >= 0.023 ? (this.Ratio_a < 0.023 || this.Ratio_a >= 0.04 ? 0.0 : -8.1588 * this.Ratio_a + 204.0 / 625.0) : 1434.0 / 625.0 * this.Ratio_a + 0.1915) : 0.4;
    this.Fa2 = this.Ratio_a < 0.0 || this.Ratio_a >= 0.005 ? (this.Ratio_a < 0.005 || this.Ratio_a >= 0.018 ? (this.Ratio_a < 0.018 || this.Ratio_a >= 0.032 ? 0.0 : -8.7071 * this.Ratio_a + 0.2786) : -2.1615 * this.Ratio_a + 0.1608) : 0.3;
    this.Fa3 = this.Ratio_a < 0.0 || this.Ratio_a >= 0.005 ? (this.Ratio_a < 0.005 || this.Ratio_a >= 0.015 ? (this.Ratio_a < 0.015 || this.Ratio_a >= 0.025 ? 0.0 : -8.0 * this.Ratio_a + 0.2) : -2.0 * this.Ratio_a + 0.11) : 0.2;
    this.CurveCriteria = false;
    if (this.Ratio_A > 0.0 && this.Ratio_A < 0.3)
      this.CurveCriteria = this.Ratio_a < 0.0 || this.Ratio_a >= 0.005 ? (this.Ratio_a < 0.005 || this.Ratio_a >= 0.023 ? this.Ratio_a >= 0.023 && this.Ratio_a < 0.04 && this.Ratio_b <= this.Fa1 : this.Ratio_b <= this.Fa1) : this.Ratio_b <= this.Fa1;
    else if (this.Ratio_A >= 0.3 && this.Ratio_A < 0.5)
      this.CurveCriteria = this.Ratio_a < 0.0 || this.Ratio_a >= 0.005 ? (this.Ratio_a < 0.005 || this.Ratio_a >= 0.018 ? this.Ratio_a >= 0.018 && this.Ratio_a < 0.032 && this.Ratio_b <= this.Fa2 : this.Ratio_b <= this.Fa2) : this.Ratio_b <= this.Fa2;
    else if (this.Ratio_A >= 0.5 && this.Ratio_A < 0.72)
      this.CurveCriteria = this.Ratio_a < 0.0 || this.Ratio_a >= 0.005 ? (this.Ratio_a < 0.005 || this.Ratio_a >= 0.015 ? this.Ratio_a >= 0.005 && this.Ratio_a < 0.015 && this.Ratio_b <= this.Fa3 : this.Ratio_b <= this.Fa3) : this.Ratio_b <= this.Fa3;
    this.Level1Passed = !this.CyclicLoading && this.MAWPCriteria && this.DentDepthCriteria && this.SMYSCriteria && this.CurveCriteria;
    if (this.Level2)
    {
      if (this.Level1Passed)
      {
        this.Level2Passed = true;
      }
      else
      {
        this.UTSCriteria = this.MinTensileStrength <= 711.0;
        this.GougeDepthCriteria = this.GougeDepth <= 0.66 * this.CorrodedWallThickness;
        if (!this.UTSCriteria || !this.GougeDepthCriteria)
        {
          this.Level2Passed = false;
          return CalculatorResult.CriteriaNotPassed;
        }
        this.Sigma_gfs = 1.15 * this.MinYieldStrength * (1.0 - this.GougeDepth / this.CorrodedWallThickness);
        this.Y1 = 1.12 - 0.023 * (this.GougeDepth / this.CorrodedWallThickness) + 10.6 * Math.Pow(this.GougeDepth / this.CorrodedWallThickness, 2.0) - 21.7 * Math.Pow(this.GougeDepth / this.CorrodedWallThickness, 3.0) + 30.4 * Math.Pow(this.GougeDepth / this.CorrodedWallThickness, 4.0);
        this.Y2 = 1.12 - 1.39 * (this.GougeDepth / this.CorrodedWallThickness) + 7.32 * Math.Pow(this.GougeDepth / this.CorrodedWallThickness, 2.0) - 13.1 * Math.Pow(this.GougeDepth / this.CorrodedWallThickness, 3.0) + 14.0 * Math.Pow(this.GougeDepth / this.CorrodedWallThickness, 4.0);
        this.C1 = 3.0 * Math.PI / 2.0 * this.ModulusElasticity * 113.0 / (Math.Pow(this.Sigma_gfs, 2.0) * 53.33 * this.GougeDepth);
        this.C2 = this.Y1 * (1.0 - 1.8 * this.DentDepthInUnpressurised / this.NominalDiameter) + this.Y2 * (10.2 * this.DentDepthInUnpressurised / (2.0 * this.CorrodedWallThickness));
        this.C3 = Math.Exp((Math.Log(0.738 * this.CharpyVNotch) - 1.9) / 0.57);
        this.RSF = 2.0 / Math.PI * Math.Acos(Math.Exp(-this.C1 * this.C3 / Math.Pow(this.C2, 2.0))) * (1.0 - this.GougeDepth / this.CorrodedWallThickness);
        this.RSFCriteria = this.RSF >= 0.9;
        if (!this.RSFCriteria)
          this.MAWPr = this.MAWP * this.RSF / 0.9;
        if (!this.CyclicLoading)
        {
          this.Level2Passed = this.MAWPCriteria && this.SMYSCriteria && this.RSFCriteria;
        }
        else
        {
          this.AmplitudeCyclicCircMembraneStress = (this.MaxCircStressPressureCycle - this.MinCircStressPressureCycle) / 2.0;
          this.AmplitudeAdjustedCyclicCircMembraneStress = this.AmplitudeCyclicCircMembraneStress / (1.0 - Math.Pow((this.MaxCircStressPressureCycle - this.AmplitudeCyclicCircMembraneStress) / this.MinTensileStrength, 2.0));
          this.StressConcentrationDent = 1.0 + (this.RadiusBaseDent / this.CorrodedWallThickness >= 5.0 ? 2.0 : 1.0) * Math.Sqrt(this.CorrodedWallThickness / this.NominalDiameter * Math.Pow(this.DentDepthInUnpressurised, 1.5));
          this.StressConcentrationGouge = 1.0 + 9.0 * (this.GougeDepth / this.CorrodedWallThickness);
          this.AllowableNumberPressureCycles = Convert.ToInt32(562.2 * Math.Pow(this.MinTensileStrength / (2.0 * this.AmplitudeAdjustedCyclicCircMembraneStress * this.StressConcentrationDent * this.StressConcentrationGouge), 5.26));
          this.CyclicLoadingCriteria = this.AllowableNumberPressureCycles >= this.TotalDesignPressureCycles;
          this.Level2Passed = this.CyclicLoadingCriteria;
        }
      }
    }
    return CalculatorResult.Completed;
  }
}
