// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_3.GeneralMetalLossThicknessProfile.GeneralMetalLossThicknessProfile
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_3.GeneralMetalLossThicknessProfile;

public class GeneralMetalLossThicknessProfile : BaseCalculator
{
  private List<MaterialASMEB31_3StressValueDTO> _allowableStrengths;
  private List<MaterialASMEB31_3_Coefficient_YDTO> _allowableStrenghtsByType;
  private TemperatureRangeDTO _minDesignTemperature;
  private MaterialASMEB31_3DTO _material;
  private ThicknessReadings _thicknessReadings;

  public bool ToLevel2 { get; set; }

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double SupplementalThickness { get; set; }

  public double LongitudinalWeldJointEfficiency { get; set; }

  public double CircumferentialWeldJointEfficiency { get; set; }

  public double MechanicalAllowances { get; set; }

  public double LongitudinalThicknessReadingSpacing { get; set; }

  public double CircumferentialThicknessReadingSpacing { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double S { get; set; }

  public double C { get; set; }

  public double AllowableStrength { get; set; }

  public double Tcmin { get; set; }

  public double Tlmin { get; set; }

  public double Tmm { get; set; }

  public double Trd { get; set; }

  public double Rt { get; set; }

  public double Q { get; set; }

  public double L { get; set; }

  public double TSam { get; set; }

  public double TCam { get; set; }

  public double MAWPrcTSamMinusFCA { get; set; }

  public double MAWPrlTCamMinusFCA { get; set; }

  public double MAWPrcTsamMinusFCAOverRSFa { get; set; }

  public double MAWPrlTcamMinusTslMinusFCAOverRSFa { get; set; }

  public bool AverageMeasuredThicknessL1 { get; set; }

  public bool MawpL1 { get; set; }

  public bool MinimumMeasuredThicknessL1 { get; set; }

  public bool Level1Passed { get; set; }

  public string Level1 { get; set; }

  public bool AverageMeasuredThicknessL2 { get; set; }

  public bool MawpL2 { get; set; }

  public bool MinimumMeasuredThicknessL2 { get; set; }

  public bool Level2Passed { get; set; }

  public string Level2 { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private bool LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa { get; set; }

  private string MinimumAllowableTemperatureC { get; set; }

  private double MinimumAllowableTemperature { get; set; }

  private double TLower { get; set; }

  private double TUpper { get; set; }

  private double SLower { get; set; }

  private double SUpper { get; set; }

  private double Tlower2 { get; set; }

  private double Tupper2 { get; set; }

  private double Ylower { get; set; }

  private double Yupper { get; set; }

  private double YB31ASMEB31 { get; set; }

  private double OutsideDiameterDo { get; set; }

  private double tc { get; set; }

  private double Radius { get; set; }

  private double tmin1 { get; set; }

  private double checkYB31 { get; set; }

  private double lcTcmin { get; set; }

  private double tlmin { get; set; }

  private double TMin { get; set; }

  private double RSFa { get; set; }

  private bool RtGreater0 { get; set; }

  private double tamcMinusFCA { get; set; }

  private double tamsMinusFCA { get; set; }

  private double tlim { get; set; }

  private double tmmMinusFCA { get; set; }

  private double Max0_5TminTlim { get; set; }

  private double MAWPrTamMinusFCA { get; set; }

  private bool LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin { get; set; }

  private bool LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin { get; set; }

  private double TsamMinusFCAOverRSFa { get; set; }

  private double TcamMinusTslMinusFCAOverRSFa { get; set; }

  private double MAWPrTamMinusFCAOverRSFa { get; set; }

  private bool LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa { get; set; }

  public GeneralMetalLossThicknessProfile(
    MaterialASMEB31_3DTO material,
    TemperatureRangeDTO minDesignTemperature,
    List<MaterialASMEB31_3StressValueDTO> allowableStrengths,
    List<MaterialASMEB31_3_Coefficient_YDTO> allowableStrenghtsByType,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this._minDesignTemperature = minDesignTemperature;
    this._allowableStrengths = allowableStrengths;
    this._allowableStrenghtsByType = allowableStrenghtsByType;
    this.Warnings = new List<string>();
    this._thicknessReadings = thicknessReadingsCalculator;
  }

  public virtual void CalculateTrd()
  {
    this.Trd = this.NominalThickness - this.ExternalUniformMetalLoss - this.InternalUniformMetalLoss;
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.StressTemperatureLimit.HasValue)
    {
      double designTemperature = this.DesignTemperature;
      double? temperatureLimit = this._material.StressTemperatureLimit;
      if ((designTemperature <= temperatureLimit.GetValueOrDefault() ? 0 : (temperatureLimit.HasValue ? 1 : 0)) != 0)
        this.Warnings.Add("Strength value exceeds two-thirds of the expected yield strength at this temperature.See paragraph 302.3.2(d)(3) and (e) ASME B31.3");
    }
    if (this._material.UserDefined)
    {
      this.AllowableStrength = Math.Min(this._material.YieldStrengthNew.Value / 1.5, this._material.TensileStrengthNew.Value / 3.0);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
      MaterialTypeStrengthLookupResult strengthByTypeResult = this.getStrengthByTypeResult(this.DesignTemperature);
      if (strengthByTypeResult.Status == "Above Range" || strengthByTypeResult.Status == "Below Range")
        return CalculatorResult.Fail;
      if (strengthByTypeResult.Status == "In Range")
      {
        this.Tlower2 = strengthByTypeResult.Tlower;
        this.Tupper2 = strengthByTypeResult.Tupper;
        this.Yupper = strengthByTypeResult.Yupper;
        this.Ylower = strengthByTypeResult.Ylower;
      }
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      this.MinimumAllowableTemperatureC = this._material.MinimumTemperatureC;
      if (!this.IsNumeric(this.MinimumAllowableTemperatureC))
      {
        double num1 = this._minDesignTemperature.TLower.Value;
        double num2 = this._minDesignTemperature.TUpper.Value;
        double num3 = this._minDesignTemperature.SUpper.Value;
        double num4 = this._minDesignTemperature.SLower.Value;
        this.MinimumAllowableTemperature = (this.NominalThickness - num1) / (num2 - num1) * (num3 - num4) + num4;
      }
      else
        this.MinimumAllowableTemperature = double.Parse(this.MinimumAllowableTemperatureC);
      if (this.DesignTemperature < this.MinimumAllowableTemperature)
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress value at the lowest listed temperature of this material (ASME B31.3) to calculate tmin.");
      MaterialStrengthLookupResult strengthResult = this.getStrengthResult(this.DesignTemperature, this.MinimumAllowableTemperature);
      if (strengthResult.Status == "Above Range")
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material.";
        return CalculatorResult.Fail;
      }
      this.TUpper = strengthResult.TUpper;
      this.TLower = strengthResult.TLower;
      this.SLower = strengthResult.SLower;
      this.SUpper = strengthResult.SUpper;
      this.AllowableStrength = this.DesignTemperature >= this.TLower ? (this.DesignTemperature - this.TLower) / (this.TUpper - this.TLower) * (this.SUpper - this.SLower) + this.SLower : this.SLower;
      MaterialTypeStrengthLookupResult strengthByTypeResult = this.getStrengthByTypeResult(this.DesignTemperature >= this.MinimumAllowableTemperature ? this.DesignTemperature : this.MinimumAllowableTemperature);
      if (strengthByTypeResult.Status == "Above Range" || strengthByTypeResult.Status == "Below Range")
        return CalculatorResult.Fail;
      if (strengthByTypeResult.Status == "In Range")
      {
        this.Tlower2 = strengthByTypeResult.Tlower;
        this.Tupper2 = strengthByTypeResult.Tupper;
        this.Yupper = strengthByTypeResult.Yupper;
        this.Ylower = strengthByTypeResult.Ylower;
      }
    }
    this.YB31ASMEB31 = (this.DesignTemperature - this.Tlower2) / (this.Tupper2 - this.Tlower2) * (this.Yupper - this.Ylower) + this.Ylower;
    this.OutsideDiameterDo = this.NominalOutsideDiameter - 2.0 * (this.ExternalFutureCorrosionAllowance + this.ExternalUniformMetalLoss);
    this.tc = this.NominalThickness - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance - this.InternalFutureCorrosionAllowance - this.InternalUniformMetalLoss;
    this.Radius = this.NominalOutsideDiameter / 2.0 - this.NominalThickness + this.InternalUniformMetalLoss + this.InternalFutureCorrosionAllowance;
    this.Tcmin = this.DesignPressure * this.OutsideDiameterDo / (2.0 * (this.AllowableStrength * this.LongitudinalWeldJointEfficiency + this.DesignPressure * this.YB31ASMEB31)) + this.MechanicalAllowances;
    this.Tlmin = this.DesignPressure * this.OutsideDiameterDo / (4.0 * (this.AllowableStrength * this.CircumferentialWeldJointEfficiency + this.DesignPressure * this.YB31ASMEB31)) + this.MechanicalAllowances + this.SupplementalThickness;
    this.tmin1 = Math.Max(this.Tcmin, this.Tlmin);
    this.checkYB31 = this.tmin1 >= this.OutsideDiameterDo / 6.0 ? (this.NominalOutsideDiameter - 2.0 * this.NominalThickness + 2.0 * this.MechanicalAllowances) / (this.NominalOutsideDiameter + this.NominalOutsideDiameter - 2.0 * this.NominalThickness + 2.0 * this.MechanicalAllowances) : this.YB31ASMEB31;
    this.lcTcmin = this.DesignPressure * this.OutsideDiameterDo / (2.0 * (this.AllowableStrength * this.LongitudinalWeldJointEfficiency + this.DesignPressure * this.checkYB31)) + this.MechanicalAllowances;
    this.tlmin = this.DesignPressure * this.OutsideDiameterDo / (4.0 * (this.AllowableStrength * this.CircumferentialWeldJointEfficiency + this.DesignPressure * this.checkYB31)) + this.MechanicalAllowances + this.SupplementalThickness;
    this.TMin = Math.Max(this.lcTcmin, this.tlmin);
    this.Trd = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss;
    try
    {
      this.Tmm = this._thicknessReadings.GetMinimumValue();
      if (this.Tmm >= this.Trd)
      {
        this.ErrorMessage = "All thickness readings are greater than trd = tnom - LOSS. No metal loss has been found. This assessment is not needed";
        return CalculatorResult.Fail;
      }
    }
    catch
    {
      this.ErrorMessage = "Profile readings contain multiple Tmm's - Currently unable to proceed with the assessment.";
      return CalculatorResult.Fail;
    }
    this.RSFa = 0.9;
    this.Rt = (this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.tc;
    this.Rt = Math.Round(this.Rt, 4);
    this.RtGreater0 = this.Rt > 0.0;
    if (!this.RtGreater0)
    {
      this.ErrorMessage = "Remaining thickness ratio is less than or equal to zero. The assessment fails. ";
      return CalculatorResult.Fail;
    }
    this.Q = this.Rt >= this.RSFa ? 50.0 : 1.123 * Math.Pow(Math.Pow((1.0 - this.Rt) / (1.0 - this.Rt / this.RSFa), 2.0) - 1.0, 0.5);
    this.L = this.Q * Math.Pow(2.0 * this.Radius * this.tc, 0.5);
    this._thicknessReadings.ProcessThicknessReadings(this.L, this.CircumferentialThicknessReadingSpacing, this.LongitudinalThicknessReadingSpacing, this.Trd);
    this.S = this._thicknessReadings.S;
    this.C = this._thicknessReadings.C;
    this.TCam = this._thicknessReadings.Tcam;
    this.TSam = this._thicknessReadings.Tsam;
    this.tamcMinusFCA = this.TCam - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.tamsMinusFCA = this.TSam - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.tlim = Math.Max(0.2 * this.NominalThickness, 2.5);
    this.tmmMinusFCA = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.Max0_5TminTlim = Math.Max(0.5 * this.TMin, this.tlim);
    this.MAWPrcTSamMinusFCA = 2.0 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency * (this.tamsMinusFCA - this.MechanicalAllowances) / (this.OutsideDiameterDo - 2.0 * this.checkYB31 * (this.tamsMinusFCA - this.MechanicalAllowances));
    this.MAWPrlTCamMinusFCA = 4.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency * (this.tamcMinusFCA - this.SupplementalThickness - this.MechanicalAllowances) / (this.OutsideDiameterDo - 4.0 * this.checkYB31 * (this.tamcMinusFCA - this.SupplementalThickness - this.MechanicalAllowances));
    this.MAWPrTamMinusFCA = Math.Min(this.MAWPrcTSamMinusFCA, this.MAWPrlTCamMinusFCA);
    this.LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin = this.tamcMinusFCA >= this.tlmin;
    this.LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin = this.tamsMinusFCA >= this.lcTcmin;
    this.MawpL1 = this.MAWPrTamMinusFCA >= this.DesignPressure;
    this.MinimumMeasuredThicknessL1 = this.tmmMinusFCA >= this.Max0_5TminTlim;
    this.AverageMeasuredThicknessL1 = this.LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin && this.LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin;
    if (this.LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin && this.LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin && this.MawpL1 && this.MinimumMeasuredThicknessL1)
    {
      this.Level1 = "The Level 1 Assessment is ACCEPTABLE";
      this.Level1Passed = true;
      return CalculatorResult.Completed;
    }
    this.Level1 = "The Level 1 Assessment is UNACCEPTABLE";
    this.Level1Passed = false;
    if (!this.ToLevel2)
      return CalculatorResult.Completed;
    this.MinimumMeasuredThicknessL2 = this.MinimumMeasuredThicknessL1;
    this.TsamMinusFCAOverRSFa = this.tamsMinusFCA / this.RSFa;
    this.TcamMinusTslMinusFCAOverRSFa = (this.tamcMinusFCA - this.SupplementalThickness) / this.RSFa;
    this.MAWPrcTsamMinusFCAOverRSFa = 2.0 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency * (this.TsamMinusFCAOverRSFa - this.MechanicalAllowances) / (this.OutsideDiameterDo - 2.0 * this.checkYB31 * (this.TsamMinusFCAOverRSFa - this.MechanicalAllowances));
    this.MAWPrlTcamMinusTslMinusFCAOverRSFa = 4.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency * (this.TcamMinusTslMinusFCAOverRSFa - this.SupplementalThickness - this.MechanicalAllowances) / (this.OutsideDiameterDo - 4.0 * this.checkYB31 * (this.TcamMinusTslMinusFCAOverRSFa - this.SupplementalThickness - this.MechanicalAllowances));
    this.MAWPrTamMinusFCAOverRSFa = Math.Min(this.MAWPrcTsamMinusFCAOverRSFa, this.MAWPrlTcamMinusTslMinusFCAOverRSFa);
    this.LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa = this.tamcMinusFCA >= this.tlmin * this.RSFa;
    this.LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa = this.tamsMinusFCA >= this.lcTcmin * this.RSFa;
    this.MawpL2 = this.MAWPrTamMinusFCAOverRSFa >= this.DesignPressure;
    this.AverageMeasuredThicknessL2 = this.LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa && this.LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa;
    if (this.LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa && this.LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa && this.MawpL2 && this.MinimumMeasuredThicknessL2)
    {
      this.Level2 = "The Level 2 Assessment is ACCEPTABLE";
      this.Level2Passed = true;
    }
    else
    {
      this.Level2 = "The Level 2 Assessment is UNACCEPTABLE";
      this.Level2Passed = false;
    }
    return CalculatorResult.Completed;
  }

  private MaterialTypeStrengthLookupResult getStrengthByTypeResult(double temperature)
  {
    double num1 = 0.0;
    string str = "In Range";
    MaterialASMEB31_3_Coefficient_YDTO obj = this._allowableStrenghtsByType.Where<MaterialASMEB31_3_Coefficient_YDTO>((Func<MaterialASMEB31_3_Coefficient_YDTO, bool>) (x =>
    {
      double num2 = temperature;
      double? tlower = x.Tlower;
      if ((num2 < tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
        return false;
      double num3 = temperature;
      double? tupper = x.Tupper;
      return num3 < tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEB31_3_Coefficient_YDTO, double?>((Func<MaterialASMEB31_3_Coefficient_YDTO, double?>) (x => x.Tlower)).FirstOrDefault<MaterialASMEB31_3_Coefficient_YDTO>() ?? this._allowableStrenghtsByType.Where<MaterialASMEB31_3_Coefficient_YDTO>((Func<MaterialASMEB31_3_Coefficient_YDTO, bool>) (x =>
    {
      double num4 = temperature;
      double? tupper = x.Tupper;
      return num4 == tupper.GetValueOrDefault() && tupper.HasValue;
    })).OrderBy<MaterialASMEB31_3_Coefficient_YDTO, double?>((Func<MaterialASMEB31_3_Coefficient_YDTO, double?>) (x => x.Tlower)).FirstOrDefault<MaterialASMEB31_3_Coefficient_YDTO>();
    if (obj == null)
    {
      num1 = this._allowableStrengths.Max<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TUpper)).Value;
      str = "Above Range";
      if (num1 < temperature)
        obj = new MaterialASMEB31_3_Coefficient_YDTO()
        {
          Tlower = new double?(0.0),
          Tupper = new double?(0.0),
          Tlower_SI = new double?(0.0),
          Tupper_SI = new double?(0.0),
          Ylower = new double?(0.0),
          Yupper = new double?(0.0)
        };
    }
    if (obj == null)
    {
      num1 = this._allowableStrengths.Min<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TLower)).Value;
      str = "Below Range";
      if (num1 > temperature)
        obj = new MaterialASMEB31_3_Coefficient_YDTO()
        {
          Tlower = new double?(0.0),
          Tupper = new double?(0.0),
          Tlower_SI = new double?(0.0),
          Tupper_SI = new double?(0.0),
          Ylower = new double?(0.0),
          Yupper = new double?(0.0)
        };
    }
    return new MaterialTypeStrengthLookupResult()
    {
      Temperature = num1,
      Status = str,
      Tlower = obj.Tlower.Value,
      Tupper = obj.Tupper.Value,
      Tlower_SI = obj.Tlower_SI.Value,
      Tupper_SI = obj.Tupper_SI.Value,
      Ylower = obj.Ylower.Value,
      Yupper = obj.Yupper.Value
    };
  }

  private MaterialStrengthLookupResult getStrengthResult(
    double designTemperature,
    double minimumAllowableTemperature)
  {
    double num1 = 0.0;
    string str = "In Range";
    MaterialASMEB31_3StressValueDTO b313StressValueDto;
    if (designTemperature < minimumAllowableTemperature)
    {
      b313StressValueDto = this._allowableStrengths.OrderBy<MaterialASMEB31_3StressValueDTO, double?>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEB31_3StressValueDTO>();
    }
    else
    {
      b313StressValueDto = this._allowableStrengths.Where<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, bool>) (x =>
      {
        double num2 = designTemperature;
        double? tlower = x.TLower;
        return num2 <= tlower.GetValueOrDefault() && tlower.HasValue;
      })).OrderBy<MaterialASMEB31_3StressValueDTO, double?>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TLower)).FirstOrDefault<MaterialASMEB31_3StressValueDTO>() ?? this._allowableStrengths.Where<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, bool>) (x =>
      {
        double num3 = designTemperature;
        double? tlower = x.TLower;
        if ((num3 <= tlower.GetValueOrDefault() ? 0 : (tlower.HasValue ? 1 : 0)) == 0)
          return false;
        double num4 = designTemperature;
        double? tupper = x.TUpper;
        return num4 <= tupper.GetValueOrDefault() && tupper.HasValue;
      })).FirstOrDefault<MaterialASMEB31_3StressValueDTO>();
      if (b313StressValueDto == null)
      {
        num1 = this._allowableStrengths.Max<MaterialASMEB31_3StressValueDTO>((Func<MaterialASMEB31_3StressValueDTO, double?>) (x => x.TUpper)).Value;
        str = "Above Range";
        b313StressValueDto = new MaterialASMEB31_3StressValueDTO()
        {
          TLower = new double?(0.0),
          TUpper = new double?(0.0),
          SLower = new double?(0.0),
          SUpper = new double?(0.0)
        };
      }
    }
    return new MaterialStrengthLookupResult()
    {
      Temperature = num1,
      Status = str,
      TLower = b313StressValueDto.TLower.Value,
      TUpper = b313StressValueDto.TUpper.Value,
      SLower = b313StressValueDto.SLower.Value,
      SUpper = b313StressValueDto.SUpper.Value
    };
  }
}
