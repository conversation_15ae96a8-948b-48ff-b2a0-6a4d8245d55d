// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_3.MinimumThickness.MinimumThickness
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_3.MinimumThickness;

public class MinimumThickness : BaseCalculator
{
  private List<MaterialASMEB31_3StressValueDTO> _allowableStrengths;
  private List<MaterialASMEB31_3_Coefficient_YDTO> _allowableStrenghtsByType;
  private TemperatureRangeDTO _minDesignTemperature;
  private MaterialASMEB31_3DTO _material;

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double? YieldStrengthNew { get; set; }

  public double? TensileStrengthNew { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double NominalThickness { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double SupplementalThickness { get; set; }

  public double LongitudinalWeldJointEfficiency { get; set; }

  public double CircumferentialWeldJointEfficiency { get; set; }

  public double MechanicalAllowances { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double TMin { get; private set; }

  public double AllowableStrength { get; private set; }

  public MinimumThickness(
    MaterialASMEB31_3DTO material,
    TemperatureRangeDTO minDesignTemperature,
    List<MaterialASMEB31_3StressValueDTO> allowableStrengths,
    List<MaterialASMEB31_3_Coefficient_YDTO> allowableStrenghtsByType)
  {
    this._material = material;
    this._minDesignTemperature = minDesignTemperature;
    this._allowableStrengths = allowableStrengths;
    this._allowableStrenghtsByType = allowableStrenghtsByType;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    double num1 = 0.0;
    double num2 = 0.0;
    double num3 = 0.0;
    double num4 = 0.0;
    if (this._material.UserDefined)
    {
      this.AllowableStrength = Math.Min(this.YieldStrengthNew.Value / 1.5, this.TensileStrengthNew.Value / 3.0);
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
      if (this._material.StressTemperatureLimit.HasValue)
      {
        double designTemperature = this.DesignTemperature;
        double? temperatureLimit = this._material.StressTemperatureLimit;
        if ((designTemperature < temperatureLimit.GetValueOrDefault() ? 0 : (temperatureLimit.HasValue ? 1 : 0)) != 0)
          this.Warnings.Add("Strength value exceeds two-thirds of the expected yield strength at this temperature.See paragraph 302.3.2(d)(3) and ( e) ASME B31.3");
      }
      MaterialTypeStrengthLookupResult strengthByTypeResult = Utilities.GetStrengthByTypeResult(this._allowableStrenghtsByType, this.DesignTemperature);
      if (strengthByTypeResult.Status == "Above Range")
      {
        this.ErrorMessage = "Design temperature too high, unable to proceed.";
        return CalculatorResult.Fail;
      }
      if (strengthByTypeResult.Status == "Below Range")
      {
        this.ErrorMessage = "Design temperature too low, unable to proceed.";
        return CalculatorResult.Fail;
      }
      if (strengthByTypeResult.Status == "In Range")
      {
        num1 = strengthByTypeResult.Tlower;
        num2 = strengthByTypeResult.Tupper;
        num3 = strengthByTypeResult.Yupper;
        num4 = strengthByTypeResult.Ylower;
        double tlowerSi1 = strengthByTypeResult.Tlower_SI;
        double tlowerSi2 = strengthByTypeResult.Tlower_SI;
      }
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      if (this._material.StressTemperatureLimit.HasValue)
      {
        double designTemperature = this.DesignTemperature;
        double? temperatureLimit = this._material.StressTemperatureLimit;
        if ((designTemperature < temperatureLimit.GetValueOrDefault() ? 0 : (temperatureLimit.HasValue ? 1 : 0)) != 0)
          this.Warnings.Add("Strength value exceeds two-thirds of the expected yield strength at this temperature.See paragraph 302.3.2(d)(3) and ( e) ASME B31.3");
      }
      string minimumTemperatureC = this._material.MinimumTemperatureC;
      double minimumAllowableTemperature;
      if (!this.IsNumeric(minimumTemperatureC))
      {
        double num5 = this._minDesignTemperature.TLower.Value;
        double num6 = this._minDesignTemperature.TUpper.Value;
        double num7 = this._minDesignTemperature.SUpper.Value;
        double num8 = this._minDesignTemperature.SLower.Value;
        minimumAllowableTemperature = (this.NominalThickness - num5) / (num6 - num5) * (num7 - num8) + num8;
      }
      else
        minimumAllowableTemperature = double.Parse(minimumTemperatureC);
      if (this.DesignTemperature < minimumAllowableTemperature)
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress value at the lowest listed temperature of this material (ASME B31.3) to calculate tmin.");
      MaterialStrengthLookupResult strengthResult = Utilities.GetStrengthResult(this._allowableStrengths, this.DesignTemperature, minimumAllowableTemperature);
      if (strengthResult.Status == "Above Range")
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material.";
        return CalculatorResult.Fail;
      }
      double tupper = strengthResult.TUpper;
      double tlower = strengthResult.TLower;
      double slower = strengthResult.SLower;
      double supper = strengthResult.SUpper;
      this.AllowableStrength = this.DesignTemperature >= tlower ? (this.DesignTemperature - tlower) / (tupper - tlower) * (supper - slower) + slower : slower;
      MaterialTypeStrengthLookupResult strengthByTypeResult = Utilities.GetStrengthByTypeResult(this._allowableStrenghtsByType, this.DesignTemperature >= minimumAllowableTemperature ? this.DesignTemperature : minimumAllowableTemperature);
      if (strengthByTypeResult.Status == "Above Range")
      {
        this.ErrorMessage = "Design temperature too high, unable to proceed.";
        return CalculatorResult.Fail;
      }
      if (strengthByTypeResult.Status == "Below Range")
      {
        this.ErrorMessage = "Design temperature too low, unable to proceed.";
        return CalculatorResult.Fail;
      }
      if (strengthByTypeResult.Status == "In Range")
      {
        num1 = strengthByTypeResult.Tlower;
        num2 = strengthByTypeResult.Tupper;
        num3 = strengthByTypeResult.Yupper;
        num4 = strengthByTypeResult.Ylower;
        double tlowerSi3 = strengthByTypeResult.Tlower_SI;
        double tlowerSi4 = strengthByTypeResult.Tlower_SI;
      }
    }
    double num9 = (this.DesignTemperature - num1) / (num2 - num1) * (num3 - num4) + num4;
    double num10 = this.NominalOutsideDiameter - 2.0 * (this.ExternalFutureCorrosionAllowance + this.ExternalUniformMetalLoss);
    this.TMin = Math.Max(this.DesignPressure * num10 / (2.0 * (this.AllowableStrength * this.LongitudinalWeldJointEfficiency + this.DesignPressure * num9)) + this.MechanicalAllowances, this.DesignPressure * num10 / (4.0 * (this.AllowableStrength * this.CircumferentialWeldJointEfficiency + this.DesignPressure * num9)) + this.MechanicalAllowances + this.SupplementalThickness);
    if (this.TMin >= num10 / 6.0)
    {
      double num11 = (this.NominalOutsideDiameter - 2.0 * this.NominalThickness + 2.0 * this.MechanicalAllowances) / (this.NominalOutsideDiameter + this.NominalOutsideDiameter - 2.0 * this.NominalThickness + 2.0 * this.MechanicalAllowances);
      this.TMin = Math.Max(this.DesignPressure * num10 / (2.0 * (this.AllowableStrength * this.LongitudinalWeldJointEfficiency + this.DesignPressure * num11)) + this.MechanicalAllowances, this.DesignPressure * num10 / (4.0 * (this.AllowableStrength * this.CircumferentialWeldJointEfficiency + this.DesignPressure * num11)) + this.MechanicalAllowances + this.SupplementalThickness);
    }
    if (this.NominalThickness == 0.0)
      this.Warnings.Add("The minimum allowable design temperature considered for this material is the lowest value shown in Table 323.2.2.A  ASMEB31.3 (nominal thickness unknown)");
    return CalculatorResult.Completed;
  }
}
