// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_3.LocalMetalLossThinArea.LocalMetalLossThinArea
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_3.LocalMetalLossThinArea;

public class LocalMetalLossThinArea : BaseCalculator
{
  private bool RtGreater0;
  private List<MaterialASMEB31_3StressValueDTO> _allowableStrengths;
  private List<MaterialASMEB31_3_Coefficient_YDTO> _allowableStrenghtsByType;
  private TemperatureRangeDTO _minDesignTemperature;
  private MaterialASMEB31_3DTO _material;
  private ThicknessReadings _thicknessReadings;
  private IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_3.MAWP.MAWP _calculatorMAWP;
  private List<MaterialTSFCurveDTO> _api579Table5_4List;

  public bool ToLevel2 { get; set; }

  public bool MAWP { get; set; }

  public double DesignTemperature { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double SupplementalThickness { get; set; }

  public double MechanicalAllowances { get; set; }

  public double LongitudinalWeldJointEfficiency { get; set; }

  public double CircumferentialWeldJointEfficiency { get; set; }

  public double Lmsd { get; set; }

  public double LongitudinalThicknessReadingSpacingLc { get; set; }

  public double CircumferentialThicknessReadingSpacingLm { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double LongitudinalMetalLossExtentS { get; set; }

  public double CircumferentiallMetalLossExtentC { get; set; }

  public double AllowableStrength { get; set; }

  public double Tmm { get; set; }

  public double Trd { get; set; }

  public double Tc { get; set; }

  public double Lambda { get; set; }

  public double LambdaC { get; set; }

  public double RSF { get; set; }

  public bool ConditionsCircumferentialExtent { get; set; }

  public double TSF { get; set; }

  public double minRSFi { get; set; }

  public bool RtGreater0_2 { get; set; }

  public bool TmmMinusFCAGreater2_5 { get; set; }

  public bool LmsdGreater1_8DtcPower0_5 { get; set; }

  public bool LambdaCLessThan9 { get; set; }

  public bool DOverTcGreaterThan20 { get; set; }

  public bool RSFBetween0_7And1 { get; set; }

  public bool ElBetween0_7And1 { get; set; }

  public bool EcBetween0_7And2 { get; set; }

  public bool ScreeningCriteriaFigure5_6 { get; set; }

  public bool RSFGreaterRSFa { get; set; }

  public bool ScreeningCriteriaFigure5_8 { get; set; }

  public string ConditionCircumferentialExtentL1 { get; set; }

  public bool Level1Passed { get; set; }

  public string ConditionLongitudinalExtentL1 { get; set; }

  public string Level1AssessmentConclusion { get; set; }

  public bool min_RSFiGreaterThanOrEqualToRSFa { get; set; }

  public bool Level2Passed { get; set; }

  public string ConditionLongitudinalExtentL2 { get; set; }

  public string Level2AssessmentConclusion { get; set; }

  public double MAWPValue { get; set; }

  public int iConditionLongitudinalExtentLevel1 { get; set; }

  public double MAWPrL1 { get; set; }

  public int iConditionLongitudinalExtentLevel2 { get; set; }

  public double MAWPrL2 { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private double TestNumber { get; set; }

  private double Radius { get; set; }

  private double RSFa { get; set; }

  private double Rt { get; set; }

  private double LambdaTable5_2 { get; set; }

  private double MtTable5_2 { get; set; }

  private double RtFigure5_6 { get; set; }

  public LocalMetalLossThinArea(
    MaterialASMEB31_3DTO material,
    TemperatureRangeDTO minDesignTemperature,
    List<MaterialASMEB31_3StressValueDTO> allowableStrengths,
    List<MaterialASMEB31_3_Coefficient_YDTO> allowableStrenghtsByType,
    List<MaterialTSFCurveDTO> api579Table5_4List,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this._minDesignTemperature = minDesignTemperature;
    this._allowableStrengths = allowableStrengths;
    this._allowableStrenghtsByType = allowableStrenghtsByType;
    this._api579Table5_4List = api579Table5_4List;
    this._thicknessReadings = thicknessReadingsCalculator;
    this.Warnings = new List<string>();
  }

  public virtual void CalculateTrd()
  {
    this.Trd = this.NominalThickness - this.ExternalUniformMetalLoss - this.InternalUniformMetalLoss;
  }

  public virtual CalculatorResult CalculateMAWP()
  {
    this._calculatorMAWP = new IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_3.MAWP.MAWP(this._material, this._minDesignTemperature, this._allowableStrengths, this._allowableStrenghtsByType);
    this._calculatorMAWP.DesignTemperature = this.DesignTemperature;
    this._calculatorMAWP.NominalOutsideDiameter = this.NominalOutsideDiameter;
    this._calculatorMAWP.NominalThickness = this.NominalThickness;
    this._calculatorMAWP.SupplementalThickness = this.SupplementalThickness;
    this._calculatorMAWP.MechanicalAllowances = this.MechanicalAllowances;
    this._calculatorMAWP.LongitudinalWeldJointEfficiency = this.LongitudinalWeldJointEfficiency;
    this._calculatorMAWP.CircumferentialWeldJointEfficiency = this.CircumferentialWeldJointEfficiency;
    this._calculatorMAWP.ExternalFutureCorrosionAllowance = this.ExternalFutureCorrosionAllowance;
    this._calculatorMAWP.ExternalUniformMetalLoss = this.ExternalUniformMetalLoss;
    this._calculatorMAWP.InternalFutureCorrosionAllowance = this.InternalFutureCorrosionAllowance;
    this._calculatorMAWP.InternalUniformMetalLoss = this.InternalUniformMetalLoss;
    this._calculatorMAWP.YieldStrengthNew = this._material.YieldStrengthNew;
    this._calculatorMAWP.TensileStrengthNew = this._material.TensileStrengthNew;
    return this._calculatorMAWP.CalculateAssessment();
  }

  public CalculatorResult CalculateAssessment()
  {
    CalculatorResult calculatorResult = CalculatorResult.Fail;
    if (this.MAWP)
    {
      calculatorResult = this.CalculateMAWP();
      if (calculatorResult == CalculatorResult.Fail)
      {
        this.ErrorMessage = this._calculatorMAWP.ErrorMessage;
        return CalculatorResult.Fail;
      }
      this.MAWPValue = this._calculatorMAWP.MAWPValue;
      this.AllowableStrength = this._calculatorMAWP.AllowableStrength;
      this.Warnings.AddRange((IEnumerable<string>) this._calculatorMAWP.Warnings);
    }
    this.Radius = this.NominalOutsideDiameter / 2.0 - this.ExternalFutureCorrosionAllowance - this.ExternalUniformMetalLoss;
    this.Tc = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
    this.CalculateTrd();
    try
    {
      this.Tmm = this._thicknessReadings.GetMinimumValue();
      if (this.Tmm >= this.Trd)
      {
        this.ErrorMessage = "All thickness readings are greater than trd = tnom - LOSS. No metal loss has been found. This assessment is not needed";
        return CalculatorResult.Fail;
      }
    }
    catch
    {
      this.ErrorMessage = "Profile readings contain multiple Tmm's - Currently unable to proceed with the assessment.";
      return CalculatorResult.Fail;
    }
    this._thicknessReadings.ProcessP5ThicknessReadings(this.CircumferentialThicknessReadingSpacingLm, this.LongitudinalThicknessReadingSpacingLc, Math.Round(this.Trd, 4));
    this.LongitudinalMetalLossExtentS = this._thicknessReadings.S;
    this.CircumferentiallMetalLossExtentC = this._thicknessReadings.C;
    this.RSFa = 0.9;
    this.Rt = (this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.Tc;
    this.Rt = Math.Round(this.Rt, 4);
    this.RtGreater0 = this.Rt > 0.0;
    if (!this.RtGreater0)
    {
      this.ErrorMessage = "Remaining thickness ratio is less than or equal to zero. The assessment fails. ";
      return CalculatorResult.Fail;
    }
    this.Lambda = 1.285 * this.LongitudinalMetalLossExtentS / Math.Pow(2.0 * this.Radius * this.Tc, 0.5);
    this.RtGreater0_2 = this.Rt > 0.2;
    this.TmmMinusFCAGreater2_5 = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance >= 2.5;
    this.LmsdGreater1_8DtcPower0_5 = this.Lmsd > 1.8 * Math.Pow(this.Radius * 2.0 * this.Tc, 0.5);
    this.LambdaTable5_2 = this.Lambda <= 20.0 ? this.Lambda : 20.0;
    this.MtTable5_2 = 1001.0 / 1000.0 - 0.014195 * this.LambdaTable5_2 + 0.2909 * Math.Pow(this.LambdaTable5_2, 2.0) - 0.09642 * Math.Pow(this.LambdaTable5_2, 3.0) + 0.02089 * Math.Pow(this.LambdaTable5_2, 4.0) - 0.003054 * Math.Pow(this.LambdaTable5_2, 5.0) + 2.957 * Math.Pow(10.0, -4.0) * Math.Pow(this.LambdaTable5_2, 6.0) - 1.8462 * Math.Pow(10.0, -5.0) * Math.Pow(this.LambdaTable5_2, 7.0) + 7.1553 * Math.Pow(10.0, -7.0) * Math.Pow(this.LambdaTable5_2, 8.0) - 1.5631 * Math.Pow(10.0, -8.0) * Math.Pow(this.LambdaTable5_2, 9.0) + 916.0 / 625.0 * Math.Pow(10.0, -10.0) * Math.Pow(this.LambdaTable5_2, 10.0);
    this.RtFigure5_6 = this.Lambda > 0.354 ? (this.Lambda >= 20.0 ? 0.9 : (this.RSFa - this.RSFa / this.MtTable5_2) * Math.Pow(1.0 - this.RSFa / this.MtTable5_2, -1.0)) : 0.2;
    this.ScreeningCriteriaFigure5_6 = this.Rt > this.RtFigure5_6;
    this.RSF = this.Rt / (1.0 - 1.0 / this.MtTable5_2 * (1.0 - this.Rt));
    this.RSFGreaterRSFa = this.RSF >= this.RSFa;
    if (this.RtGreater0_2 && this.TmmMinusFCAGreater2_5 && this.LmsdGreater1_8DtcPower0_5)
    {
      if (this.ScreeningCriteriaFigure5_6 || this.RSFGreaterRSFa)
      {
        this.ConditionLongitudinalExtentL1 = "The longitudinal extent of the flaw is ACCEPTABLE for operation at the MAWP.";
        this.iConditionLongitudinalExtentLevel1 = 1;
      }
      else
      {
        this.ConditionLongitudinalExtentL1 = "The longitudinal extent of the flaw is ACCEPTABLE for operation at the MAWPr.";
        this.iConditionLongitudinalExtentLevel1 = 2;
      }
    }
    else
    {
      this.ConditionLongitudinalExtentL1 = "The longitudinal extent of the flaw is UNACCEPTABLE";
      this.iConditionLongitudinalExtentLevel1 = 3;
    }
    this.LambdaC = 1.285 * this.CircumferentiallMetalLossExtentC / Math.Pow(this.Radius * 2.0 * this.Tc, 0.5);
    this.LambdaCLessThan9 = this.LambdaC <= 9.0;
    this.DOverTcGreaterThan20 = this.Radius * 2.0 / this.Tc >= 20.0;
    this.RSFBetween0_7And1 = this.RSF >= 0.7 && this.RSF <= 1.0;
    this.ElBetween0_7And1 = this.LongitudinalWeldJointEfficiency >= 0.7 && this.LongitudinalWeldJointEfficiency <= 1.0;
    this.EcBetween0_7And2 = this.CircumferentialWeldJointEfficiency >= 0.7 && this.CircumferentialWeldJointEfficiency <= 1.0;
    this.ConditionsCircumferentialExtent = this.LambdaCLessThan9 && this.DOverTcGreaterThan20 && this.RSFBetween0_7And1 && this.ElBetween0_7And1 && this.EcBetween0_7And2;
    if (!this.ConditionsCircumferentialExtent)
    {
      this.ConditionCircumferentialExtentL1 = "The Circumferential extent of the flaw is UNACCEPTABLE";
      this.Level1Passed = false;
      this.Level1AssessmentConclusion = "The Level 1 Assessment is UNACCEPTABLE";
    }
    else
    {
      this.TSF = this.CircumferentialWeldJointEfficiency / (2.0 * this.RSF) * (1.0 + Math.Pow(4.0 - 3.0 * Math.Pow(this.LongitudinalWeldJointEfficiency, 2.0), 0.5) / this.LongitudinalWeldJointEfficiency);
      double RtFigure5_8;
      if (!this.GetRtFigure5_8(this.TSF, this.LambdaC, out RtFigure5_8))
        return CalculatorResult.Fail;
      this.ScreeningCriteriaFigure5_8 = this.Rt >= RtFigure5_8;
      if (this.ScreeningCriteriaFigure5_8)
      {
        this.ConditionCircumferentialExtentL1 = "The Circumferential extent of the flaw is ACCEPTABLE";
        if (this.iConditionLongitudinalExtentLevel1 == 1)
        {
          this.Level1Passed = true;
          this.Level1AssessmentConclusion = "The Level 1 Assessment is ACCEPTABLE";
          return CalculatorResult.Completed;
        }
        if (this.iConditionLongitudinalExtentLevel1 == 2)
        {
          this.Level1Passed = false;
          this.Level1AssessmentConclusion = "The Level 1 Assessment is UNACCEPTABLE.The component is unacceptable for operation at MAWP but it is ACCEPTABLE for operation at MAWPr";
        }
        else
        {
          this.Level1Passed = false;
          this.Level1AssessmentConclusion = "The Level 1 Assessment is UNACCEPTABLE";
        }
      }
      else
      {
        this.ConditionCircumferentialExtentL1 = "The Circumferential extent of the flaw is UNACCEPTABLE";
        this.Level1Passed = false;
        this.Level1AssessmentConclusion = "Level 1 Assessment UNACCEPTABLE";
      }
    }
    if (this.MAWP && calculatorResult == CalculatorResult.Completed && this.iConditionLongitudinalExtentLevel1 == 2)
      this.MAWPrL1 = this.MAWPValue * this.RSF / this.RSFa;
    if (!this.ToLevel2)
      return CalculatorResult.Completed;
    this._thicknessReadings.ProcessP5Level2(this.Trd, this.LongitudinalThicknessReadingSpacingLc, 2.0 * this.Radius, this.Tc);
    this.minRSFi = this._thicknessReadings.GetLevel2RSF();
    this.min_RSFiGreaterThanOrEqualToRSFa = this.minRSFi >= this.RSFa;
    if (this.RtGreater0_2 && this.TmmMinusFCAGreater2_5 && this.LmsdGreater1_8DtcPower0_5)
    {
      if (this.min_RSFiGreaterThanOrEqualToRSFa)
      {
        this.ConditionLongitudinalExtentL2 = "The longitudinal extent of the flaw is ACCEPTABLE for operation at the MAWP";
        this.iConditionLongitudinalExtentLevel2 = 1;
      }
      else
      {
        this.ConditionLongitudinalExtentL2 = "The longitudinal extent of the flaw is ACCEPTABLE for operation at the MAWPr";
        this.iConditionLongitudinalExtentLevel2 = 2;
      }
    }
    else
    {
      this.ConditionLongitudinalExtentL2 = "The longitudinal extent of the flaw is UNACCEPTABLE";
      this.iConditionLongitudinalExtentLevel2 = 3;
    }
    if (this.ConditionCircumferentialExtentL1 == "The Circumferential extent of the flaw is ACCEPTABLE")
    {
      if (this.iConditionLongitudinalExtentLevel2 == 1)
      {
        this.Level2AssessmentConclusion = "The Level 2 Assessment is ACCEPTABLE for operation at the MAWP";
        this.Level2Passed = true;
      }
      else if (this.iConditionLongitudinalExtentLevel2 == 2)
      {
        this.Level2AssessmentConclusion = "The Level 2 Assessment is UNACCEPTABLE.The component is unacceptable for operation at MAWP but it is ACCEPTABLE for operation at MAWPr";
        this.Level2Passed = false;
      }
      else
      {
        this.Level2AssessmentConclusion = "The Level 2 Assessment is UNACCEPTABLE";
        this.Level2Passed = false;
      }
    }
    else
    {
      this.Level2AssessmentConclusion = "The Level 2 Assessment is UNACCEPTABLE";
      this.Level2Passed = false;
    }
    if (this.MAWP && calculatorResult == CalculatorResult.Completed && this.iConditionLongitudinalExtentLevel2 == 2)
      this.MAWPrL2 = this.MAWPValue * this.minRSFi / this.RSFa;
    return CalculatorResult.Completed;
  }

  private bool GetRtFigure5_8(double TSF, double LambdaC, out double RtFigure5_8)
  {
    MaterialTSFCurveDTO materialTsfCurveDto1 = this._api579Table5_4List.Where<MaterialTSFCurveDTO>((Func<MaterialTSFCurveDTO, bool>) (x =>
    {
      double? tsf = x.TSF;
      double num = TSF;
      return tsf.GetValueOrDefault() > num && tsf.HasValue;
    })).OrderBy<MaterialTSFCurveDTO, double?>((Func<MaterialTSFCurveDTO, double?>) (x => x.TSF)).FirstOrDefault<MaterialTSFCurveDTO>();
    if (materialTsfCurveDto1 == null)
    {
      this.ErrorMessage = "TSF Outside normal operating conditions";
      RtFigure5_8 = -1.0;
      return false;
    }
    MaterialTSFCurveDTO materialTsfCurveDto2 = this._api579Table5_4List.Where<MaterialTSFCurveDTO>((Func<MaterialTSFCurveDTO, bool>) (x =>
    {
      double? tsf = x.TSF;
      double num = TSF;
      return tsf.GetValueOrDefault() < num && tsf.HasValue;
    })).OrderByDescending<MaterialTSFCurveDTO, double?>((Func<MaterialTSFCurveDTO, double?>) (x => x.TSF)).FirstOrDefault<MaterialTSFCurveDTO>() ?? materialTsfCurveDto1;
    double num1 = materialTsfCurveDto2.TSF.Value;
    double num2 = materialTsfCurveDto1.TSF.Value;
    double num3 = materialTsfCurveDto2.LambdaCMinus02.Value;
    double num4 = materialTsfCurveDto1.LambdaCMinus02.Value;
    double num5 = num1 != 2.3 ? (TSF - num1) / (num2 - num1) * (num4 - num3) + num3 : num3;
    double num6 = materialTsfCurveDto2.C1.Value;
    double num7 = materialTsfCurveDto2.C2.Value;
    double num8 = materialTsfCurveDto2.C3.Value;
    double num9 = materialTsfCurveDto2.C4.Value;
    double num10 = materialTsfCurveDto2.C5.Value;
    double num11 = materialTsfCurveDto2.C6.Value;
    double num12 = materialTsfCurveDto1.C1.Value;
    double num13 = materialTsfCurveDto1.C2.Value;
    double num14 = materialTsfCurveDto1.C3.Value;
    double num15 = materialTsfCurveDto1.C4.Value;
    double num16 = materialTsfCurveDto1.C5.Value;
    double num17 = materialTsfCurveDto1.C6.Value;
    double num18;
    double num19;
    double num20;
    double num21;
    double num22;
    double num23;
    if (num1 == 2.3)
    {
      num18 = num6;
      num19 = num7;
      num20 = num8;
      num21 = num9;
      num22 = num10;
      num23 = num11;
    }
    else
    {
      num18 = (TSF - num1) / (num2 - num1) * (num12 - num6) + num6;
      num19 = (TSF - num1) / (num2 - num1) * (num13 - num7) + num7;
      num20 = (TSF - num1) / (num2 - num1) * (num14 - num8) + num8;
      num21 = (TSF - num1) / (num2 - num1) * (num15 - num9) + num9;
      num22 = (TSF - num1) / (num2 - num1) * (num16 - num10) + num10;
      num23 = (TSF - num1) / (num2 - num1) * (num17 - num11) + num11;
    }
    RtFigure5_8 = LambdaC > num5 ? num18 + num19 / LambdaC + num20 / Math.Pow(LambdaC, 2.0) + num21 / Math.Pow(LambdaC, 3.0) + num22 / Math.Pow(LambdaC, 4.0) + num23 / Math.Pow(LambdaC, 5.0) : 0.2;
    return true;
  }
}
