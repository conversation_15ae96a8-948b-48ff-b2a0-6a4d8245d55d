// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_3.MAWP.MAWP
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_3;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_3.MAWP;

public class MAWP : BaseCalculator
{
  private double Tlower2;
  private double Tupper2;
  private double? Ylower;
  private double? Yupper;
  private List<MaterialASMEB31_3StressValueDTO> _allowableStrengths;
  private List<MaterialASMEB31_3_Coefficient_YDTO> _allowableStrenghtsByType;
  private TemperatureRangeDTO _minDesignTemperature;
  private MaterialASMEB31_3DTO _material;

  public double DesignTemperature { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double SupplementalThickness { get; set; }

  public double LongitudinalWeldJointEfficiency { get; set; }

  public double CircumferentialWeldJointEfficiency { get; set; }

  public double MechanicalAllowances { get; set; }

  public double? YieldStrengthNew { get; set; }

  public double? TensileStrengthNew { get; set; }

  private string MinimumAllowableTemperatureCURVE { get; set; }

  private double ThicknessLower { get; set; }

  private double ThicknessUpper { get; set; }

  private double TminLower { get; set; }

  private double TminUpper { get; set; }

  private double MinimumAllowableTemperature { get; set; }

  private string TemperatureLowLimit { get; set; }

  private string TemperatureUpLimit { get; set; }

  private string StrengthTemperatureLimit { get; set; }

  private double Tlower { get; set; }

  private double Tupper { get; set; }

  private double StrengthLower { get; set; }

  private double StrengthUpper { get; set; }

  private string Material { get; set; }

  private double YB31ASMEB31 { get; set; }

  private double OutsideDiameterDo { get; set; }

  public double Tc { get; set; }

  private double checkYB31 { get; set; }

  public double MAWPc { get; set; }

  public double MAWPl { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double MAWPValue { get; private set; }

  public double AllowableStrength { get; private set; }

  public MAWP()
  {
  }

  public MAWP(
    MaterialASMEB31_3DTO material,
    TemperatureRangeDTO minDesignTemperature,
    List<MaterialASMEB31_3StressValueDTO> allowableStrengths,
    List<MaterialASMEB31_3_Coefficient_YDTO> allowableStrenghtsByType)
  {
    this._material = material;
    this._minDesignTemperature = minDesignTemperature;
    this._allowableStrengths = allowableStrengths;
    this._allowableStrenghtsByType = allowableStrenghtsByType;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    try
    {
      bool flag1 = false;
      bool flag2;
      if (this._material.UserDefined)
      {
        double? yieldStrengthNew = this.YieldStrengthNew;
        double val1 = (yieldStrengthNew.HasValue ? new double?(yieldStrengthNew.GetValueOrDefault() / 1.5) : new double?()).Value;
        double? tensileStrengthNew = this.TensileStrengthNew;
        double val2 = (tensileStrengthNew.HasValue ? new double?(tensileStrengthNew.GetValueOrDefault() / 3.0) : new double?()).Value;
        this.AllowableStrength = Math.Min(val1, val2);
        this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
        if (this._material.MaterialID == 0)
        {
          this.Material = "Ferritic Steel";
          this.Warnings.Add("Ferritic Steel material has been assumed to take the coefficient YB31 from table 304.1.1 ASMEB31.3 required for the assessment.");
        }
        flag2 = Utilities.GetB_36_1Coefficient(this._material.Material, this.DesignTemperature, out this.Tlower2, out this.Tupper2, out this.Ylower, out this.Yupper);
      }
      else
      {
        this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
        string minimumTemperatureC = this._material.MinimumTemperatureC;
        double num1;
        if (!this.IsNumeric(minimumTemperatureC))
        {
          double num2 = this._minDesignTemperature.TLower.Value;
          double num3 = this._minDesignTemperature.TUpper.Value;
          double num4 = this._minDesignTemperature.SUpper.Value;
          double num5 = this._minDesignTemperature.SLower.Value;
          num1 = (this.NominalThickness - num2) / (num3 - num2) * (num4 - num5) + num5;
        }
        else
          num1 = double.Parse(minimumTemperatureC);
        if (this.DesignTemperature < num1)
        {
          this.TemperatureLowLimit = "Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress value at the lowest listed temperature of this material (ASME B31.3) to calculate tmin.";
          this.Warnings.Add(this.TemperatureLowLimit);
        }
        MaterialStrengthLookupResult strengthResult = Utilities.GetStrengthResult(this._allowableStrengths, this.DesignTemperature, num1);
        if (strengthResult.Status == "Above Range")
        {
          this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material.";
          return CalculatorResult.Fail;
        }
        if (strengthResult.Status == "Below Range")
        {
          this.TemperatureLowLimit = "Brittle fracture assessment is required because the temperature is too low.  If it is passed, we take the Allowable Stress value at the lowest listed temperature of this material (ASME B31.3) to calculate tmin.";
          this.Warnings.Add(this.TemperatureLowLimit);
        }
        if (this._material.StressTemperatureLimit.HasValue)
        {
          double designTemperature = this.DesignTemperature;
          double? temperatureLimit = this._material.StressTemperatureLimit;
          if ((designTemperature < temperatureLimit.GetValueOrDefault() ? 0 : (temperatureLimit.HasValue ? 1 : 0)) != 0)
            this.Warnings.Add("Strength value exceeds two-thirds of the expected yield strength at this temperature.See paragraph 302.3.2(d)(3) and ( e) ASME B31.3");
        }
        this.Tlower = strengthResult.TLower;
        this.Tupper = strengthResult.TUpper;
        this.StrengthLower = strengthResult.SLower;
        this.StrengthUpper = strengthResult.SUpper;
        flag1 = false;
        this.AllowableStrength = this.DesignTemperature >= this.Tlower ? (this.DesignTemperature - this.Tlower) / (this.Tupper - this.Tlower) * (this.StrengthUpper - this.StrengthLower) + this.StrengthLower : this.StrengthLower;
        flag2 = this.DesignTemperature >= num1 ? Utilities.GetB_36_1Coefficient(this._material.MaterialType, this.DesignTemperature, out this.Tlower2, out this.Tupper2, out this.Ylower, out this.Yupper) : Utilities.GetB_36_1Coefficient(this._material.MaterialType, num1, out this.Tlower2, out this.Tupper2, out this.Ylower, out this.Yupper);
      }
      if (flag2 && this.Yupper.HasValue && this.Ylower.HasValue)
        this.YB31ASMEB31 = (this.DesignTemperature - this.Tlower2) / (this.Tupper2 - this.Tlower2) * (this.Yupper.Value - this.Ylower.Value) + this.Ylower.Value;
      this.OutsideDiameterDo = this.NominalOutsideDiameter - 2.0 * (this.ExternalFutureCorrosionAllowance + this.ExternalUniformMetalLoss);
      this.Tc = this.NominalThickness - this.ExternalUniformMetalLoss - this.ExternalFutureCorrosionAllowance - this.InternalFutureCorrosionAllowance - this.InternalUniformMetalLoss;
      this.checkYB31 = this.Tc >= this.OutsideDiameterDo / 6.0 ? (this.NominalOutsideDiameter - 2.0 * this.NominalThickness + 2.0 * this.MechanicalAllowances) / (this.NominalOutsideDiameter + this.NominalOutsideDiameter - 2.0 * this.NominalThickness + 2.0 * this.MechanicalAllowances) : this.YB31ASMEB31;
      this.MAWPc = 2.0 * this.AllowableStrength * this.LongitudinalWeldJointEfficiency * (this.Tc - this.MechanicalAllowances) / (this.OutsideDiameterDo - 2.0 * this.checkYB31 * (this.Tc - this.MechanicalAllowances));
      this.MAWPl = 4.0 * this.AllowableStrength * this.CircumferentialWeldJointEfficiency * (this.Tc - this.SupplementalThickness - this.MechanicalAllowances) / (this.OutsideDiameterDo - 4.0 * this.checkYB31 * (this.Tc - this.SupplementalThickness - this.MechanicalAllowances));
      this.MAWPValue = Math.Min(this.MAWPc, this.MAWPl);
      return CalculatorResult.Completed;
    }
    catch (Exception ex)
    {
      this.ErrorMessage = $"MAWP B31.3 failed: {ex.Message}";
      return CalculatorResult.Fail;
    }
  }
}
