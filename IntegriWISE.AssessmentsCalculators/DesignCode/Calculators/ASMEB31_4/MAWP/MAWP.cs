// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_4.MAWP.MAWP
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_4.MAWP;

public class MAWP : BaseCalculator
{
  public double TempertureLimits;
  public double TempertureLimits2;
  private MaterialASMEB31_4DTO _material;

  public double DesignTemperature { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double WeldJointEfficiency { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double FCAi { get; set; }

  public double FCAe { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double MAWPValue { get; private set; }

  public double CorrodedWallThickness { get; private set; }

  public double AllowableStrength { get; private set; }

  public double? YieldStrengthNew { get; set; }

  public MAWP()
  {
  }

  public MAWP(MaterialASMEB31_4DTO material)
  {
    this._material = material;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    try
    {
      if (this.DesignTemperature < -30.0)
        this.Warnings.Add("Brittle fracture assessment is required because the temperature is too low.  This assessment is carried out assuming that brittle fracture assessment is passed and taking the Allowable Stress at -30 degrees to calculate Maximum Allowable Working Pressure (lowest listed temperature for carbon steel materials designed to ASME B31.3)");
      if (this.DesignTemperature > 120.0)
      {
        this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
        return CalculatorResult.Fail;
      }
      if (this._material.UserDefined)
      {
        this.AllowableStrength = this.YieldStrengthNew.Value;
        this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
      }
      else
      {
        this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
        this.AllowableStrength = Convert.ToDouble((object) this._material.SMYS_MPA);
      }
      this.AllowableStrength = this.AllowableStrength * 0.72 * this.WeldJointEfficiency;
      double num = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss - this.FCAi - this.FCAe;
      this.CorrodedWallThickness = num;
      this.MAWPValue = 2.0 * this.AllowableStrength * num / this.NominalOutsideDiameter;
      return CalculatorResult.Completed;
    }
    catch (Exception ex)
    {
      this.ErrorMessage = $"MAWP B31.4 failed: {ex.Message}";
      return CalculatorResult.Fail;
    }
  }
}
