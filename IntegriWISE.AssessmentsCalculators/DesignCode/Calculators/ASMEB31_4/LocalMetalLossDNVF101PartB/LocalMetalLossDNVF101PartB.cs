// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_4.LocalMetalLossDNVF101PartB.LocalMetalLossDNVF101PartB
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.AbstractCalculators.LocalMetalLossDNVF101PartB;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_4.LocalMetalLossDNVF101PartB;

public class LocalMetalLossDNVF101PartB : AbstractLocalMetalLossDNVF101PartB
{
  public LocalMetalLossDNVF101PartB(bool userDefinedMaterial)
  {
    this.Warnings = new List<string>();
    if (userDefinedMaterial)
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    else
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
  }
}
