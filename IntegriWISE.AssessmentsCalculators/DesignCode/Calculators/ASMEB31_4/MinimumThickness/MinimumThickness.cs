// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_4.MinimumThickness.MinimumThickness
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_4.MinimumThickness;

public class MinimumThickness : BaseCalculator
{
  private MaterialASMEB31_4DTO _material;
  private MaterialASMEB31_4DTO _allowableStressForMaterial;

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double? YieldStrengthNew { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double MechanicalAllowances { get; set; }

  public double WeldJointEfficiency { private get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double TMin { get; private set; }

  public double AllowableStrength { get; private set; }

  public MinimumThickness(
    MaterialASMEB31_4DTO material,
    MaterialASMEB31_4DTO allowableStressForMaterial)
  {
    this._material = material;
    this._allowableStressForMaterial = allowableStressForMaterial;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.UserDefined)
    {
      this.AllowableStrength = this.YieldStrengthNew.Value;
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      this.AllowableStrength = Convert.ToDouble((object) this._allowableStressForMaterial.SMYS_MPA);
    }
    this.AllowableStrength = this.AllowableStrength * 0.72 * this.WeldJointEfficiency;
    this.TMin = this.DesignPressure * this.NominalOutsideDiameter / (2.0 * this.AllowableStrength) + this.MechanicalAllowances;
    return CalculatorResult.Completed;
  }
}
