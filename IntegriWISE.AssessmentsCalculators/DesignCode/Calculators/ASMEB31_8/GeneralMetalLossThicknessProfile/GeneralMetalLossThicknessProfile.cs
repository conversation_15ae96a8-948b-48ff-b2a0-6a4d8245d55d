// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_8.GeneralMetalLossThicknessProfile.GeneralMetalLossThicknessProfile
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_8;
using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_8.GeneralMetalLossThicknessProfile;

public class GeneralMetalLossThicknessProfile : BaseCalculator
{
  private bool CheckPLessThanSE;
  private double checkTcMin;
  private double CheckTlMin;
  private bool CheckRtGreaterThanZero;
  private double WorkingTemperature;
  private string TemperatureLowerLimit;
  private bool OutsideDiaMinusTMin_GT_0;
  private MaterialASMEB31_8DTO _material;
  private ThicknessReadings _thicknessReadings;

  public bool ToLevel2 { get; set; }

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double? DesignFactor { private get; set; }

  public string LocationClass { private get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double WeldJointEfficiency { get; set; }

  public double LongitudinalThicknessReadingSpacing { get; set; }

  public double CircumferentialThicknessReadingSpacing { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double S { get; set; }

  public double C { get; set; }

  public double AllowableStrength { get; set; }

  public double TMin { get; set; }

  public double Tmm { get; set; }

  public double Trd { get; set; }

  public double Rt { get; set; }

  public double Q { get; set; }

  public double L { get; set; }

  public double TSam { get; set; }

  public double TCam { get; set; }

  public double MAWPrcTSamMinusFCA { get; set; }

  public double MAWPrlTCamMinusFCA { get; set; }

  public double MAWPrcTsamMinusFCAOverRSFa { get; set; }

  public double MAWPrlTcamMinusFCAOverRSFa { get; set; }

  public bool AverageMeasuredThicknessL1 { get; set; }

  public bool MawpL1 { get; set; }

  public bool MinimumMeasuredThickness { get; set; }

  public bool Level1Passed { get; set; }

  public string Level1 { get; set; }

  public bool AverageMeasuredThicknessL2 { get; set; }

  public bool MawpL2 { get; set; }

  public bool Level2Passed { get; set; }

  public string Level2 { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private bool RtGreater0 { get; set; }

  private bool LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa { get; set; }

  private double Tc { get; set; }

  private double Radius { get; set; }

  private double RSFa { get; set; }

  private double TamCMinusFCA { get; set; }

  private double TamSMinusFCA { get; set; }

  private double TamSMinusFCAOverRSFa { get; set; }

  private double TamCMinusFCAOverRSFa { get; set; }

  private double Tlim { get; set; }

  private double TmmMinusFCA { get; set; }

  private double Max0_5TminTlim { get; set; }

  private double MAWPrTamMinusFCA { get; set; }

  private bool LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin { get; set; }

  private bool LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin { get; set; }

  private double MAWPrTamMinusFCAOverRSFa { get; set; }

  private bool LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa { get; set; }

  private double TLower { get; set; }

  private double TUpper { get; set; }

  private double SLower { get; set; }

  private double SUpper { get; set; }

  private double LongitudinalJointFactorE_Table841_115a { get; set; }

  public GeneralMetalLossThicknessProfile(
    MaterialASMEB31_8DTO material,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this.Warnings = new List<string>();
    this._thicknessReadings = thicknessReadingsCalculator;
  }

  public virtual void CalculateTrd()
  {
    this.Trd = this.NominalThickness - this.ExternalUniformMetalLoss - this.InternalUniformMetalLoss;
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this.DesignTemperature > 232.2222222)
    {
      this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
      return CalculatorResult.Fail;
    }
    try
    {
      if (this._material.UserDefined)
      {
        this.AllowableStrength = this._material.YieldStrengthNew.Value;
        this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
      }
      else
      {
        this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
        if (this._material.MinimumYieldStrengthMpa.HasValue)
          this.AllowableStrength = this._material.MinimumYieldStrengthMpa.Value;
      }
      this.LongitudinalJointFactorE_Table841_115a = this.WeldJointEfficiency;
      double TempLowerFactor;
      double TempUpperFactorT;
      double TLower;
      double TUpper;
      Utilities.Table841_116A(this.DesignTemperature, out TempLowerFactor, out TempUpperFactorT, out TLower, out TUpper);
      double num = (TLower - TUpper) / (TempLowerFactor - TempUpperFactorT) * (this.DesignTemperature - TempUpperFactorT) + TUpper;
      if (!this.DesignFactor.HasValue)
        this.DesignFactor = new double?(Utilities.Table841_114A(this.LocationClass));
      this.AllowableStrength = this.AllowableStrength * this.DesignFactor.Value * this.LongitudinalJointFactorE_Table841_115a * num;
      this.TMin = this.DesignPressure * this.NominalOutsideDiameter / (2.0 * this.AllowableStrength);
      this.Radius = this.NominalOutsideDiameter / 2.0 - this.NominalThickness + this.InternalFutureCorrosionAllowance + this.InternalUniformMetalLoss;
      this.Trd = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss;
      this.Tc = this.Trd - this.ExternalFutureCorrosionAllowance - this.InternalFutureCorrosionAllowance;
      this.RSFa = 0.9;
      try
      {
        this.Tmm = this._thicknessReadings.GetMinimumValue();
        if (this.Tmm >= this.Trd)
        {
          this.ErrorMessage = "All thickness readings are greater than trd = tnom - LOSS. No metal loss has been found. This assessment is not needed";
          return CalculatorResult.Fail;
        }
      }
      catch
      {
        this.ErrorMessage = "Profile readings contain multiple Tmm's - Currently unable to proceed with the assessment.";
        return CalculatorResult.Fail;
      }
      this.Rt = (this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance) / this.Tc;
      this.Rt = Math.Round(this.Rt, 4);
      this.RtGreater0 = this.Rt > 0.0;
      if (!this.RtGreater0)
      {
        this.ErrorMessage = "Remaining thickness ratio is less than or equal to zero. The assessment fails. ";
        return CalculatorResult.Fail;
      }
      this.Q = this.Rt >= this.RSFa ? 50.0 : 1.123 * Math.Pow(Math.Pow((1.0 - this.Rt) / (1.0 - this.Rt / this.RSFa), 2.0) - 1.0, 0.5);
      this.L = this.Q * Math.Pow(2.0 * this.Radius * this.Tc, 0.5);
      this._thicknessReadings.ProcessThicknessReadings(this.L, this.CircumferentialThicknessReadingSpacing, this.LongitudinalThicknessReadingSpacing, this.Trd);
      this.S = this._thicknessReadings.S;
      this.C = this._thicknessReadings.C;
      this.TCam = this._thicknessReadings.Tcam;
      this.TSam = this._thicknessReadings.Tsam;
      this.TamCMinusFCA = this.TCam - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
      this.TamSMinusFCA = this.TSam - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
      this.Tlim = Math.Max(0.2 * this.NominalThickness, 2.5);
      this.TmmMinusFCA = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
      this.Max0_5TminTlim = Math.Max(0.5 * this.TMin, this.Tlim);
      this.MAWPrcTSamMinusFCA = 2.0 * this.AllowableStrength * this.TamSMinusFCA / this.NominalOutsideDiameter;
      this.MAWPrlTCamMinusFCA = 4.0 * this.AllowableStrength * this.TamCMinusFCA / this.NominalOutsideDiameter;
      this.MAWPrTamMinusFCA = Math.Min(this.MAWPrcTSamMinusFCA, this.MAWPrlTCamMinusFCA);
      this.LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin = this.TamCMinusFCA >= this.TMin;
      this.LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin = this.TamSMinusFCA >= this.TMin;
      this.MawpL1 = this.MAWPrTamMinusFCA >= this.DesignPressure;
      this.MinimumMeasuredThickness = this.TmmMinusFCA >= this.Max0_5TminTlim;
      if (this.LEVEL1AverageMeasuredThicknessTCamMinusFCATLmin && this.LEVEL1AverageMeasuredThicknessTSamMinusFCATCmin && this.MawpL1 && this.MinimumMeasuredThickness)
      {
        this.Level1 = "The Level 1 Assessment is ACCEPTABLE";
        this.Level1Passed = true;
      }
      else
      {
        this.Level1 = "The Level 1 Assessment is UNACCEPTABLE";
        this.Level1Passed = false;
      }
      if (!this.ToLevel2)
        return CalculatorResult.Completed;
      this.TamCMinusFCAOverRSFa = this.TamCMinusFCA / this.RSFa;
      this.TamSMinusFCAOverRSFa = this.TamSMinusFCA / this.RSFa;
      this.MAWPrcTsamMinusFCAOverRSFa = 2.0 * this.AllowableStrength * this.TamSMinusFCAOverRSFa / this.NominalOutsideDiameter;
      this.MAWPrlTcamMinusFCAOverRSFa = 4.0 * this.AllowableStrength * this.TamCMinusFCAOverRSFa / this.NominalOutsideDiameter;
      this.MAWPrTamMinusFCAOverRSFa = Math.Min(this.MAWPrcTsamMinusFCAOverRSFa, this.MAWPrlTcamMinusFCAOverRSFa);
      this.LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa = this.TamCMinusFCA >= this.TMin * this.RSFa;
      this.LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa = this.TamSMinusFCA >= this.TMin * this.RSFa;
      this.AverageMeasuredThicknessL2 = this.LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa && this.LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa;
      this.MawpL2 = this.MAWPrTamMinusFCAOverRSFa >= this.DesignPressure;
      if (this.LEVEL2AverageMeasuredThicknessTcamMinusFCATLminRSFa && this.LEVEL2AverageMeasuredThicknessTsamMinusFCATCminRSFa && this.MawpL2 && this.MinimumMeasuredThickness)
      {
        this.Level2 = "The Level 2 Assessment is ACCEPTABLE";
        this.Level2Passed = true;
      }
      else
      {
        this.Level2 = "The Level 2 Assessment is UNACCEPTABLE";
        this.Level2Passed = false;
      }
      return CalculatorResult.Completed;
    }
    catch (Exception ex)
    {
      this.ErrorMessage = $"GeneralMetalLossThicknessProfile Cylindrical failed: {ex.Message}";
      return CalculatorResult.Fail;
    }
  }
}
