// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_8.MinimumThickness.MinimumThickness
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_8;
using IntegriWISE.DataTransferObjects.Material;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_8.MinimumThickness;

public class MinimumThickness : BaseCalculator
{
  private MaterialASMEB31_8DTO _material;

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double? YieldStrengthNew { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double WeldJointEfficiency { private get; set; }

  public double? DesignFactor { private get; set; }

  public string locationClass { private get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double TMin { get; private set; }

  public double AllowableStrength { get; private set; }

  public MinimumThickness(MaterialASMEB31_8DTO material)
  {
    this._material = material;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this._material.UserDefined)
    {
      this.AllowableStrength = this.YieldStrengthNew.Value;
      this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
    }
    else
    {
      this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
      if (this._material.MinimumYieldStrengthMpa.HasValue)
        this.AllowableStrength = this._material.MinimumYieldStrengthMpa.Value;
    }
    double TempLowerFactor;
    double TempUpperFactorT;
    double TLower;
    double TUpper;
    Utilities.Table841_116A(this.DesignTemperature, out TempLowerFactor, out TempUpperFactorT, out TLower, out TUpper);
    double num = (TLower - TUpper) / (TempLowerFactor - TempUpperFactorT) * (this.DesignTemperature - TempUpperFactorT) + TUpper;
    if (!this.DesignFactor.HasValue)
      this.DesignFactor = new double?(Utilities.Table841_114A(this.locationClass));
    this.AllowableStrength = this.AllowableStrength * this.DesignFactor.Value * this.WeldJointEfficiency * num;
    this.TMin = this.DesignPressure * this.NominalOutsideDiameter / (2.0 * this.AllowableStrength);
    return CalculatorResult.Completed;
  }
}
