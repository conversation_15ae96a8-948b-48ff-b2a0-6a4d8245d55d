// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_8.MAWP.MAWP
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_8;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_8.MAWP;

public class MAWP : BaseCalculator
{
  private double TempLowerFactor;
  private double TempUpperFactorT;
  private double Tlower;
  private double Tupper;
  private MaterialASMEB31_8DTO _material;

  public double DesignTemperature { get; set; }

  private double TestNumber { get; set; }

  public string LocationClass { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double FCAi { get; set; }

  public double FCAe { get; set; }

  public double? B31_8DesignFactor { get; set; }

  private string TemperatureLimits { get; set; }

  public double TemperatureDeratingFactorTTable841_116A { get; set; }

  public double? YieldStrengthNew { get; set; }

  private double LongitudinalJointFactorE_Table841_115a { get; set; }

  private bool OutsidediameterMinusTmin { get; set; }

  public double WeldJointEfficiency { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  public double MAWPValue { get; private set; }

  public double CorrodedWallThickness { get; private set; }

  public double AllowableStrength { get; private set; }

  public MAWP()
  {
  }

  public MAWP(MaterialASMEB31_8DTO material)
  {
    this._material = material;
    this.Warnings = new List<string>();
  }

  public CalculatorResult CalculateAssessment()
  {
    try
    {
      if (this.DesignTemperature > 232.2222222)
      {
        this.TemperatureLimits = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
        this.ErrorMessage = this.TemperatureLimits;
        return CalculatorResult.Fail;
      }
      if (this._material.UserDefined)
      {
        this.LongitudinalJointFactorE_Table841_115a = this.WeldJointEfficiency;
        this.AllowableStrength = this.YieldStrengthNew.Value;
        this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
      }
      else
      {
        this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
        if (this._material.MinimumYieldStrengthMpa.HasValue)
          this.AllowableStrength = this._material.MinimumYieldStrengthMpa.Value;
        this.LongitudinalJointFactorE_Table841_115a = this.WeldJointEfficiency;
      }
      Utilities.Table841_116A(this.DesignTemperature, out this.TempLowerFactor, out this.TempUpperFactorT, out this.Tlower, out this.Tupper);
      this.TemperatureDeratingFactorTTable841_116A = (this.Tlower - this.Tupper) / (this.TempLowerFactor - this.TempUpperFactorT) * (this.DesignTemperature - this.TempUpperFactorT) + this.Tupper;
      this.AllowableStrength = this.AllowableStrength * this.LongitudinalJointFactorE_Table841_115a * this.TemperatureDeratingFactorTTable841_116A * this.B31_8DesignFactor.Value;
      double num = this.NominalThickness - this.InternalUniformMetalLoss - this.ExternalUniformMetalLoss - this.FCAi - this.FCAe;
      this.CorrodedWallThickness = num;
      this.MAWPValue = 2.0 * this.AllowableStrength * num / this.NominalOutsideDiameter;
      return CalculatorResult.Completed;
    }
    catch (Exception ex)
    {
      this.ErrorMessage = $"MAWP B31.8 failed: {ex.Message}";
      return CalculatorResult.Fail;
    }
  }
}
