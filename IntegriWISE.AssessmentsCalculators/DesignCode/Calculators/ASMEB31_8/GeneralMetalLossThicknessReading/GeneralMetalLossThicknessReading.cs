// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_8.GeneralMetalLossThicknessReading.GeneralMetalLossThicknessReading
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.AssessmentsCalculators.Calculators.ASMEB31_8;
using IntegriWISE.AssessmentsCalculators.Calculators.Thickness;
using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators.DesignCode.Calculators.ASMEB31_8.GeneralMetalLossThicknessReading;

public class GeneralMetalLossThicknessReading : BaseCalculator
{
  private bool OutsideDiaMinusTMin_GT_0;
  private MaterialASMEB31_8DTO _material;
  private ThicknessReadings _thicknessReadingsCalculator;

  public bool ToLevel2 { get; set; }

  public double DesignTemperature { get; set; }

  public double DesignPressure { get; set; }

  public double NominalOutsideDiameter { get; set; }

  public double NominalThickness { get; set; }

  public double WeldJointEfficiency { get; set; }

  public double? DesignFactor { private get; set; }

  public string LocationClass { private get; set; }

  public double InternalUniformMetalLoss { get; set; }

  public double InternalFutureCorrosionAllowance { get; set; }

  public double ExternalUniformMetalLoss { get; set; }

  public double ExternalFutureCorrosionAllowance { get; set; }

  public double AllowableStrength { get; set; }

  public double TMin { get; set; }

  public double Tmm { get; set; }

  public double Tam { get; set; }

  public double Cov { get; set; }

  public double Tlim { get; set; }

  public double MAWPrTamMinusFCA { get; set; }

  public double MAWPrTamMinusFCAOverRSFa { get; set; }

  public bool AverageMeasuredThicknessL1 { get; set; }

  public bool MawpL1 { get; set; }

  public bool MinimumMeasuredThicknessL1 { get; set; }

  public bool Level1Passed { get; set; }

  public string Level1ResultMsg { get; set; }

  public bool AverageMeasuredThicknessL2 { get; set; }

  public bool MawpL2 { get; set; }

  public bool MinimumMeasuredThicknessL2 { get; set; }

  public bool Level2Passed { get; set; }

  public string Level2ResultMsg { get; set; }

  public List<string> Warnings { get; private set; }

  public string ErrorMessage { get; private set; }

  private double ThicknessReadingsN { get; set; }

  private double S { get; set; }

  private string CheckCOV { get; set; }

  private double tamMinusFCA { get; set; }

  private double tmmMinusFCA { get; set; }

  private double Max0_5MminTlim { get; set; }

  private double Max0_5TminTlim { get; set; }

  private double RSFa { get; set; }

  private double TamMinusFCAOverRSFa { get; set; }

  private double LongitudinalJointFactorE_Table841_115a { get; set; }

  public GeneralMetalLossThicknessReading(
    MaterialASMEB31_8DTO material,
    ThicknessReadings thicknessReadingsCalculator)
  {
    this._material = material;
    this.Warnings = new List<string>();
    this._thicknessReadingsCalculator = thicknessReadingsCalculator;
  }

  public CalculatorResult CalculateAssessment()
  {
    if (this.DesignTemperature > 232.2222222)
    {
      this.ErrorMessage = "Temperature out of range.The temperature is higher than the maximum permissible operating temperature of this material";
      return CalculatorResult.Fail;
    }
    try
    {
      if (this._material.UserDefined)
      {
        this.AllowableStrength = this._material.YieldStrengthNew.Value;
        this.Warnings.Add("The tensile properties used in the assessment come from the material properties entered by the user.");
      }
      else
      {
        this.Warnings.Add("The tensile properties belong to the latest edition of the design code. Please make sure the value conforms with the original design document.");
        if (this._material.MinimumYieldStrengthMpa.HasValue)
          this.AllowableStrength = this._material.MinimumYieldStrengthMpa.Value;
      }
      this.LongitudinalJointFactorE_Table841_115a = this.WeldJointEfficiency;
      double TempLowerFactor;
      double TempUpperFactorT;
      double TLower;
      double TUpper;
      Utilities.Table841_116A(this.DesignTemperature, out TempLowerFactor, out TempUpperFactorT, out TLower, out TUpper);
      double num = (TLower - TUpper) / (TempLowerFactor - TempUpperFactorT) * (this.DesignTemperature - TempUpperFactorT) + TUpper;
      if (!this.DesignFactor.HasValue)
        this.DesignFactor = new double?(Utilities.Table841_114A(this.LocationClass));
      this.AllowableStrength = this.AllowableStrength * this.DesignFactor.Value * this.LongitudinalJointFactorE_Table841_115a * num;
      this.TMin = this.DesignPressure * this.NominalOutsideDiameter / (2.0 * this.AllowableStrength);
      this.ThicknessReadingsN = (double) this._thicknessReadingsCalculator.TotalNumThicknessPoints();
      this.S = this._thicknessReadingsCalculator.SumOfThicknessReadings();
      this.Tam = this.S / this.ThicknessReadingsN;
      this.Tmm = this._thicknessReadingsCalculator.MinimumThicknessPointValue();
      this.S = this._thicknessReadingsCalculator.SumOfThicknessReadingsPow2();
      this.Cov = 1.0 / this.Tam * Math.Pow(this.S / (this.ThicknessReadingsN - 1.0), 0.5) * 100.0;
      if (this.Cov > 10.0)
      {
        this.CheckCOV = "The Coefficient of Variation (COV) of the thickness readings is greater than 10%, the general metal loss procedure can not be used in this case. Then thickness profiles shall be considered for use in the assessment (see paragraph 4.3.3.3 API 579)";
        this.ErrorMessage = this.CheckCOV;
        return CalculatorResult.Fail;
      }
      this.tamMinusFCA = this.Tam - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
      this.Tlim = Math.Max(0.2 * this.NominalThickness, 2.5);
      this.tmmMinusFCA = this.Tmm - this.InternalFutureCorrosionAllowance - this.ExternalFutureCorrosionAllowance;
      this.Max0_5TminTlim = Math.Max(0.5 * this.TMin, this.Tlim);
      this.MAWPrTamMinusFCA = 2.0 * this.AllowableStrength * this.tamMinusFCA / this.NominalOutsideDiameter;
      this.RSFa = 0.9;
      this.AverageMeasuredThicknessL1 = this.tamMinusFCA >= this.TMin;
      this.MawpL1 = this.MAWPrTamMinusFCA >= this.DesignPressure;
      this.MinimumMeasuredThicknessL1 = this.tmmMinusFCA >= this.Max0_5TminTlim;
      if (this.AverageMeasuredThicknessL1 && this.MawpL1 && this.MinimumMeasuredThicknessL1)
      {
        this.Level1ResultMsg = "The Level 1 is Assessment ACCEPTABLE";
        this.Level1Passed = true;
        return CalculatorResult.Completed;
      }
      this.Level1ResultMsg = "The Level 1 Assessment is UNACCEPTABLE";
      this.Level1Passed = false;
      if (!this.ToLevel2)
        return CalculatorResult.Completed;
      this.MinimumMeasuredThicknessL2 = this.MinimumMeasuredThicknessL1;
      this.TamMinusFCAOverRSFa = this.tamMinusFCA / this.RSFa;
      this.MAWPrTamMinusFCAOverRSFa = 2.0 * this.AllowableStrength * this.TamMinusFCAOverRSFa / this.NominalOutsideDiameter;
      this.AverageMeasuredThicknessL2 = this.tamMinusFCA >= this.TMin * this.RSFa;
      this.MawpL2 = this.MAWPrTamMinusFCAOverRSFa >= this.DesignPressure;
      if (this.MinimumMeasuredThicknessL2 && this.AverageMeasuredThicknessL2 && this.MawpL2)
      {
        this.Level2ResultMsg = "The Level 2 Assessment is ACCEPTABLE";
        this.Level2Passed = true;
      }
      else
      {
        this.Level2ResultMsg = "The Level 2 Assessment is UNACCEPTABLE";
        this.Level2Passed = false;
      }
      return CalculatorResult.Completed;
    }
    catch (Exception ex)
    {
      this.ErrorMessage = $"GeneralMetalLossThicknessProfile Cylindrical failed: {ex.Message}";
      return CalculatorResult.Fail;
    }
  }
}
