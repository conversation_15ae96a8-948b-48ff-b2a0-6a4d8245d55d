// Decompiled with JetBrains decompiler
// Type: IntegriWISE.AssessmentsCalculators.BaseCalculator
// Assembly: IntegriWISE.AssessmentsCalculators, Version=1.0.1.24840, Culture=neutral, PublicKeyToken=null
// MVID: 2B9CCB22-44E4-43E2-8DEB-2049BCF9571B
// Assembly location: C:\Program Files (x86)\TWI Software\IntegriWISE\IntegriWISE.AssessmentsCalculators.dll

using IntegriWISE.DataTransferObjects.Material;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;

#nullable disable
namespace IntegriWISE.AssessmentsCalculators;

public abstract class BaseCalculator
{
  protected bool IsNumeric(string text)
  {
    return !string.IsNullOrEmpty(text) && Regex.IsMatch(text, "^\\s*\\-?\\d+(\\.\\d+)?\\s*$");
  }

  protected bool GetRtFigure5_8(
    List<MaterialTSFCurveDTO> api579Table5_4List,
    double TSF,
    double LambdaC,
    out double RtFigure5_8,
    out string errorMessage)
  {
    errorMessage = string.Empty;
    MaterialTSFCurveDTO materialTsfCurveDto1 = api579Table5_4List.Where<MaterialTSFCurveDTO>((Func<MaterialTSFCurveDTO, bool>) (x =>
    {
      double? tsf = x.TSF;
      double num = TSF;
      return tsf.GetValueOrDefault() > num && tsf.HasValue;
    })).OrderBy<MaterialTSFCurveDTO, double?>((Func<MaterialTSFCurveDTO, double?>) (x => x.TSF)).FirstOrDefault<MaterialTSFCurveDTO>();
    if (materialTsfCurveDto1 == null)
    {
      errorMessage = "TSF Outside normal operating conditions";
      RtFigure5_8 = -1.0;
      return false;
    }
    MaterialTSFCurveDTO materialTsfCurveDto2 = api579Table5_4List.Where<MaterialTSFCurveDTO>((Func<MaterialTSFCurveDTO, bool>) (x =>
    {
      double? tsf = x.TSF;
      double num = TSF;
      return tsf.GetValueOrDefault() < num && tsf.HasValue;
    })).OrderByDescending<MaterialTSFCurveDTO, double?>((Func<MaterialTSFCurveDTO, double?>) (x => x.TSF)).FirstOrDefault<MaterialTSFCurveDTO>() ?? materialTsfCurveDto1;
    double num1 = materialTsfCurveDto2.TSF.Value;
    double num2 = materialTsfCurveDto1.TSF.Value;
    double num3 = materialTsfCurveDto2.LambdaCMinus02.Value;
    double num4 = materialTsfCurveDto1.LambdaCMinus02.Value;
    double num5 = num1 != 2.3 ? (TSF - num1) / (num2 - num1) * (num4 - num3) + num3 : num3;
    double num6 = materialTsfCurveDto2.C1.Value;
    double num7 = materialTsfCurveDto2.C2.Value;
    double num8 = materialTsfCurveDto2.C3.Value;
    double num9 = materialTsfCurveDto2.C4.Value;
    double num10 = materialTsfCurveDto2.C5.Value;
    double num11 = materialTsfCurveDto2.C6.Value;
    double num12 = materialTsfCurveDto1.C1.Value;
    double num13 = materialTsfCurveDto1.C2.Value;
    double num14 = materialTsfCurveDto1.C3.Value;
    double num15 = materialTsfCurveDto1.C4.Value;
    double num16 = materialTsfCurveDto1.C5.Value;
    double num17 = materialTsfCurveDto1.C6.Value;
    double num18;
    double num19;
    double num20;
    double num21;
    double num22;
    double num23;
    if (num1 == 2.3)
    {
      num18 = num6;
      num19 = num7;
      num20 = num8;
      num21 = num9;
      num22 = num10;
      num23 = num11;
    }
    else
    {
      num18 = (TSF - num1) / (num2 - num1) * (num12 - num6) + num6;
      num19 = (TSF - num1) / (num2 - num1) * (num13 - num7) + num7;
      num20 = (TSF - num1) / (num2 - num1) * (num14 - num8) + num8;
      num21 = (TSF - num1) / (num2 - num1) * (num15 - num9) + num9;
      num22 = (TSF - num1) / (num2 - num1) * (num16 - num10) + num10;
      num23 = (TSF - num1) / (num2 - num1) * (num17 - num11) + num11;
    }
    RtFigure5_8 = LambdaC > num5 ? num18 + num19 / LambdaC + num20 / Math.Pow(LambdaC, 2.0) + num21 / Math.Pow(LambdaC, 3.0) + num22 / Math.Pow(LambdaC, 4.0) + num23 / Math.Pow(LambdaC, 5.0) : 0.2;
    return true;
  }

  protected bool GetRtFigure5_8(
    List<MaterialTSFCurveDTO> api579Table5_4List,
    double TSF,
    double LambdaC,
    RtFigure5_8Result RtFigure5_8,
    out string errorMessage)
  {
    errorMessage = string.Empty;
    MaterialTSFCurveDTO materialTsfCurveDto1 = api579Table5_4List.Where<MaterialTSFCurveDTO>((Func<MaterialTSFCurveDTO, bool>) (x =>
    {
      double? tsf = x.TSF;
      double num = TSF;
      return tsf.GetValueOrDefault() > num && tsf.HasValue;
    })).OrderBy<MaterialTSFCurveDTO, double?>((Func<MaterialTSFCurveDTO, double?>) (x => x.TSF)).FirstOrDefault<MaterialTSFCurveDTO>();
    if (materialTsfCurveDto1 == null)
    {
      errorMessage = "TSF Outside normal operating conditions";
      RtFigure5_8.RtFigure5_8 = -1.0;
      return false;
    }
    MaterialTSFCurveDTO materialTsfCurveDto2 = api579Table5_4List.Where<MaterialTSFCurveDTO>((Func<MaterialTSFCurveDTO, bool>) (x =>
    {
      double? tsf = x.TSF;
      double num = TSF;
      return tsf.GetValueOrDefault() < num && tsf.HasValue;
    })).OrderByDescending<MaterialTSFCurveDTO, double?>((Func<MaterialTSFCurveDTO, double?>) (x => x.TSF)).FirstOrDefault<MaterialTSFCurveDTO>() ?? materialTsfCurveDto1;
    double num1 = materialTsfCurveDto2.TSF.Value;
    double num2 = materialTsfCurveDto1.TSF.Value;
    double num3 = materialTsfCurveDto2.LambdaCMinus02.Value;
    double num4 = materialTsfCurveDto1.LambdaCMinus02.Value;
    RtFigure5_8.LambdaCMinus0_2 = num1 != 2.3 ? (TSF - num1) / (num2 - num1) * (num4 - num3) + num3 : num3;
    double num5 = materialTsfCurveDto2.C1.Value;
    double num6 = materialTsfCurveDto2.C2.Value;
    double num7 = materialTsfCurveDto2.C3.Value;
    double num8 = materialTsfCurveDto2.C4.Value;
    double num9 = materialTsfCurveDto2.C5.Value;
    double num10 = materialTsfCurveDto2.C6.Value;
    double num11 = materialTsfCurveDto1.C1.Value;
    double num12 = materialTsfCurveDto1.C2.Value;
    double num13 = materialTsfCurveDto1.C3.Value;
    double num14 = materialTsfCurveDto1.C4.Value;
    double num15 = materialTsfCurveDto1.C5.Value;
    double num16 = materialTsfCurveDto1.C6.Value;
    if (num1 == 2.3)
    {
      RtFigure5_8.C1 = num5;
      RtFigure5_8.C2 = num6;
      RtFigure5_8.C3 = num7;
      RtFigure5_8.C4 = num8;
      RtFigure5_8.C5 = num9;
      RtFigure5_8.C6 = num10;
    }
    else
    {
      RtFigure5_8.C1 = (TSF - num1) / (num2 - num1) * (num11 - num5) + num5;
      RtFigure5_8.C2 = (TSF - num1) / (num2 - num1) * (num12 - num6) + num6;
      RtFigure5_8.C3 = (TSF - num1) / (num2 - num1) * (num13 - num7) + num7;
      RtFigure5_8.C4 = (TSF - num1) / (num2 - num1) * (num14 - num8) + num8;
      RtFigure5_8.C5 = (TSF - num1) / (num2 - num1) * (num15 - num9) + num9;
      RtFigure5_8.C6 = (TSF - num1) / (num2 - num1) * (num16 - num10) + num10;
    }
    RtFigure5_8.RtFigure5_8 = LambdaC > RtFigure5_8.LambdaCMinus0_2 ? RtFigure5_8.C1 + RtFigure5_8.C2 / LambdaC + RtFigure5_8.C3 / Math.Pow(LambdaC, 2.0) + RtFigure5_8.C4 / Math.Pow(LambdaC, 3.0) + RtFigure5_8.C5 / Math.Pow(LambdaC, 4.0) + RtFigure5_8.C6 / Math.Pow(LambdaC, 5.0) : 0.2;
    return true;
  }
}
